import { NestFactory } from '@nestjs/core';
import {
  FastifyAdapter,
  NestFastifyApplication,
} from '@nestjs/platform-fastify';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { EXCLUDE_APIS, Prefix } from './common/constants/index.constant';
import { Logger } from '@nestjs/common';

async function bootstrap() {
  console.log('rebuild 3');
  const app = await NestFactory.create<NestFastifyApplication>(
    AppModule,
    new FastifyAdapter(),
  );

  app.enableVersioning();
  app.enableCors({ origin: '*', credentials: true });
  app.setGlobalPrefix(Prefix.GLOBAL, {
    exclude: [EXCLUDE_APIS.ADD_POINT_GO, EXCLUDE_APIS.CHECK_PRODUCT],
  });

  const config = new DocumentBuilder()
    .setTitle('Vitadairy Loyalty API')
    .setDescription('Vitadairy Loyalty API')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup(`${Prefix.GLOBAL}/api`, app, document);

  await app.listen(process.env.PORT || 5000, '0.0.0.0');
  Logger.log(`APP RUN ON PORT ${process.env.PORT}`);
}
bootstrap();
