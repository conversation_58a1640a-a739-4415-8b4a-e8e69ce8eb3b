import { IsValidDate, IsValidEnum, IsValidText, IsValidNumber } from '../../../../common/decorators/custom-validator.decorator';
import { FeedbackType } from '../../../enums/action.enum';
import { ChartTime } from '../../../enums/chart-time.enum';

export class FeedbackChartAdminReqDto {
  @IsValidText({ required: false, maxLength: 20 })
  phoneNumber?: string;

  @IsValidDate({ required: false })
  fromDate?: Date;

  @IsValidDate({ required: false })
  toDate?: Date;

  @IsValidEnum({ enum: FeedbackType, required: false })
  action?: FeedbackType;

  @IsValidNumber({ required: false, min: 1, max: 5 })
  starCount?: number;

  @IsValidEnum({ enum: ChartTime })
  chartTime: ChartTime;
} 