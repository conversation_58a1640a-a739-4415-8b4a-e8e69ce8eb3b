import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { USER_PACKAGE_NAME, USER_SERVICE_NAME } from '../proto/user.pb';
import {
  ACCOUNT_PACKAGE_NAME,
  ACCOUNT_SERVICE_NAME,
} from '../proto/account.pb';
import { join } from 'path';
import { ReviewTemplateRepository } from './repositories/review-template.repository';
import { ReviewTemplateAdminController } from './controllers/admin/review-template.admin.controller';
import { ReviewTemplateUserController } from './controllers/user/review-template.user.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ReviewTemplate } from './entities/review-template.entity';
import { AuthService } from '../auth/auth.service';
import { ReviewTemplateAdminService } from './services/review-template/review-template.admin.service';
import { ReviewTemplateUserService } from './services/review-template/review-template.user.service';
import { TrackingUserActionUserController } from './controllers/user/tracking-user-action.user.controller';
import { TrackingUserActionUserService } from './services/tracking-user-action/tracking-user-action.user.service';
import { TrackingUserActionAdminService } from './services/tracking-user-action/tracking-user-action.admin.service';
import { TrackingUserActionRepository } from './repositories/tracking-user-action.repository';
import { TrackingUserAction } from './entities/tracking-user-action.entity';
import { UserActionTrackingConsumer } from './consumers/user-action-tracking.consumer';
import { HistoryPointRepository } from '../point/repositories/history-point.repository';
import { TrackingUserActionAdminController } from './controllers/admin/tracking-user-action.admin.controller';
import { VitaJavaService } from '../external/services/vita-java.service';
import { HttpModule, HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { AdminActionHistoryRepository } from '../admin-action-history/repositories/admin-action-history.repository';
import { AdminRoleRepository } from '../admin_authorization/repositories/admin-role.repository';
import { AdminAclRepository } from '../admin_authorization/repositories/admin-acl.repository';
import { TrackingUserActionCleanupService } from '../cron-job/services/cron-job-tracking-user-action.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([ReviewTemplate, TrackingUserAction]),
    ScheduleModule.forRoot(),
    ClientsModule.register([
      {
        name: USER_SERVICE_NAME,
        transport: Transport.GRPC,
        options: {
          url: 'vtd-service-user-v3:50051',
          package: USER_PACKAGE_NAME,
          protoPath: join(
            __dirname + '/../../../node_modules/vtd-common-v3/proto/user.proto',
          ),
        },
      },
      {
        name: ACCOUNT_SERVICE_NAME,
        transport: Transport.GRPC,
        options: {
          url: 'vtd-service-user-v3:50052',
          package: ACCOUNT_PACKAGE_NAME,
          protoPath: join(
            __dirname +
              '/../../../node_modules/vtd-common-v3/proto/account.proto',
          ),
        },
      },
    ]),
    HttpModule,
  ],
  controllers: [
    ReviewTemplateAdminController,
    ReviewTemplateUserController,
    TrackingUserActionAdminController,
    TrackingUserActionUserController,
  ],
  providers: [
    ReviewTemplateAdminService,
    ReviewTemplateUserService,
    ReviewTemplateRepository,
    TrackingUserActionRepository,
    TrackingUserActionAdminService,
    TrackingUserActionUserService,
    AuthService,
    UserActionTrackingConsumer,
    HistoryPointRepository,
    VitaJavaService,
    TrackingUserActionCleanupService,
    AdminAclRepository,
    AdminRoleRepository,
    AdminActionHistoryRepository,
  ],
  exports: [
    ReviewTemplateAdminService,
    ReviewTemplateUserService,
    TrackingUserActionAdminService,
    TrackingUserActionUserService,
  ],
})
export class FeedbackModule {}
