import { Injectable } from '@nestjs/common';
import { Between, FindOptionsWhere, ILike, In, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import {
  GetAllListFeedbackAdminReqDto,
  GetListFeedbackAdminReqDto,
} from '../../dtos/feedback/req/list-feedback.admin.req.dto';
import { paginate } from 'nestjs-typeorm-paginate';
import { AppResponseDto } from '../../../common/dtos/app-response.dto';
import { Parser } from 'json2csv';
import { FeedbackType } from '../../enums/action.enum';
import { TrackingUserActionRepository } from '../../repositories/tracking-user-action.repository';
import {
  convertDateToDateInTimeZoneAndFormatToString,
  convertToVietnamTime,
  formatDateTime,
} from '../../../common/utils';
import { ReviewTemplateRepository } from '../../repositories/review-template.repository';
import { FeedbackStatus } from '../../enums/feedback-status.enum';
import dayjs from 'dayjs';
import { TIME_ZONE } from '../../../common/constants/index.constant';
import { FeedbackChartAdminReqDto } from '../../dtos/feedback/req/feedback-chart.admin.req.dto';
import { FeedbackDoughnutChartAdminResDto, FeedbackBarChartAdminResDto } from '../../dtos/feedback/res/feedback-chart.admin.res.dto';
import { ChartTime } from '../../enums/chart-time.enum';
@Injectable()
export class TrackingUserActionAdminService {
  constructor(
    private readonly trackingUserActionRepository: TrackingUserActionRepository,
    private readonly reviewTemplateRepository: ReviewTemplateRepository,
  ) {}

  private buildWhereClause(
    dto: GetListFeedbackAdminReqDto,
  ): FindOptionsWhere<any> {
    const { phoneNumber, fromDate, toDate, action, starCount } = dto;
    const whereClause: FindOptionsWhere<any> = {
      feedbackStatus: FeedbackStatus.REVIEWED,
    };

    // Filter by phone number
    if (phoneNumber) {
      whereClause.user = {
        phoneNumber: ILike(`%${phoneNumber}%`),
      };
    }

    if (fromDate && toDate && fromDate > toDate) {
      throw new Error('fromDate must be before or equal to toDate');
    }

    // Filter by date range
    if (fromDate && toDate) {
      whereClause.lastShown = Between(fromDate, toDate);
    } else if (fromDate) {
      whereClause.lastShown = MoreThanOrEqual(fromDate);
    } else if (toDate) {
      whereClause.lastShown = LessThanOrEqual(toDate);
    }

    // Filter by action
    if (action) {
      whereClause.action = action as FeedbackType;
    }

    // Filter by star count
    if (starCount) {
      whereClause.starCount = starCount;
    }

    return whereClause;
  }

  private buildChartWhereClause(
    dto: FeedbackChartAdminReqDto,
  ): FindOptionsWhere<any> {
    const { phoneNumber, fromDate, toDate, action, starCount } = dto;
    const whereClause: FindOptionsWhere<any> = {
      feedbackStatus: FeedbackStatus.REVIEWED,
    };

    // Filter by phone number
    if (phoneNumber) {
      whereClause.user = {
        phoneNumber: ILike(`%${phoneNumber}%`),
      };
    }

    if (fromDate && toDate && fromDate > toDate) {
      throw new Error('fromDate must be before or equal to toDate');
    }

    // Filter by date range
    if (fromDate && toDate) {
      whereClause.lastShown = Between(fromDate, toDate);
    } else if (fromDate) {
      whereClause.lastShown = MoreThanOrEqual(fromDate);
    } else if (toDate) {
      whereClause.lastShown = LessThanOrEqual(toDate);
    }

    // Filter by action
    if (action) {
      whereClause.action = action as FeedbackType;
    }

    // Filter by star count
    if (starCount) {
      whereClause.starCount = starCount;
    }

    return whereClause;
  }

  async getListFeedback(dto: GetListFeedbackAdminReqDto): Promise<any> {
    const { limit, page } = dto;
    const whereClause = this.buildWhereClause(dto);

    const { items, meta } = await paginate(
      this.trackingUserActionRepository,
      { limit, page },
      {
        where: whereClause,
        relations: ['user'],
        order: {
          createdDate: 'DESC',
        },
      },
    );

    // Fetch review templates for each feedback item
    for (const item of items) {
      (item as any).lastShown = convertDateToDateInTimeZoneAndFormatToString(
        item.lastShown,
      );
      if (item.tagIds?.length) {
        const reviewTemplates = await this.reviewTemplateRepository.findBy({
          id: In(item.tagIds),
        });
        (item as any).reviewTemplates = reviewTemplates;
      } else {
        (item as any).reviewTemplates = [];
      }
    }

    return AppResponseDto.fromNestJsPagination(items, meta);
  }

  async getFeedbackById(id: number): Promise<any> {
    const feedback = await this.trackingUserActionRepository.findOne({
      where: { id },
      relations: ['user'],
    });

    if (feedback.tagIds?.length) {
      const reviewTemplates = await this.reviewTemplateRepository.findBy({
        id: In(feedback.tagIds),
      });
      (feedback as any).reviewTemplates = reviewTemplates;
    }

    return new AppResponseDto(feedback);
  }

  async getAllListFeedback(dto: GetAllListFeedbackAdminReqDto): Promise<any> {
    const whereClause: FindOptionsWhere<any> = this.buildWhereClause(dto);

    const items = await this.trackingUserActionRepository.find({
      where: whereClause,
      relations: ['user'],
      order: { createdDate: 'DESC' },
    });

    for (const item of items) {
      if (item.tagIds?.length) {
        const reviewTemplates = await this.reviewTemplateRepository.findBy({
          id: In(item.tagIds),
        });
        (item as any).reviewTemplates = reviewTemplates;
      } else {
        (item as any).reviewTemplates = [];
      }
    }

    return new AppResponseDto(items);
  }

  async exportCsv(dto: GetListFeedbackAdminReqDto): Promise<any> {
    const whereClause: FindOptionsWhere<any> = this.buildWhereClause(dto);

    const trackingUserActions = await this.trackingUserActionRepository.find({
      where: whereClause,
      relations: ['user'],
      order: { createdDate: 'DESC' },
    });

    for (const item of trackingUserActions) {
      if (item.tagIds?.length) {
        const reviewTemplates = await this.reviewTemplateRepository.findBy({
          id: In(item.tagIds),
        });
        (item as any).reviewTemplates = reviewTemplates
          .map((rt: any) => rt.tag)
          .join(', ');
      } else {
        (item as any).reviewTemplates = '';
      }
    }

    const transformedData = trackingUserActions.map((item: any) => ({
      id: item.id,
      lastShown: convertToVietnamTime(item.lastShown),
      customerName: item.user.firstName + ' ' + item.user.lastName,
      phoneNumber: item.user.phoneNumber,
      starCount: item.starCount,
      action: item.action,
      feedback: item.feedback,
      reviewTemplates: item.reviewTemplates,
    }));

    const parser = new Parser({
      header: true,
      fields: [
        { label: 'ID', value: 'id' },
        { label: 'Ngày đánh giá', value: 'lastShown' },
        { label: 'Tên khách hàng', value: 'customerName' },
        { label: 'Số điện thoại', value: 'phoneNumber' },
        { label: 'Số sao', value: 'starCount' },
        { label: 'Hoạt động', value: 'action' },
        { label: 'Chi tiết đánh giá', value: 'feedback' },
        { label: 'Danh sách tag', value: 'reviewTemplates' },
      ],
    });

    return parser.parse(transformedData);
  }

  // Chart star count options (colors handled by frontend)
  private static readonly STAR_COUNT_OPTIONS = [
    { value: 1, label: '1 sao' },
    { value: 2, label: '2 sao' },
    { value: 3, label: '3 sao' },
    { value: 4, label: '4 sao' },
    { value: 5, label: '5 sao' },
  ];

  // Helper for date range check
  private isDateInRange(date: Date, start: dayjs.Dayjs, end: dayjs.Dayjs): boolean {
    const d = dayjs(date);
    return (d.isAfter(start) || d.isSame(start, 'day')) && (d.isBefore(end) || d.isSame(end, 'day'));
  }

  async getFeedbackDoughnutChartData(dto: FeedbackChartAdminReqDto): Promise<FeedbackDoughnutChartAdminResDto> {
    const whereClause: FindOptionsWhere<any> = this.buildChartWhereClause(dto);
    const items = await this.trackingUserActionRepository.find({
      where: whereClause,
      relations: ['user'],
      order: { createdDate: 'DESC' },
    });

    const chartTimeValue = dto.chartTime || ChartTime.YEAR;
    const isFromValid = dto.fromDate instanceof Date && !isNaN(new Date(dto.fromDate).getTime());
    const isToValid = dto.toDate instanceof Date && !isNaN(new Date(dto.toDate).getTime());
    const current = dayjs();
    let start: dayjs.Dayjs;
    let end: dayjs.Dayjs;
    
    if (isFromValid && isToValid) {
      start = dayjs(dto.fromDate);
      end = dayjs(dto.toDate);
    } else if (isFromValid && !isToValid) {
      start = dayjs(dto.fromDate);
      end = current.endOf(chartTimeValue.toLowerCase() as dayjs.OpUnitType);
    } else if (!isFromValid && isToValid) {
      start = dayjs(0);
      end = dayjs(dto.toDate);
    } else {
      start = dayjs(0);
      end = current.endOf(chartTimeValue.toLowerCase() as dayjs.OpUnitType);
    }

    // --- FILTER LOGIC ---
    let filteredData: any[] = [];
    if (dto.action) {
      // Case 1: Filter by specific action
      filteredData = items.filter((item) =>
        this.isDateInRange(item.lastShown, start, end) &&
        item.action === dto.action &&
        typeof item.starCount === 'number' &&
        item.starCount >= 1 &&
        item.starCount <= 5
      );
      // Always return all star labels and their counts for the selected action
      const starCountMap = new Map<number, number>();
      filteredData.forEach(({ starCount }) => {
        starCountMap.set(starCount, (starCountMap.get(starCount) || 0) + 1);
      });
      const starCountObjects = TrackingUserActionAdminService.STAR_COUNT_OPTIONS.map((option) => ({
        label: option.label,
        count: starCountMap.get(option.value) || 0,
      }));
      return {
        labels: starCountObjects.map((item) => item.label),
        datasets: [
          {
            data: starCountObjects.map((item) => item.count),
          },
        ],
      };
    } else {
      // Case 2: No action selected, show all actions
      filteredData = items.filter((item) =>
        this.isDateInRange(item.lastShown, start, end) &&
        typeof item.starCount === 'number' &&
        item.starCount >= 1 &&
        item.starCount <= 5
      );
    }

    if (filteredData.length === 0) {
      return { labels: [], datasets: [] };
    }

    const starCountMap = new Map<number, number>();
    filteredData.forEach(({ starCount }) => {
      starCountMap.set(starCount, (starCountMap.get(starCount) || 0) + 1);
    });

    const starCountObjects = TrackingUserActionAdminService.STAR_COUNT_OPTIONS.map((option) => ({
      label: option.label,
      count: starCountMap.get(option.value) || 0,
    }));

    return {
      labels: starCountObjects.map((item) => item.label),
      datasets: [
        {
          data: starCountObjects.map((item) => item.count),
        },
      ],
    };
  }

  async getFeedbackBarChartData(dto: FeedbackChartAdminReqDto): Promise<FeedbackBarChartAdminResDto> {
    const whereClause: FindOptionsWhere<any> = this.buildChartWhereClause(dto);
    const items = await this.trackingUserActionRepository.find({
      where: whereClause,
      relations: ['user'],
      order: { createdDate: 'DESC' },
    });

    const formatMap = {
      [ChartTime.DAY]: ['YYYY-MM-DD', 'DD MMM YYYY'],
      [ChartTime.MONTH]: ['YYYY-MM', 'MMM YYYY'],
      [ChartTime.YEAR]: ['YYYY', 'YYYY'],
    };
    const [keyFormat, labelFormat] = formatMap[dto.chartTime];
    const isFromDateValid = dto.fromDate instanceof Date && !isNaN(new Date(dto.fromDate).getTime());
    const isToDateValid = dto.toDate instanceof Date && !isNaN(new Date(dto.toDate).getTime());
    const current = dayjs();
    let start: dayjs.Dayjs;
    let end: dayjs.Dayjs;
    
    if (isFromDateValid && isToDateValid) {
      start = dayjs(dto.fromDate);
      end = dayjs(dto.toDate);
    } else if (isFromDateValid && !isToDateValid) {
      start = dayjs(dto.fromDate);
      end = current.endOf(dto.chartTime.toLowerCase() as dayjs.OpUnitType);
    } else if (!isFromDateValid && isToDateValid) {
      start = dayjs(0);
      end = dayjs(dto.toDate);
    } else {
      start = dayjs(0);
      end = current.endOf(dto.chartTime.toLowerCase() as dayjs.OpUnitType);
    }

    // --- FILTER LOGIC ---
    let filtered: any[] = [];
    if (dto.action) {
      // Case 1: Filter by action (may also have date) - Group by time periods
      filtered = items.filter((i) =>
        this.isDateInRange(i.lastShown, start, end) &&
        i.action === dto.action &&
        typeof i.starCount === 'number' &&
        i.starCount >= 1 &&
        i.starCount <= 5
      );
      // Group by time
      const fullDateKeys = Array.from(
        new Set(filtered.map((i) => dayjs(i.lastShown).format(keyFormat)))
      ).sort();
      const labels = fullDateKeys.map((d) => dayjs(d).format(labelFormat));
      const labelIndexMap = Object.fromEntries(fullDateKeys.map((d, i) => [d, i]));
      // Always return 5 datasets for 1-5 sao
      const datasets = TrackingUserActionAdminService.STAR_COUNT_OPTIONS.map((option) => ({
        label: option.label,
        data: Array(labels.length).fill(0),
      }));
      filtered.forEach(({ lastShown, starCount }) => {
        const key = dayjs(lastShown).format(keyFormat);
        const idx = labelIndexMap[key];
        if (idx !== undefined && starCount >= 1 && starCount <= 5) {
          datasets[starCount - 1].data[idx]++;
        }
      });
      return { labels, datasets };
    } else {
      // Case 2: No action selected - Group by star count
      filtered = items.filter((i) =>
        this.isDateInRange(i.lastShown, start, end) &&
        typeof i.starCount === 'number' &&
        i.starCount >= 1 &&
        i.starCount <= 5
      );
      
      // Group by time periods and show star count distribution
      const fullDateKeys = Array.from(
        new Set(filtered.map((i) => dayjs(i.lastShown).format(keyFormat)))
      ).sort();
      const labels = fullDateKeys.map((d) => dayjs(d).format(labelFormat));
      const labelIndexMap = Object.fromEntries(fullDateKeys.map((d, i) => [d, i]));

      const datasets = TrackingUserActionAdminService.STAR_COUNT_OPTIONS.map(({ label }) => ({
        label,
        data: Array(labels.length).fill(0),
      }));

      filtered.forEach(({ lastShown, starCount }) => {
        const key = dayjs(lastShown).format(keyFormat);
        const idx = labelIndexMap[key];
        if (idx !== undefined) {
          datasets[starCount - 1].data[idx]++;
        }
      });

      return { labels, datasets };
    }
  }

  async getFeedbackCharts(dto: FeedbackChartAdminReqDto): Promise<{ doughnut: FeedbackDoughnutChartAdminResDto; bar: FeedbackBarChartAdminResDto }> {
    // OPTIMIZATION: Fetch data only once, then build both charts
    const whereClause: FindOptionsWhere<any> = this.buildChartWhereClause(dto);
    const items = await this.trackingUserActionRepository.find({
      where: whereClause,
      relations: ['user'],
      order: { createdDate: 'DESC' },
    });

    // Build both charts from single dataset
    const doughnut = this.buildDoughnutChartFromData(items, dto);
    const bar = this.buildBarChartFromData(items, dto);
    
    return { doughnut, bar };
  }

  private buildDoughnutChartFromData(items: any[], dto: FeedbackChartAdminReqDto): FeedbackDoughnutChartAdminResDto {
    const chartTimeValue = dto.chartTime || ChartTime.YEAR;
    const isFromValid = dto.fromDate instanceof Date && !isNaN(new Date(dto.fromDate).getTime());
    const isToValid = dto.toDate instanceof Date && !isNaN(new Date(dto.toDate).getTime());
    const current = dayjs();
    let start: dayjs.Dayjs;
    let end: dayjs.Dayjs;
    
    if (isFromValid && isToValid) {
      start = dayjs(dto.fromDate);
      end = dayjs(dto.toDate);
    } else if (isFromValid && !isToValid) {
      start = dayjs(dto.fromDate);
      end = current.endOf(chartTimeValue.toLowerCase() as dayjs.OpUnitType);
    } else if (!isFromValid && isToValid) {
      start = dayjs(0);
      end = dayjs(dto.toDate);
    } else {
      start = dayjs(0);
      end = current.endOf(chartTimeValue.toLowerCase() as dayjs.OpUnitType);
    }

    // Filter data by date range
    let filteredData: any[] = [];
    if (dto.action) {
      // Case 1: Filter by specific action
      filteredData = items.filter((item) =>
        this.isDateInRange(item.lastShown, start, end) &&
        item.action === dto.action &&
        typeof item.starCount === 'number' &&
        item.starCount >= 1 &&
        item.starCount <= 5
      );
    } else {
      // Case 2: No action selected, show all actions
      filteredData = items.filter((item) =>
        this.isDateInRange(item.lastShown, start, end) &&
        typeof item.starCount === 'number' &&
        item.starCount >= 1 &&
        item.starCount <= 5
      );
    }

    if (filteredData.length === 0) {
      return { labels: [], datasets: [] };
    }

    // Count by star count
    const starCountMap = new Map<number, number>();
    filteredData.forEach(({ starCount }) => {
      starCountMap.set(starCount, (starCountMap.get(starCount) || 0) + 1);
    });

    const starCountObjects = TrackingUserActionAdminService.STAR_COUNT_OPTIONS.map((option) => ({
      label: option.label,
      count: starCountMap.get(option.value) || 0,
    }));

    return {
      labels: starCountObjects.map((item) => item.label),
      datasets: [
        {
          data: starCountObjects.map((item) => item.count),
        },
      ],
    };
  }

  private buildBarChartFromData(items: any[], dto: FeedbackChartAdminReqDto): FeedbackBarChartAdminResDto {
    const formatMap = {
      [ChartTime.DAY]: ['YYYY-MM-DD', 'DD MMM YYYY'],
      [ChartTime.MONTH]: ['YYYY-MM', 'MMM YYYY'],
      [ChartTime.YEAR]: ['YYYY', 'YYYY'],
    };
    const [keyFormat, labelFormat] = formatMap[dto.chartTime];
    
    const chartTimeValue = dto.chartTime || ChartTime.YEAR;
    const isFromDateValid = dto.fromDate instanceof Date && !isNaN(new Date(dto.fromDate).getTime());
    const isToDateValid = dto.toDate instanceof Date && !isNaN(new Date(dto.toDate).getTime());
    const current = dayjs();
    let start: dayjs.Dayjs;
    let end: dayjs.Dayjs;
    
    if (isFromDateValid && isToDateValid) {
      start = dayjs(dto.fromDate);
      end = dayjs(dto.toDate);
    } else if (isFromDateValid && !isToDateValid) {
      start = dayjs(dto.fromDate);
      end = current.endOf(chartTimeValue.toLowerCase() as dayjs.OpUnitType);
    } else if (!isFromDateValid && isToDateValid) {
      start = dayjs(0);
      end = dayjs(dto.toDate);
    } else {
      start = dayjs(0);
      end = current.endOf(chartTimeValue.toLowerCase() as dayjs.OpUnitType);
    }

    // Filter data by date range
    let filtered: any[] = [];
    if (dto.action) {
      // Case 1: Filter by action (may also have date) - Group by time periods
      filtered = items.filter((i) =>
        this.isDateInRange(i.lastShown, start, end) &&
        i.action === dto.action &&
        typeof i.starCount === 'number' &&
        i.starCount >= 1 &&
        i.starCount <= 5
      );
    } else {
      // Case 2: No action selected - Group by star count
      filtered = items.filter((i) =>
        this.isDateInRange(i.lastShown, start, end) &&
        typeof i.starCount === 'number' &&
        i.starCount >= 1 &&
        i.starCount <= 5
      );
    }

    // Group by time periods
    const fullDateKeys = Array.from(
      new Set(filtered.map((i) => dayjs(i.lastShown).format(keyFormat)))
    ).sort();
    
    const labels = fullDateKeys.map((d) => dayjs(d).format(labelFormat));
    const labelIndexMap = Object.fromEntries(fullDateKeys.map((d, i) => [d, i]));

    // Build datasets for each star count
    const datasets = TrackingUserActionAdminService.STAR_COUNT_OPTIONS.map((option) => ({
      label: option.label,
      data: Array(labels.length).fill(0),
    }));

    // Fill data
    filtered.forEach(({ lastShown, starCount }) => {
      const key = dayjs(lastShown).format(keyFormat);
      const idx = labelIndexMap[key];
      if (idx !== undefined && starCount >= 1 && starCount <= 5) {
        datasets[starCount - 1].data[idx]++;
      }
    });

    return { labels, datasets };
  }
}
