import { TrackingUserActionAdminService } from '../../services/tracking-user-action/tracking-user-action.admin.service';
import {
  GetAllListFeedbackAdminReqDto,
  GetListFeedbackAdminReqDto,
} from '../../dtos/feedback/req/list-feedback.admin.req.dto';
import { Body, Controller, Get, Param, Post, Query, Res } from '@nestjs/common';
import { UseAdmin } from '../../../common/decorators/user.decorator';
import { Prefix } from '../../../common/constants/index.constant';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { FastifyReply } from 'fastify';
import { AppResponse } from '../../../common/decorators/app-response.decorator';
import { FeedbackListResponseDto } from '../../dtos/feedback/res/feedback-list.res.dto';
import { TrackingUserActionCleanupService } from '../../../cron-job/services/cron-job-tracking-user-action.service';
import { FeedbackChartAdminReqDto } from '../../dtos/feedback/req/feedback-chart.admin.req.dto';
import { FeedbackDoughnutChartAdminResDto, FeedbackBarChartAdminResDto } from '../../dtos/feedback/res/feedback-chart.admin.res.dto';

@Controller({
  version: '1',
  path: `${Prefix.ADMIN}/feedback/tracking-user-actions`,
})
@UseAdmin()
@ApiTags('Tracking User Action Admin')
export class TrackingUserActionAdminController {
  constructor(
    private readonly trackingUserActionAdminService: TrackingUserActionAdminService,
    private readonly trackingUserActionCleanupService: TrackingUserActionCleanupService,
  ) {}

  @Get('list')
  @ApiOperation({ summary: 'Get list of feedbacks with filtering' })
  @AppResponse(FeedbackListResponseDto)
  async getListFeedback(@Query() dto: GetListFeedbackAdminReqDto) {
    return await this.trackingUserActionAdminService.getListFeedback(dto);
  }

  @Get('detail/:id')
  @ApiOperation({ summary: 'Get detail of feedback' })
  @AppResponse(FeedbackListResponseDto)
  async getFeedbackById(@Param('id') id: number) {
    return await this.trackingUserActionAdminService.getFeedbackById(id);
  }

  @Get('export')
  @ApiOperation({ summary: 'Export feedbacks to CSV' })
  async exportFeedback(
    @Query() dto: GetListFeedbackAdminReqDto,
    @Res({ passthrough: true }) res: FastifyReply,
  ) {
    const csv = await this.trackingUserActionAdminService.exportCsv(dto);

    res.header(
      'Content-Disposition',
      'attachment; filename=feedback_export.csv',
    );
    res.type('text/csv');

    return res.send(csv);
  }

  @Get('all')
  @ApiOperation({ summary: 'Get all feedbacks' })
  @AppResponse(FeedbackListResponseDto)
  async getAllListFeedback(@Query() dto: GetAllListFeedbackAdminReqDto) {
    return await this.trackingUserActionAdminService.getAllListFeedback(dto);
  }


  @Get('chart/combined')
  @ApiOperation({ summary: 'Get both doughnut and bar chart data for feedback' })
  @AppResponse(Object)
  async getFeedbackCharts(@Query() dto: FeedbackChartAdminReqDto) {
    return await this.trackingUserActionAdminService.getFeedbackCharts(dto);
  }

  @Post('cleanup')
  @ApiOperation({ summary: 'Manually trigger cleanup for a last month' })
  async triggerCleanup() {
    return await this.trackingUserActionCleanupService.cleanupOldRecords();
  }
}
