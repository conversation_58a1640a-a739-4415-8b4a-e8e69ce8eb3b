import {
  <PERSON>Load,
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { VitaQrFile } from '../../qr-code-sbps/entities/qr-vita-file.entity';
import { UserRequestHistoryPoint } from '../../webapp-calosure/entities/user-request-history-point.entity';
import { EventAddCanWebview } from '../../event-add-can/entities/event-add-can-webview.entity';
import { EventWebview } from '../../event/entities/event-webview.entity';
import { EventAddCanPopup } from '../../event-add-can/entities/event-add-can-popup.entity';
import { EventDetailPopup } from '../../event/entities/event-detail-popup.entity';
import { WelcomePopup } from '../../welcome-popup/entities/welcome-popup.entity';
import { StoreQrFileEntity } from '../../store/entities/store-qr-file.entity';

@Entity('file')
export class File {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column()
  bucket: string;

  @Column()
  size: number;

  @Column()
  type: string;

  @CreateDateColumn({ name: 'created_date', type: 'timestamptz' })
  createdDate: Date;

  @DeleteDateColumn({ name: 'deleted_at', type: 'timestamptz' })
  deletedAt: Date;

  @UpdateDateColumn({
    name: 'updated_date',
    type: 'timestamptz',
    nullable: true,
  })
  updatedDate: Date;

  @OneToOne(() => VitaQrFile, (vitaQrFile) => vitaQrFile.fileId)
  vitaQrFile: VitaQrFile;

  @OneToOne(() => UserRequestHistoryPoint, (apfuh) => apfuh.qrCodeFileId)
  qrCodeReference: UserRequestHistoryPoint;

  @OneToOne(() => UserRequestHistoryPoint, (apfuh) => apfuh.spoonCodeFileId)
  spoonCodeReference: UserRequestHistoryPoint;

  @OneToOne(() => EventWebview, (ew) => ew.fileId)
  eventWebview: EventWebview;

  @OneToOne(() => EventDetailPopup, (edp) => edp.fileId)
  eventDetailPopup: EventDetailPopup;

  @OneToOne(() => EventAddCanWebview, (eacw) => eacw.fileId)
  eventAddCanWebview: EventAddCanWebview;

  @OneToOne(() => EventAddCanPopup, (eacp) => eacp.fileId)
  eventAddCanPopup: EventAddCanPopup;

  @OneToOne(() => StoreQrFileEntity, (s) => s.file)
  storeQrFile: StoreQrFileEntity;

  url: string;

  @AfterLoad()
  afterLoad() {
    this.url = `https://storage.googleapis.com/${this.bucket}/${this.name}`;
    if (this.bucket == '' || this.bucket == null) {
      this.url = null;
    }
  }

  @OneToOne(() => WelcomePopup, (wp) => wp.fileId)
  welcomePopup: WelcomePopup;

  constructor(data: any = null) {
    if (data) {
      this.url = data.url;
      this.bucket = data.bucket;
      this.name = data.name;
    }
  }
}