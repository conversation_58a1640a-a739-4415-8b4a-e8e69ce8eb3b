import {
  Body,
  Controller,
  Delete,
  Get,
  Headers,
  HttpCode,
  Post,
  Query,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import {
  AuthUser,
  CurrentToken,
  UseAuth,
} from '../../../common/decorators/user.decorator';
import { Prefix } from '../../../common/constants/index.constant';
import { AppResponseDto } from '../../../common/dtos/app-response.dto';
import { GetNewWardsUserRequestDto } from '../../dtos/requests/user/get-new-wards.user.request.dto';
import { UserNewProvinceUserResponseDto } from '../../dtos/responses/user/user-new-province.user.response.dto';
import { ProvinceUserResponseDto } from '../../dtos/responses/user/province.user.response.dto';
import { WardUserResponseDto } from '../../dtos/responses/user/ward.user.response.dto';
import { ProvinceUserService } from '../../services/user/province.user.service';
import { UserSessionData } from '../../../proto/user.pb';
import { BadRequestExc } from '../../../common/exceptions/custom-http.exception';

@Controller({ version: '1', path: `${Prefix.USER}/provinces` })
@ApiTags('Province User Controller')
@UseAuth()
export class ProvinceUserController {
  constructor(private readonly provinceUserService: ProvinceUserService) {}

  @Get('user-new-province')
  async getUserNewProvice(@AuthUser() user: UserSessionData) {
    const rs = await this.provinceUserService.getUserNewProvice(
      user.userId,
      user.phoneNumber,
    );
    const rsCheck = rs ? true : false;
    // if (!rs) {
    //   throw new BadRequestExc('Chưa liên kết xã / phường mới');
    // }

    return new AppResponseDto(rsCheck);
  }

  @Get('new-provinces')
  async getNewProvinces() {
    return new AppResponseDto(
      (await this.provinceUserService.getNewProvinces()).map((item) => {
        return new ProvinceUserResponseDto(item);
      }),
    );
  }

  @Get('new-wards')
  async getNewWards(@Query() req: GetNewWardsUserRequestDto) {
    return new AppResponseDto(
      (await this.provinceUserService.getNewWards(req)).map((item) => {
        return new WardUserResponseDto(item);
      }),
    );
  }
}
