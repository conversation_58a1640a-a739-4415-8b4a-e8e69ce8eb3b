import { RedisClientOptions, RedisModule } from '@liaoliaots/nestjs-redis';
import { BullModule } from '@nestjs/bull';
import {
  MiddlewareConsumer,
  Module,
  NestModule,
  OnModuleInit,
  RequestMethod,
  ValidationPipe,
} from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_FILTER, APP_PIPE, ModuleRef } from '@nestjs/core';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import {
  addTransactionalDataSource,
  initializeTransactionalContext,
} from 'typeorm-transactional';
// import { dataSource } from '../data-source';
import { dataSource } from '../data-source';
import { dataSourcePhysical } from '../data-source-physical';
import { AppController } from './app.controller';
import { AuthModule } from './auth/auth.module';
import { BannerModule } from './banner/banner.module';
import { StoreModule } from './store/store.module';
import { CacheModule } from './cache/cache.module';
import globalConfig from './common/config/global.config';
import { kafkaConfig } from './common/config/kafka.config';
import { TIME_ZONE } from './common/constants/index.constant';
import { AppFilter } from './common/filters/app.filter';
import { ConfigModule as AppConfigModule } from './config/config.module';
import { CoreModule } from './core';
import { CronJobModule } from './cron-job/cron-job.module';
import { EventAddCanModule } from './event-add-can/event-add-can.module';
import { EventCanMarkModule } from './event-can-mark/event-can-mark.module';
import { DATASOURCE_NAME } from './external/constants/index.constant';
import { ExternalModule } from './external/external.module';
import { FileModule } from './file/file.module';
import { GiftModule } from './gift/gift.module';
import { HealthModule } from './health/health.module';
import { HomeModule } from './home-view/home.module';
import { KafkaModule } from './kafka/kafka.module';
import { LoggerMiddleware } from './middlewares';
import { NotificationModule } from './notification/notification.module';
import { OutboxMessageModule } from './outbox-message/outbox-message.module';
import { PointModule } from './point/point.module';
import { ProductModule } from './product/product.module';
import { QrCodeSbpsModule } from './qr-code-sbps/qr-code-sbps.module';
import { SyncEventPointHistoryToWhModule } from './sync-event-point-history-to-wh/sync-event-point-history-to-wh.module';
import { SpoonModule } from './spoon/spoon.module';
import { SurveyModule } from './survey/survey.module';
import { TierModule } from './tier/tier.module';
import { TriplayzModule } from './triplayz/triplayz.module';
import { UserShowPopupModule } from './user_show_popup/user-show-popup.module';
import { WebappCalosureModule } from './webapp-calosure/webapp-calosure.module';
import { WebhookModule } from './webhook/webhook.module';
import { ZaloZnsModule } from './zalo-zns/zalo-zns.module';
import { FavoriteGiftModule } from './favorite-gift';
import { CustomFilterModule } from './custom-filter';
import { GiftJavaModule } from './gift-java-service/gift-java-service.module';
import { LoggingEventDataModule } from './logging-event-data/logging-event-data.module';
import { WebappModule } from './webapp/webapp.module';
import { SystemFeatureModule } from './system-feature/system-feature.module';
import { EventCommonModule } from './event-common/event-common.module';
import { CrmTransactionTypeModule } from './crm-transaction-type/crm-transaction-type.module';
import { AdminAuthorizationModule } from './admin_authorization/admin_authorization.module';
import { FeedbackModule } from './feedback/feedback.module';
import { UserModule } from './user/user.module';
import { AdminModule } from './admin/admin.module';
import { WelcomePopupModule } from './welcome-popup/welcome-popup.module';
import { SystemConfigModule } from './system-config/system-config.module';
import { ProvinceModule } from './provinces/province.module';
import { TrackingTimeLockModule } from './tracking-time-lock/tracking-time-lock.module';
import { HotlineRequestAddPointModule } from './hotline-request-add-point/hotline-request-add-point.module';
// const kafkaConfig: KafkaConfig = {
//   clientId: 'my-app',
//   brokers: ['vtd-kafka-cluster-kafka-brokers.kafka:9092'],
//   ssl: false,
//   connectionTimeout: 99999,
// };

// const consumerConfig: ConsumerConfig = {
//   groupId: 'test',
//   allowAutoTopicCreation: true,
//   //maxBytesPerPartition: 10485760, // (10MB)
//   //maxBytes: 10485760 * 2, // (20MB)
// };

const redisConfig: RedisClientOptions = {
  host: process.env.REDIS_HOST,
  port: Number(process.env.REDIS_PORT),
  onClientCreated(client) {
    client.on('error', (err) => {
      console.log('Redis connected error: ', err);
    });
    client.on('ready', () => console.log('Redis connected'));
  },
};

@Module({
  imports: [
    ScheduleModule.forRoot(),
    BullModule.forRoot({
      redis: {
        host: redisConfig.host,
        port: redisConfig.port,
      },
    }),
    ConfigModule.forRoot({
      isGlobal: true,
      load: [globalConfig],
      cache: true,
    }),
    TypeOrmModule.forRootAsync({
      useFactory: () => ({}),
      dataSourceFactory: async () => {
        initializeTransactionalContext();
        return addTransactionalDataSource(dataSource);
      },
    }),
    TypeOrmModule.forRootAsync({
      name: DATASOURCE_NAME.DATASOUCE_PHYSICAL,
      useFactory: () => ({}),
      dataSourceFactory: async () => {
        return addTransactionalDataSource({
          name: DATASOURCE_NAME.DATASOUCE_PHYSICAL,
          dataSource: dataSourcePhysical,
        });
      },
    }),

    KafkaModule.forRoot(kafkaConfig),
    EventEmitterModule.forRoot(),
    RedisModule.forRoot({
      config: redisConfig,
    }),
    CoreModule,
    HealthModule,
    PointModule,
    QrCodeSbpsModule,
    SyncEventPointHistoryToWhModule,
    SpoonModule,
    ExternalModule,
    ProductModule,
    AppConfigModule,
    TierModule,
    AuthModule,
    NotificationModule,
    OutboxMessageModule,
    WebhookModule,
    CronJobModule,
    ZaloZnsModule,
    EventAddCanModule,
    EventCanMarkModule,
    SystemFeatureModule,
    HomeModule,
    CacheModule,
    SurveyModule,
    BannerModule,
    StoreModule,
    GiftModule,
    UserShowPopupModule,
    FileModule,
    WebappCalosureModule,
    TriplayzModule,
    FavoriteGiftModule,
    CustomFilterModule,
    GiftJavaModule,
    LoggingEventDataModule,
    WebappModule,
    EventCommonModule,
    CrmTransactionTypeModule,
    UserModule,
    AdminModule,
    AdminAuthorizationModule,
    FeedbackModule,
    WelcomePopupModule,
    SystemConfigModule,
    ProvinceModule,
    TrackingTimeLockModule,
    HotlineRequestAddPointModule,
  ].filter(Boolean),
  providers: [
    { provide: APP_PIPE, useValue: new ValidationPipe({ transform: true }) },
    { provide: APP_FILTER, useValue: new AppFilter() },
  ],
  controllers: [AppController],
})
export class AppModule implements OnModuleInit, NestModule {
  constructor(
    private moduleRef: ModuleRef,
    private configService: ConfigService,
  ) {}
  onModuleInit() {
    dayjs.extend(utc);
    dayjs.extend(timezone);
    dayjs.tz.setDefault(TIME_ZONE);
  }
  public configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(LoggerMiddleware)
      .exclude(
        {
          path: '/api-v3/loyalty/health/liveness',
          method: RequestMethod.GET,
        },
        {
          path: '/api-v3/loyalty/health/readiness',
          method: RequestMethod.GET,
        },
      )
      .forRoutes({
        path: '*',
        method: RequestMethod.ALL,
      });
  }
}
