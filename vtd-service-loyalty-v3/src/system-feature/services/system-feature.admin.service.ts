import { Injectable } from '@nestjs/common';
import { paginate } from 'nestjs-typeorm-paginate';
import { Transactional } from 'typeorm-transactional';
import { EventAddCanStatus } from 'vtd-common-v3';
import { AdminActionHistoryRepository } from '../../admin-action-history/repositories/admin-action-history.repository';
import { EnumNameAdminMenuModule } from '../../admin_authorization/common/enums/admin-menu-module.enum';
import { AppResponseDto } from '../../common/dtos/app-response.dto';
import {
  BadRequestExc,
  ConflictExc,
  NotFoundExc,
} from '../../common/exceptions/custom-http.exception';
import { EventStatus } from '../../event/enums/event.enum';
import { AccountData } from '../../proto/account.pb';
import { CreateSystemFeatureAdminReqDto } from '../dtos/req/create-system-feature.admin.req.dto';
import { GetListSystemFeatureAdminReqDto } from '../dtos/req/get-list-system-feature.admin.req.dto';
import { UpdateSystemFeatureAdminReqDto } from '../dtos/req/update-system-feature.admin.req.dto';
import { SystemFeatureAttrRepository } from '../repositorires/system-feature-attr.repository';
import { SystemFeatureRepository } from '../repositorires/system-feature.repository';

@Injectable()
export class SystemFeatureAdminService {
  constructor(
    private readonly systemFeatureRepo: SystemFeatureRepository,
    private readonly systemFeatureAttrRepo: SystemFeatureAttrRepository,
    private readonly adminActionHistoryRepo: AdminActionHistoryRepository,
  ) {}
  async getListSystemFeature(dto: GetListSystemFeatureAdminReqDto) {
    const { page, limit } = dto;
    const queryBuilder =
      this.systemFeatureRepo.createQueryBuilder('systemFeature');
    queryBuilder.orderBy('systemFeature.createdAt', 'DESC');
    const { items, meta } = await paginate(queryBuilder, {
      limit,
      page,
    });
    return AppResponseDto.fromNestJsPagination(items, meta);
  }

  async getSystemFeatureByCode(code: string) {
    // Get the system feature with attributes
    const systemFeature = await this.systemFeatureRepo.findOne({
      where: { code },
      relations: ['attributes'],
    });
    if (!systemFeature) {
      throw new NotFoundExc('System Feature not found');
    }
    return new AppResponseDto({
      ...systemFeature,
      attributes: systemFeature.attributes,
    });
  }

  @Transactional()
  async createSystemFeature(
    dto: CreateSystemFeatureAdminReqDto,
    admin: AccountData,
  ) {
    const { attributes } = dto;
    // Check code is available
    const isCodeExist = await this.systemFeatureRepo.findOne({
      where: { code: dto.code },
    });
    if (isCodeExist) {
      throw new ConflictExc(`[ ${dto.code} ] đã tồn tại`);
    }

    // Create new systemFeature
    const { eventWebviewId, eventAddCanWebviewId, ...systemFeatureData } = dto;
    const newSystemFeature = this.systemFeatureRepo.create({
      ...systemFeatureData,
      eventWebviewId,
      eventAddCanWebviewId,
    });
    await this.systemFeatureRepo.save(newSystemFeature);

    // Create new systemFeatureAttr
    if (attributes && attributes.length) {
      for (const attr of attributes) {
        const latestId = await this.getLatestSystemFeatureAttrId();
        const newSystemFeatureAttr = this.systemFeatureAttrRepo.create({
          ...attr,
          systemFeature: newSystemFeature,
          id: latestId + 1,
        });
        await this.systemFeatureAttrRepo.save(newSystemFeatureAttr);
      }
    }

    await this.adminActionHistoryRepo.loggingCreateAction(
      admin.email,
      EnumNameAdminMenuModule.QUAN_LY_WEBVIEW,
      newSystemFeature.code,
    );

    return this.getSystemFeatureByCode(newSystemFeature.code);
  }

  @Transactional()
  async updateSystemFeature(
    code: string,
    dto: UpdateSystemFeatureAdminReqDto,
    admin: AccountData,
    skipEventStatusCheck = false,
  ) {
    const { attributes } = dto;
    // Check systemFeature is available
    const systemFeature = await this.systemFeatureRepo.findOne({
      where: { code },
      relations: [
        'eventWebview',
        'eventWebview.event',
        'eventAddCanWebview',
        'eventAddCanWebview.eventAddCan',
      ],
    });
    if (!systemFeature) {
      throw new NotFoundExc('System Feature not found');
    }

    // Validate event active status when admin manually sets isActive = true
    if (
      !skipEventStatusCheck &&
      systemFeature.isActive === false &&
      dto.isActive === true
    ) {
      this.validateEventActiveForSystemFeature(systemFeature);
    }

    // Update systemFeature
    const mergeSystemFeature = this.systemFeatureRepo.merge(systemFeature, {
      description: dto.description,
      priority: dto.priority,
      group: dto.group,
      source: dto.source,
      isActive: dto.isActive,
      eventWebviewId: dto.eventWebviewId,
      eventAddCanWebviewId: dto.eventAddCanWebviewId,
    });
    
    await Promise.all([
      this.systemFeatureRepo.save(mergeSystemFeature),
      this.adminActionHistoryRepo.loggingUpdateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_WEBVIEW,
        mergeSystemFeature.code,
      ),
    ]);

    // override attributes
    if (attributes && attributes.length) {
      await this.systemFeatureAttrRepo.delete({ systemFeatureCode: code });
      for (const attr of attributes) {
        const latestId = await this.getLatestSystemFeatureAttrId();
        const newSystemFeatureAttr = this.systemFeatureAttrRepo.create({
          ...attr,
          systemFeature: systemFeature,
          id: latestId + 1,
        });
        await this.systemFeatureAttrRepo.save(newSystemFeatureAttr);
      }
    }

    return this.getSystemFeatureByCode(code);
  }

  private validateEventActiveForSystemFeature(systemFeature: {
    eventWebview?: { event?: { status: EventStatus } };
    eventAddCanWebview?: { eventAddCan?: { status: EventAddCanStatus } };
  }) {
    const event = systemFeature.eventWebview?.event;
    const eventAddCan = systemFeature.eventAddCanWebview?.eventAddCan;

    if (!event && !eventAddCan) {
      throw new BadRequestExc(
        'Không thể update do không có Event / Event Add Can liên kết nào',
      );
    }

    // Check linked Event (via EventWebview)
    if (event && event.status !== EventStatus.ACTIVE) {
      throw new BadRequestExc(
        'Không thể bật isActive vì Event liên kết đang không hoạt động',
      );
    }

    // Check linked EventAddCan (via EventAddCanWebview)
    if (eventAddCan && eventAddCan.status !== EventAddCanStatus.ACTIVE) {
      throw new BadRequestExc(
        'Không thể bật isActive vì Event Add Can liên kết đang không hoạt động',
      );
    }
  }

  @Transactional()
  async deleteSystemFeature(code: string, admin: AccountData) {
    const systemFeature = await this.systemFeatureRepo.findOne({
      where: { code },
    });
    if (!systemFeature) {
      throw new NotFoundExc('System Feature not found');
    }
    await Promise.all([
      this.systemFeatureRepo.remove(systemFeature),
      this.adminActionHistoryRepo.loggingDeleteAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_WEBVIEW,
        systemFeature.code,
      ),
    ]);
  }

  private async getLatestSystemFeatureAttrId() {
    const queryBuilder =
      this.systemFeatureAttrRepo.createQueryBuilder('systemFeatureAttr');
    queryBuilder.orderBy('systemFeatureAttr.id', 'DESC');
    const latestSystemFeatureAttr = await queryBuilder.getOne();
    return latestSystemFeatureAttr.id;
  }
}
