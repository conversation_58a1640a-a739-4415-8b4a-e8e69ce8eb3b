import {
  IsValidArrayObject,
  IsValidBoolean,
  IsValidEnum,
  IsValidNumber,
  IsValidText,
} from 'src/common/decorators/custom-validator.decorator';
import {
  SystemFeatureGroup,
  SystemFeatureSource,
} from '../../enums/system-feature.enum';
import { CreateSystemFeatureAttrAdminReqDto } from './create-system-feature-attr.admin.req.dto';

class SystemFeatureAttr {
  // code: string;
  // value: string;
  @IsValidText({
    required: true,
    minLength: 1,
    maxLength: 255,
    stringMsg: 'code phải là chuỗi ký tự hợp lệ',
    emptyMsg: 'code không được để trống',
    minLengthMsg: 'code phải có ít nhất 1 ký tự',
    maxLengthMsg: 'code không vượt quá 255 ký tự',
  })
  code: string;

  @IsValidText({
    required: true,
    minLength: 1,
    maxLength: 255,
    stringMsg: 'value phải là chuỗi ký tự hợp lệ',
    emptyMsg: 'value không được để trống',
    minLengthMsg: 'value phải có ít nhất 1 ký tự',
    maxLengthMsg: 'value không vượt quá 255 ký tự',
  })
  value: string;
}

export class CreateSystemFeatureAdminReqDto {
  @IsValidText()
  code: string;

  @IsValidText({ required: false })
  description: string;

  @IsValidNumber()
  priority: number;

  @IsValidEnum({ enum: SystemFeatureGroup })
  group: SystemFeatureGroup;

  @IsValidEnum({ enum: SystemFeatureSource })
  source: SystemFeatureSource;

  @IsValidBoolean({ required: false })
  isActive: boolean;

  @IsValidArrayObject({ required: false }, SystemFeatureAttr)
  attributes?: SystemFeatureAttr[];

  @IsValidNumber({ required: false })
  eventWebviewId?: number;

  @IsValidNumber({ required: false })
  eventAddCanWebviewId?: number;
}
