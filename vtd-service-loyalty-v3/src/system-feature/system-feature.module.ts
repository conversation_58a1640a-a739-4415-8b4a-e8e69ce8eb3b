import { Module } from '@nestjs/common';
import { AuthModule } from '../auth/auth.module';
import { SystemFeatureAttrAdminController } from './controllers/system-feature-attr.admin.controller';
import { SystemFeatureAdminController } from './controllers/system-feature.admin.controller';
import { SystemFeatureAttrRepository } from './repositorires/system-feature-attr.repository';
import { SystemFeatureRepository } from './repositorires/system-feature.repository';
import { SystemFeatureAttrAdminService } from './services/system-feature-attr.admin.service';
import { SystemFeatureAdminService } from './services/system-feature.admin.service';
import { AdminAuthorizationModule } from '../admin_authorization/admin_authorization.module';
import { AdminActionHistoryModule } from '../admin-action-history/admin-action-history.module';
import { EventRepository } from '../event/repositories/event.repository';
import { EventAddCanRepository } from '../event-add-can/repositories/event-add-can.repository';

@Module({
  imports: [AuthModule, AdminAuthorizationModule, AdminActionHistoryModule],
  controllers: [SystemFeatureAdminController, SystemFeatureAttrAdminController],
  providers: [
    SystemFeatureAdminService,
    SystemFeatureAttrAdminService,
    SystemFeatureRepository,
    SystemFeatureAttrRepository,
    EventRepository,
    EventAddCanRepository,
  ],
  exports: [SystemFeatureAdminService, SystemFeatureRepository],
})
export class SystemFeatureModule {}
