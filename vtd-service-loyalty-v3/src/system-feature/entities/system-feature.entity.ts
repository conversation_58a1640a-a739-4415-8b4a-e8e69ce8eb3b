import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToMany,
  OneToOne,
  PrimaryColumn,
} from 'typeorm';
import { SystemFeatureAttr } from './system-feature-attr.entity';
import { EventWebview } from '../../event/entities/event-webview.entity';
import { EventAddCanWebview } from '../../event-add-can/entities/event-add-can-webview.entity';

@Entity({ name: 'mst_system_feature' })
export class SystemFeature {
  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @PrimaryColumn({ type: 'text' })
  code: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ type: 'text' })
  group: string;

  @Column({ type: 'text' })
  source: string;

  @Column({ type: 'int4' })
  priority: number;

  @Column({ name: 'created_at', type: 'timestamp' })
  createdAt: Date;

  @OneToMany(() => SystemFeatureAttr, (sfa) => sfa.systemFeature)
  attributes: SystemFeatureAttr[];

  @Column({ name: 'event_webview_id', type: 'int', nullable: true })
  eventWebviewId?: number;

  @OneToOne(() => EventWebview, (eventWebview) => eventWebview.systemFeature)
  @JoinColumn({ name: 'event_webview_id' })
  eventWebview?: EventWebview;

  @Column({ name: 'event_add_can_webview_id', type: 'int', nullable: true })
  eventAddCanWebviewId?: number;

  @OneToOne(
    () => EventAddCanWebview,
    (eventAddCanWebview) => eventAddCanWebview.systemFeature,
  )
  @JoinColumn({ name: 'event_add_can_webview_id' })
  eventAddCanWebview?: EventAddCanWebview;
}
