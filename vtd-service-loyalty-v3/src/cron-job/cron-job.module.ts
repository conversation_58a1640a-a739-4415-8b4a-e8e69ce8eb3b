import { Module } from '@nestjs/common';
import { CronJobNotificationService } from './services/cron-job.notification.service';
import { NotificationUserRepository } from '../notification/repositories/notification-user.repository';
import { BirthdayService } from './services/birthday.service';
import { UserRepository } from '../auth/repositories/user.repository';
import { CrmTransactionTypeRepository } from '../point/repositories/crm-transaction-type.repository';
import { HistoryPointRepository } from '../qr-code-sbps/repositories/history-point.repository';
import { PointModule } from '../point/point.module';
import { OutboxMessageRepository } from '../point/repositories/outbox-message.repository';
import { UserSessionRepository } from '../qr-code-sbps/repositories/user-session.repository';
import { VitaJavaService } from '../external/services/vita-java.service';
import { HttpModule } from '@nestjs/axios';
import { HistoryPointAttributeRepository } from '../point/repositories/history-point-attribute.repository';
import { CronJobNotificationUserService } from './services/cron-job-notification-user.service';
import { NotificationUserPhysicalServerDevRepository } from '../notification/repositories/notification-user-physical-server-dev.repository';
import { NotificationUserPhysicalServerProdRepository } from '../notification/repositories/notification-user-physical-server-prod.repository';
import { NotificationUserPhysicalServerUatRepository } from '../notification/repositories/notification-user-physical-server-uat.repository';
import { NotificationModule } from '../notification/notification.module';
import { NotificationUserPhysicalServerDev } from '../notification/entities/notification-user-physical-server-dev.entity.ra';
import { TypeOrmModule } from '@nestjs/typeorm';
import { InactiveUserRepository } from '../auth/repositories/inactive-user.repository';
import { CacheService } from '../cache/services/cache.service';
import { ElasticSearchService } from '../elasticSearch/elasticSearch.service';
import { VgsService } from '../external/services/vgs.service';
import { NotificationHistoryRepository } from '../notification/repositories/notification-history.repository';
import { TierRepository } from '../tier/repositories/tier.repository';
import { UtilService } from '../utils/services/util.service';
import { CronJobRemindDownTierRankService } from './services/cron-job-remind-down-tier-rank.service';
import { CronRemoveLogESService } from './services/cron-remove-log-ES.service';
import { CronJobRefreshHomeCacheService } from './services/refresh-home-cache.service';
import { ElasticSearchModule } from '../elasticSearch/elasticSearch.module';
import { CronJobTestController } from './controllers/cron-job.test.controller';
import { CronRemoveExchangeGiftLoggingESService } from './services/cron-remove-exchange-gift-logging-es.service';
import { AdminActionHistoryModule } from '../admin-action-history/admin-action-history.module';

@Module({
  imports: [
    PointModule,
    HttpModule,
    NotificationModule,
    // TypeOrmModule.forFeature(
    //   [NotificationUserPhysicalServerDev],
    //   'dataSourcePhysical',
    // ),
    ElasticSearchModule,
    AdminActionHistoryModule,
  ],
  providers: [
    NotificationUserRepository,
    CronJobNotificationService,
    BirthdayService,
    VitaJavaService,
    UserRepository,
    OutboxMessageRepository,
    UserSessionRepository,
    CrmTransactionTypeRepository,
    HistoryPointRepository,
    HistoryPointAttributeRepository,
    NotificationUserPhysicalServerDevRepository,
    NotificationUserPhysicalServerUatRepository,
    NotificationUserPhysicalServerProdRepository,
    CronJobNotificationUserService,
    CronJobRefreshHomeCacheService,
    CacheService,
    UtilService,
    CronJobRemindDownTierRankService,
    VgsService,
    NotificationHistoryRepository,
    InactiveUserRepository,
    CronRemoveExchangeGiftLoggingESService,
    TierRepository,
    ElasticSearchService,
    CronRemoveLogESService,
  ],
  controllers: [CronJobTestController],
})
export class CronJobModule {}
