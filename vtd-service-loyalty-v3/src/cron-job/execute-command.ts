import { Logger } from '@nestjs/common';
import { Command } from 'commander';
import { NestFactory } from '@nestjs/core';
import { FastifyAdapter } from '@nestjs/platform-fastify';
import { CronJobNotificationService } from './services/cron-job.notification.service';
import { AppModule } from '../app.module';
import { CronAddPointBirthdayService } from '../point/cron/cron.add-point-birthday.service';
import { BirthdayService } from './services/birthday.service';
import { PointService } from '../point/point.service';
import { CronDeletePointMessagesService } from '../point/cron/cron.delete-outbox-messages.service';
import { CronJobNotificationUserService } from './services/cron-job-notification-user.service';
import { CronJobRefreshHomeCacheService } from './services/refresh-home-cache.service';
import { CronJobRemindDownTierRankService } from './services/cron-job-remind-down-tier-rank.service';
import { CronRemoveLogESService } from './services/cron-remove-log-ES.service';
import { CronRemoveExchangeGiftLoggingESService } from './services/cron-remove-exchange-gift-logging-es.service';
import { BirthdayUserDto } from './dtos/birthday-user.dto';
import { TrackingUserActionCleanupService } from './services/cron-job-tracking-user-action.service';
import { AdminActionHistoryCronJob } from '../admin-action-history/cronjobs/admin-action-history.cronjob';

const program = new Command();
const logger = new Logger('ExecuteCommand');

program
  .command('cron-job-delete-notification')
  .description('cron job delete notification')
  .action(async () => {
    try {
      console.log('cron-job-noti');
      const app = await NestFactory.create(AppModule, new FastifyAdapter());
      await app.init();
      await app.get(CronJobNotificationService).removeAfterMonths();
      console.log('after call handleDeleteNotification');
      process.exit(0);
    } catch (error) {
      logger.error(`Error Delete Notifications`, JSON.stringify(error));
      process.exit(1);
    }
  });

program
  .command('cron-job-birthday')
  .description('cron job birthday')
  .action(async () => {
    try {
      const query: BirthdayUserDto = {
        numBatching: 10,
        limit: 100,
      };
      console.log('cron-job-birthday');
      const app = await NestFactory.create(AppModule, new FastifyAdapter());
      await app.init();
      await app.get(BirthdayService).addPointBirthDay(query);
      process.exit(0);
    } catch (error) {
      logger.error(`Error birthday`, JSON.stringify(error));
      process.exit(1);
    }
  });

program
  .command('cron-job-outbox-message')
  .description('cron job delete outbox message')
  .action(async () => {
    try {
      console.log('cron-job-outbox-message');
      const app = await NestFactory.create(AppModule, new FastifyAdapter());
      await app.init();
      await app
        .get(CronDeletePointMessagesService)
        .handleDeleteOutboxMessages();
      console.log('after call handleDeleteOutboxMessages');
      process.exit(0);
    } catch (error) {
      logger.error(`Error outbox message`, JSON.stringify(error));
      process.exit(1);
    }
  });

program
  .command('cron-job-sync-noti')
  .description('cron job sync noti to physical server')
  .action(async () => {
    try {
      console.log('cron-job-sync-noti');
      const app = await NestFactory.create(AppModule, new FastifyAdapter());
      await app.init();
      await app.get(CronJobNotificationUserService).syncDataToServerPhysical();
      console.log('after call syncDataToServerPhysical');
      process.exit(0);
    } catch (error) {
      logger.error(`Error sync noti`, JSON.stringify(error));
      process.exit(1);
    }
  });

// program
//   .command('cron-job-refresh-home-cache')
//   .description('cron job refresh home cache')
//   .action(async () => {
//     try {
//       console.log('cron-job-refresh-home-cache');
//       const app = await NestFactory.create(AppModule, new FastifyAdapter());
//       await app.init();
//       await app.get(CronJobRefreshHomeCacheService).refreshHomeCache();
//       console.log('Finish call refreshHomeCache');
//       process.exit(0);
//     } catch (error) {
//       logger.error(`Error refresh home cache`, JSON.stringify(error));
//       process.exit(1);
//     }
//   });

program
  .command('loyalty-cron-job-remind-down-rank-after-180days')
  .description('loyalty-cron-job-remind-down-rank-after-180days')
  .action(async () => {
    try {
      console.log('loyalty-cron-job-remind-down-rank-after-180days');
      const app = await NestFactory.create(AppModule, new FastifyAdapter());
      await app.init();
      await app
        .get(CronJobRemindDownTierRankService)
        .sendSmsRemindDownTierRankRule180Days();
      console.log('after call loyalty-cron-job-remind-down-rank-after-180days');
      process.exit(0);
    } catch (error) {
      logger.error(
        `Error loyalty-cron-job-remind-down-rank-after-180days message`,
        JSON.stringify(error),
      );
      process.exit(1);
    }
  });

program
  .command('loyalty-cron-job-remind-down-rank-after-365days')
  .description('loyalty-cron-job-remind-down-rank-after-365days')
  .action(async () => {
    try {
      console.log('loyalty-cron-job-remind-down-rank-after-365days');
      const app = await NestFactory.create(AppModule, new FastifyAdapter());
      await app.init();
      await app
        .get(CronJobRemindDownTierRankService)
        .sendSmsRemindDownTierRankRule365Days();
      console.log('after call loyalty-cron-job-remind-down-rank-after-365days');
      process.exit(0);
    } catch (error) {
      logger.error(
        `Error loyalty-cron-job-remind-down-rank-after-365days message`,
        JSON.stringify(error),
      );
      process.exit(1);
    }
  });

program
  .command('cron-job-remove-log-elastic-search')
  .description('cron job delete log on elastic search')
  .action(async () => {
    try {
      console.log('cron-job-remove-log-elastic-search');
      const app = await NestFactory.create(AppModule, new FastifyAdapter());
      await app.init();
      await app.get(CronRemoveLogESService).removeLogESAfterDays();
      console.log('after call removeLogESAfterDays');
      process.exit(0);
    } catch (error) {
      logger.error(`Error outbox message`, JSON.stringify(error));
      process.exit(1);
    }
  });

program
  .command('cron-job-remove-exchange-gift-logging-elastic-search')
  .description('cron job delete exchange gift logging on elastic search')
  .action(async () => {
    try {
      console.log('cron-job-remove-exchange-gift-logging-elastic-search');
      const app = await NestFactory.create(AppModule, new FastifyAdapter());
      await app.init();
      await app
        .get(CronRemoveExchangeGiftLoggingESService)
        .removeExchangeGiftLogging();
      console.log('after call removeExchangeGiftLogging');
      process.exit(0);
    } catch (error) {
      logger.error(`Error removeExchangeGiftLogging`, JSON.stringify(error));
      process.exit(1);
    }
  });

program
  .command('cron-job-cleanup-tracking-actions')
  .description('cron job to cleanup tracking user actions from previous month')
  .action(async () => {
    try {
      console.log('cron-job-cleanup-tracking-actions');
      const app = await NestFactory.create(AppModule, new FastifyAdapter());
      await app.init();
      await app.get(TrackingUserActionCleanupService).cleanupOldRecords();
      console.log('after call cleanupOldRecords');
      process.exit(0);
    } catch (error) {
      logger.error(`Error cleaning up tracking actions`, JSON.stringify(error));
      process.exit(1);
    }
  });

program
  .command('cron-job-cleanup-admin-action-histories')
  .description('cron job remove old data of admin action histories')
  .action(async () => {
    try {
      const app = await NestFactory.create(AppModule, new FastifyAdapter());
      await app.init();
      await app.get(AdminActionHistoryCronJob).removeOldData();
      process.exit(0);
    } catch (error) {
      logger.error(`Error`, JSON.stringify(error));
      process.exit(1);
    }
  });

program.parse();
