import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import dayjs from 'dayjs';
import { IsNull, Not } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { performance } from 'perf_hooks';
import {
  NotiDisplayTemplateType,
  PushNotiKafkaDto,
  PushNotiKafkaDtoVersion,
  FeatureNoti,
  IdentityPointContent,
  IdentityPointActionType,
} from 'vtd-common-v3';
import { User } from '../../auth/entities/user.entity';
import { UserRepository } from '../../auth/repositories/user.repository';
import { GlobalConfig } from '../../common/config/global.config';
import {
  CRM_TRANS_TYPE_CODE,
  REDEEM_BIRTHDAY_POINT,
  TIME_FORMAT_CRM,
  TIME_ZONE,
  USER_RANK,
} from '../../common/constants/index.constant';
import { StatusCode } from '../../common/constants/status-code.constant';
import { AppBaseExc } from '../../common/exceptions/custom-app.exception';
import {
  compareDateBetweenFromDateIAndToDateInTimezone,
  getNowAtTimeHcm,
  getNowAtTimeZoneHcm,
  getNowInTimeZoneAndFormatToString,
  randomTransactionExternalId,
} from '../../common/utils';
import { CrmPointGiftingRequest } from '../../external/interfaces/crm.interface';
import { VitaJavaService } from '../../external/services/vita-java.service';
import {
  NotificationUserFirebaseStatus,
  NotificationUserStatus,
} from '../../notification/enums/notification-user.enum';
import { NotificationUserRepository } from '../../notification/repositories/notification-user.repository';
import { HistoryPoint } from '../../point/entities/history-point.entity';
import { OutboxMessage } from '../../point/entities/outbox-message.entity';
import { HistoryPointAttributeCode } from '../../point/enums/history-point-attribute.enum';
import {
  HistoryPointStatus,
  HistoryPointType,
} from '../../point/enums/history-point.enum';
import {
  CallType,
  OutboxMessageStatus,
  SyncProvider,
  SyncType,
} from '../../point/enums/outbox-message.enum';
import { BirthdayUserDto } from '../dtos/birthday-user.dto';
import { CrmTransactionTypeRepository } from '../../point/repositories/crm-transaction-type.repository';
import { HistoryPointAttributeRepository } from '../../point/repositories/history-point-attribute.repository';
import { HistoryPointRepository } from '../../point/repositories/history-point.repository';
import { OutboxMessageRepository } from '../../point/repositories/outbox-message.repository';
import { PushNotificationRequest } from '../../qr-code-sbps/constants';
import { UserSessionRepository } from '../../qr-code-sbps/repositories/user-session.repository';
import { SyncData3rdServiceToWhRequestDetail } from '../../external/interfaces/vita-java.interface';
import {
  JavaV4WhSyncTo3rdServiceCode,
  JavaV4WhSyncTo3rdServiceDestination,
} from '../../external/constants/java_v4.constant';
import { DDXTriggerCalculateUserResetGiftPoint } from 'src/point/entities/ddx_trigger_calculate_user_reset_gift_point.entity';
import { DDXTriggerCalculateUserResetGiftPointRepository } from 'src/point/repositories/ddx_trigger_calculate_user_reset_gift_point.repository';
import { mapHistoryPointType } from 'src/point/helpers';
@Injectable()
export class BirthdayService {
  private logger = new Logger(BirthdayService.name);
  private crmSyncUsingWh = false;

  constructor(
    private configSer: ConfigService<GlobalConfig>,

    private userRepo: UserRepository,
    private crmTransactionTypeRepo: CrmTransactionTypeRepository,
    private outboxMessageRepo: OutboxMessageRepository,
    private userSessionRepo: UserSessionRepository,
    private notificationUserRepo: NotificationUserRepository,
    private vitaJavaService: VitaJavaService,
    private historyPointAttributeRepo: HistoryPointAttributeRepository,

    private identityPointRepo: DDXTriggerCalculateUserResetGiftPointRepository,
    private historyPointRepo: HistoryPointRepository,
  ) {
    this.crmSyncUsingWh = this.configSer.get('crm.options.syncUsingWh');
  }
  async addPointBirthDay(req: BirthdayUserDto) {
    console.log('birthday');
    const { numBatching, limit } = req;
    const [crmTypeBirthDayTitan, crmTypeBirthDayGold, crmTypeBirthDayPlatinum] =
      await Promise.all([
        this.crmTransactionTypeRepo.findOneBy({
          code: CRM_TRANS_TYPE_CODE.BIRTHDAY_TITAN_2023,
        }),
        this.crmTransactionTypeRepo.findOneBy({
          code: CRM_TRANS_TYPE_CODE.BIRTHDAY_GOLD_2023,
        }),
        this.crmTransactionTypeRepo.findOneBy({
          code: CRM_TRANS_TYPE_CODE.BIRTHDAY_PLATINUM_2023,
        }),
      ]);
    const crmType = {
      TITAN: crmTypeBirthDayTitan,
      GOLD: crmTypeBirthDayGold,
      PLATINUM: crmTypeBirthDayPlatinum,
    };

    for (let index = 0; index < limit; index += Number(numBatching)) {
      try {
        const begin = performance.now();
        const listUsersProcessed = await this.hanledAddPointBirthDayNewVersion(
          index,
          numBatching,
          crmType,
        );
        const after = performance.now();
        console.log(
          `Call to hanledAddPointBirthDay took ${after - begin} milliseconds.`,
        );
        if (!listUsersProcessed || !listUsersProcessed.length) {
          break;
        }
      } catch (error) {
        console.log(error);
        break;
      }
    }

    //await this.hanledAddPointBirthDay(300, users);
    return 'running...';
  }

  @Transactional()
  private async hanledAddPointBirthDayNewVersion(
    skip: number,
    limit: number,
    listCrmType: any,
  ) {
    //const minDate = dayjs('2024-05-11 00:00:00').tz(TIME_ZONE);
    //const maxDate = dayjs().tz(TIME_ZONE).endOf('day').subtract(1, 'day');
    const minDay = 11;
    const minMonth = 5;
    const curentTime = dayjs().tz(TIME_ZONE);
    const month = curentTime.month() + 1;
    const day = curentTime.date();
    const year = curentTime.year();
    let begin = performance.now();
    const historyPointUserIdIgnoreQuery = await this.historyPointRepo
      .createQueryBuilder('hp')
      .select('customer_id')
      .where(`EXTRACT(YEAR FROM transaction_date) = ${year}`)
      .andWhere(`hp.type = '${HistoryPointType.GIFT}'`)
      .andWhere(`hp.action_type like 'BIRTHDAY_%'`)
      .getSql();
    //console.log(historyPointUserIdIgnoreQuery);
    const users = await this.userRepo
      .createQueryBuilder('user')
      //.leftJoinAndSelect('user.historyPoints', 'historyPoint')
      .where((qb) => {
        qb.where(
          '((EXTRACT(MONTH FROM user.birthDay) = :month AND  EXTRACT(DAY FROM user.birthDay) = :day))',
          {
            month,
            day,
          },
        ) /*.orWhere('(birthday >= :minDate AND birthday <= :maxDate))', {
          minDate,
          maxDate,
        })
          .orWhere(
            '(EXTRACT(MONTH FROM user.birthDay) = :minMonth AND EXTRACT(DAY FROM user.birthDay) >= :minDay))',
            {
              minMonth,
              minDay,
            },
          )*/;
      })
      .andWhere(`user.id NOT IN (${historyPointUserIdIgnoreQuery})`)
      .andWhere('user.tierCode NOT LIKE :tierCode', {
        tierCode: USER_RANK.MEMBER,
      })
      .orderBy('user.birthDay')
      .skip(skip)
      .limit(limit)
      .getMany();
    let after = performance.now();
    console.log(`Call to get list users took ${after - begin} milliseconds.`);
    console.log('Users');
    console.log(
      users.map((user) => {
        return user.id;
      }),
    );
    if (!users || !users.length) {
      return [];
    }

    const outboxMessageRepo: OutboxMessage[] = [];
    const listHistoryPoint: HistoryPoint[] = [];

    // identity point
    const listIdentityPoint: DDXTriggerCalculateUserResetGiftPoint[] = [];

    const listNewUser: User[] = [];
    const userIds: any[] = [];
    const promisesPushNoti: any[] = [];
    // listUsers.forEach((user) => {
    begin = performance.now();
    let forceError = false;
    for (const user of users) {
      userIds.push(user.id);
      //delete user.historyPoints;
      //console.log('user');
      //console.log(user);
      //console.log('isAddPointBirthday');
      //console.log(isAddPointBirthday);
      //console.log('REDEEM_BIRTHDAY_POINT');
      //console.log(REDEEM_BIRTHDAY_POINT);

      const currentTime = getNowAtTimeZoneHcm();
      console.log(currentTime);
      console.log(user.startFreezePoint);

      console.log(user.endFreezePoint);

      const isWithinFreezePeriod =
        compareDateBetweenFromDateIAndToDateInTimezone(
          currentTime,
          user.startFreezePoint,
          user.endFreezePoint,
        );
      if (isWithinFreezePeriod === 1) {
        console.log('isWithinFreezePeriod', isWithinFreezePeriod);
        continue;
      }

      if (Object.keys(REDEEM_BIRTHDAY_POINT).includes(user.tierCode)) {
        const point = Number(REDEEM_BIRTHDAY_POINT[user.tierCode]);
        //console.log('point');
        //console.log(point);
        const { historyPoints, ...newUser } = user;
        console.log('old user');
        console.log(newUser);
        console.log('new user');
        console.log({
          ...newUser,
          giftPoint: user.giftPoint + point,
          totalPoint: user.totalPoint + point,
        });
        listNewUser.push(
          this.userRepo.create({
            ...newUser,
            giftPoint: user.giftPoint + point,
            totalPoint: user.totalPoint + point,
          }),
        );
        //console.log('listNewUser');
        //console.log(listNewUser);
        const transactionExternalId = randomTransactionExternalId();
        const history = this.historyPointRepo.create({
          type: HistoryPointType.GIFT,
          customerId: user.id,
          customerName: user.getFullName(),
          customerPhone: user.phoneNumber,
          status: HistoryPointStatus.SUCCESS,
          transactionDate: getNowAtTimeZoneHcm(),
          transactionExternalId: transactionExternalId,
          cdpSyncUp: false,
          actionType: listCrmType[user.tierCode].code,
          giftPoint: point,
        });

        const request: CrmPointGiftingRequest = {
          userId: user.id,
          Transaction_Type__c: listCrmType[user.tierCode].name,
          Type__c: 'Adding',
          Level_Points__c: 0,
          Redeem_Points__c: point,
          Transaction_External_ID__c: transactionExternalId,
          Transaction_Date__c: dayjs().tz(TIME_ZONE).format(TIME_FORMAT_CRM),
          Rule_Name__c: listCrmType[user.tierCode].description,
          Campaign__c: listCrmType[user.tierCode].campaignName,
          Earned_Tier_Point__c: point,
          Tier__c: user.tierCode,
          Tier_Points__c: user.tierPoint,
        };
        listHistoryPoint.push(history);
        // TODO
        const historyPointCategory = mapHistoryPointType(
          HistoryPointType.GIFT,
          point,
        );
        const identityPoint = this.identityPointRepo.create({
          customerId: String(user.id),
          customerPhone: user.phoneNumber || 'Null',
          customerName:
            user.name || user.firstName + user.lastName || 'Undefined',
          totalPointBefore: user.giftPoint ?? 0,
          totalPointAfter: user.giftPoint + point,
          numberPoint: point,
          contentTransaction:
            IdentityPointContent[listCrmType[user.tierCode].code] ||
            'Content empty',
          type: historyPointCategory,
          actionType:
            IdentityPointActionType[listCrmType[user.tierCode].code] ||
            'verifyPoint',
          transactionTime: getNowAtTimeHcm(),
          transactionExternalId: transactionExternalId,
          account: {
            created_at: user.createdDate,
            last_time_login: user.lastLoginDate,
          },
        });
        listIdentityPoint.push(identityPoint);
        // listRequestCrm.push(request);
        if (this.crmSyncUsingWh) {
          const syncAddPointData3rdServiceToWhRequestDetails: SyncData3rdServiceToWhRequestDetail[] =
            [];
          syncAddPointData3rdServiceToWhRequestDetails.push(
            this.vitaJavaService.generateSyncData3rdServiceToWhRequestDetail(
              [request],
              JavaV4WhSyncTo3rdServiceDestination.SF,
              JavaV4WhSyncTo3rdServiceCode.ADD_POINT,
            ),
          );
          syncAddPointData3rdServiceToWhRequestDetails.push(
            this.vitaJavaService.generateSyncData3rdServiceToWhRequestDetail(
              [request],
              JavaV4WhSyncTo3rdServiceDestination.SAP,
              JavaV4WhSyncTo3rdServiceCode.ADD_POINT_MAMUONG,
            ),
          );
          const whAddPointOutboxMessageRequestData =
            this.vitaJavaService.generateWhOutboxMessageSyncData3rdServiceRequestDataInBatch(
              null,
              syncAddPointData3rdServiceToWhRequestDetails,
            );

          outboxMessageRepo.push(
            this.outboxMessageRepo.createSyncWhSyncData3rdService(
              whAddPointOutboxMessageRequestData,
            ),
          );
        } else {
          outboxMessageRepo.push(
            this.outboxMessageRepo.create({
              provider: SyncProvider.CRM,
              callType: CallType.SYNC,
              syncType: SyncType.IMMEDIATE,
              request: JSON.stringify([request]),
              status: OutboxMessageStatus.PROCESSING,
              retryNumber: 0,
            }),
          );
        }

        promisesPushNoti.push(
          this.pushUpdateRankNotification(
            user.id,
            `THƯỞNG XU THÀNH CÔNG.`,
            `Bạn được thưởng ${point} xu vào ngày sinh nhật. Hãy sử dụng Xu tích luỹ để tham gia đổi quà.`,
            `"Bạn được thưởng ${point} xu vào ngày sinh nhật. Hãy sử dụng Xu tích luỹ để tham gia đổi quà."`,
            point,
          ),
        );
      }

      // if ([137135, 137136, 137138, 137141].includes(user.id)) {
      //   forceError = true;
      // }
    }
    after = performance.now();
    console.log(`Call to loop users took ${after - begin} milliseconds.`);
    console.log('listNewUser');
    console.log(listNewUser);
    if (!listNewUser || !listNewUser.length) {
      return [];
    }
    let promisesData: any[] = [
      this.userRepo.save(listNewUser),
      this.historyPointRepo.insert(listHistoryPoint),
      this.identityPointRepo.insert(listIdentityPoint),
      // this.crmService.createListTransactionPointGifting(listRequestCrm),
      this.outboxMessageRepo.save(outboxMessageRepo),
    ];
    promisesData = promisesData.concat(promisesPushNoti);
    begin = performance.now();
    const [_, historyPoints] = await Promise.all(promisesData);
    if (historyPoints.identifiers.length > 0) {
      const historyPointAttributePromises = historyPoints.identifiers.map(
        async (historyPointId) => {
          return new Promise(async (resolve) => {
            const historyPoint = await this.historyPointRepo.findOne({
              where: {
                id: historyPointId.id,
              },
            });

            if (!historyPoint) return;

            const user = await this.userRepo.findOne({
              where: {
                id: historyPoint.customerId,
              },
            });

            if (!user) return;

            const historyPointAttribute = this.historyPointAttributeRepo.create(
              {
                attributeCode: HistoryPointAttributeCode.USER_BIRTH_DAY,
                value: dayjs(user.birthDay)
                  .tz(TIME_ZONE)
                  .format('YYYY-MM-DD HH:mm:ss'),
                historyPointId: historyPoint.id,
              },
            );

            await this.historyPointAttributeRepo.save(historyPointAttribute);

            resolve(true);
          });
        },
      );

      await Promise.all(historyPointAttributePromises);
    }
    after = performance.now();
    console.log(`Call to update users took ${after - begin} milliseconds.`);

    //throw new Error('Test');
    if (forceError) {
      throw new Error('Test birthday fail and lose data');
    }

    return listNewUser;
  }

  private async pushUpdateRankNotification(
    userId: number,
    title: string,
    content: string,
    description: string,
    point: number,
  ) {
    const useNotiV3 = this.configSer.get('useNotiV3');
    this.logger.log(`useNotiV3: ${useNotiV3}`);

    if (useNotiV3) {
      const kafkaDto = new PushNotiKafkaDto({
        userIds: [userId],
        version: PushNotiKafkaDtoVersion.V1,
        notiDisplayTemplateType: NotiDisplayTemplateType.BIRTHDAY_2023,
        notiDisplayTemplateParams: {
          gifted_point_rank: point,
        },
        featureNoti: FeatureNoti.NOTI_USER_BIRTHDAY,
      });

      const outboxMsg = this.outboxMessageRepo.createPushNoti(kafkaDto);
      await this.outboxMessageRepo.save(outboxMsg);
      return;
    }

    const topic = `USER_${userId}_${dayjs().unix()}`;
    // const title = `THĂNG HẠNG ${rank} THÀNH CÔNG.`;
    // const content = `Bạn đã thăng hạng ${rank} thành công.`;
    // const description = `"Bạn đã thăng hạng ${rank} thành công."`;
    const userSessions = await this.userSessionRepo.find({
      where: { userId, deviceToken: Not(IsNull()) },
    });
    const tokens = userSessions.map((item) => item.deviceToken);

    const request: PushNotificationRequest = {
      message: content,
      title: title,
      topic: topic,
      type: 2,
      tokenOfTopic: tokens,
    };
    const notification = this.notificationUserRepo.create({
      userId,
      status: NotificationUserStatus.UNREAD,
      firebaseStatus: NotificationUserFirebaseStatus.SENT,
      title,
      content,
      description,
    });

    await this.vitaJavaService.pushNotification(request);
    await this.notificationUserRepo.save(notification);
  }
}
