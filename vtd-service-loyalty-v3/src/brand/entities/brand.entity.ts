import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { BaseEntityOnlyCreatedAt } from '../../common/entities/base.entity';
import { Product } from '../../product/entities/product.entity';
import { MapUserToBrandPointEntity } from '../../map-user-to-brand-point/entities/map-user-to-brand-point.entity';

@Entity({ name: 'brands' })
export class BrandEntity extends BaseEntityOnlyCreatedAt {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    name: 'code',
    length: 255,
  })
  code: string;

  @Column({
    name: 'name',
    length: 255,
  })
  name: string;

  @OneToMany(() => Product, (p) => p.brandEntity)
  products: Product[];

  @OneToMany(() => MapUserToBrandPointEntity, (p) => p.brand)
  userBrandPoints: MapUserToBrandPointEntity[];
}
