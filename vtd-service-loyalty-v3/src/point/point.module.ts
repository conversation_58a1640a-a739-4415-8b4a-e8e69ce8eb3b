import { Module } from '@nestjs/common';
import { AuthModule } from '../auth/auth.module';
import { ConfigModule } from '../config/config.module';
import { BirthdayService } from '../cron-job/services/birthday.service';
import { ElasticSearchModule } from '../elasticSearch/elasticSearch.module';
import { EventPointHistory } from '../event/entities/event-point-history.entity';
import { EventModule } from '../event/event.module';
import { EventDetailExclusionRepository } from '../event/repositories/event-detail-exclusion.repository';
import { EventDetailIgnoreStoreRepository } from '../event/repositories/event-detail-ignore-store.repository';
import { EventDetailProvinceRepository } from '../event/repositories/event-detail-province.repository';
import { EventDetailRepository } from '../event/repositories/event-detail.repository';
import { EventDetailTimeGiftingGiftRepository } from '../event/repositories/event-detail-time-gifting-gift.repository';
import { EventGroupGiftExclusionRepository } from '../event/repositories/event-group-gift-exclusion.repository';
import { EventLimitRepository } from '../event/repositories/event-limit.repository';
import { EventPointHistoryRepository } from '../event/repositories/event-point-history.repository';
import { EventProductChildRepository } from '../event/repositories/event-product-child.repository';
import { EventUsersLimitRepository } from '../event/repositories/event-users-limit.repository';
import { EventUsersRepository } from '../event/repositories/event-users.repository';
import { GiftRepository } from '../event/repositories/gift.repository';
import { UserGiftRepository } from '../event/repositories/user-gift.repository';
import { HistoryUserGiftingGiftEventRepository } from '../history-user-gifting-gift-event/repositories/history-user-gifting-gift-event.repository';
import { VitaCodeRepository } from '../event/repositories/vita-code.repository';
import { ExternalModule } from '../external/external.module';
import { GoogleSheetService } from '../external/services/google-sheet.service';
import { SfNotiTransactionRepository } from '../noti/repositories/sf-noti-transaction.repository';
import { SfNotiUserRepository } from '../noti/repositories/sf-noti-user.repository';
import { NotificationModule } from '../notification/notification.module';
import { ProductModule } from '../product/product.module';
import { ProductMstRepository } from '../qr-code-sbps/repositories/product-mst.repository';
import { VitaQrRepository } from '../qr-code-sbps/repositories/qr-code-sbps.repository';
import { SpoonModule } from '../spoon/spoon.module';
import { TierModule } from '../tier/tier.module';
import { UtilsModule } from '../utils/utils.module';
import { HotlineHistoryPointRepository } from '../webapp-calosure/repositories/hotline-history-point.repository';
import { WebhookModule } from '../webhook/webhook.module';
import { CronDeletePointMessagesService } from './cron/cron.delete-outbox-messages.service';
import { EventListenerService } from './listeners/event.listener';
import { PointGoController } from './point-go.controller';
import { PointController } from './point.controller';
import { PointService } from './point.service';
import { BlockedHistoryRepository } from './repositories/blocked-history.repository';
import { BravoQrRepository } from './repositories/bravo-qr.repository';
import { CallCrmService } from './repositories/call-crm.service';
import { CrmTransactionTypeRepository } from './repositories/crm-transaction-type.repository';
import { HistoryPointAttributeRepository } from './repositories/history-point-attribute.repository';
import { HistoryPointRepository } from './repositories/history-point.repository';
import { OutboxMessageRepository } from './repositories/outbox-message.repository';
import { ScanHistoryRepository } from './repositories/scan-history.repository';
import { StoreInvitationRepository } from './repositories/store-invitation.repository';
import { StoreRepository } from './repositories/store.repository';
import { UserNumberScanRepository } from './repositories/user-number-scan.repository';
import { EventDetailBlockUserRepository } from '../event/repositories/event-detail-block-user.repository';
import { EventDetailSkuRepository } from '../event/repositories/event-detail-sku.repository';
import { EventDetailToUserTypeRepository } from '../event/repositories/event-detail-to-user-type.repository';
import { ZenposQrRepository } from './repositories/zenpos-qr.repository';
import {
  EVENT_QR_CLS_PROVIDER,
  EventClsQ4Service,
  EventQ4ClsLon400GrService,
  EventQ4ClsLon800GrService,
  PointActionService,
  PointEventRecoveryService,
  EventQ32024UpRankService,
  EventQuy32024ClbbMassSampling,
  EventGiftBy4StepService,
} from './services';
import { TaskKafkaConsumerService } from './task-kafka-consumer.service';
import { SyncEventPointHistoryToWhEntityRepository } from '../sync-event-point-history-to-wh/repositories/sync-event-point-history-to-wh.repository';
import { SyncEventPointHistoryToWhModule } from '../sync-event-point-history-to-wh/sync-event-point-history-to-wh.module';
import { EventCommonModule } from '../event-common/event-common.module';
import { EventPopupRepository } from '../event/repositories/event-popup.repository';
import { DDXTriggerCalculateUserResetGiftPointRepository } from './repositories/ddx_trigger_calculate_user_reset_gift_point.repository';
import { EventGiftBy4StepCountUserAddedPointRepository } from '../event_gift_by_4_step_count_user_added_point/repositories/event_gift_by_4_step_count_user_added_point.repository';
import { SystemConfigV3Repository } from '../system-config/repositories/system-config.repository';
import { ProvinceModule } from '../provinces/province.module';

@Module({
  imports: [
    // ClientsModule.register([
    //   {
    //     name: USER_SERVICE_NAME,
    //     transport: Transport.GRPC,
    //     options: {
    //       url: 'vtd-service-user-v3:50051',
    //       package: USER_PACKAGE_NAME,
    //       protoPath: join(
    //         __dirname + '/../../../node_modules/vtd-common-v3/proto/user.proto',
    //       ),
    //     },
    //   },
    //   {
    //     name: ACCOUNT_SERVICE_NAME,
    //     transport: Transport.GRPC,
    //     options: {
    //       url: 'vtd-service-user-v3:50052',
    //       package: ACCOUNT_PACKAGE_NAME,
    //       protoPath: join(
    //         __dirname +
    //           '/../../../node_modules/vtd-common-v3/proto/account.proto',
    //       ),
    //     },
    //   },
    // ]), commented out because we use one imported from AuthModule
    AuthModule,
    SpoonModule,
    ExternalModule,
    //forwardRef(() => QrCodeSbpsModule),
    //QrCodeSbpsModule,
    ProductModule,
    ConfigModule,
    TierModule,
    EventModule,
    UtilsModule,
    NotificationModule,
    WebhookModule,
    ElasticSearchModule,
    SyncEventPointHistoryToWhModule,
    EventCommonModule,
    ProvinceModule,
  ],
  controllers: [PointController, PointGoController],
  providers: [
    BirthdayService,
    CallCrmService,
    PointService,

    // AuthService, commented out because we use one imported from AuthModule
    // CronAddPointBirthdayService,
    CronDeletePointMessagesService,
    UserNumberScanRepository,
    ScanHistoryRepository,
    BlockedHistoryRepository,
    ZenposQrRepository,
    BravoQrRepository,
    VitaQrRepository,
    CrmTransactionTypeRepository,
    HistoryPointRepository,
    HistoryPointAttributeRepository,
    StoreInvitationRepository,
    EventUsersRepository,
    EventLimitRepository,
    EventUsersLimitRepository,
    StoreRepository,
    EventDetailRepository,
    EventPointHistory,
    EventPointHistoryRepository,
    EventDetailExclusionRepository,
    EventGroupGiftExclusionRepository,
    GiftRepository,
    EventDetailProvinceRepository,
    UserGiftRepository,
    HistoryUserGiftingGiftEventRepository,
    EventDetailTimeGiftingGiftRepository,
    TaskKafkaConsumerService,
    OutboxMessageRepository,
    HistoryPointRepository,
    DDXTriggerCalculateUserResetGiftPointRepository,
    EventGiftBy4StepCountUserAddedPointRepository,
    ProductMstRepository,
    EventDetailIgnoreStoreRepository,
    EventProductChildRepository,
    VitaCodeRepository,
    GoogleSheetService,
    HotlineHistoryPointRepository,
    EventListenerService,
    SfNotiUserRepository,
    SfNotiTransactionRepository,
    EventDetailBlockUserRepository,
    EventDetailSkuRepository,
    EventDetailToUserTypeRepository,
    SyncEventPointHistoryToWhEntityRepository,
    SystemConfigV3Repository,
    EventClsQ4Service,
    EventGiftBy4StepService,
    PointActionService,
    {
      provide: EVENT_QR_CLS_PROVIDER.LON_400GR,
      useClass: EventQ4ClsLon400GrService,
    },
    {
      provide: EVENT_QR_CLS_PROVIDER.LON_800GR,
      useClass: EventQ4ClsLon800GrService,
    },
    PointEventRecoveryService,
    EventQ32024UpRankService,
    EventQuy32024ClbbMassSampling,
    EventPopupRepository,
  ],
  exports: [
    UserNumberScanRepository,
    ScanHistoryRepository,
    BlockedHistoryRepository,
    ZenposQrRepository,
    BravoQrRepository,
    VitaQrRepository,
    CrmTransactionTypeRepository,
    HistoryPointRepository,
    HistoryPointAttributeRepository,
    DDXTriggerCalculateUserResetGiftPointRepository,
    StoreInvitationRepository,
    EventClsQ4Service,
    EventGiftBy4StepService,
    PointActionService,
    EventQ32024UpRankService,
    EventQuy32024ClbbMassSampling,
    PointService,
  ],
})
export class PointModule {}
