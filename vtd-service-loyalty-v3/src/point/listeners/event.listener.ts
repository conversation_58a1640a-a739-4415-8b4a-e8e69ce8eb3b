import { Injectable } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { User } from '../../auth/entities/user.entity';
import { EVENT_EMITTER_NAME } from '../../common/constants/index.constant';
import { SyncData3rdServiceToWhRequestDetail } from '../../external/interfaces/vita-java.interface';
import { LoggerService } from '../../core';
import { VitaCode } from '../../event/entities/vita-code.entity';
import { Product } from '../../product/entities/product.entity';
import { ProductDataGotByQrDto } from '../dto/misc/product-data-got-by-qr.dto';
import { HistoryPoint } from '../entities/history-point.entity';
import { OutboxMessage } from '../entities/outbox-message.entity';
import { RequestOutboxMessage } from '../interfaces/outbox-message.interface';
import { PointService } from '../point.service';
import { PointActionService } from '../services';
import { PushPopup3rdTopicPayload, ReuseUserGiftPayload } from './types';
import { EventType } from '../enums/event.enum';
import { WebAppList } from '../../common/enums/webapp-list.enum';
import { VitaQr } from '../../qr-code-sbps/entities/vita-qr.entity';

@Injectable()
export class EventListenerService {
  private _logger = new LoggerService(EventListenerService.name);
  constructor(
    private pointService: PointService,
    private _pointActionService: PointActionService,
  ) {}

  @OnEvent('event.Q2')
  async handleEvents(
    user: User,
    product: Product,
    code: string,
    productData: ProductDataGotByQrDto,
    transactionRefId: string,
    arrNoti: string[],
    appversionname: string,
    requestOutboxMessage: RequestOutboxMessage,
    token: string,
    type: string,
    outboxMessageGift: OutboxMessage[] = [],
    syncData3rdServiceToWhRequestDetails: SyncData3rdServiceToWhRequestDetail[] = [],
    eventTrigger: EventType[] = [],
    vitaQr: VitaQr = null,
    webapp_name: WebAppList = null,
  ) {
    return await this.pointService.handleEventQ2(
      user.id,
      product,
      code,
      productData,
      transactionRefId,
      arrNoti,
      appversionname,
      requestOutboxMessage,
      token,
      type,
      outboxMessageGift,
      syncData3rdServiceToWhRequestDetails,
      eventTrigger,
      vitaQr,
      webapp_name,
    );
  }

  @OnEvent('event.Q32024ClbbMassSampling')
  async handleEventQ32024ClbbMassSampling(
    user: User,
    product: Product,
    spoonCode: string,
    productData: ProductDataGotByQrDto,
    transactionRefId: string,
    arrNoti: string[],
    appversionname: string,
    requestOutboxMessage: RequestOutboxMessage,
    token: string,
    outboxMessageGift?: OutboxMessage[],
    syncData3rdServiceToWhRequestDetails: SyncData3rdServiceToWhRequestDetail[] = [],
    type?: string,
  ) {
    await this.pointService.handleEventQ32024ClbbMassSampling(
      user.id,
      product,
      spoonCode,
      productData,
      transactionRefId,
      arrNoti,
      appversionname,
      requestOutboxMessage,
      token,
      outboxMessageGift,
      syncData3rdServiceToWhRequestDetails,
      type,
    );
  }

  @OnEvent('event.product01')
  async handleEventProduct01(
    user: User,
    product: Product,
    code: string,
    productData: ProductDataGotByQrDto,
    transactionRefId: string,
    arrNoti: string[],
    appversionname: string,
    requestOutboxMessage: RequestOutboxMessage,
  ) {
    await this.pointService.handleEvent01(
      user,
      product,
      code,
      productData,
      transactionRefId,
      arrNoti,
      appversionname,
      requestOutboxMessage,
    );
  }

  @OnEvent('event.handleEvent4Lon')
  async handleEvent4LonEmiter(
    vitaCode: VitaCode,
    user: User,
    historyPoint: HistoryPoint,
  ) {
    await this.pointService.handleEvent4Lon(vitaCode, user, historyPoint);
  }

  @OnEvent(EVENT_EMITTER_NAME.PUSH_POPUP_NOTIFICATION)
  async handlePushPopupNotification(payload: PushPopup3rdTopicPayload) {
    const { data, request, userNotiData } = payload;
    this._logger.debug('Payload pushPopupNotification:', payload);

    try {
      await this._pointActionService.pushPopup3rdTopic(
        data,
        request,
        userNotiData,
      );
    } catch (error) {
      this._logger.error('Error when pushPopupNotification:', error.message);
    }
  }

  @OnEvent(EVENT_EMITTER_NAME.REUSE_USER_GIFT)
  async handleReuseUserGift(payload: ReuseUserGiftPayload) {
    const { userGiftId, token, userId, transactionDate } = payload;
    this._logger.debug('Payload handleReuseUserGift:', payload);

    try {
      await this._pointActionService.reuseUserGift(
        userGiftId,
        token,
        userId,
        transactionDate,
      );
    } catch (error) {
      this._logger.error('Error when handleReuseUserGift:', error.message);
    }
  }
}
