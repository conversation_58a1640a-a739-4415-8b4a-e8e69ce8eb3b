import { Column, Entity, PrimaryColumn, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'user_number_scan' })
export class UserNumberScan {
  @PrimaryColumn({ name: 'user_id', type: 'int4' })
  userId: number;

  @Column({ name: 'number_scan_in_day', default: 0, type: 'int4' })
  numberScanInDay: number;

  @Column({ name: 'number_scan_in_month', default: 0, type: 'int4' })
  numberScanInMonth: number;

  @Column({ name: 'number_scan', type: 'int4', default: 0 })
  numberScan: number;

  @Column({
    name: 'number_point_used_in_day',
    nullable: true,
    default: 0,
    type: 'int4',
  })
  numberPointUsedInDay: number;
}
