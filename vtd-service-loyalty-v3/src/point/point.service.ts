import {
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { REQUEST } from '@nestjs/core';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { objectify } from 'radash';
import dayjs from 'dayjs';
import { Request } from 'express';
import { join } from 'path';
import { load } from 'protobufjs';
import { EventDetailToStore } from 'src/event/entities/event-detail-to-store.entity';
import { EventLimit } from 'src/event/entities/event-limit.entity';
import { VitaCode } from 'src/event/entities/vita-code.entity';
import { Event4LonToGoogleSheet } from 'src/external/interfaces/user-sent-to-ggsheet.interface';
import { GoogleSheetService } from 'src/external/services/google-sheet.service';
import {
  In,
  <PERSON>Null,
  <PERSON><PERSON>han,
  <PERSON>ThanOrEqual,
  <PERSON><PERSON>han,
  MoreThanOrEqual,
  Not,
  Between,
} from 'typeorm';
import {
  Transactional,
  runInTransaction,
  runOnTransactionCommit,
} from 'typeorm-transactional';
import {
  IdentityPointActionType,
  IdentityPointContent,
  NotiDisplayTemplateType,
  PushNotiKafkaDto,
  PushNotiKafkaDtoVersion,
  FeatureNoti,
} from 'vtd-common-v3';
import { User } from '../auth/entities/user.entity';
import { UserStatus, UserType, UserTypeV2 } from '../auth/enums/user.enum';
import { UserRepository } from '../auth/repositories/user.repository';
import { GlobalConfig } from '../common/config/global.config';
import {
  BooleanValueEnum,
  CRM_TRANS_TYPE_CODE,
  EVENT_EMITTER_NAME,
  GIFT_POINT,
  PER_REDEEM_POINT,
  TIME_FORMAT_CRM,
  TIME_FORMAT_DAY,
  TIME_FORMAT_VGS,
  TIME_ZONE,
  USER_RANK,
  UTC_HCM,
} from '../common/constants/index.constant';
import { StatusCode } from '../common/constants/status-code.constant';
import { AppResponseDto } from '../common/dtos/app-response.dto';
import { AppBaseExc } from '../common/exceptions/custom-app.exception';
import { asyncMapSettled, stringToNumbers } from '../common/helpers';
import {
  calMonthDiff,
  compareDateBetweenFromDateIAndToDateInTimezone,
  compareDateWithCurrentDateInTimezone,
  formatDayjsToTimeZoneZero,
  getNowAtTimeHcm,
  getNowAtTimeZoneHcm,
  getStartOfCurrentYearAtTimeHcm,
  getEndOfCurrentYearAtTimeHcm,
  compareDateWithDateInTimezoneNewVersion,
  makeDateIsDateAtTimeHcm,
  getFormatCurrentDateInTimezone,
  randomTransactionExternalId,
  sortByAttribute,
  getFormattedDateAfterDays,
  getStartOfDateInTimezone,
  convertObjectToString,
} from '../common/utils';
import { CountNumberOf400GrAnd800GrCansInterface } from './interfaces/point.interface';
import {
  compareDateWithDateInTimezone,
  getNowAtTimezone,
  getStartOfCurrentMonthInTimezone,
  getStartOfNowInTimezone,
} from '../common/datetime.util';
import { FeatureStatus } from '../config/enums/feature.enum';
import { EnumProductWeightIdentity } from '../product/enums/product.enum';
import { EventSkuRepository } from '../config/repositories/event-sku.repositories';
import { FeatureRepository } from '../config/repositories/feature.repository';
import { PopupTemplateRepository } from '../config/repositories/popup-template.repository';
import { SystemConfigRepository } from '../config/repositories/system-config.repository';
import { HandleEventEvoucherVacxinReqDto } from '../event/dtos/customer/event.customer.req.dto';
import { EventDetailProvince } from '../event/entities/event-detail-province.entity';
import { EventDetail } from '../event/entities/event-detail.entity';
import { EventGroupGiftExclusion } from '../event/entities/event-group-gift-exclusion.entity';
import { EventPointHistory } from '../event/entities/event-point-history.entity';
import { EventUserLimit } from '../event/entities/event-users-limit.entity';
import { EventUser } from '../event/entities/event-users.entity';
import { EventStatus } from '../event/enums/event.enum';
import { EventAddCanType } from '../event-add-can/enums/event-add-can.enum';
import { GiftType, GiftTypeNew } from '../event/enums/gift.enum';
import { UserGiftStatus } from '../event/enums/user-gift.enum';
import { EventDetailExclusionRepository } from '../event/repositories/event-detail-exclusion.repository';
import { EventDetailIgnoreStoreRepository } from '../event/repositories/event-detail-ignore-store.repository';
import { EventDetailProvinceRepository } from '../event/repositories/event-detail-province.repository';
import { EventDetailSupplierRepository } from '../event/repositories/event-detail-supplier.repository';
import { EventDetailSupplierV2Repository } from '../event/repositories/event-detail-supplier-v2.repository';
import { EventDetailSupplierV2 } from '../event/entities/event-detail-supplier-v2.entity';
import { EventDetailToStoreRepository } from '../event/repositories/event-detail-to-store.repository';
import { EventDetailRepository } from '../event/repositories/event-detail.repository';
import { EventGroupGiftExclusionRepository } from '../event/repositories/event-group-gift-exclusion.repository';
import { EventLimitRepository } from '../event/repositories/event-limit.repository';
import { EventPointHistoryRepository } from '../event/repositories/event-point-history.repository';
import { EventProductChildRepository } from '../event/repositories/event-product-child.repository';
import { EventProductRepository } from '../event/repositories/event-product.repository';
import { EventUsersLimitRepository } from '../event/repositories/event-users-limit.repository';
import { EventUsersRepository } from '../event/repositories/event-users.repository';
import { EventRepository } from '../event/repositories/event.repository';
import { Event } from '../event/entities/event.entity';
import { GiftRepository } from '../event/repositories/gift.repository';
import { HistoryUserGiftingGiftEventRepository } from '../history-user-gifting-gift-event/repositories/history-user-gifting-gift-event.repository';
import { HistoryUserGiftingGiftEvent } from '../history-user-gifting-gift-event/entities/history-user-gifting-gift-event.entity';
import { EventDetailTimeGiftingGiftRepository } from '../event/repositories/event-detail-time-gifting-gift.repository';
import { SelfEventDetailExclusionRepository } from '../event/repositories/self-event-detail-exclusion.repository';
import { UserGiftRepository } from '../event/repositories/user-gift.repository';
import { VitaCodeRepository } from '../event/repositories/vita-code.repository';
import { WheelLuckyBlockUserRepository } from '../event/repositories/wheel-lucky-block-user.repository';
import { EventCustomerService } from '../event/services/customer/event.customer.service';
import {
  GiftID,
  NotificationData,
  PUSH_POP_UP_REQUEST_MESSAGE,
  PUSH_POP_UP_REQUEST_NOTI_TITLE,
  PUSH_POP_UP_REQUEST_TITLE,
  PopupV2Code,
  notificationCallingQ2CBB,
  notificationCallingQ2TC,
  notificationCallingQ3CBB,
  notificationCallingQ3CLS400g,
  notificationCallingQ3DHA400g,
  notificationCallingQ3OGGI400g,
  notificationCallingQ3OPTI400g,
  notificationCallingT52024BigC400g,
} from '../external/constants/index.constant';
import {
  JavaV4WhSyncTo3rdServiceCode,
  JavaV4WhSyncTo3rdServiceDestination,
} from '../external/constants/java_v4.constant';
import {
  CrmTransactionTypeRequest,
  PointTransferStatus,
} from '../external/enums/crm.enum';
import { SendZnsSource } from '../external/enums/vgs.enum';
import {
  CreateAddPointTransactionReqDto,
  CrmPointGiftingRequest,
  CrmRedeemGiftRequest,
  CrmRedeemVitaCodeRequest,
  CrmRedeemVoucherRequest,
} from '../external/interfaces/crm.interface';
import {
  SyncData3rdServiceToWhRequestDetail,
  UserGiftReuseRequest,
} from '../external/interfaces/vita-java.interface';
import { VgsRedeemStoreRequest } from '../external/interfaces/vgs.interface';
import {
  PushNotificationRequest,
  PushPopupRequestV2,
  GiftingGiftRequest,
  GiftingGiftResponse,
  GetUserGiftPreOrderEnoughPointToSendNotifyRequest,
  GetUserGiftPreOrderEnoughPointToSendNotifyResponse,
} from '../external/interfaces/vita-java.interface';
import { IResultValidateMatchSetupEventTimeGiftingGift } from './interfaces/event-detail-time-gifting-gift.interface';
import { BravoQrService } from '../external/services/bravo-qr.service';
import { CrmService } from '../external/services/crm.service';
import { VgsService } from '../external/services/vgs.service';
import { VitaGoService } from '../external/services/vita-go.service';
import { VitaJavaService } from '../external/services/vita-java.service';
import { ZenposQrService } from '../external/services/zenpos-qr.service';
import {
  NotificationUserFirebaseStatus,
  NotificationUserSource,
  NotificationUserStatus,
} from '../notification/enums/notification-user.enum';
import { NotificationUserRepository } from '../notification/repositories/notification-user.repository';
import { Product } from '../product/entities/product.entity';
import { ProductRepository } from '../product/repositories/product.repository';
import { AddPoint, protobufPackage } from '../proto/message.pb';
import { UserSessionData } from '../proto/user.pb';
import { VitaQr } from '../qr-code-sbps/entities/vita-qr.entity';
import { ProductMstRepository } from '../qr-code-sbps/repositories/product-mst.repository';
import { VitaQrRepository } from '../qr-code-sbps/repositories/qr-code-sbps.repository';
import { UserSessionRepository } from '../qr-code-sbps/repositories/user-session.repository';
import { Spoon } from '../spoon/entities/spoon.entity';
import { SpoonStatus } from '../spoon/enums/spoon.enum';
import { SpoonRepository } from '../spoon/repositories/spoon.repository';
import { TierRepository } from '../tier/repositories/tier.repository';
import { TRIPLAYZ_EVENT_NAME } from '../triplayz/type';
import { GcPubsubService } from '../utils/services/gc-pubsub.service';
import { HotlineHistoryPointRepository } from '../webapp-calosure/repositories/hotline-history-point.repository';
import { StoreRewardRepository } from '../webhook/repositories/store-reward.repository';
import {
  DATE_APPLY_NEW_SPOON,
  END_TIME_ID_PHASE2,
  SB_SCAN_FAIL_VALID_ERROR_CODE,
  sbErrorCodes,
  sbpsErrorCodes,
  START_TIME_ID_PHASE2,
  PRODUCT_CODE_CBB_PEDIA,
} from './constants/index.constant';
import { ProductDataGotByQrDto } from './dto/misc/product-data-got-by-qr.dto';
import { AddPointReqDto } from './dto/req/add-point.req.dto';
import { TriggerEventMayRuiQ12024ReqDto } from './dto/req/trigger-event-may-rui-q1-2024.req.dto';
import { UserGiftReqDto } from './dto/req/user-gift.req.dto';
import { WebAppGetQrInformationReqDto } from './dto/req/web-app-get-qr-information.req.dto';
import { BravoQr } from './entities/bravo-qr.entity';
import { HistoryPointAttribute } from './entities/history-point-attribute.entity';
import { HistoryPoint } from './entities/history-point.entity';
import { OutboxMessage } from './entities/outbox-message.entity';
import { ZenposQr } from './entities/zenpos-qr.entity';
import { BlockedHistoryType } from './enums/block-history.enum';
import { CrmTransactionTypeMainCode } from './enums/crm-transaction-type.enum';
import { HistoryPointAttributeCode } from './enums/history-point-attribute.enum';
import {
  HistoryPointStatus,
  HistoryPointType,
} from './enums/history-point.enum';
import {
  CallType,
  OutboxMessageStatus,
  SyncProvider,
  SyncType,
} from './enums/outbox-message.enum';
import { QrType } from './enums/qr-type.enum';
import {
  ScanHistoryApiType,
  ScanHistoryStatus,
} from './enums/scan-history.enum';
import { mapHistoryPointType } from './helpers';
import { RequestOutboxMessage } from './interfaces/outbox-message.interface';
import { EventClsQ4Payload } from './listeners/types';
import { BlockedHistoryRepository } from './repositories/blocked-history.repository';
import { BravoQrRepository } from './repositories/bravo-qr.repository';
import { CrmTransactionTypeRepository } from './repositories/crm-transaction-type.repository';
import { DDXTriggerCalculateUserResetGiftPointRepository } from './repositories/ddx_trigger_calculate_user_reset_gift_point.repository';
import { HistoryPointAttributeRepository } from './repositories/history-point-attribute.repository';
import { HistoryPointRepository } from './repositories/history-point.repository';
import { OutboxMessageRepository } from './repositories/outbox-message.repository';
import { ScanHistoryRepository } from './repositories/scan-history.repository';
import { StoreInvitationRepository } from './repositories/store-invitation.repository';
import { StoreRepository } from './repositories/store.repository';
import { UserNumberScanRepository } from './repositories/user-number-scan.repository';
import { UserNumberScan } from './entities/user-number-scan.entity';
import { ZenposQrRepository } from './repositories/zenpos-qr.repository';
import { EventDetailBlockUserRepository } from '../event/repositories/event-detail-block-user.repository';
import { EventDetailSkuRepository } from '../event/repositories/event-detail-sku.repository';
import { EventDetailSku } from '../event/entities/event-detail-sku.entity';
import { EventDetailToUserTypeRepository } from '../event/repositories/event-detail-to-user-type.repository';
import { EventDetailToUserType } from '../event/entities/event-detail-to-user-type.entity';
import {
  EventClsQ4Service,
  EventQ32024UpRankService,
  EventQuy32024ClbbMassSampling,
  EventGiftBy4StepService,
} from './services';
import { EventQuy32024UpRankData } from './services/event-quy-3-2024-up-rank/types';
import { SyncEventPointHistoryToWhEntityRepository } from '../sync-event-point-history-to-wh/repositories/sync-event-point-history-to-wh.repository';
import { generateSyncEventPointHistoryToWhRequestData } from '../sync-event-point-history-to-wh/helpers/index.helper';
import { WebAppList } from '../common/enums/webapp-list.enum';
import { TIMESTAMP_BEGIN_20241001_STRING } from '../qr-code-sbps/constants';
import { SyncEventPointHistoryToWhMethod } from '../sync-event-point-history-to-wh/constants/index.constant';
import { SyncEventPointHistoryToWhUserService } from '../sync-event-point-history-to-wh/services/sync-event-point-history-to-wh.user.service';
import {
  SapProductBySpoonCodeResponse,
  SapCallApiGetProductBySpoonCodeResponse,
  SapProductByQrResponse,
  SapCallApiGetProductByQrResponse,
} from '../external/interfaces/sap.interface';
import { SapService } from '../external/services/sap.service';
import { EventCommonPopupUserService } from '../event-common/services/user/event-common-popup.user.service';
import { EvenlyHistory } from '../event/dtos/admin/req/event-detail.req.dto';
import { EventPopupRepository } from '../event/repositories/event-popup.repository';
import { EventPopupType } from '../event/enums/event-popup.enum';
import { SystemConfigCode } from './enums/system-config.enum';
import { SystemConfigV3Repository } from '../system-config/repositories/system-config.repository';
import { AppResponse } from '../common/decorators/app-response.decorator';
import { EventType, GiftIdInEvent } from './enums/event.enum';
import { CrmTransactionType } from './entities/crm-transaction-type.entity';
import { EventGiftBy4StepCountUserAddedPointEntity } from '../event_gift_by_4_step_count_user_added_point/entities/event_gift_by_4_step_count_user_added_point.entity';
import { EventProduct } from '../event/entities/event-product.entity';
import { ProvinceUserService } from '../provinces/services/user/province.user.service';

@Injectable()
export class PointService {
  private logger = new Logger(PointService.name);
  private crmSyncUsingWh = false;

  constructor(
    private eventEmitter: EventEmitter2,

    @Inject(REQUEST) private readonly request: Request,
    private configService: ConfigService<GlobalConfig>,

    private zenposQrService: ZenposQrService,
    private bravoQrService: BravoQrService,
    private crmService: CrmService,
    private vitaGoService: VitaGoService,
    private vitaJavaService: VitaJavaService,
    private gcPubsubService: GcPubsubService,
    private vgsService: VgsService,
    private sapService: SapService,
    private systemConfigRepo: SystemConfigRepository,
    private userNumberScanRepo: UserNumberScanRepository,
    private scanHistoryRepo: ScanHistoryRepository,
    private blockedHistoryRepo: BlockedHistoryRepository,
    private userRepo: UserRepository,
    private featureRepo: FeatureRepository,
    private spoonRepo: SpoonRepository,
    private zenposQrRepo: ZenposQrRepository,
    private bravoQrRepo: BravoQrRepository,
    private vitaQrRepo: VitaQrRepository,
    private productRepo: ProductRepository,
    private eventSkuRepo: EventSkuRepository,
    private tierRepo: TierRepository,
    private crmTransactionTypeRepo: CrmTransactionTypeRepository,
    private historyPointRepo: HistoryPointRepository,
    private systemConfigV3Repo: SystemConfigV3Repository,
    private historyPointAttributeRepo: HistoryPointAttributeRepository,
    private storeInvitationRepo: StoreInvitationRepository,
    private eventRepo: EventRepository,
    private eventProductRepo: EventProductRepository,
    private wheelLuckyBlockUserRepo: WheelLuckyBlockUserRepository,
    private userSessionRepo: UserSessionRepository,
    private popupTemplateRepo: PopupTemplateRepository,
    private notificationUserRepo: NotificationUserRepository,
    private eventUserRepo: EventUsersRepository,
    private eventLimitRepo: EventLimitRepository,
    private eventUserLimitRepo: EventUsersLimitRepository,
    private storeRepo: StoreRepository,
    private eventDetailRepo: EventDetailRepository,
    private eventPointHistoryRepo: EventPointHistoryRepository,
    private eventDetailExclusionRepo: EventDetailExclusionRepository,
    private eventGroupGiftExclusionRepo: EventGroupGiftExclusionRepository,
    private userGiftRepo: UserGiftRepository,
    private readonly historyUserGiftingGiftEventRepo: HistoryUserGiftingGiftEventRepository,
    private readonly eventDetailTimeGiftingGiftRepo: EventDetailTimeGiftingGiftRepository,
    private eventDetailProvinceRepo: EventDetailProvinceRepository,
    private giftRepo: GiftRepository,
    private outboxMessageRepo: OutboxMessageRepository,
    private storeRewardRepo: StoreRewardRepository,
    private selfEventDetailExclusionRepo: SelfEventDetailExclusionRepository,
    private eventDetailToStoreRepo: EventDetailToStoreRepository,
    private eventDetailSupplierRepo: EventDetailSupplierRepository,
    private eventDetailSupplierV2Repo: EventDetailSupplierV2Repository,
    private productMstRepo: ProductMstRepository,
    private eventDetailIgnoreStoreRepo: EventDetailIgnoreStoreRepository,

    private eventProductChildRepo: EventProductChildRepository,

    private vitaCodeRepo: VitaCodeRepository,
    private googleSheetService: GoogleSheetService,
    private hotlineHistoryPointRepo: HotlineHistoryPointRepository,
    private eventDetailBlockUserRepo: EventDetailBlockUserRepository,
    private eventDetailSkuRepo: EventDetailSkuRepository,
    private eventDetailToUserTypeRepo: EventDetailToUserTypeRepository,
    private syncEventPointHistoryToWhEntityRepo: SyncEventPointHistoryToWhEntityRepository,
    private eventCustomerService: EventCustomerService,
    private syncEventPointHistoryToWhUserService: SyncEventPointHistoryToWhUserService,
    private _eventClsQ4Service: EventClsQ4Service,
    private _eventGiftBy4StepService: EventGiftBy4StepService,
    private _eventQ32024UpRankService: EventQ32024UpRankService,
    private _eventQuy32024ClbbMassSampling: EventQuy32024ClbbMassSampling,
    private eventCommonPopupUserService: EventCommonPopupUserService,
    private eventPopupRepo: EventPopupRepository,

    private ddxTriggerCalculateUserResetGiftPointRepo: DDXTriggerCalculateUserResetGiftPointRepository,
    private readonly provinceUserService: ProvinceUserService,
  ) {
    this.crmSyncUsingWh = this.configService.get('crm.options.syncUsingWh');
  }

  /*
  async debugDateTime() {
    const [user, event] = await Promise.all([
      this.userRepo.findOneBy({
        id: 135408,
      }),
      this.eventRepo.findOneBy({
        id: 619,
      }),
    ]);
    console.log(new Date());
    console.log(user.createdDate);
    console.log(user.createdDate.toLocaleString());
    console.log(event.startDate);
    console.log(event.startDate.toLocaleString());

    console.log(
      compareDateWithDateInTimezoneNewVersion(
        user.createdDate.toLocaleString(),
        event.startDate,
      ),
    );

    return;
  }
  */

  async addPoint(
    dto: AddPointReqDto,
    user: UserSessionData,
    token: string,
    appversionname: string,
    source?: ScanHistoryApiType,
  ) {
    console.log('start add point user', user.userId);
    try {
      const result = await runInTransaction(() =>
        this.handleAddPoint(dto, user.userId, token, appversionname, source),
      );
      return result;
    } catch (error) {
      await runInTransaction(() =>
        this.handleAddPointError(error, dto, user.userId, source),
      );

      const addPointErrorCount = await this.addPointErrorCount(user);
      if (error instanceof AppBaseExc) {
        if (sbErrorCodes.includes(error.error))
          error.subInfo = addPointErrorCount.response;

        throw error;
      }

      throw new AppBaseExc(
        StatusCode.SB_DEFAULT_ERROR,
        '',
        true,
        StatusCode.API_FAILED_UNKNOWN,
        this.request,
      );
    }
  }

  async addPointErrorCount(user: UserSessionData) {
    const currentUser = await this.userRepo.findOne({
      where: {
        id: user.userId,
      },
    });

    const startOfNextDay = getStartOfDateInTimezone(
      dayjs().add(1, 'day').toDate(),
    );

    const sbpsConfigLimit = await this.systemConfigRepo.findOne({
      where: {
        code: SystemConfigCode.LIMIT_NUMBER_OF_SCAN_SBPS_FAILED_IN_DAY,
      },
    });

    const sbConfigLimit = await this.systemConfigRepo.findOne({
      where: {
        code: SystemConfigCode.LIMIT_NUMBER_OF_SCAN_SAME_QR_FAILED,
      },
    });

    const currentTime = getNowAtTimezone();
    if (currentTime > currentUser.dateCompareResetFailedAddPoint) {
      currentUser.sbpsFailAddPointCounter = 0;
      currentUser.sbFailAddPointCounter = 0;
      currentUser.dateCompareResetFailedAddPoint = startOfNextDay;
      await this.userRepo.save(currentUser);
      return new AppResponseDto({
        sb_fail_count: 0,
        sb_remain_count: Number(sbConfigLimit.value),
      });
    }

    return new AppResponseDto({
      sb_fail_count: currentUser.sbFailAddPointCounter,
      sb_remain_count: Math.max(
        0,
        Number(sbConfigLimit.value) - currentUser.sbFailAddPointCounter,
      ),
    });
  }

  async eventMayRuiQuy12024(
    user: UserSessionData,
    reqData: TriggerEventMayRuiQ12024ReqDto,
    token?: string,
  ) {
    const eventQuy12024MayRuiId = this.configService.get(
      'event.eventQuy12024MayRui',
    );
    const popupCode = await this.handleEventMayRuiQuy12024(
      user,
      eventQuy12024MayRuiId,
      reqData,
      token,
    );

    return new AppResponseDto(popupCode, {
      status: HttpStatus.OK,
    });
  }

  @Transactional()
  async handleEventMayRuiQuy12024(
    userSessionData: UserSessionData,
    eventId: number,
    reqData: TriggerEventMayRuiQ12024ReqDto,
    token?: string,
  ) {
    const userId = userSessionData.userId;
    const current = getNowAtTimeHcm();
    console.log(`Handle event may rui Q1 2024 for user`, userId);
    const [user, event] = await Promise.all([
      this.userRepo.findOneBy({
        id: userId,
      }),
      this.eventRepo.findOneBy({
        id: eventId,
        status: EventStatus.ACTIVE,
        endDate: MoreThan(current),
        startDate: LessThan(current),
      }),
    ]);
    if (!user) {
      throw new AppBaseExc(
        StatusCode.SB_DEFAULT_ERROR,
        null,
        null,
        StatusCode.API_FAILED_UNKNOWN,
        this.request,
      );
    }
    if (!event) {
      return;
    }
    const now = getNowAtTimeZoneHcm();
    const transactionRefId = randomTransactionExternalId();

    // check user in blacklist
    const blockedUser = await this.wheelLuckyBlockUserRepo.findOneBy({
      phoneNumber: user.phoneNumber,
    });
    if (event.activeBlacklist) {
      console.log('Check blocked for user', user.id);
      if (blockedUser) {
        return '';
      }
    }
    // check user_type
    const userType: string = await this.getUserType(user, event.startDate);
    console.log('userType', userType);
    // check if already add point, create or update event_user
    let eventUser = await this.eventUserRepo.findOneBy({
      userId: user.id,
      eventId: event.id,
    });
    // console.log(`Event user`);
    // console.log(eventUser);

    let eventLimit = await this.eventLimitRepo.findOneBy({
      eventId: event.id,
      type: userType as UserTypeV2,
    });
    // console.log(`Event limit`);
    // console.log(eventLimit);

    if (!eventUser) {
      const userLimitCustomer = await this.eventLimitRepo.findOneBy({
        eventId: event.id,
        type: UserTypeV2.CUSTOMER,
      });
      const userLimitStore = await this.eventLimitRepo.findOneBy({
        eventId: event.id,
        type: UserTypeV2.STORE,
      });
      const userLimitNewUser = await this.eventLimitRepo.findOneBy({
        eventId: event.id,
        type: UserTypeV2.NEW_USER,
      });
      let userLimit = 0;
      switch (userType) {
        case UserTypeV2.CUSTOMER:
          userLimit = userLimitCustomer.userLimit;
          eventLimit = userLimitCustomer;
          break;
        case UserTypeV2.STORE:
          userLimit = userLimitStore.userLimit;
          eventLimit = userLimitStore;
          break;
        case UserTypeV2.NEW_USER:
          userLimit = userLimitNewUser.userLimit;
          eventLimit = userLimitNewUser;
          break;
        default:
          userLimit = 0;
          break;
      }

      const eventUserNew = this.eventUserRepo.create({
        eventId: event.id,
        userId: user.id,
        winRate: eventLimit.winRate,
        userLimit: 1,
      });

      const savedEventUser = await this.eventUserRepo.save(eventUserNew);
      eventUser = eventUserNew;

      const eventUserLimit = this.eventUserLimitRepo.create({
        eventUserId: savedEventUser.id,
        type: userType,
        userLimit,
      });
      await this.eventUserLimitRepo.save(eventUserLimit);
    }
    console.log('Check user event win rate for user', user.id);

    // check winRate
    if (eventUser.winRate !== 100) {
      console.log('-------------eventUser.winRate !== 100------------------');
      await this.increaseWinrate(eventUser, eventLimit.upRate);
      return '';
    }

    const eventUserLimit = await this.eventUserLimitRepo.findOneBy({
      eventUserId: eventUser.id,
      type: userType,
    });
    console.log('Check user event limit for user', user.id);

    // check if user reaches limit
    if (!eventUserLimit || eventUserLimit.userLimit === 0) {
      console.log(
        '-------------!eventUserLimit || eventUserLimit.userLimit === 0------------------',
      );
      await this.increaseWinrate(eventUser, eventLimit.upRate);
      return '';
    }

    // check if user received exclusive gift on child events
    const eventPointHistoriesOfUser = await this.eventPointHistoryRepo.findBy({
      userId: user.id,
    });
    const eventDetailIdsUserReceived = eventPointHistoriesOfUser.map(
      (eventPointHistory) => eventPointHistory.eventDetailId,
    );

    // get possible ordinals
    const eventOrdinals = await this.eventDetailRepo
      .createQueryBuilder('eventDetail')
      .where('eventDetail.eventId = :eventId', { eventId: event.id })
      .andWhere('eventDetail.quantity > 0')
      .andWhere('eventDetail.ordinal > 0')
      .orderBy('eventDetail.ordinal', 'ASC')
      .maxExecutionTime(60000) // timeout 60s
      .getMany();

    const listEventDetailIdsOfCurrentEvent = eventOrdinals.map(
      (ordinal) => ordinal.id,
    );

    const eventDetailIdsUserReceivedOfCurrentEvent =
      eventDetailIdsUserReceived.filter((ids) =>
        listEventDetailIdsOfCurrentEvent.includes(ids),
      );

    const exclusiveEventDetail = await this.eventDetailExclusionRepo.findOneBy({
      userType,
      eventDetailId: In(eventDetailIdsUserReceivedOfCurrentEvent),
    });

    console.log('Check exclusive event detail for user', user.id);

    if (exclusiveEventDetail) {
      console.log('-------------exclusiveEventDetail------------------');
      await this.increaseWinrate(eventUser, eventLimit.upRate);
      return '';
    }

    let userReceiveGift = false;
    let popupCode = '';
    console.log('Loop event detail for user', user.id);
    console.log('date history point sb', now);

    /*const blockedEventDetailUser =
      await this.eventDetailBlockUserRepo.findOneBy({
        phoneNumber: user.phoneNumber,
        //eventDetailId: eventOrdinal.id,
      });*/
    for (const eventOrdinal of eventOrdinals) {
      // Check event detail black list
      if (eventOrdinal.activeBlacklist) {
        /*const blockedUser = await this.eventDetailBlockUserRepo.findOneBy({
          phoneNumber: user.phoneNumber,
          eventDetailId: eventOrdinal.id,
        });*/
        console.log('Check block user event detail for user', user.id);
        if (blockedUser) {
          continue;
        }
      }
      // check if gift belongs to self exclusive gifts
      const selfEventDetailExclusion =
        await this.selfEventDetailExclusionRepo.findOneBy({
          userType,
          eventDetailId: eventOrdinal.id,
        });
      console.log('Check self exclusion event detail for user', user.id);
      if (selfEventDetailExclusion) {
        continue;
      }
      console.log('Check event detail probability for user', user.id);
      // check probability
      if (eventOrdinal.probability !== 100) {
        continue;
      }

      // check if gift belongs to exclusive gifts of parent event
      const eventDetailExclusion =
        await this.eventDetailExclusionRepo.findOneBy({
          eventDetailId: eventOrdinal.id,
          userType,
        });
      if (eventDetailExclusion) {
        const eventGroupGiftExclusion =
          await this.eventGroupGiftExclusionRepo.findOneBy({
            eventGroupId: event.eventGroupId,
            eventDetailExclusionId: eventDetailExclusion.id,
          });
        if (eventGroupGiftExclusion) {
          // if belongs, check if user received exclusive gift from parent event
          const eventDetailExclusionReceived =
            await this.eventDetailExclusionRepo.findBy({
              userType,
              eventDetailId: In(eventDetailIdsUserReceived),
            });
          const eventDetailExclusionIdsReceived =
            eventDetailExclusionReceived.map((ede) => ede.id);
          const exclusiveEventGroup =
            await this.eventGroupGiftExclusionRepo.findOneBy({
              eventGroupId: event.eventGroupId,
              eventDetailExclusionId: In(eventDetailExclusionIdsReceived),
            });
          console.log('Check exclusive event group for user', user.id);
          if (exclusiveEventGroup) {
            continue;
          }
        }
      }
      console.log('Check event detail quantity for user', user.id);
      // check quantity of ordinal
      if (eventOrdinal.quantity === 0) {
        continue;
      }

      console.log(
        'Check start date end date event detail not null for user',
        user.id,
      );

      // check if province setup
      if (eventOrdinal.startDate && eventOrdinal.endDate) {
        console.log(
          'Check start date end date event detail valid for user',
          user.id,
        );
        // province not setup, check if event is still on air
        const rsCompareEventDetailStartDateWithNow =
          compareDateWithCurrentDateInTimezone(eventOrdinal.startDate);
        const rsCompareEventDetailEndDateWithNow =
          compareDateWithCurrentDateInTimezone(eventOrdinal.endDate);
        /*if (
            dayjs(now).isAfter(dayjs(eventOrdinal.startDate)) &&
            dayjs(now).isBefore(dayjs(eventOrdinal.endDate))
          ) {*/

        console.log('test 1');
        if (
          -1 == rsCompareEventDetailStartDateWithNow &&
          1 == rsCompareEventDetailEndDateWithNow
        ) {
          // BOOM
          console.log('test 2');
          userReceiveGift = true;
          await this.redeemGiftMayRuiEventQuy12024(
            userSessionData,
            eventUser,
            eventLimit,
            eventOrdinal,
            user,
            now,
            eventUserLimit,
            reqData,
          );
          console.log('test 3');
          // popupCode = GiftID[eventOrdinal.giftId];
          popupCode = this.getPopupCode(eventOrdinal);
          console.log('Check popup code exists for user', user.id);
          if (!popupCode) {
            throw new AppBaseExc(
              StatusCode.SB_DEFAULT_ERROR,
              null,
              null,
              StatusCode.API_FAILED_UNKNOWN,
              this.request,
            );
          }
          console.log('Push popup for user', user.id);
          await this.pushEventQ1Notification(user.id, eventOrdinal);
          break;
        }
      } else {
        // province setup
        // date have 2023-06-14 15:10:00.000 +0700  use this function
        const current = getNowAtTimeHcm();
        const eventDetailProvince =
          await this.eventDetailProvinceRepo.findOneBy({
            eventDetailId: eventOrdinal.id,
            provinceId: user.provinceId,
            endDate: MoreThanOrEqual(current),
            startDate: LessThanOrEqual(current),
          });

        console.log('Check event detail province for user', user.id);
        // check if user province not in ordinal
        if (!eventDetailProvince) {
          // next ordinal
          continue;
        }
        console.log('Check event detail province quantity for user', user.id);
        // check if province quantity > 0
        if (eventDetailProvince.quantity === 0) {
          // next ordinal
          continue;
        }

        // BOOM
        userReceiveGift = true;
        await this.redeemGiftMayRuiEventQuy12024(
          userSessionData,
          eventUser,
          eventLimit,
          eventOrdinal,
          user,
          now,
          eventUserLimit,
          reqData,
          eventDetailProvince,
          token,
        );

        // popupCode = GiftID[eventOrdinal.giftId];
        const popupCode = this.getPopupCode(eventOrdinal);
        console.log('Check popup code exists for user', user.id);
        if (!popupCode) {
          throw new AppBaseExc(
            StatusCode.SB_DEFAULT_ERROR,
            null,
            null,
            StatusCode.API_FAILED_UNKNOWN,
            this.request,
          );
        }
        console.log('Push popup for user', user.id);
        await this.pushEventQ1Notification(user.id, eventOrdinal);
        break;
      }
    }

    if (!userReceiveGift) {
      console.log('-------------!userReceiveGift------------------');
      await this.increaseWinrate(eventUser, eventLimit.upRate);
      return '';
    }

    return popupCode;
  }

  private async handleCheckQrDuplicate(
    dto: AddPointReqDto,
    user: User,
    transactionExternalId: string,
    isFirstScan: boolean,
    token: string,
    requestOutBoxMessage: RequestOutboxMessage,
  ) {
    const { qrCode, spoonCode } = dto;
    const scanHistory = await this.scanHistoryRepo.findOneBy({
      qrCode: dto.qrCode,
      status: ScanHistoryStatus.SUCCESS,
    });

    if (scanHistory && scanHistory.userId != user.id) {
      const spoon = await this.spoonRepo
        .createQueryBuilder('spoon')
        .where('spoon.spoonCode = :spoonCode', { spoonCode })
        .setLock('pessimistic_write')
        .maxExecutionTime(60000) // lock timeout 60s
        .getOne();
      const { productData } = await this.getProductByQr(qrCode);
      const product = await this.getAndCheckProduct(productData);
      // this.createAddPointTransaction(
      //   dto,
      //   user,
      //   product,
      //   productData,
      //   isFirstScan,
      //   spoon,
      //   token,
      //   transactionExternalId,
      //   requestOutBoxMessage,
      //   true,
      // );
      throw new AppBaseExc(StatusCode.SB_QR_USED, '', true);
    }
  }

  private async handleAddPoint(
    dto: AddPointReqDto,
    userId: number,
    token: string,
    appversionname: string,
    source?: ScanHistoryApiType,
  ) {
    console.log('start handle add point user', userId);

    const transactionExternalId = randomTransactionExternalId();

    const user = await this.userRepo
      .createQueryBuilder('user')
      .where('user.id = :userId', { userId })
      .setLock('pessimistic_write')
      .maxExecutionTime(60000) // lock timeout 60s
      .getOne();

    if (user.blockedAccount) {
      throw new AppBaseExc(
        StatusCode.BLOCKED_ACCOUNT,
        null,
        null,
        StatusCode.BLOCKED_ACCOUNT,
        this.request,
      );
    }

    // freeze user

    if (user.startFreezePoint && user.endFreezePoint) {
      const currentTime = getNowAtTimeZoneHcm();
      const isWithinFreezePeriod =
        compareDateBetweenFromDateIAndToDateInTimezone(
          currentTime,
          user.startFreezePoint,
          user.endFreezePoint,
        );
      if (isWithinFreezePeriod === 1) {
        throw new AppBaseExc(
          StatusCode.BLOCK_IDENTITY_SCAN,
          null,
          null,
          StatusCode.BLOCK_IDENTITY_SCAN,
          this.request,
        );
      }
    }
    //

    if (user.status === UserStatus.NON_ACTIVE)
      throw new AppBaseExc(
        StatusCode.SB_DEFAULT_ERROR,
        null,
        null,
        StatusCode.API_FAILED_UNKNOWN,
        this.request,
      );

    const validateUserSetupNewProvinceOnly =
      await this.provinceUserService.validateUserSetupNewProvinceOnly(user.id);
    // if (!user?.provinceId)
    //   throw new AppBaseExc(
    //     StatusCode.SB_ADD_EMPTY,
    //     '',
    //     true,
    //     StatusCode.API_FAILED_UNKNOWN,
    //     this.request,
    //   );
    if (!validateUserSetupNewProvinceOnly) {
      throw new AppBaseExc(
        StatusCode.SB_ADD_EMPTY,
        '',
        true,
        StatusCode.API_FAILED_UNKNOWN,
        this.request,
      );
    }

    console.log('add point 1, user', userId);

    const { qrCode: qrCodeUser, spoonCode: spoonCodeUser } = dto;
    const qrCode = qrCodeUser.toUpperCase();
    const spoonCode = spoonCodeUser.toUpperCase();

    let isFirstScan = false;

    const dataFirstScan = await this.historyPointRepo
      .createQueryBuilder('historyPoint')
      .where('historyPoint.customerId = :customerId', { customerId: user.id })
      .andWhere(
        `historyPoint.actionType LIKE 'FIRST_SCAN%' AND historyPoint.actionType != 'FIRST_SCAN_SBPS'`,
      )
      .getOne();

    if (!dataFirstScan) {
      const dataQrCode = await this.historyPointRepo
        .createQueryBuilder('historyPoint')
        .where('historyPoint.customerId = :customerId', { customerId: user.id })
        .andWhere('historyPoint.actionType LIKE :actionType', {
          actionType: 'QR_CODE',
        })
        .getOne();

      isFirstScan = dataQrCode ? false : true;
    }

    await this.checkBlockScan(user);

    console.log('add point 2, user', userId);

    const requestOutboxMessage: RequestOutboxMessage = [];
    const outboxMessageGift: OutboxMessage[] = [];
    const syncData3rdServiceToWhRequestDetails: SyncData3rdServiceToWhRequestDetail[] =
      [];

    await this.handleCheckQrDuplicate(
      dto,
      user,
      transactionExternalId,
      isFirstScan,
      token,
      requestOutboxMessage,
    );

    // eslint-disable-next-line prefer-const
    let [
      { spoon, spoonOnSap },
      { productData, saveQrCallback },
      userNumberScan,
    ] = await Promise.all([
      this.getSpoonAndCheckSpoonWithQr(spoonCode, qrCode),
      this.getProductByQr(qrCode),
      this.checkLimitNumberOfScan(user.id),
      this.checkFeatureScanQr(),
    ]);

    console.log('add point 3, user', userId);

    const product = await this.getAndCheckProduct(productData);
    this.applySpoonQrRule(
      product,
      productData,
      spoon,
      spoonOnSap,
      productData.manufactureDate,
      qrCode,
    );

    console.log('add point 4, user', userId);

    let value;

    let arrNoti = [];

    spoon = await this.useSpoon(
      spoon,
      spoonOnSap,
      productData.manufactureDate,
      qrCode,
    );

    await Promise.allSettled([
      //this.useSpoon(spoon, spoonOnSap, productData.manufactureDate, qrCode),
      this.createScanHistory(user, dto, ScanHistoryStatus.SUCCESS, source),
      saveQrCallback(),
      this.createOrUpdateUserNumberScan(userId, userNumberScan),
      this.updateUserPoint(user, product, transactionExternalId, isFirstScan),
    ]).then((results) =>
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          throw result.reason;
        }
        //if (index === 4) {
        if (index === 3) {
          value = result.value;
        }
      }),
    );
    const userUpdated = value?.userUpdate;
    const notiUpRank = value?.notiUpRank;
    const listTransGiftPoint = value?.listTransGiftPoint;

    console.log('add point 5, user', userId);

    const historyPoint = await this.createAddPointTransaction(
      dto,
      userUpdated,
      product,
      productData,
      isFirstScan,
      spoon,
      token,
      transactionExternalId,
      requestOutboxMessage,
    );

    if (listTransGiftPoint && !!listTransGiftPoint?.length) {
      for (let i = 0; i < listTransGiftPoint?.length; i++) {
        if (listTransGiftPoint[i]?.user) {
          await this.createTranGiftPoint(
            listTransGiftPoint[i]?.user,
            listTransGiftPoint[i]?.transactionExternalId,
            listTransGiftPoint[i]?.actionType,
            listTransGiftPoint[i]?.point,
            listTransGiftPoint[i]?.crmCode,
            requestOutboxMessage,
            listTransGiftPoint[i]?.notiDisplayTemplateType,
            listTransGiftPoint[i]?.notiParams,
            listTransGiftPoint[i]?.totalPoint,
          );
        }
      }
    }
    if (notiUpRank && notiUpRank?.userId) {
      await this.pushUpdateRankNotification(
        notiUpRank?.userId,
        notiUpRank?.title,
        notiUpRank?.content,
        notiUpRank?.description,
        user,
      );
    }

    console.log('add point 6, user', userId);

    console.log('add point and event success, user', userId);

    runOnTransactionCommit(() => {
      console.log('Giao dịch đã được xác nhận.');
      this.pushAddPointToPubsub(user, dto, productData, product, historyPoint);
    });

    // Invitation Store Phase 2
    console.log(
      getNowAtTimeZoneHcm().toString() < END_TIME_ID_PHASE2,
      END_TIME_ID_PHASE2,
      getNowAtTimeZoneHcm().toString(),
    );

    if (
      isFirstScan &&
      new Date() >= START_TIME_ID_PHASE2 &&
      getNowAtTimeZoneHcm().toString() < END_TIME_ID_PHASE2
    ) {
      await this.redeemStore(user, historyPoint);
    }

    await this.eventEmitter.emitAsync(
      'event.product01',
      userUpdated,
      product,
      spoon.spoonCode,
      productData,
      transactionExternalId,
      arrNoti,
      appversionname,
      requestOutboxMessage,
    );

    // TODO: Event q4
    const eventAddCanTypeTrigger: EventAddCanType[] = [];
    const arrTemplate2468Noti = [];
    const eventTrigger: EventType[] = [];
    const arrNotiLength = arrNoti.length;

    // await this.eventEmitter.emitAsync(
    //   'event.Q2',
    //   userUpdated,
    //   product,
    //   spoon.spoonCode,
    //   productData,
    //   transactionExternalId,
    //   arrNoti,
    //   appversionname,
    //   requestOutboxMessage,
    //   token,
    //   'SB',
    //   outboxMessageGift,
    //   syncData3rdServiceToWhRequestDetails,
    //   eventTrigger,
    // );

    // let isEventQ32024ClbbMassSamplingTriggered = false;
    // const arrNotiEventQ32024ClbbMassSampling: any[] = [];
    // await this.eventEmitter.emitAsync(
    //   'event.Q32024ClbbMassSampling',
    //   userUpdated,
    //   product,
    //   spoon.spoonCode,
    //   productData,
    //   transactionExternalId,
    //   arrNotiEventQ32024ClbbMassSampling,
    //   appversionname,
    //   requestOutboxMessage,
    //   token,
    //   outboxMessageGift,
    //   syncData3rdServiceToWhRequestDetails,
    //   'SB',
    // );
    // if (arrNotiEventQ32024ClbbMassSampling.length) {
    //   isEventQ32024ClbbMassSamplingTriggered = true;
    //   arrNoti = arrNotiEventQ32024ClbbMassSampling;
    // }

    // await this.eventEmitter.emitAsync(
    //   EVENT_EMITTER_NAME.EVENT_2468,
    //   userUpdated,
    //   product,
    //   spoon.spoonCode,
    //   productData,
    //   transactionExternalId,
    //   arrTemplate2468Noti,
    //   appversionname,
    //   requestOutboxMessage,
    //   eventAddCanTypeTrigger,
    //   'SB',
    // );

    // let isEventThang52024BigC = false;
    // let isEventQuy32024ClbbEvoucherTopup = false;
    // for (const type of eventTrigger) {
    //   if (EventType.EV_BIGC_24_Q3 == type) {
    //     isEventThang52024BigC = true;
    //   }
    //   if (EventType.EV_CLBB_EVOUCHER_TOPUP_24_Q3 == type) {
    //     isEventQuy32024ClbbEvoucherTopup = true;
    //   }
    // }
    // const eventBigCHasPopupCode =
    //   isEventThang52024BigC && arrNotiLength != arrNoti.length;
    // const eventQuy32024ClbbEvoucherTopupHasPopupCode =
    //   isEventQuy32024ClbbEvoucherTopup && arrNotiLength != arrNoti.length;
    // let isEventCLBBQ32024Trigger = false;
    // for (const type of eventAddCanTypeTrigger) {
    //   if (EventAddCanType.EVQ3_CLBB_24 == type) {
    //     isEventCLBBQ32024Trigger = true;
    //   }
    // }
    // if (!isEventQ32024ClbbMassSamplingTriggered) {
    //   if (eventQuy32024ClbbEvoucherTopupHasPopupCode) {
    //     arrNoti = arrNoti;
    //   } else if (isEventCLBBQ32024Trigger && arrTemplate2468Noti.length) {
    //     arrNoti = arrTemplate2468Noti;
    //   } else if (!eventBigCHasPopupCode) {
    //     arrNoti = arrNoti.concat(arrTemplate2468Noti);
    //   }
    // }

    // // hotline add point -> do not trigger event CLS
    // if (!source || source != ScanHistoryApiType.HOTLINE) {
    //   const {
    //     notificationCodes = [],
    //     postCommitActions = [],
    //     // NOTE: comment for not add request receive gift
    //     // clsOutBoxMessageRequests = [],
    //     outBoxMessages = [],
    //     syncData3rdServiceToWhRequestDetailArr = [],
    //   } = await this.handleEventClsPhase1(
    //     new EventClsQ4Payload({
    //       productData,
    //       token,
    //       spoon,
    //       user,
    //       appversionname,
    //       product,
    //       transactionRefId: transactionExternalId,
    //       isInTransaction: true,
    //     }),
    //   );
    //   arrNoti = notificationCodes?.length ? notificationCodes : arrNoti;
    //   // NOTE: push outbox messages from CLS to outboxMessageGift for bulk insert
    //   outboxMessageGift.push(...outBoxMessages);
    //   syncData3rdServiceToWhRequestDetails.push(
    //     ...syncData3rdServiceToWhRequestDetailArr,
    //   );

    //   runOnTransactionCommit(() => {
    //     asyncMapSettled(postCommitActions, (action) => action());
    //   });

    //   // NOTE: add request receive gift
    //   // requestOutboxMessage.push(...clsOutBoxMessageRequests);
    // }

    // if (this.crmSyncUsingWh) {
    //   const syncAddPointData3rdServiceToWhRequestDetails: SyncData3rdServiceToWhRequestDetail[] =
    //     [];
    //   syncAddPointData3rdServiceToWhRequestDetails.push(
    //     this.vitaJavaService.generateSyncData3rdServiceToWhRequestDetail(
    //       requestOutboxMessage,
    //       JavaV4WhSyncTo3rdServiceDestination.SF,
    //       JavaV4WhSyncTo3rdServiceCode.ADD_POINT,
    //     ),
    //   );
    //   syncAddPointData3rdServiceToWhRequestDetails.push(
    //     this.vitaJavaService.generateSyncData3rdServiceToWhRequestDetail(
    //       requestOutboxMessage,
    //       JavaV4WhSyncTo3rdServiceDestination.SAP,
    //       JavaV4WhSyncTo3rdServiceCode.ADD_POINT_MAMUONG,
    //     ),
    //   );
    //   const whAddPointOutboxMessageRequestData =
    //     this.vitaJavaService.generateWhOutboxMessageSyncData3rdServiceRequestDataInBatch(
    //       token,
    //       syncAddPointData3rdServiceToWhRequestDetails,
    //     );
    //   const outboxAddPointSyncWh =
    //     this.outboxMessageRepo.createSyncWhSyncData3rdService(
    //       whAddPointOutboxMessageRequestData,
    //     );
    //   await this.outboxMessageRepo.save(outboxAddPointSyncWh);

    //   if (syncData3rdServiceToWhRequestDetails.length) {
    //     const whOutboxMessageRequestData =
    //       this.vitaJavaService.generateWhOutboxMessageSyncData3rdServiceRequestDataInBatch(
    //         token,
    //         syncData3rdServiceToWhRequestDetails,
    //       );
    //     const outboxSyncWh =
    //       this.outboxMessageRepo.createSyncWhSyncData3rdService(
    //         whOutboxMessageRequestData,
    //       );
    //     runOnTransactionCommit(() => {
    //       this.outboxMessageRepo.save(outboxSyncWh);
    //     });
    //   }
    // } else {
    //   const newOutboxMessage = this.outboxMessageRepo.create({
    //     provider: SyncProvider.CRM,
    //     callType: CallType.SYNC,
    //     syncType: SyncType.IMMEDIATE,
    //     request: JSON.stringify(requestOutboxMessage),
    //     status: OutboxMessageStatus.PROCESSING,
    //     createdDate: getNowAtTimeHcm(),
    //     retryNumber: 0,
    //   });

    //   await this.outboxMessageRepo.save(newOutboxMessage);

    //   if (outboxMessageGift.length) {
    //     runOnTransactionCommit(() => {
    //       this.outboxMessageRepo.insert(outboxMessageGift);
    //     });
    //   }
    // }

    // console.log('arrNoti', arrNoti);
    // //handle add transaction to Triplayz third party Transaction
    // this.eventEmitter.emit(TRIPLAYZ_EVENT_NAME.ADD_TRANSACTION, {
    //   token,
    //   historyPoint,
    //   user,
    //   product,
    //   dto,
    //   productData,
    //   crmPointRequest: requestOutboxMessage?.[0],
    // });

    // const eventQuy32024UpRankData: EventQuy32024UpRankData = {
    //   user,
    //   requestOutboxMessage,
    //   product,
    //   token,
    //   spoonCode,
    //   qrCode,
    // };
    // const {
    //   postCommitActions = [],
    //   popupCode = '',
    //   gift = null,
    //   triggered = false,
    // } = await this.handleEventQ32024UpRank(eventQuy32024UpRankData);
    // if (triggered) {
    //   arrNoti = [];
    //   if (postCommitActions && postCommitActions.length) {
    //     runOnTransactionCommit(() => {
    //       asyncMapSettled(postCommitActions, (action) => action());
    //     });
    //   }
    //   await Promise.all([
    //     this.pushPopupV2(user.id, popupCode, arrNoti, appversionname),
    //     this.pushEventQ1Notification(user.id, gift),
    //   ]);
    // }

    // // Send noti enough point to finish pre order user gift
    // if (user.giftPoint) {
    //   const request: GetUserGiftPreOrderEnoughPointToSendNotifyRequest = {
    //     userTotalPoint: user.giftPoint,
    //   };
    //   const response: GetUserGiftPreOrderEnoughPointToSendNotifyResponse =
    //     await this.vitaJavaService.getUserGiftPreOrderEnoughPointToSendNotify(
    //       token,
    //       request,
    //     );
    //   if (response) {
    //     const giftName = response?.data?.gift?.name;
    //     if (giftName) {
    //       try {
    //         const useNotiV3 = this.configService.get('useNotiV3');
    //         if (useNotiV3) {
    //           const outboxesMsg: OutboxMessage[] = [];
    //           for (const name of giftName) {
    //             const kafkaDto = new PushNotiKafkaDto({
    //               userIds: [user.id],
    //               version: PushNotiKafkaDtoVersion.V1,
    //               notiDisplayTemplateParams: {
    //                 gift_name: name,
    //               },
    //               notiDisplayTemplateType:
    //                 NotiDisplayTemplateType.ENOUGH_POINT_TO_FINISH_PRE_ORDER_USER_GIFT,
    //               featureNoti: FeatureNoti.NOTI_PRE_ORDER_GIFT,
    //             });

    //             const outboxMsg =
    //               this.outboxMessageRepo.createPushNoti(kafkaDto);
    //             outboxesMsg.push(outboxMsg);
    //           }

    //           await this.outboxMessageRepo.save(outboxesMsg);
    //         }
    //       } catch (err) {
    //         console.log(
    //           `error when push notification finish pre order user gift,`,
    //           err,
    //         );
    //       }
    //     }
    //   }
    // }

    arrNoti = await this.triggerEvents(
      user,
      userUpdated,
      product,
      spoon,
      productData,
      transactionExternalId,
      arrNoti,
      appversionname,
      requestOutboxMessage,
      token,
      outboxMessageGift,
      syncData3rdServiceToWhRequestDetails,
      'SB',
      source,
      eventTrigger,
      eventAddCanTypeTrigger,
      arrTemplate2468Noti,
      this.crmSyncUsingWh,
      historyPoint,
      dto,
    );

    // if ([135323, 135322].includes(userId)) {
    //   throw new AppBaseExc(
    //     StatusCode.SB_DEFAULT_ERROR,
    //     '',
    //     true,
    //     StatusCode.SB_DEFAULT_ERROR,
    //     this.request,
    //   );
    // }

    arrNoti =
      await this.eventCommonPopupUserService.getPopupCodeByPriorityHighest(
        arrNoti,
      );

    return new AppResponseDto(
      {
        addPoint: product.point,
        historyPointId: historyPoint.id,
        totalPoint: user.giftPoint,
      },
      null,
      {
        code: arrNoti,
        notification_title: 'SYSTEM_MESSAGE',
        title: 'SYSTEM_MESSAGE',
      },
    );
  }

  private async redeemStore(user: User, historyPoint: HistoryPoint) {
    const storeInvitation = await this.storeInvitationRepo.findOne({
      where: {
        userId: user.id,
      },
    });

    if (!storeInvitation || storeInvitation.historyPoint) return;

    const store = await this.storeRepo.findOne({
      where: {
        code: storeInvitation.storeCode,
      },
    });

    if (!store)
      throw new AppBaseExc(
        StatusCode.SB_DEFAULT_ERROR,
        `Store has code is ${storeInvitation.storeCode} not exist.`,
      );

    const transactionExternalId = randomTransactionExternalId();

    const request: VgsRedeemStoreRequest = {
      tid: transactionExternalId,
      store_id: store.code,
      store_name: store.name,
      store_phone: store.phoneNumber,
      customer_phone: user.phoneNumber,
      transaction_date: dayjs().tz(TIME_ZONE).format(TIME_FORMAT_VGS),
      rule_name: historyPoint.actionType,
    };

    const newStoreReward = this.storeRewardRepo.create({
      storeCode: store,
      storePhone: store.phoneNumber,
      storeName: store.name,
      customerPhone: user.phoneNumber,
      transactionExternalId,
      transactionDate: dayjs().tz(TIME_ZONE).format(TIME_FORMAT_VGS) + UTC_HCM,
      ruleName: historyPoint.actionType,
    });

    const storeRewardSaved = await this.storeRewardRepo.save(newStoreReward);

    const newOutboxMessage = this.outboxMessageRepo.create({
      provider: SyncProvider.VGS,
      callType: CallType.SYNC,
      syncType: SyncType.IMMEDIATE,
      request: JSON.stringify(request),
      status: OutboxMessageStatus.PROCESSING,
      retryNumber: 0,
    });
    const outboxMessageSaved = await this.outboxMessageRepo.save(
      newOutboxMessage,
    );

    const res = await this.vgsService.redeemMoneyStore(request);

    if (res.resp_code === 0) {
      storeRewardSaved.vgsRefId = res.tid;
      await this.storeRewardRepo.save(storeRewardSaved);
    }

    outboxMessageSaved.response = JSON.stringify(res);
    outboxMessageSaved.status =
      res.resp_code === 0
        ? OutboxMessageStatus.SUCCESS
        : OutboxMessageStatus.FAILED;

    await this.outboxMessageRepo.save(outboxMessageSaved);
  }

  private async checkBlockScan(user: User) {
    if (!user.blockedScan) return;

    const blockedScanHistory = await this.blockedHistoryRepo.findOne({
      where: { userId: user.id },
      order: { actionDate: 'DESC' },
    });

    if (!blockedScanHistory)
      throw new AppBaseExc(
        StatusCode.SB_DEFAULT_ERROR,
        'Block Scan History not found',
        true,
        StatusCode.API_FAILED_UNKNOWN,
        this.request,
      );

    if (
      blockedScanHistory.type ===
      BlockedHistoryType.BLOCKED_ACCOUNT_WHEN_SCAN_FAILED
    ) {
      throw new AppBaseExc(
        StatusCode.SB_ACCBLOCK_ADDPOINT,
        '',
        true,
        StatusCode.BLOCKED_ACCOUNT_WHEN_SCAN_FAILED,
        this.request,
      );
    }

    const curentTime = new Date(dayjs().tz(TIME_ZONE).format(TIME_FORMAT_CRM));

    if (
      !user.blockedScanExpiryDate ||
      dayjs(user.blockedScanExpiryDate).isBefore(curentTime)
    ) {
      return;
    }

    switch (blockedScanHistory.type) {
      case BlockedHistoryType.BLOCKED_SCAN_QR:
        throw new AppBaseExc(
          StatusCode.SB_BLOCK_SCANQR,
          '',
          true,
          StatusCode.SB_BLOCK_SCANQR,
          this.request,
        );

      case BlockedHistoryType.BLOCKED_SCAN_SAME_QR:
        throw new AppBaseExc(
          StatusCode.SB_BLOCK_SAMEQR,
          '',
          true,
          StatusCode.BLOCKED_SCAN_SAME_QR,
          this.request,
        );

      default:
        throw new AppBaseExc(
          StatusCode.SB_DEFAULT_ERROR,
          'Block Scan History Type not correct',
          true,
          StatusCode.API_FAILED_UNKNOWN,
          this.request,
        );
    }
  }

  async handleAddPointError(
    error: any,
    dto: AddPointReqDto,
    userId: number,
    source?: ScanHistoryApiType,
  ) {
    const { qrCode, spoonCode } = dto;

    const user = await this.userRepo
      .createQueryBuilder('user')
      .where('user.id = :userId', { userId })
      .setLock('pessimistic_write')
      .maxExecutionTime(60000) // lock timeout 60s
      .getOne();

    let errorCode: string;
    if (error instanceof AppBaseExc) errorCode = error.error;
    else {
      console.log(`error add point user_id ${userId},`, error);
      errorCode = 'ERROR';
    }

    await this.createScanHistory(
      user,
      dto,
      ScanHistoryStatus.FAILED,
      errorCode,
      source,
    );

    // stop process if account blocked
    if (error instanceof AppBaseExc) {
      if (errorCode == StatusCode.BLOCKED_ACCOUNT.error) {
        return;
      }
    }

    const blockedStatuses = await Promise.all([
      this.checkScanSameQrFailedManyTimes(user, qrCode),
      this.checkScanQrFailedManyTimes(user),
      //this.checkScanManyTimes(user),
    ]);

    const oldNumberOfBlockedScan = user.numberOfBlockedScan;

    blockedStatuses.forEach((item) => {
      switch (item) {
        /*case BlockedHistoryType.BLOCKED_ACCOUNT_WHEN_SCAN_FAILED:
          user.blockedAccountType = item;
          user.blockedAccount = true;
          user.blockedAccountDate = getNowAtTimeZoneHcm();
          break;*/

        case BlockedHistoryType.BLOCKED_SCAN_SAME_QR:
        case BlockedHistoryType.BLOCKED_SCAN_QR:
          if (oldNumberOfBlockedScan != user.numberOfBlockedScan) break;
          user.blockedScan = true;
          user.blockedScanType = item;
          user.blockedScanDate = getNowAtTimeZoneHcm();
          const endTime = dayjs()
            .tz(TIME_ZONE)
            .startOf('day')
            .add(1, 'day')
            .format(TIME_FORMAT_CRM);
          user.blockedScanExpiryDate = new Date(endTime);
          user.numberOfBlockedScan += 1;
          break;
      }
    });
    const blockedStatus = await this.checkScanManyTimes(user);
    if (BlockedHistoryType.BLOCKED_ACCOUNT_WHEN_SCAN_FAILED == blockedStatus) {
      user.blockedAccountType = blockedStatus;
      user.blockedAccount = true;
      user.blockedAccountDate = getNowAtTimeZoneHcm();
    }

    if (sbpsErrorCodes.includes(errorCode)) {
      user.sbpsFailAddPointCounter += 1;
    }

    if (sbErrorCodes.includes(errorCode)) {
      user.sbFailAddPointCounter += 1;
    }

    const startOfNextDay = getStartOfDateInTimezone(
      dayjs().add(1, 'day').toDate(),
    );
    user.dateCompareResetFailedAddPoint = startOfNextDay;

    await this.userRepo.save(user);

    console.log(`handle add point error success, user`, userId);
  }

  private async getAndCheckProduct(productData: ProductDataGotByQrDto) {
    const product = await this.productRepo.findOneBy({
      code: productData.code,
    });
    if (!product || !product.isActive)
      throw new AppBaseExc(
        StatusCode.SB_QR_UNMATCH,
        '',
        true,
        StatusCode.PRODUCT_NOT_EXIST,
        this.request,
      );

    return product;
  }

  @Transactional()
  async handleEventQ2(
    userId: number,
    product: Product,
    code: string,
    productData: ProductDataGotByQrDto,
    transactionRefId: string,
    arrNoti: string[],
    appversionname: string,
    requestOutboxMessage: RequestOutboxMessage,
    token: string,
    type: string,
    outboxMessageGift: OutboxMessage[] = [],
    syncData3rdServiceToWhRequestDetails: SyncData3rdServiceToWhRequestDetail[] = [],
    eventTrigger: EventType[] = [],
    vitaQr: VitaQr = null,
    webapp_name: WebAppList = null,
  ) {
    const identity = `Handle event Q2 for user ${userId}`;
    this.logger.debug(`${identity}: Begin`);

    const numberCheckEvent01 = this.configService.get(
      'event.numberCheckProduct01',
    );

    // NOTE: When add new event ids, concat to notQ2EventIds
    const notQ2EventIds = stringToNumbers(numberCheckEvent01).concat(
      EventClsQ4Service.EVENT_IDS,
    );
    // Exclude event quy 3 2024 CLBB mass sampling
    const eventQuy32024ClbbMassSamplingId =
      this._eventQuy32024ClbbMassSampling.getEventQuy32024ClbbMassSamplingId();
    if (eventQuy32024ClbbMassSamplingId) {
      notQ2EventIds.push(eventQuy32024ClbbMassSamplingId);
    }
    this.logger.debug(
      `${identity}: Event ids not in template Q2 ${convertObjectToString(
        notQ2EventIds,
      )}`,
    );

    const [user, events, productMstGetBySapProductCode] = await Promise.all([
      this.userRepo.findOneBy({
        id: userId,
      }),
      this.eventRepo.findActiveEventsAndEventIdNotIn(notQ2EventIds),
      this.productRepo.findOneBy({
        code: productData.code,
      }),
    ]);
    this.logger.debug(`${identity}: Check events active`);
    if (!events || !events.length) {
      return;
    }

    const eventsIdList = events.map((item) => item.id);
    this.logger.debug(
      `${identity}: Active event ids ${convertObjectToString(eventsIdList)}`,
    );

    const eventProductsInEvents = await this.eventProductRepo.find({
      where: {
        eventId: In(eventsIdList),
        sku: product.code,
      },
      relations: {
        event: true,
      },
    });
    this.logger.debug(
      `${identity}: Event products of events ${convertObjectToString(
        eventProductsInEvents,
      )}`,
    );
    if (!eventProductsInEvents || !eventProductsInEvents.length) {
      return;
    }

    const postCommitActions: Array<() => Promise<unknown>> = [];
    let userReceiveGift = false;
    const promisesIncreaseWinRateNotWinning: any[] = [];
    const promisesPushPopupNotWinning: any[] = [];
    const defaultNotWinning = PopupV2Code.QUY1_KHONG_TRUNG_GIAI;
    for (const eventProduct of eventProductsInEvents) {
      const event = eventProduct.event;
      this.logger.debug(`${identity}: Event ${convertObjectToString(event)}`);

      const [goodLuckPopup, eventProducts] = await Promise.all([
        this.eventPopupRepo.findOne({
          where: {
            eventId: event.id,
            type: EventPopupType.GOOD_LUCK,
          },
        }),
        this.eventProductRepo
          .createQueryBuilder('eventProduct')
          .innerJoinAndSelect('eventProduct.event', 'event')
          .where('event.id = :eventId', { eventId: event.id })
          // .select('eventProduct.sku')
          // .addSelect('eventProduct.eventId')
          .getMany(),
      ]);

      const addingMore = PopupV2Code.QUY1_KEU_GOI_TICH_THEM_SB;
      const notWinning =
        goodLuckPopup?.code || PopupV2Code.QUY1_KHONG_TRUNG_GIAI;
      const hadSetupGoodLuckPopup = goodLuckPopup && goodLuckPopup.code;
      const outOfQuantity = '';
      let numberOf400GrCans = 0;
      let numberOf800GrCans = 0;
      const activeLogic400Gr = event.activeLogic400gr;
      const determineWeightProductSap = productMstGetBySapProductCode
        ? this.determineProductWeight(
            '',
            productMstGetBySapProductCode.prefixWeightCode,
          )
        : null;
      const rsCountNumberOf400GrAnd800GrCans =
        await this.countNumberOf400GrAnd800GrCans(user, eventProducts, type);
      numberOf400GrCans = rsCountNumberOf400GrAnd800GrCans.numberOf400GrCans;
      numberOf800GrCans = rsCountNumberOf400GrAnd800GrCans.numberOf800GrCans;

      if (
        activeLogic400Gr &&
        determineWeightProductSap ===
          EnumProductWeightIdentity.WEIGHT_400_GRAM &&
        numberOf400GrCans % 2 !== 0
      ) {
        await this.pushPopupV2(user.id, addingMore, arrNoti, appversionname);
      } else {
        this.logger.debug(`${identity}: Check blocked`);

        const blockedUser = await this.wheelLuckyBlockUserRepo.findOneBy({
          phoneNumber: user.phoneNumber,
        });
        // check user in blacklist
        if (event.activeBlacklist) {
          if (blockedUser) {
            // await this.pushPopupV2(
            //   user.id,
            //   notWinning,
            //   arrNoti,
            //   appversionname,
            // );
            // return;
            if (hadSetupGoodLuckPopup && !promisesPushPopupNotWinning.length) {
              promisesPushPopupNotWinning.push(
                this.pushPopupV2(user.id, notWinning, arrNoti, appversionname),
              );
            }
            continue;
          }
        }

        // check user_type
        let [userType, eventUser] = await Promise.all([
          this.getUserType(user, event.startDate),
          this.eventUserRepo.findOneBy({
            userId: user.id,
            eventId: event.id,
          }),
        ]);

        let eventLimit = await this.eventLimitRepo.findOneBy({
          eventId: event.id,
          type: userType as UserTypeV2,
        });
        // console.log(`Event limit`);
        // console.log(eventLimit);

        if (!eventUser) {
          const userLimitsGet = await this.eventLimitRepo.findBy({
            eventId: event.id,
            type: In([
              UserTypeV2.CUSTOMER,
              UserTypeV2.STORE,
              UserTypeV2.NEW_USER,
            ]),
          });
          const mapUserLimitsGetByType = objectify(
            userLimitsGet,
            (item) => item.type,
          );

          const userLimitCustomer =
            mapUserLimitsGetByType[UserTypeV2.CUSTOMER] ?? null;
          const userLimitStore =
            mapUserLimitsGetByType[UserTypeV2.STORE] ?? null;
          const userLimitNewUser =
            mapUserLimitsGetByType[UserTypeV2.NEW_USER] ?? null;
          let userLimit = 0;
          switch (userType) {
            case UserTypeV2.CUSTOMER:
              userLimit = userLimitCustomer.userLimit;
              eventLimit = userLimitCustomer;
              break;
            case UserTypeV2.STORE:
              userLimit = userLimitStore.userLimit;
              eventLimit = userLimitStore;
              break;
            case UserTypeV2.NEW_USER:
              userLimit = userLimitNewUser.userLimit;
              eventLimit = userLimitNewUser;
              break;
            default:
              userLimit = 0;
              break;
          }

          const eventUserNew = this.eventUserRepo.create({
            eventId: event.id,
            userId: user.id,
            winRate: eventLimit.winRate,
            userLimit: 1,
          });

          const savedEventUser = await this.eventUserRepo.save(eventUserNew);
          eventUser = eventUserNew;

          const eventUserLimit = this.eventUserLimitRepo.create({
            eventUserId: savedEventUser.id,
            type: userType,
            userLimit,
          });
          await this.eventUserLimitRepo.save(eventUserLimit);
        }

        this.logger.debug(`${identity}: Check user event win rate`);
        // check winRate
        if (eventUser.winRate !== 100) {
          // await Promise.all([
          //   this.increaseWinrate(eventUser, eventLimit.upRate),
          //   this.pushPopupV2(user.id, notWinning, arrNoti, appversionname),
          // ]);
          // return;
          promisesIncreaseWinRateNotWinning.push(
            this.increaseWinrate(eventUser, eventLimit.upRate),
          );
          if (hadSetupGoodLuckPopup && !promisesPushPopupNotWinning.length) {
            promisesPushPopupNotWinning.push(
              this.pushPopupV2(user.id, notWinning, arrNoti, appversionname),
            );
          }
          continue;
        }

        const eventUserLimit = await this.eventUserLimitRepo.findOneBy({
          eventUserId: eventUser.id,
          type: userType,
        });

        this.logger.debug(`${identity}: Check user event limit`);
        // check if user reaches limit
        if (!eventUserLimit || eventUserLimit.userLimit === 0) {
          // await this.increaseWinrate(eventUser, eventLimit.upRate);
          // await this.pushPopupV2(user.id, notWinning, arrNoti, appversionname);
          // return;
          promisesIncreaseWinRateNotWinning.push(
            this.increaseWinrate(eventUser, eventLimit.upRate),
          );
          if (hadSetupGoodLuckPopup && !promisesPushPopupNotWinning.length) {
            promisesPushPopupNotWinning.push(
              this.pushPopupV2(user.id, notWinning, arrNoti, appversionname),
            );
          }
          continue;
        }

        // check if user received exclusive gift on child events
        const eventPointHistoriesOfUser =
          await this.eventPointHistoryRepo.findBy({ userId: user.id });
        const eventDetailIdsUserReceived = eventPointHistoriesOfUser.map(
          (eventPointHistory) => eventPointHistory.eventDetailId,
        );

        let eventGiftBy4StepCountUserAddedPointEntity: EventGiftBy4StepCountUserAddedPointEntity =
          null;
        let eventOrdinals: EventDetail[] = [];
        if (
          true ==
          this._eventGiftBy4StepService.checkEventIsGiftBy4StepTemplate(event)
        ) {
          eventGiftBy4StepCountUserAddedPointEntity =
            await this._eventGiftBy4StepService.getCountUserAddedPointOfEvent(
              event,
              user,
            );
          const rsGets = await Promise.all([
            this._eventGiftBy4StepService.getEventDetailsBaseOnSetupGiftBy4Step(
              event,
              user,
              eventGiftBy4StepCountUserAddedPointEntity,
            ),
            this._eventGiftBy4StepService.updateCountingUserAddedPoint(
              event,
              user,
              this.request,
              eventGiftBy4StepCountUserAddedPointEntity,
            ),
          ]);
          eventOrdinals = rsGets[0];
        } else {
          eventOrdinals = await this.eventDetailRepo
            .createQueryBuilder('eventDetail')
            .where('eventDetail.eventId = :eventId', { eventId: event.id })
            //.andWhere('eventDetail.quantity > 0')
            .andWhere('eventDetail.quantity >= 0')
            .andWhere('eventDetail.ordinal > 0')
            .orderBy('eventDetail.ordinal', 'ASC')
            .leftJoinAndSelect(
              'eventDetail.eventDetailPopup',
              'eventDetailPopup',
            )
            .maxExecutionTime(60000) // timeout 60s
            .getMany();
        }

        const listEventDetailIdsOfCurrentEvent = eventOrdinals.map(
          (ordinal) => ordinal.id,
        );

        const eventDetailIdsUserReceivedOfCurrentEvent =
          eventDetailIdsUserReceived.filter((ids) =>
            listEventDetailIdsOfCurrentEvent.includes(ids),
          );

        const exclusiveEventDetail =
          await this.eventDetailExclusionRepo.findOneBy({
            userType,
            eventDetailId: In(eventDetailIdsUserReceivedOfCurrentEvent),
          });

        this.logger.debug(`${identity}: Check exclusive event detail`);
        if (exclusiveEventDetail) {
          // await this.increaseWinrate(eventUser, eventLimit.upRate);
          // await this.pushPopupV2(user.id, notWinning, arrNoti, appversionname);
          // return;
          promisesIncreaseWinRateNotWinning.push(
            this.increaseWinrate(eventUser, eventLimit.upRate),
          );
          if (hadSetupGoodLuckPopup && !promisesPushPopupNotWinning.length) {
            promisesPushPopupNotWinning.push(
              this.pushPopupV2(user.id, notWinning, arrNoti, appversionname),
            );
          }
          continue;
        }

        this.logger.debug(`${identity}: Loop event detail`);
        const now = getNowAtTimeZoneHcm();
        let giftOutOfQuantity = false;

        for (const eventOrdinal of eventOrdinals) {
          let eventDetailSku: EventDetailSku = null;
          let eventDetailToUserType: EventDetailToUserType = null;
          let eventDetailProvince: EventDetailProvince = null;
          let eventDetailSupplierV2: EventDetailSupplierV2 = null;
          let eventDetailToStore: EventDetailToStore = null;
          const [
            isEventProductChildSetup,
            eventDetailToUserTypes,
            eventDetailSupplierV2s,
            selfEventDetailExclusion,
            eventDetailExclusion,
            eventDetailProvinces,
            eventDetailToStores,
          ] = await Promise.all([
            this.eventProductChildRepo.findOneBy({
              eventDetailId: eventOrdinal.id,
            }),
            this.eventDetailToUserTypeRepo.findBy({
              eventDetailId: eventOrdinal.id,
              //endDate: MoreThanOrEqual(current),
              //startDate: LessThanOrEqual(current),
            }),
            this.eventDetailSupplierV2Repo.findBy({
              eventDetailId: eventOrdinal.id,
              //endDate: MoreThanOrEqual(current),
              //startDate: LessThanOrEqual(current),
            }),
            this.selfEventDetailExclusionRepo.findOneBy({
              userType,
              eventDetailId: eventOrdinal.id,
            }),
            this.eventDetailExclusionRepo.findOneBy({
              eventDetailId: eventOrdinal.id,
              userType,
            }),
            this.eventDetailProvinceRepo.findBy({
              eventDetailId: eventOrdinal.id,
              //provinceId: user.provinceId,
              //endDate: MoreThanOrEqual(current),
              //startDate: LessThanOrEqual(current),
            }),
            this.eventDetailToStoreRepo.findBy({
              eventDetailId: eventOrdinal.id,
              //storeCode: productData.storeId,
            }),
          ]);

          // Check event detail black list
          // Kiểm tra giải có active blacklist không
          if (eventOrdinal.activeBlacklist) {
            // Kiểm tra user có thuộc blacklist của event không
            this.logger.debug(`${identity}: Check block user event detail`);
            if (blockedUser) {
              continue;
            }
          }

          // Check admin setup child sku
          // Kiểm tra admin có setup SKU con (field Mã sản phẩm) không
          if (isEventProductChildSetup) {
            const eventProductChild =
              await this.eventProductChildRepo.findOneBy({
                sku: product.code,
                eventDetailId: eventOrdinal.id,
              });
            // Kiểm tra SKU user quét có thuộc SKU con đã setup
            if (!eventProductChild) {
              // Không thuộc
              continue;
            }
          } else {
            // Không setup
            // check event detail sku
            // only check if not setup event product child
            // KIểm tra SKU user quét có thuộc SKU con được setup không
            this.logger.debug(`${identity}: Check event detail follow sku`);
            const eventProductSkuOfProduct =
              await this.eventDetailSkuRepo.findOneBy({
                eventDetailId: eventOrdinal.id,
                sku: product.code,
              });
            this.logger.debug(
              `${identity}: Check had setup event detail follow sku of product`,
            );
            // Không thuộc SKU setup
            if (!eventProductSkuOfProduct) {
              continue;
            }

            this.logger.debug(
              `${identity}: Check quantity event detail follow sku of product`,
            );
            if (
              !eventProductSkuOfProduct.totalQuantity ||
              eventProductSkuOfProduct.totalQuantity -
                eventProductSkuOfProduct.usedQuantity <=
                0
            ) {
              giftOutOfQuantity = true;
              continue;
            }

            eventDetailSku = eventProductSkuOfProduct;
          }

          // check if gift belongs to self exclusive gifts
          // Kiểm tra giải có thuộc giải loại trừ theo tệp user không
          this.logger.debug(`${identity}: Check self exclusion event detail`);
          if (selfEventDetailExclusion) {
            // Thuộc giải loại trừ theo tệp user
            continue;
          }

          this.logger.debug(`${identity}: Check event detail probability`);
          // check probability
          // Kiểm tra probability của giải
          if (eventOrdinal.probability !== 100) {
            // Probability  không đạt 100%
            giftOutOfQuantity = true;
            continue;
          }

          // check if gift belongs to exclusive gifts of parent event
          // Kiểm tra giải có thuộc danh sách loại trừ của event lớn không
          let eventGroupGiftExclusion: EventGroupGiftExclusion = null;
          if (eventDetailExclusion) {
            eventGroupGiftExclusion =
              await this.eventGroupGiftExclusionRepo.findOneBy({
                eventGroupId: event.eventGroupId,
                eventDetailExclusionId: eventDetailExclusion.id,
              });
          } else {
            eventGroupGiftExclusion =
              await this.eventGroupGiftExclusionRepo.findOneBy({
                eventGroupId: event.eventGroupId,
                eventDetailId: eventOrdinal.id,
                userType,
              });
          }
          // Thuộc danh sách loại trừ của event lớn
          if (eventGroupGiftExclusion) {
            // if belongs, check if user received exclusive gift from parent event
            // Kiểm tra user đã trúng quà thuộc giải loại trừ của event lớn chưa
            const eventDetailExclusionReceived =
              await this.eventDetailExclusionRepo.findBy({
                userType,
                eventDetailId: In(eventDetailIdsUserReceived),
              });
            const eventDetailExclusionIdsReceived =
              eventDetailExclusionReceived.map((ede) => ede.id);
            const exclusiveEventGroupFollowEventDetailExclusion =
              await this.eventGroupGiftExclusionRepo.findOneBy({
                eventGroupId: event.eventGroupId,
                eventDetailExclusionId: In(eventDetailExclusionIdsReceived),
              });
            const exclusiveEventGroupFollowEventDetail =
              await this.eventGroupGiftExclusionRepo.findOneBy({
                eventGroupId: event.eventGroupId,
                userType,
                eventDetailId: In(eventDetailIdsUserReceived),
              });

            this.logger.debug(`${identity}: Check exclusive event group`);
            if (
              exclusiveEventGroupFollowEventDetailExclusion ||
              exclusiveEventGroupFollowEventDetail
            ) {
              // Đã trúng quà thuộc giải loại trừ của event lớn
              continue;
            }
          }

          this.logger.debug(`${identity}: Check event detail quantity`);
          // check quantity of ordinal
          // Kiểm tra số lượng tổng giải còn lại
          if (eventOrdinal.quantity === 0) {
            giftOutOfQuantity = true;
            continue;
          }

          this.logger.debug(
            `${identity}: Check start date end date event detail not null`,
          );
          // Kiểm tra có setup start date, end date của event nhỏ không
          if (eventOrdinal.startDate && eventOrdinal.endDate) {
            // Kiểm tra thời gian hiện tại có thuộc khoảng thời gian nổ giải của event nhỏ không
            const rsCompareEventDetailStartDateWithNow =
              compareDateWithCurrentDateInTimezone(eventOrdinal.startDate);
            const rsCompareEventDetailEndDateWithNow =
              compareDateWithCurrentDateInTimezone(eventOrdinal.endDate);

            // Không thuộc thời gian nổ giải
            if (
              -1 != rsCompareEventDetailStartDateWithNow ||
              1 != rsCompareEventDetailEndDateWithNow
            ) {
              continue;
            }
          }

          this.logger.debug(
            `${identity}: Check setup event detail active check supplier null`,
          );
          // check qr of supplier
          // Admin có setup điều kiện về suplier null không
          if (type == 'SB' && !productData.distributorCode) {
            if (true == eventOrdinal.activeCheckSupplierNull) {
              // Có setup
              continue;
            }
          }

          // check if event ignore store
          // Kiểm tra thông tin supplier của QR có trong danh sách loại trừ không
          this.logger.debug(`${identity}: Check event detail ignore supplier`);
          const checkSetupIgnoreSupplier =
            await this.eventDetailIgnoreStoreRepo.findOneBy({
              eventDetailId: eventOrdinal.id,
              storeCode: productData.distributorCode,
            });
          if (checkSetupIgnoreSupplier) {
            // Thuộc danh sách
            continue;
          }

          this.logger.debug(
            `${identity}: Check setup event detail winning supplier`,
          );
          // check if setup winning supplier
          // Admin có setup điều kiện về supplier trúng giải (field winning supplier) không
          const checkSetupWinningSupplier =
            await this.eventDetailSupplierRepo.findBy({
              eventDetailId: eventOrdinal.id,
            });
          // Có setup
          if (checkSetupWinningSupplier.length !== 0) {
            // Kiểm tra thông tin supplier của QR có trong danh sách setup không
            const foundWinningSupplier = checkSetupWinningSupplier.some(
              (item) => item.supplierCode === productData.distributorCode,
            );

            // Không thuộc
            if (!foundWinningSupplier) {
              continue;
            }
          } else {
            // Check event detail supplier v2
            // Không setup, User setup số lượng theo từng NPP
            this.logger.debug(
              `${identity}: Check setup event detail supplier v2`,
            );
            if (eventDetailSupplierV2s && eventDetailSupplierV2s.length) {
              const eventDetailSupplierV2sMatch = eventDetailSupplierV2s.filter(
                (item) => {
                  return item.supplierCode === productData.distributorCode;
                },
              );
              // KIểm tra supplier của QR user quét có thuộc NPP con được setup không
              if (
                !eventDetailSupplierV2sMatch ||
                !eventDetailSupplierV2sMatch.length
              ) {
                // Không thuộc danh sách
                continue;
              }
              eventDetailSupplierV2 = eventDetailSupplierV2sMatch[0];
              this.logger.debug(
                `${identity}: Check time range event detail supplier v2`,
              );
              const rsCompareEventDetailSupplierV2StartDateWithNow =
                compareDateWithCurrentDateInTimezone(
                  eventDetailSupplierV2.startDate,
                );
              const rsCompareEventDetailSupplierV2EndDateWithNow =
                compareDateWithCurrentDateInTimezone(
                  eventDetailSupplierV2.endDate,
                );
              // Kiểm tra thời gian nổ giải theo Supplier
              if (
                1 == rsCompareEventDetailSupplierV2StartDateWithNow ||
                -1 == rsCompareEventDetailSupplierV2EndDateWithNow
              ) {
                // Không thuộc thời gian nổ giải
                continue;
              }

              this.logger.debug(
                `${identity}: Check quantity event detail supplier v2`,
              );
              // Kiểm tra số lượng giải còn lại theo supplier
              if (
                !eventDetailSupplierV2.totalQuantity ||
                eventDetailSupplierV2.totalQuantity -
                  eventDetailSupplierV2.usedQuantity <=
                  0
              ) {
                // Số lượng giải theo supplier hết
                continue;
              }

              // Kiểm tra phân bổ nổ giải đồng đều theo supplier
              this.logger.debug(
                `${identity}: Check limit per day event detail supplier v2`,
              );
              const remainQuantity =
                eventDetailSupplierV2.totalQuantity -
                eventDetailSupplierV2.usedQuantity;
              if (
                eventOrdinal.activePrizeEvenlySupplierV2 &&
                !this.canRedeemGiftToday(
                  eventDetailSupplierV2.evenlyHistory,
                  remainQuantity,
                )
              ) {
                giftOutOfQuantity = true;
                continue;
              }
            }
          }

          // Check event detail to user type
          // Kiểm tra có setup theo tệp user không
          this.logger.debug(
            `${identity}: Check setup event detail follow user type ${userType}`,
          );
          if (eventDetailToUserTypes && eventDetailToUserTypes.length) {
            // Có setup tệp user
            const eventDetailToUserTypesMatch = eventDetailToUserTypes.filter(
              (item) => {
                return item.userType === userType;
              },
            );
            // Kiểm tra user có thuộc têp user được setup không
            if (
              !eventDetailToUserTypesMatch ||
              !eventDetailToUserTypesMatch.length
            ) {
              // Không thuộc tệp setup
              continue;
            }
            eventDetailToUserType = eventDetailToUserTypesMatch[0];

            this.logger.debug(
              `${identity}: Check time range event detail follow user type ${userType}`,
            );
            const rsCompareEventDetailToUserTypeStartDateWithNow =
              compareDateWithCurrentDateInTimezone(
                eventDetailToUserType.startDate,
              );
            const rsCompareEventDetailToUserTypeEndDateWithNow =
              compareDateWithCurrentDateInTimezone(
                eventDetailToUserType.endDate,
              );
            // Kiểm tra thời gian nổ giải theo người dùng
            if (
              1 == rsCompareEventDetailToUserTypeStartDateWithNow ||
              -1 == rsCompareEventDetailToUserTypeEndDateWithNow
            ) {
              // Không thuộc thời gian nổ giải
              continue;
            }

            this.logger.debug(
              `${identity}: Check quantity event detail follow user type ${userType}`,
            );
            // Kiểm tra số lượng giải còn lại theo tệp người dùng còn không
            if (
              !eventDetailToUserType.totalQuantity ||
              eventDetailToUserType.totalQuantity -
                eventDetailToUserType.usedQuantity <=
                0
            ) {
              // Số lượng giải theo tệp hết
              continue;
            }

            // Kiểm tra phân bổ nổ giải đồng đều theo supplier
            this.logger.debug(
              `${identity}: Check limit per day event detail follow user type ${userType}`,
            );
            const remainQuantity =
              eventDetailToUserType.totalQuantity -
              eventDetailToUserType.usedQuantity;
            if (
              eventOrdinal.activePrizeEvenlyUserType &&
              !this.canRedeemGiftToday(
                eventDetailToUserType.evenlyHistory,
                remainQuantity,
              )
            ) {
              giftOutOfQuantity = true;
              continue;
            }
          }

          // check if qr of store is valid
          // Kiểm tra giải có setup store không
          this.logger.debug(`${identity}: Check setup event detail to store`);
          if (eventDetailToStores && eventDetailToStores.length) {
            // Có setup tỉnh thành
            // Kiểm tra thông tin store của QR có trong danh sách nổ giải theo cửa hàng không
            const eventDetailToStoreMatchStoreCode = eventDetailToStores.filter(
              (item) => {
                return item.storeCode == productData.storeId;
              },
            );

            this.logger.debug(
              `${identity}: Check event detail to store match store code`,
            );
            if (
              !eventDetailToStoreMatchStoreCode ||
              !eventDetailToStoreMatchStoreCode.length
            ) {
              // Không setup store của QR
              giftOutOfQuantity = true;
              continue;
            }
            eventDetailToStore = eventDetailToStoreMatchStoreCode[0];

            this.logger.debug(
              `${identity}: Check event detail to store quantity`,
            );
            // Kiểm tra số lượng giải còn lại theo cửa hàng
            if (eventDetailToStore.quantity <= 0) {
              // Số lượng hết
              giftOutOfQuantity = true;
              continue;
            }

            this.logger.debug(
              `${identity}: Check start date end date event detail to store not null`,
            );
            if (!eventDetailToStore.startDate || !eventDetailToStore.endDate) {
              giftOutOfQuantity = true;
              continue;
            }

            // Kiểm tra phân bổ nổ giải đồng đều theo cửa hàng
            this.logger.debug(
              `${identity}: Check event detail to store limit quantity per day`,
            );
            if (
              eventOrdinal.activePrizeEvenlyStore &&
              !this.canRedeemGiftToday(
                eventDetailToStore.evenlyHistory,
                eventDetailToStore.quantity,
              )
            ) {
              giftOutOfQuantity = true;
              continue;
            }

            this.logger.debug(
              `${identity}: Check start date end date event detail to store valid`,
            );
            const rsCompareEventDetailToStoreStartDateWithNow =
              compareDateWithCurrentDateInTimezone(
                eventDetailToStore.startDate,
              );
            const rsCompareEventDetailToStoreEndDateWithNow =
              compareDateWithCurrentDateInTimezone(eventDetailToStore.endDate);
            // Kiểm tra thời gian nổ giải theo cửa hàng
            if (
              -1 != rsCompareEventDetailToStoreStartDateWithNow ||
              1 != rsCompareEventDetailToStoreEndDateWithNow
            ) {
              // Không thuộc thời gian nổ giải
              giftOutOfQuantity = true;
              continue;
            }
          }

          // province setup
          // Kiểm tra giải có setup tỉnh thành không
          this.logger.debug(`${identity}: Check setup event detail province`);
          if (eventDetailProvinces && eventDetailProvinces.length) {
            // Có setup tỉnh thành
            // KIểm tra user có thuộc tỉnh thành được setup không
            const eventDetailProvinceMatchUserProvince =
              eventDetailProvinces.filter((item) => {
                return item.provinceId == user.provinceId;
              });

            this.logger.debug(
              `${identity}: Check event detail province match user province`,
            );
            if (
              !eventDetailProvinceMatchUserProvince ||
              !eventDetailProvinceMatchUserProvince.length
            ) {
              // Không thuộc tỉnh thành setup
              giftOutOfQuantity = true;
              continue;
            }
            eventDetailProvince = eventDetailProvinceMatchUserProvince[0];

            this.logger.debug(
              `${identity}: Check event detail province quantity`,
            );
            // Kiểm tra số lượng giải còn lại theo tỉnh
            if (eventDetailProvince.quantity === 0) {
              // next ordinal
              // Số lượng giải theo tỉnh hết
              giftOutOfQuantity = true;
              continue;
            }

            this.logger.debug(
              `${identity}: Check start date end date event detail province not null`,
            );
            if (
              !eventDetailProvince.startDate ||
              !eventDetailProvince.endDate
            ) {
              giftOutOfQuantity = true;
              continue;
            }

            this.logger.debug(
              `${identity}: Check start date end date event detail province valid`,
            );
            // Kiểm tra phân bổ nổ giải đồng đều theo tỉnh
            this.logger.debug(
              `${identity}: Check event detail to province limit quantity per day`,
            );
            if (
              eventOrdinal.activePrizeEvenlyProvince &&
              !this.canRedeemGiftToday(
                eventDetailProvince.evenlyHistory,
                eventDetailProvince.quantity,
              )
            ) {
              giftOutOfQuantity = true;
              continue;
            }
            const rsCompareEventDetailProvinceStartDateWithNow =
              compareDateWithCurrentDateInTimezone(
                eventDetailProvince.startDate,
              );
            const rsCompareEventDetailProvinceEndDateWithNow =
              compareDateWithCurrentDateInTimezone(eventDetailProvince.endDate);
            // Kiểm tra thời gian nổ giải theo tỉnh
            if (
              -1 != rsCompareEventDetailProvinceStartDateWithNow ||
              1 != rsCompareEventDetailProvinceEndDateWithNow
            ) {
              // Không thuộc thời gian nổ giải
              giftOutOfQuantity = true;
              continue;
            }
          }

          // Validate setup gifting gift time of event detail
          const rsValidate: IResultValidateMatchSetupEventTimeGiftingGift =
            await this.validateMatchSetupEventTimeGiftingGift(
              user,
              event,
              eventOrdinal,
            );
          if (!rsValidate.result) {
            giftOutOfQuantity = true;
            continue;
          }

          // BOOM
          userReceiveGift = true;
          const popupCode = this.getPopupCode(eventOrdinal);

          this.logger.debug(`${identity}: Check popup code exists`);
          if (!popupCode) {
            throw new AppBaseExc(
              StatusCode.SB_DEFAULT_ERROR,
              null,
              null,
              StatusCode.API_FAILED_UNKNOWN,
              this.request,
            );
          }
          await this.redeemGift(
            eventUser,
            eventLimit,
            eventOrdinal,
            product,
            user,
            now,
            eventUserLimit,
            transactionRefId,
            code,
            type,
            requestOutboxMessage,
            outboxMessageGift,
            syncData3rdServiceToWhRequestDetails,
            arrNoti,
            token,
            eventDetailProvince,
            eventDetailToStore,
            eventDetailSku,
            eventDetailToUserType,
            eventDetailSupplierV2,
            postCommitActions,
            rsValidate,
          );

          this.logger.debug(`${identity}: Push popup`);
          await Promise.all([
            this.pushPopupV2(user.id, popupCode, arrNoti, appversionname),
            this.pushEventQ1Notification(user.id, eventOrdinal),
          ]);
          break;
        }
      }
    }

    if (!userReceiveGift) {
      if (!promisesPushPopupNotWinning.length) {
        promisesPushPopupNotWinning.push(
          this.pushPopupV2(user.id, defaultNotWinning, arrNoti, appversionname),
        );
      }
      await Promise.all([
        ...promisesIncreaseWinRateNotWinning,
        ...promisesPushPopupNotWinning,
      ]);
    } else {
      // Calling function after transaction add point finished
      if (postCommitActions.length) {
        runOnTransactionCommit(() => {
          asyncMapSettled(postCommitActions, (action) => action());
        });
      }
    }
  }

  @Transactional()
  async handleEventQ32024ClbbMassSampling(
    userId: number,
    product: Product,
    spoonCode: string,
    productData: ProductDataGotByQrDto,
    transactionRefId: string,
    arrNoti: string[],
    appversionname: string,
    requestOutboxMessage: RequestOutboxMessage,
    token: string,
    outboxMessageGift: OutboxMessage[],
    syncData3rdServiceToWhRequestDetails: SyncData3rdServiceToWhRequestDetail[] = [],
    type?: string,
  ) {
    console.log(`Handle event Q32024ClbbMassSampling for user`, userId);
    const eventQuy32024ClbbMassSamplingId =
      this._eventQuy32024ClbbMassSampling.getEventQuy32024ClbbMassSamplingId();
    if (!eventQuy32024ClbbMassSamplingId) {
      return;
    }
    const [user, event] = await Promise.all([
      this.userRepo.findOneBy({
        id: userId,
      }),
      this.eventRepo.findActiveEvent(eventQuy32024ClbbMassSamplingId),
    ]);
    console.log('Check active event for user', user.id);
    if (!user || !event) {
      return;
    }

    // find matching event
    const eventProduct = await this.eventProductRepo.findOne({
      where: {
        eventId: In([event.id]),
        sku: product.code,
      },
      relations: {
        event: true,
      },
    });
    console.log(
      'Check event product of active event and sku product for user',
      user.id,
    );
    if (!eventProduct) return;

    console.log('Check user scanned sku belong to list exclude skus', user.id);
    if (
      await this._eventQuy32024ClbbMassSampling.checkUserScanSkuBelongToListExcludeSkusEvent(
        user,
      )
    ) {
      return;
    }

    console.log(`Event id: ${event.id} for user`, user.id);

    // check user in blacklist
    const blockedUser = await this.wheelLuckyBlockUserRepo.findOneBy({
      phoneNumber: user.phoneNumber,
    });
    if (event.activeBlacklist) {
      console.log('Check blocked for user', user.id);
      if (blockedUser) {
        return;
      }
    }

    // check user_type
    const userType: string = await this.getUserType(user, event.startDate);

    // check if already add point, create or update event_user
    let eventUser = await this.eventUserRepo.findOneBy({
      userId: user.id,
      eventId: event.id,
    });
    // console.log(`Event user`);
    // console.log(eventUser);

    let eventLimit = await this.eventLimitRepo.findOneBy({
      eventId: event.id,
      type: userType as UserTypeV2,
    });
    // console.log(`Event limit`);
    // console.log(eventLimit);

    if (!eventUser) {
      const userLimitCustomer = await this.eventLimitRepo.findOneBy({
        eventId: event.id,
        type: UserTypeV2.CUSTOMER,
      });
      const userLimitStore = await this.eventLimitRepo.findOneBy({
        eventId: event.id,
        type: UserTypeV2.STORE,
      });
      const userLimitNewUser = await this.eventLimitRepo.findOneBy({
        eventId: event.id,
        type: UserTypeV2.NEW_USER,
      });
      let userLimit = 0;
      switch (userType) {
        case UserTypeV2.CUSTOMER:
          userLimit = userLimitCustomer.userLimit;
          eventLimit = userLimitCustomer;
          break;
        case UserTypeV2.STORE:
          userLimit = userLimitStore.userLimit;
          eventLimit = userLimitStore;
          break;
        case UserTypeV2.NEW_USER:
          userLimit = userLimitNewUser.userLimit;
          eventLimit = userLimitNewUser;
          break;
        default:
          userLimit = 0;
          break;
      }

      const eventUserNew = this.eventUserRepo.create({
        eventId: event.id,
        userId: user.id,
        winRate: eventLimit.winRate,
        userLimit: 1,
      });

      const savedEventUser = await this.eventUserRepo.save(eventUserNew);
      eventUser = eventUserNew;

      const eventUserLimit = this.eventUserLimitRepo.create({
        eventUserId: savedEventUser.id,
        type: userType,
        userLimit,
      });
      await this.eventUserLimitRepo.save(eventUserLimit);
    }
    console.log('Check user event win rate for user', user.id);

    // check winRate
    if (eventUser.winRate !== 100) {
      await this.increaseWinrate(eventUser, eventLimit.upRate);
      return;
    }

    const eventUserLimit = await this.eventUserLimitRepo.findOneBy({
      eventUserId: eventUser.id,
      type: userType,
    });
    console.log('Check user event limit for user', user.id);

    // check if user reaches limit
    if (!eventUserLimit || eventUserLimit.userLimit === 0) {
      await this.increaseWinrate(eventUser, eventLimit.upRate);
      return;
    }

    // check if user received exclusive gift on child events
    const eventPointHistoriesOfUser = await this.eventPointHistoryRepo.findBy({
      userId: user.id,
    });
    const eventDetailIdsUserReceived = eventPointHistoriesOfUser.map(
      (eventPointHistory) => eventPointHistory.eventDetailId,
    );

    // get possible ordinals
    const eventOrdinals = await this.eventDetailRepo
      .createQueryBuilder('eventDetail')
      .where('eventDetail.eventId = :eventId', { eventId: event.id })
      //.andWhere('eventDetail.quantity > 0')
      .andWhere('eventDetail.quantity >= 0')
      .andWhere('eventDetail.ordinal > 0')
      .orderBy('eventDetail.ordinal', 'ASC')
      .maxExecutionTime(60000) // timeout 60s
      .getMany();

    const listEventDetailIdsOfCurrentEvent = eventOrdinals.map(
      (ordinal) => ordinal.id,
    );

    const eventDetailIdsUserReceivedOfCurrentEvent =
      eventDetailIdsUserReceived.filter((ids) =>
        listEventDetailIdsOfCurrentEvent.includes(ids),
      );

    const exclusiveEventDetail = await this.eventDetailExclusionRepo.findOneBy({
      userType,
      eventDetailId: In(eventDetailIdsUserReceivedOfCurrentEvent),
    });

    console.log('Check exclusive event detail for user', user.id);

    if (exclusiveEventDetail) {
      await this.increaseWinrate(eventUser, eventLimit.upRate);
      return;
    }

    let userReceiveGift = false;
    console.log('Loop event detail for user', user.id);

    const now = getNowAtTimeZoneHcm();

    console.log('date history point sb', now);

    let giftOutOfQuantity = false;
    const current = getNowAtTimeHcm();
    /*const blockedEventDetailUser =
      await this.eventDetailBlockUserRepo.findOneBy({
        phoneNumber: user.phoneNumber,
        //eventDetailId: eventOrdinal.id,
      });*/
    for (const eventOrdinal of eventOrdinals) {
      let eventDetailSku: EventDetailSku = null;
      let eventDetailToUserType: EventDetailToUserType = null;
      let eventDetailSupplierV2: EventDetailSupplierV2 = null;
      const [
        isEventProductChildSetup,
        eventDetailToUserTypes,
        eventDetailSupplierV2s,
        selfEventDetailExclusion,
        eventDetailExclusion,
      ] = await Promise.all([
        this.eventProductChildRepo.findBy({
          eventDetailId: eventOrdinal.id,
        }),
        this.eventDetailToUserTypeRepo.findBy({
          eventDetailId: eventOrdinal.id,
          //endDate: MoreThanOrEqual(current),
          //startDate: LessThanOrEqual(current),
        }),
        this.eventDetailSupplierV2Repo.findBy({
          eventDetailId: eventOrdinal.id,
          //endDate: MoreThanOrEqual(current),
          //startDate: LessThanOrEqual(current),
        }),
        this.selfEventDetailExclusionRepo.findOneBy({
          userType,
          eventDetailId: eventOrdinal.id,
        }),
        this.eventDetailExclusionRepo.findOneBy({
          eventDetailId: eventOrdinal.id,
          userType,
        }),
      ]);

      if (isEventProductChildSetup.length) {
        const eventProductChild = await this.eventProductChildRepo.findBy({
          sku: product.code,
          eventDetailId: eventOrdinal.id,
        });
        if (!eventProductChild.length) {
          continue;
        }
      }

      // Check event detail black list
      if (eventOrdinal.activeBlacklist) {
        /*const blockedUser = await this.eventDetailBlockUserRepo.findOneBy({
          phoneNumber: user.phoneNumber,
          eventDetailId: eventOrdinal.id,
        });*/
        console.log('Check block user event detail for user', user.id);
        if (blockedUser) {
          continue;
        }
      }

      // check if gift belongs to self exclusive gifts
      /*const selfEventDetailExclusion =
        await this.selfEventDetailExclusionRepo.findOneBy({
          userType,
          eventDetailId: eventOrdinal.id,
        });*/
      console.log('Check self exclusion event detail for user', user.id);
      if (selfEventDetailExclusion) {
        continue;
      }
      console.log('Check event detail probability for user', user.id);
      // check probability
      if (eventOrdinal.probability !== 100) {
        giftOutOfQuantity = true;
        continue;
      }

      // check if gift belongs to exclusive gifts of parent event
      let eventGroupGiftExclusion: EventGroupGiftExclusion = null;
      /*const eventDetailExclusion =
        await this.eventDetailExclusionRepo.findOneBy({
          eventDetailId: eventOrdinal.id,
          userType,
        });*/
      if (eventDetailExclusion) {
        eventGroupGiftExclusion =
          await this.eventGroupGiftExclusionRepo.findOneBy({
            eventGroupId: event.eventGroupId,
            eventDetailExclusionId: eventDetailExclusion.id,
          });
      } else {
        eventGroupGiftExclusion =
          await this.eventGroupGiftExclusionRepo.findOneBy({
            eventGroupId: event.eventGroupId,
            eventDetailId: eventOrdinal.id,
            userType,
          });
      }
      if (eventGroupGiftExclusion) {
        // if belongs, check if user received exclusive gift from parent event
        const eventDetailExclusionReceived =
          await this.eventDetailExclusionRepo.findBy({
            userType,
            eventDetailId: In(eventDetailIdsUserReceived),
          });
        const eventDetailExclusionIdsReceived =
          eventDetailExclusionReceived.map((ede) => ede.id);
        const exclusiveEventGroupFollowEventDetailExclusion =
          await this.eventGroupGiftExclusionRepo.findOneBy({
            eventGroupId: event.eventGroupId,
            eventDetailExclusionId: In(eventDetailExclusionIdsReceived),
          });
        const exclusiveEventGroupFollowEventDetail =
          await this.eventGroupGiftExclusionRepo.findOneBy({
            eventGroupId: event.eventGroupId,
            userType,
            eventDetailId: In(eventDetailIdsUserReceived),
          });
        console.log('Check exclusive event group for user', user.id);
        if (
          exclusiveEventGroupFollowEventDetailExclusion ||
          exclusiveEventGroupFollowEventDetail
        ) {
          continue;
        }
      }
      console.log('Check event detail quantity for user', user.id);
      // check quantity of ordinal
      if (eventOrdinal.quantity === 0) {
        giftOutOfQuantity = true;
        continue;
      }

      // check event detail sku
      // only check if not setup event product child
      console.log('Check event detail follow sku for user', user.id);
      if (!isEventProductChildSetup || !isEventProductChildSetup.length) {
        const [eventProductSkuOfProduct, eventProductSkuNotOfProduct] =
          await Promise.all([
            this.eventDetailSkuRepo.findOneBy({
              eventDetailId: eventOrdinal.id,
              sku: product.code,
            }),
            this.eventDetailSkuRepo.findOneBy({
              eventDetailId: eventOrdinal.id,
              sku: Not(product.code),
            }),
          ]);
        console.log(
          'Check had setup event detail follow sku for user',
          user.id,
        );
        if (eventProductSkuOfProduct || eventProductSkuNotOfProduct) {
          console.log(
            'Check had setup event detail follow sku of product for user',
            user.id,
          );
          if (!eventProductSkuOfProduct) {
            continue;
          }
          console.log(
            'Check quantity event detail follow sku of product for user',
            user.id,
          );
          if (
            !eventProductSkuOfProduct.totalQuantity ||
            eventProductSkuOfProduct.totalQuantity -
              eventProductSkuOfProduct.usedQuantity <=
              0
          ) {
            giftOutOfQuantity = true;
            continue;
          }
          eventDetailSku = eventProductSkuOfProduct;
        }
      }
      // Check event detail to user type
      console.log(`Check setup event detail to user type`);
      if (eventDetailToUserTypes && eventDetailToUserTypes.length) {
        console.log(
          `Check setup event detail follow user type ${userType} for user`,
          user.id,
        );
        const eventDetailToUserTypesMatch = eventDetailToUserTypes.filter(
          (item) => {
            return item.userType === userType;
          },
        );
        if (
          !eventDetailToUserTypesMatch ||
          !eventDetailToUserTypesMatch.length
        ) {
          continue;
        }
        eventDetailToUserType = eventDetailToUserTypesMatch[0];
        console.log(
          `Check time range event detail follow user type ${userType} for user`,
          user.id,
        );
        const rsCompareEventDetailToUserTypeStartDateWithNow =
          compareDateWithCurrentDateInTimezone(eventDetailToUserType.startDate);
        const rsCompareEventDetailToUserTypeEndDateWithNow =
          compareDateWithCurrentDateInTimezone(eventDetailToUserType.endDate);
        if (
          1 == rsCompareEventDetailToUserTypeStartDateWithNow ||
          -1 == rsCompareEventDetailToUserTypeEndDateWithNow
        ) {
          continue;
        }
        console.log(
          `Check quantity event detail follow user type ${userType} for user`,
          user.id,
        );
        if (
          !eventDetailToUserType.totalQuantity ||
          eventDetailToUserType.totalQuantity -
            eventDetailToUserType.usedQuantity <=
            0
        ) {
          continue;
        }
      }

      // check qr of supplier

      if (type == 'SB' && !productData.distributorCode) {
        if (true == eventOrdinal.activeCheckSupplierNull) {
          continue;
        }
      }

      const [checkSetupIgnoreSupplier, checkSetupWinningSupplier] =
        await Promise.all([
          this.eventDetailIgnoreStoreRepo.findBy({
            eventDetailId: eventOrdinal.id,
          }),
          this.eventDetailSupplierRepo.findBy({
            eventDetailId: eventOrdinal.id,
          }),
        ]);
      // check if event ignore store
      /*const checkSetupIgnoreSupplier =
        await this.eventDetailIgnoreStoreRepo.findBy({
          eventDetailId: eventOrdinal.id,
        });*/

      if (checkSetupIgnoreSupplier.length !== 0) {
        const foundIgnoreSupplier = checkSetupIgnoreSupplier.some(
          (item) => item.storeCode === productData.distributorCode,
        );

        if (foundIgnoreSupplier) continue;
      }

      // check if event supplier
      /*const checkSetupWinningSupplier =
        await this.eventDetailSupplierRepo.findBy({
          eventDetailId: eventOrdinal.id,
        });*/

      if (checkSetupWinningSupplier.length !== 0) {
        const foundWinningSupplier = checkSetupWinningSupplier.some(
          (item) => item.supplierCode === productData.distributorCode,
        );

        if (!foundWinningSupplier) continue;
      }

      // Check event detail supplier v2
      console.log(`Check setup event detail supplier v2`);
      if (eventDetailSupplierV2s && eventDetailSupplierV2s.length) {
        console.log(`Check setup event detail supplier v2 for user`, user.id);
        const eventDetailSupplierV2sMatch = eventDetailSupplierV2s.filter(
          (item) => {
            return item.supplierCode === productData.distributorCode;
          },
        );
        if (
          !eventDetailSupplierV2sMatch ||
          !eventDetailSupplierV2sMatch.length
        ) {
          continue;
        }
        eventDetailSupplierV2 = eventDetailSupplierV2sMatch[0];
        console.log(
          `Check time range event detail supplier v2 for user`,
          user.id,
        );
        const rsCompareEventDetailSupplierV2StartDateWithNow =
          compareDateWithCurrentDateInTimezone(eventDetailSupplierV2.startDate);
        const rsCompareEventDetailSupplierV2EndDateWithNow =
          compareDateWithCurrentDateInTimezone(eventDetailSupplierV2.endDate);
        if (
          1 == rsCompareEventDetailSupplierV2StartDateWithNow ||
          -1 == rsCompareEventDetailSupplierV2EndDateWithNow
        ) {
          continue;
        }
        console.log(
          `Check quantity event detail supplier v2 for user`,
          user.id,
        );
        if (
          !eventDetailSupplierV2.totalQuantity ||
          eventDetailSupplierV2.totalQuantity -
            eventDetailSupplierV2.usedQuantity <=
            0
        ) {
          continue;
        }
      }

      // check if qr of store is valid

      let eventDetailToStore: EventDetailToStore = null;

      if (productData.storeId)
        eventDetailToStore = await this.eventDetailToStoreRepo.findOneBy({
          eventDetailId: eventOrdinal.id,
          storeCode: productData.storeId,
        });

      console.log('Check event detail to store for user', user.id);

      if (eventDetailToStore) {
        console.log(
          'Check start date end date event detail to store not null for user',
          user.id,
        );
        if (
          eventDetailToStore.startDate &&
          eventDetailToStore.endDate
          /*dayjs(now).isAfter(dayjs(eventOrdinal.startDate)) &&
          dayjs(now).isBefore(dayjs(eventOrdinal.endDate))*/
        ) {
          console.log('Check event detail to store quantity for user', user.id);
          if (eventDetailToStore.quantity > 0) {
            console.log(
              'Check start date end date event detail to store valid for user',
              user.id,
            );
            const rsCompareEventDetailToStoreStartDateWithNow =
              compareDateWithCurrentDateInTimezone(
                eventDetailToStore.startDate,
              );
            const rsCompareEventDetailToStoreEndDateWithNow =
              compareDateWithCurrentDateInTimezone(eventDetailToStore.endDate);
            /*if (
              dayjs(now).isAfter(dayjs(eventDetailToStore.startDate)) &&
              dayjs(now).isBefore(dayjs(eventDetailToStore.endDate))
            ) {*/
            if (
              -1 == rsCompareEventDetailToStoreStartDateWithNow &&
              1 == rsCompareEventDetailToStoreEndDateWithNow
            ) {
              // BOOM
              userReceiveGift = true;
              await this.redeemGift(
                eventUser,
                eventLimit,
                eventOrdinal,
                product,
                user,
                now,
                eventUserLimit,
                transactionRefId,
                spoonCode,
                type,
                requestOutboxMessage,
                outboxMessageGift,
                syncData3rdServiceToWhRequestDetails,
                arrNoti,
                token,
                null,
                eventDetailToStore,
                eventDetailSku,
                eventDetailToUserType,
                eventDetailSupplierV2,
              );

              const popupCode = this.getPopupCode(eventOrdinal);
              console.log('Check popup code exists for user', user.id);
              if (!popupCode) {
                throw new AppBaseExc(
                  StatusCode.SB_DEFAULT_ERROR,
                  null,
                  null,
                  StatusCode.API_FAILED_UNKNOWN,
                  this.request,
                );
              }
              console.log('Push popup for user', user.id);
              await this.pushPopupV2(
                user.id,
                popupCode,
                arrNoti,
                appversionname,
              );
              await this.pushEventQ1Notification(user.id, eventOrdinal);
              break;
            }
          } else {
            giftOutOfQuantity = true;
          }
        }
      } else {
        console.log(
          'Check start date end date event detail not null for user',
          user.id,
        );

        // check if province setup
        if (eventOrdinal.startDate && eventOrdinal.endDate) {
          console.log(
            'Check start date end date event detail valid for user',
            user.id,
          );
          // province not setup, check if event is still on air
          const rsCompareEventDetailStartDateWithNow =
            compareDateWithCurrentDateInTimezone(eventOrdinal.startDate);
          const rsCompareEventDetailEndDateWithNow =
            compareDateWithCurrentDateInTimezone(eventOrdinal.endDate);
          /*if (
            dayjs(now).isAfter(dayjs(eventOrdinal.startDate)) &&
            dayjs(now).isBefore(dayjs(eventOrdinal.endDate))
          ) {*/

          console.log('test 1');
          if (
            -1 == rsCompareEventDetailStartDateWithNow &&
            1 == rsCompareEventDetailEndDateWithNow
          ) {
            // BOOM
            console.log('test 2');
            userReceiveGift = true;
            await this.redeemGift(
              eventUser,
              eventLimit,
              eventOrdinal,
              product,
              user,
              now,
              eventUserLimit,
              transactionRefId,
              spoonCode,
              type,
              requestOutboxMessage,
              outboxMessageGift,
              syncData3rdServiceToWhRequestDetails,
              arrNoti,
              token,
              null,
              null,
              eventDetailSku,
              eventDetailToUserType,
              eventDetailSupplierV2,
            );
            console.log('test 3');

            const popupCode = this.getPopupCode(eventOrdinal);
            console.log('Check popup code exists for user', user.id);
            if (!popupCode) {
              throw new AppBaseExc(
                StatusCode.SB_DEFAULT_ERROR,
                null,
                null,
                StatusCode.API_FAILED_UNKNOWN,
                this.request,
              );
            }
            console.log('Push popup for user', user.id);
            await this.pushPopupV2(user.id, popupCode, arrNoti, appversionname);
            await this.pushEventQ1Notification(user.id, eventOrdinal);
            break;
          }
        } else {
          // province setup
          // date have 2023-06-14 15:10:00.000 +0700  use this function
          //const current = getNowAtTimeHcm();
          const eventDetailProvince =
            await this.eventDetailProvinceRepo.findOneBy({
              eventDetailId: eventOrdinal.id,
              provinceId: user.provinceId,
              endDate: MoreThanOrEqual(current),
              startDate: LessThanOrEqual(current),
            });

          console.log('Check event detail province for user', user.id);
          // check if user province not in ordinal
          if (!eventDetailProvince) {
            // next ordinal
            continue;
          }
          console.log('Check event detail province quantity for user', user.id);
          // check if province quantity > 0
          if (eventDetailProvince.quantity === 0) {
            // next ordinal
            giftOutOfQuantity = true;
            continue;
          }

          console.log(
            'Check start date end date event detail province valid for user',
            user.id,
          );

          // BOOM
          userReceiveGift = true;
          await this.redeemGift(
            eventUser,
            eventLimit,
            eventOrdinal,
            product,
            user,
            now,
            eventUserLimit,
            transactionRefId,
            spoonCode,
            type,
            requestOutboxMessage,
            outboxMessageGift,
            syncData3rdServiceToWhRequestDetails,
            arrNoti,
            token,
            eventDetailProvince,
            null,
            eventDetailSku,
            eventDetailToUserType,
            eventDetailSupplierV2,
          );

          const popupCode = this.getPopupCode(eventOrdinal);
          console.log('Check popup code exists for user', user.id);
          if (!popupCode) {
            throw new AppBaseExc(
              StatusCode.SB_DEFAULT_ERROR,
              null,
              null,
              StatusCode.API_FAILED_UNKNOWN,
              this.request,
            );
          }
          console.log('Push popup for user', user.id);
          await this.pushPopupV2(user.id, popupCode, arrNoti, appversionname);
          await this.pushEventQ1Notification(user.id, eventOrdinal);
          break;
        }
      }
    }
  }

  @Transactional()
  async handleEvent01(
    user: User,
    product: Product,
    code: string,
    productData: ProductDataGotByQrDto,
    transactionRefId: string,
    arrNoti: string[],
    appversionname: string,
    requestOutboxMessage: RequestOutboxMessage,
  ) {
    console.log(`Handle event 01 for user`, user.id);

    // const eventsIdList = [299, 298, 297, 296];
    const events = await this.eventRepo.findActiveEvents();

    if (events.length === 0) return;

    const numberCheckEvent01 = this.configService.get(
      'event.numberCheckProduct01',
    );

    console.log(numberCheckEvent01);
    const eventsIdListProduct01: number[] = numberCheckEvent01
      .split(',')
      .map((item) => {
        return Number(item);
      });

    const eventsIdList = events
      .filter((event) => eventsIdListProduct01.indexOf(event.id) >= 0)
      .map((item) => item.id);
    // find matching event
    const eventProduct = await this.eventProductRepo.findOne({
      where: {
        eventId: In(eventsIdList),
        sku: product.code,
      },
      relations: {
        event: true,
      },
    });

    if (!eventProduct) return;

    const event = eventProduct.event;
    console.log(`Event id: ${event.id} for user`, user.id);

    const eventProducts = await this.eventProductRepo
      .createQueryBuilder('eventProduct')
      .innerJoinAndSelect('eventProduct.event', 'event')
      .where('event.id = :eventId', { eventId: event.id })
      .select('eventProduct.sku')
      .addSelect('eventProduct.eventId')
      .getMany();

    const eventIds = [];
    const addingMore = PopupV2Code.QUY1_KEU_GOI_TICH_THEM_SB;
    const notWinning = PopupV2Code.QUY1_KHONG_TRUNG_GIAI;

    eventProducts.forEach((eventProduct) => {
      eventIds.push(eventProduct.eventId);
    });

    //
    const blockedUser = await this.wheelLuckyBlockUserRepo.findOneBy({
      phoneNumber: user.phoneNumber,
    });
    if (event.activeBlacklist) {
      console.log('Check blocked for user', user.id);
      if (blockedUser) {
        await this.pushPopupV2(user.id, notWinning, arrNoti, appversionname);
        return;
      }
    }

    // check user_type
    const userType: string = await this.getUserType(user, event.startDate);

    // check if already add point, create or update event_user
    let eventUser = await this.eventUserRepo.findOneBy({
      userId: user.id,
      eventId: event.id,
    });
    //console.log(`Event user`);
    //console.log(eventUser);

    let eventLimit = await this.eventLimitRepo.findOneBy({
      eventId: event.id,
      type: userType as UserTypeV2,
    });
    //console.log(`Event limit`);
    //console.log(eventLimit);

    if (!eventUser) {
      const userLimitCustomer = await this.eventLimitRepo.findOneBy({
        eventId: event.id,
        type: UserTypeV2.CUSTOMER,
      });
      const userLimitStore = await this.eventLimitRepo.findOneBy({
        eventId: event.id,
        type: UserTypeV2.STORE,
      });
      const userLimitNewUser = await this.eventLimitRepo.findOneBy({
        eventId: event.id,
        type: UserTypeV2.NEW_USER,
      });
      let userLimit = 0;
      switch (userType) {
        case UserTypeV2.CUSTOMER:
          userLimit = userLimitCustomer.userLimit;
          eventLimit = userLimitCustomer;
          break;
        case UserTypeV2.STORE:
          userLimit = userLimitStore.userLimit;
          eventLimit = userLimitStore;
          break;
        case UserTypeV2.NEW_USER:
          userLimit = userLimitNewUser.userLimit;
          eventLimit = userLimitNewUser;
          break;
        default:
          userLimit = 0;
          break;
      }

      const eventUserNew = this.eventUserRepo.create({
        eventId: event.id,
        userId: user.id,
        winRate: eventLimit.winRate,
        userLimit: 1,
      });

      const savedEventUser = await this.eventUserRepo.save(eventUserNew);
      eventUser = eventUserNew;

      const eventUserLimit = this.eventUserLimitRepo.create({
        eventUserId: savedEventUser.id,
        type: userType,
        userLimit,
      });
      await this.eventUserLimitRepo.save(eventUserLimit);
    }
    console.log('Check user event win rate for user', user.id);

    // check winRate
    if (eventUser.winRate !== 100) {
      await this.increaseWinrate(eventUser, eventLimit.upRate);
      await this.pushPopupV2(user.id, notWinning, arrNoti, appversionname);
      return;
    }

    const eventUserLimit = await this.eventUserLimitRepo.findOneBy({
      eventUserId: eventUser.id,
      type: userType,
    });
    console.log('Check user event limit for user', user.id);

    // check if user reaches limit
    if (!eventUserLimit || eventUserLimit.userLimit === 0) {
      await this.increaseWinrate(eventUser, eventLimit.upRate);
      await this.pushPopupV2(user.id, notWinning, arrNoti, appversionname);
      return;
    }

    // check if user received exclusive gift on child events
    const eventPointHistoriesOfUser = await this.eventPointHistoryRepo.findBy({
      userId: user.id,
    });
    const eventDetailIdsUserReceived = eventPointHistoriesOfUser.map(
      (eventPointHistory) => eventPointHistory.eventDetailId,
    );

    // get possible ordinals
    const eventOrdinals = await this.eventDetailRepo
      .createQueryBuilder('eventDetail')
      .where('eventDetail.eventId = :eventId', { eventId: event.id })
      .andWhere('eventDetail.quantity > 0')
      .andWhere('eventDetail.ordinal > 0')
      .orderBy('eventDetail.ordinal', 'ASC')
      .maxExecutionTime(60000) // timeout 60s
      .getMany();

    const listEventDetailIdsOfCurrentEvent = eventOrdinals.map(
      (ordinal) => ordinal.id,
    );

    const eventDetailIdsUserReceivedOfCurrentEvent =
      eventDetailIdsUserReceived.filter((ids) =>
        listEventDetailIdsOfCurrentEvent.includes(ids),
      );

    const exclusiveEventDetail = await this.eventDetailExclusionRepo.findOneBy({
      userType,
      eventDetailId: In(eventDetailIdsUserReceivedOfCurrentEvent),
    });

    console.log('Check exclusive event detail for user', user.id);

    if (exclusiveEventDetail) {
      await this.increaseWinrate(eventUser, eventLimit.upRate);
      await this.pushPopupV2(user.id, notWinning, arrNoti, appversionname);
      return;
    }

    let userReceiveGift = false;
    console.log('Loop event detail for user', user.id);

    const now = getNowAtTimeZoneHcm();

    console.log('date history point sb', now);

    /*const blockedEventDetailUser =
      await this.eventDetailBlockUserRepo.findOneBy({
        phoneNumber: user.phoneNumber,
        //eventDetailId: eventOrdinal.id,
      });*/
    for (const eventOrdinal of eventOrdinals) {
      // Check event detail black list
      if (eventOrdinal.activeBlacklist) {
        /*const blockedUser = await this.eventDetailBlockUserRepo.findOneBy({
          phoneNumber: user.phoneNumber,
          eventDetailId: eventOrdinal.id,
        });*/
        console.log('Check block user event detail for user', user.id);
        if (blockedUser) {
          continue;
        }
      }
      // check if gift belongs to self exclusive gifts
      const selfEventDetailExclusion =
        await this.selfEventDetailExclusionRepo.findOneBy({
          userType,
          eventDetailId: eventOrdinal.id,
        });
      console.log('Check self exclusion event detail for user', user.id);
      if (selfEventDetailExclusion) {
        continue;
      }
      console.log('Check event detail probability for user', user.id);
      // check probability
      if (eventOrdinal.probability !== 100) {
        continue;
      }

      // check if gift belongs to exclusive gifts of parent event
      const eventDetailExclusion =
        await this.eventDetailExclusionRepo.findOneBy({
          eventDetailId: eventOrdinal.id,
          userType,
        });
      if (eventDetailExclusion) {
        const eventGroupGiftExclusion =
          await this.eventGroupGiftExclusionRepo.findOneBy({
            eventGroupId: event.eventGroupId,
            eventDetailExclusionId: eventDetailExclusion.id,
          });
        if (eventGroupGiftExclusion) {
          // if belongs, check if user received exclusive gift from parent event
          const eventDetailExclusionReceived =
            await this.eventDetailExclusionRepo.findBy({
              userType,
              eventDetailId: In(eventDetailIdsUserReceived),
            });
          const eventDetailExclusionIdsReceived =
            eventDetailExclusionReceived.map((ede) => ede.id);
          const exclusiveEventGroup =
            await this.eventGroupGiftExclusionRepo.findOneBy({
              eventGroupId: event.eventGroupId,
              eventDetailExclusionId: In(eventDetailExclusionIdsReceived),
            });
          console.log('Check exclusive event group for user', user.id);
          if (exclusiveEventGroup) {
            continue;
          }
        }
      }
      console.log('Check event detail quantity for user', user.id);
      // check quantity of ordinal
      if (eventOrdinal.quantity === 0) {
        continue;
      }

      // check if qr of store is valid

      let eventDetailToStore: EventDetailToStore = null;

      if (productData.storeId)
        eventDetailToStore = await this.eventDetailToStoreRepo.findOneBy({
          eventDetailId: eventOrdinal.id,
          storeCode: productData.storeId,
        });

      console.log('Check event detail to store for user', user.id);

      if (eventDetailToStore) {
        console.log(
          'Check start date end date event detail to store not null for user',
          user.id,
        );
        if (
          eventDetailToStore.startDate &&
          eventDetailToStore.endDate
          /*dayjs(now).isAfter(dayjs(eventOrdinal.startDate)) &&
            dayjs(now).isBefore(dayjs(eventOrdinal.endDate))*/
        ) {
          console.log('Check event detail to store quantity for user', user.id);
          if (eventDetailToStore.quantity > 0) {
            console.log(
              'Check start date end date event detail to store valid for user',
              user.id,
            );
            const rsCompareEventDetailToStoreStartDateWithNow =
              compareDateWithCurrentDateInTimezone(
                eventDetailToStore.startDate,
              );
            const rsCompareEventDetailToStoreEndDateWithNow =
              compareDateWithCurrentDateInTimezone(eventDetailToStore.endDate);
            /*if (
                dayjs(now).isAfter(dayjs(eventDetailToStore.startDate)) &&
                dayjs(now).isBefore(dayjs(eventDetailToStore.endDate))
              ) {*/
            if (
              -1 == rsCompareEventDetailToStoreStartDateWithNow &&
              1 == rsCompareEventDetailToStoreEndDateWithNow
            ) {
              // BOOM
              userReceiveGift = true;
              await this.redeemGiftProduct01(
                eventUser,
                eventLimit,
                eventOrdinal,
                product,
                user,
                now,
                eventUserLimit,
                transactionRefId,
                code,
                requestOutboxMessage,
                null,
                eventDetailToStore,
              );

              const popupCode = this.getPopupCode(eventOrdinal);
              console.log('Check popup code exists for user', user.id);
              if (!popupCode) {
                throw new AppBaseExc(
                  StatusCode.SB_DEFAULT_ERROR,
                  null,
                  null,
                  StatusCode.API_FAILED_UNKNOWN,
                  this.request,
                );
              }
              console.log('Push popup for user', user.id);
              await this.pushPopupV2(
                user.id,
                popupCode,
                arrNoti,
                appversionname,
              );
              break;
            }
          }
        }
      } else {
        console.log(
          'Check start date end date event detail not null for user',
          user.id,
        );
        // check if province setup
        if (eventOrdinal.startDate && eventOrdinal.endDate) {
          console.log(
            'Check start date end date event detail valid for user',
            user.id,
          );
          // province not setup, check if event is still on air
          const rsCompareEventDetailStartDateWithNow =
            compareDateWithCurrentDateInTimezone(eventOrdinal.startDate);
          const rsCompareEventDetailEndDateWithNow =
            compareDateWithCurrentDateInTimezone(eventOrdinal.endDate);
          /*if (
              dayjs(now).isAfter(dayjs(eventOrdinal.startDate)) &&
              dayjs(now).isBefore(dayjs(eventOrdinal.endDate))
            ) {*/

          console.log('test 1');
          if (
            -1 == rsCompareEventDetailStartDateWithNow &&
            1 == rsCompareEventDetailEndDateWithNow
          ) {
            // BOOM
            console.log('test 2');
            userReceiveGift = true;
            await this.redeemGiftProduct01(
              eventUser,
              eventLimit,
              eventOrdinal,
              product,
              user,
              now,
              eventUserLimit,
              transactionRefId,
              code,
              requestOutboxMessage,
            );
            console.log('test 3');

            const popupCode = this.getPopupCode(eventOrdinal);
            console.log('Check popup code exists for user', user.id);
            if (!popupCode) {
              throw new AppBaseExc(
                StatusCode.SB_DEFAULT_ERROR,
                null,
                null,
                StatusCode.API_FAILED_UNKNOWN,
                this.request,
              );
            }
            console.log('Push popup for user', user.id);
            await this.pushPopupV2(user.id, popupCode, arrNoti, appversionname);
            break;
          }
        } else {
          // province setup
          // date have 2023-06-14 15:10:00.000 +0700  use this function
          const current = getNowAtTimeHcm();
          const eventDetailProvince =
            await this.eventDetailProvinceRepo.findOneBy({
              eventDetailId: eventOrdinal.id,
              provinceId: user.provinceId,
              endDate: MoreThanOrEqual(current),
              startDate: LessThanOrEqual(current),
            });

          console.log('Check event detail province for user', user.id);
          // check if user province not in ordinal
          if (!eventDetailProvince) {
            // next ordinal
            continue;
          }
          console.log('Check event detail province quantity for user', user.id);
          // check if province quantity > 0
          if (eventDetailProvince.quantity === 0) {
            // next ordinal
            continue;
          }

          console.log(
            'Check start date end date event detail province valid for user',
            user.id,
          );

          // BOOM
          userReceiveGift = true;
          await this.redeemGiftProduct01(
            eventUser,
            eventLimit,
            eventOrdinal,
            product,
            user,
            now,
            eventUserLimit,
            transactionRefId,
            code,
            requestOutboxMessage,
            eventDetailProvince,
          );

          const popupCode = this.getPopupCode(eventOrdinal);
          console.log('Check popup code exists for user', user.id);
          if (!popupCode) {
            throw new AppBaseExc(
              StatusCode.SB_DEFAULT_ERROR,
              null,
              null,
              StatusCode.API_FAILED_UNKNOWN,
              this.request,
            );
          }
          console.log('Push popup for user', user.id);
          await this.pushPopupV2(user.id, popupCode, arrNoti, appversionname);
          break;
        }
      }
    }

    if (!userReceiveGift) {
      await this.increaseWinrate(eventUser, eventLimit.upRate);
      await this.pushPopupV2(user.id, notWinning, arrNoti, appversionname);
    }
  }

  private async redeemGift(
    eventUser: EventUser,
    eventLimit: EventLimit,
    eventOrdinal: EventDetail,
    product: Product,
    user: User,
    now: Date,
    eventUserLimit: EventUserLimit,
    transactionRefId: string,
    code: string,
    type: string,
    requestOutboxMessage: RequestOutboxMessage,
    outboxMessageGift: OutboxMessage[],
    syncData3rdServiceToWhRequestDetails: SyncData3rdServiceToWhRequestDetail[],
    arrNoti: string[],
    token: string,
    eventDetailProvince?: EventDetailProvince,
    eventDetailToStore?: EventDetailToStore,
    eventDetailSku?: EventDetailSku,
    eventDetailToUserType?: EventDetailToUserType,
    eventDetailSupplierV2?: EventDetailSupplierV2,
    postCommitActions?: Array<() => Promise<unknown>>,
    rsValidateMatchSetupEventTimeGiftingGift?: IResultValidateMatchSetupEventTimeGiftingGift,
  ) {
    const today = getFormatCurrentDateInTimezone();
    const promisesUpdate: any[] = [];
    promisesUpdate.push(
      this.eventDetailRepo
        .createQueryBuilder()
        .update(EventDetail)
        .set({ ['quantity']: () => `quantity - 1` })
        .where('id = :id and quantity > 0', {
          id: eventOrdinal.id,
        })
        .execute(),
    );
    //eventOrdinal.quantity--;
    //await this.eventDetailRepo.save(eventOrdinal);
    if (eventDetailProvince) {
      const isActiveEvenly = eventOrdinal.activePrizeEvenlyProvince;
      const hasEvenlyHistory = !!eventDetailProvince.evenlyHistory?.length;
      const latestDate = hasEvenlyHistory
        ? this.getLatestDateInEvenlyHistory(eventDetailProvince.evenlyHistory)
        : null;

      let updateCondition = `id = :id AND quantity > 0`;
      const params: any = {
        id: eventDetailProvince.id,
        today: today,
      };

      // Kiểm tra điều kiện phân bổ nổ giải đồng đều.
      if (isActiveEvenly && hasEvenlyHistory && today !== latestDate) {
        updateCondition += ` AND COALESCE((evenly_history->:today->>'usedQuantity')::int, 0) 
                          < COALESCE((evenly_history->:today->>'evenQuantity')::int, 0)`;
      }

      // Trừ số lượng quà
      promisesUpdate.push(
        this.eventDetailProvinceRepo
          .createQueryBuilder()
          .update(EventDetailProvince)
          .set({
            quantity: () => `quantity - 1`,
            evenlyHistory: isActiveEvenly
              ? () => `
                        jsonb_set(
                          evenly_history,
                          '{${today},usedQuantity}',
                          (COALESCE((evenly_history->'${today}'->>'usedQuantity')::int, 0) + 1)::text::jsonb
                        )
                      `
              : undefined,
          })
          .where(updateCondition, params)
          .execute(),
      );
    }
    if (eventDetailToStore) {
      const isActiveEvenly = eventOrdinal.activePrizeEvenlyStore;
      const hasEvenlyHistory = !!eventDetailToStore.evenlyHistory?.length;
      const latestDate = hasEvenlyHistory
        ? this.getLatestDateInEvenlyHistory(eventDetailToStore.evenlyHistory)
        : null;

      let updateCondition = `id = :id AND quantity > 0`;
      const params: any = {
        id: eventDetailToStore.id,
        today: today,
      };

      // Kiểm tra điều kiện phân bổ nổ giải đồng đều.
      if (isActiveEvenly && hasEvenlyHistory && today !== latestDate) {
        updateCondition += ` AND COALESCE((evenly_history->:today->>'usedQuantity')::int, 0) 
                          < COALESCE((evenly_history->:today->>'evenQuantity')::int, 0)`;
      }

      // Trừ số lượng quà
      promisesUpdate.push(
        this.eventDetailToStoreRepo
          .createQueryBuilder()
          .update(EventDetailToStore)
          .set({
            quantity: () => `quantity - 1`,
            evenlyHistory: isActiveEvenly
              ? () => `
                        jsonb_set(
                          evenly_history,
                          '{${today},usedQuantity}',
                          (COALESCE((evenly_history->'${today}'->>'usedQuantity')::int, 0) + 1)::text::jsonb
                        )
                      `
              : undefined,
          })
          .where(updateCondition, params)
          .execute(),
      );
    }
    if (eventDetailSku) {
      promisesUpdate.push(
        this.eventDetailSkuRepo
          .createQueryBuilder()
          .update(EventDetailSku)
          .set({ ['usedQuantity']: () => `used_quantity + 1` })
          .where('id = :id and usedQuantity < totalQuantity', {
            id: eventDetailSku.id,
          })
          .execute(),
      );
    }
    if (eventDetailToUserType) {
      const isActiveEvenly = eventOrdinal.activePrizeEvenlyUserType;
      const hasEvenlyHistory = !!eventDetailToUserType.evenlyHistory?.length;
      const latestDate = hasEvenlyHistory
        ? this.getLatestDateInEvenlyHistory(eventDetailToUserType.evenlyHistory)
        : null;

      let updateCondition = `id = :id AND usedQuantity < totalQuantity`;
      const params: any = {
        id: eventDetailToUserType.id,
        today: today,
      };

      // Kiểm tra điều kiện phân bổ nổ giải đồng đều.
      if (isActiveEvenly && hasEvenlyHistory && today !== latestDate) {
        updateCondition += ` AND COALESCE((evenly_history->:today->>'usedQuantity')::int, 0) 
                          < COALESCE((evenly_history->:today->>'evenQuantity')::int, 0)`;
      }

      // Trừ số lượng quà
      promisesUpdate.push(
        this.eventDetailToUserTypeRepo
          .createQueryBuilder()
          .update(EventDetailToUserType)
          .set({
            usedQuantity: () => `used_quantity + 1`,
            evenlyHistory: isActiveEvenly
              ? () => `
                        jsonb_set(
                          evenly_history,
                          '{${today},usedQuantity}',
                          (COALESCE((evenly_history->'${today}'->>'usedQuantity')::int, 0) + 1)::text::jsonb
                        )
                      `
              : undefined,
          })
          .where(updateCondition, params)
          .execute(),
      );
    }
    if (eventDetailSupplierV2) {
      const isActiveEvenly = eventOrdinal.activePrizeEvenlySupplierV2;
      const hasEvenlyHistory = !!eventDetailSupplierV2.evenlyHistory?.length;
      const latestDate = hasEvenlyHistory
        ? this.getLatestDateInEvenlyHistory(eventDetailSupplierV2.evenlyHistory)
        : null;

      let updateCondition = `id = :id AND usedQuantity < totalQuantity`;
      const params: any = {
        id: eventDetailSupplierV2.id,
        today: today,
      };

      // Kiểm tra điều kiện phân bổ nổ giải đồng đều.
      if (isActiveEvenly && hasEvenlyHistory && today !== latestDate) {
        updateCondition += ` AND COALESCE((evenly_history->:today->>'usedQuantity')::int, 0) 
                          < COALESCE((evenly_history->:today->>'evenQuantity')::int, 0)`;
      }

      // Trừ số lượng quà
      promisesUpdate.push(
        this.eventDetailSupplierV2Repo
          .createQueryBuilder()
          .update(EventDetailSupplierV2)
          .set({
            usedQuantity: () => `used_quantity + 1`,
            evenlyHistory: isActiveEvenly
              ? () => `
                        jsonb_set(
                          evenly_history,
                          '{${today},usedQuantity}',
                          (COALESCE((evenly_history->'${today}'->>'usedQuantity')::int, 0) + 1)::text::jsonb
                        )
                      `
              : undefined,
          })
          .where(updateCondition, params)
          .execute(),
      );
    }
    if (
      eventOrdinal.enableTimeGiftingGift &&
      rsValidateMatchSetupEventTimeGiftingGift
    ) {
      if (rsValidateMatchSetupEventTimeGiftingGift.eventDetailTimeGiftingGift) {
        if (
          !rsValidateMatchSetupEventTimeGiftingGift.historyUserGiftingGiftEvent
        ) {
          promisesUpdate.push(
            this.historyUserGiftingGiftEventRepo.save({
              userId: user.id,
              eventDetailTimeGiftingGiftId:
                rsValidateMatchSetupEventTimeGiftingGift
                  .eventDetailTimeGiftingGift.id,
              total: 1,
            }),
          );
        } else {
          promisesUpdate.push(
            this.historyUserGiftingGiftEventRepo
              .createQueryBuilder()
              .update(HistoryUserGiftingGiftEvent)
              .set({
                total: () => `total + 1`,
              })
              .where('id = :id AND total < :total', {
                id: rsValidateMatchSetupEventTimeGiftingGift
                  .historyUserGiftingGiftEvent.id,
                total:
                  rsValidateMatchSetupEventTimeGiftingGift
                    .eventDetailTimeGiftingGift.total,
              })
              .execute(),
          );
        }
      }
    }

    const resultUpdateds = await Promise.all(promisesUpdate);
    for (const resultUpdated of resultUpdateds) {
      if (!resultUpdated || (!resultUpdated.affected && !resultUpdated.id)) {
        throw new AppBaseExc(
          StatusCode.SB_DEFAULT_ERROR,
          null,
          null,
          StatusCode.API_FAILED_UNKNOWN,
          this.request,
        );
      }
    }
    this.decreaseWinrate(eventUser, eventLimit.downRate);
    eventUserLimit.userLimit--;
    await this.eventUserLimitRepo.save(eventUserLimit);

    const crmTxType = await this.crmTransactionTypeRepo.findOneBy({
      id: eventOrdinal.crmTransactionTypeId,
    });
    let transactionExternalId = randomTransactionExternalId();

    // gift: get new gift when improvement event phase 1
    let gift: any;
    if (
      eventOrdinal.eventId >
      Number(this.configService.get('event.eventLatestId'))
    ) {
      gift = await this.vitaJavaService.getGiftById(
        token,
        eventOrdinal.gsGiftId,
      );
    } else {
      gift = await this.giftRepo.findOneBy({
        id: eventOrdinal.giftId,
      });
    }

    let giftPoint = 0;
    if (
      gift.id === 660 ||
      gift.id === 680 ||
      gift.id === 685 ||
      gift.id === 697 ||
      gift.id === 702 ||
      gift.id === 705 ||
      gift.id === 758
    ) {
      giftPoint = product.point;
    }

    /*if (
      GiftType.E_VOUCHER == gift.type ||
      GiftType.E_VOUCHER_SHOP == gift.type ||
      GiftType.E_VOUCHER_SHOP_BKIDS == gift.type
    ) {
      // update user point
      user.giftPoint += giftPoint;
      user.totalPoint += giftPoint;

      await this.userRepo.save(user);

      const exchangeGiftRequest: ExchangeGiftRequest = {
        giftId: gift.id,
        quantity: 1,
        transactionRefId,
        productId: product.id,
        spoonCode: code,
        giftName: gift.name,
        eventDetailId: eventOrdinal.id,
      };
      //const exchangeGiftResponse: ExchangeGiftResponse =
      //  await this.vitaJavaService.exchangeGift(token, exchangeGiftRequest);
      //if (exchangeGiftResponse) {
      //  const codes = exchangeGiftResponse.notification.codes;
      //  arrNoti = arrNoti.concat(codes);
      //}
      this.vitaJavaService.exchangeGift(token, exchangeGiftRequest);

      return;
    }*/

    const enableGiftingGiftOnJavaV4 = this.configService.get<boolean>(
      'vitaJava.application_v4.enable',
    );
    let vitaCode: VitaCode = null;
    let userGiftId = null;
    let userGiftStatus = null;
    let sourceGift = null;
    let giftType = null;
    if (enableGiftingGiftOnJavaV4) {
      const giftObject =
        eventOrdinal.eventId >
        Number(this.configService.get('event.eventLatestId'))
          ? {
              giftId: gift.id,
            }
          : {
              oldGiftId: gift.id,
            };
      const requestGiftingGiftOnJavaV4: GiftingGiftRequest = {
        ...giftObject,
        crmTransactionTypeId: crmTxType.id,
        quantity: 1,
        point: giftPoint,
        brand: product.brand,
        decreaseGift: false,
        eventId: eventOrdinal.eventId,
      };
      if (!token) {
        requestGiftingGiftOnJavaV4.userId = user.id;
      }

      const resposnseGiftingGiftOnJavaV4: GiftingGiftResponse =
        await this.vitaJavaService.giftingGift(
          token,
          requestGiftingGiftOnJavaV4,
        );
      if (!resposnseGiftingGiftOnJavaV4) {
        throw new AppBaseExc(
          StatusCode.SB_DEFAULT_ERROR,
          null,
          null,
          StatusCode.API_FAILED_UNKNOWN,
          this.request,
        );
      }
      userGiftId = resposnseGiftingGiftOnJavaV4.data.userGiftId;
      //const historyPointId = resposnseGiftingGiftOnJavaV4.data.historyPointId;
      transactionExternalId =
        resposnseGiftingGiftOnJavaV4.data.transactionExternalId;
      userGiftStatus = resposnseGiftingGiftOnJavaV4.data.userGiftStatus;
      sourceGift = resposnseGiftingGiftOnJavaV4.data.sourceGift;
      giftType = resposnseGiftingGiftOnJavaV4.data.giftType;
      // Update vitaCode from java response
      if (resposnseGiftingGiftOnJavaV4.data.vitaCode) {
        vitaCode = await this.vitaCodeRepo.findOne({
          where: { code: resposnseGiftingGiftOnJavaV4.data.vitaCode },
        });
      }
      if (!userGiftId || !transactionExternalId || !userGiftStatus) {
        throw new AppBaseExc(
          StatusCode.SB_DEFAULT_ERROR,
          null,
          null,
          StatusCode.API_FAILED_UNKNOWN,
          this.request,
        );
      }
    } else {
      // save user_gift
      const userGift = this.userGiftRepo.create({
        userId: user.id,
        giftId: gift.id,
        status: UserGiftStatus.PENDING,
        actionType: crmTxType.code,
        point: giftPoint,
        version: 0,
        createdDate: now,
      });
      vitaCode = await this.vitaCodeRepo.findOne({
        where: { provinceId: user.provinceId, smsStatus: false },
      });
      if (
        (gift.type === GiftType.EV_VITA_CODE ||
          gift.type === GiftTypeNew.EV_VITA_CODE) &&
        vitaCode
      ) {
        userGift.status = UserGiftStatus.USED;
        userGift.voucherCode = vitaCode.code;
        // userGift.expiryDate = dayjs().add(90, 'day').toDate();
        userGift.expiryDate = vitaCode.expiryDate;
      }

      if (
        GiftType.E_VOUCHER == gift.type ||
        gift.type == GiftTypeNew.E_VOUCHER ||
        GiftType.E_VOUCHER_SHOP == gift.type ||
        gift.type == GiftTypeNew.E_VOUCHER_SHOP ||
        GiftType.E_VOUCHER_SHOP_BKIDS == gift.type ||
        gift.type == GiftTypeNew.E_VOUCHER_SHOP_BKIDS
      ) {
        userGift.status = UserGiftStatus.NEED_REUSE;
      }

      await this.userGiftRepo.save(userGift);
      userGiftId = userGift.id;
      userGiftStatus = userGift.status;
    }

    // check user in reset identity time
    if (user.startFreezePoint && user.endFreezePoint) {
      const currentTime = getNowAtTimeZoneHcm();
      const isWithinFreezePeriod =
        compareDateBetweenFromDateIAndToDateInTimezone(
          currentTime,
          user.startFreezePoint,
          user.endFreezePoint,
        );
      if (isWithinFreezePeriod === 1) {
        throw new AppBaseExc(
          StatusCode.BLOCK_IDENTITY_SCAN,
          null,
          null,
          StatusCode.BLOCK_IDENTITY_SCAN,
        );
      }
    }

    // save history_point
    const historyPoint = this.historyPointRepo.create({
      customerId: user.id,
      customerName: user.name,
      customerPhone: user.phoneNumber,
      giftPoint: giftPoint,
      status: HistoryPointStatus.SUCCESS,
      transactionDate: now,
      type: HistoryPointType.GIFT,
      actionType: crmTxType.code,
      transactionExternalId: transactionExternalId,
      brand: product.brand,
      cdpSyncUp: false,
      userGiftId: userGiftId,
    });

    await this.historyPointRepo.save(historyPoint);

    // handle event 4 lon - send vita code to user
    if (
      (gift.type === GiftType.EV_VITA_CODE ||
        gift.type === GiftTypeNew.EV_VITA_CODE) &&
      vitaCode
    ) {
      console.log('event 4 lon');

      await this.eventEmitter.emitAsync(
        'event.handleEvent4Lon',
        vitaCode,
        user,
        historyPoint,
      );
    }

    const historyPointAttribute = this.historyPointAttributeRepo.create({
      attributeCode: HistoryPointAttributeCode.REF_TRAN_ID,
      value: transactionRefId,
      historyPointId: historyPoint.id,
    });

    await this.historyPointAttributeRepo.save(historyPointAttribute);

    // update user point
    user.giftPoint += giftPoint;
    user.totalPoint += giftPoint;

    await this.userRepo.save(user);

    // save history_point_event
    let eventPointHistory: EventPointHistory;
    if (type === 'SB') {
      eventPointHistory = this.eventPointHistoryRepo.create({
        userId: user.id,
        productId: product.id,
        spoonCode: code,
        historyPointId: historyPoint.id,
        giftName: gift.name,
        eventDetailId: eventOrdinal.id,
      });
    } else if ((type = 'SBPS')) {
      eventPointHistory = this.eventPointHistoryRepo.create({
        userId: user.id,
        productId: product.id,
        historyPointId: historyPoint.id,
        giftName: gift.name,
        eventDetailId: eventOrdinal.id,
        qrCode: code,
      });
    }

    await this.eventPointHistoryRepo.save(eventPointHistory);

    await this.syncEventPointHistoryToWhUserService.insertSyncEventPointHistoryToWh(
      eventPointHistory.id,
      userGiftStatus,
      token,
    );

    const currentDateStr = getFormatCurrentDateInTimezone(TIME_FORMAT_CRM);

    // handle crm api
    if (
      gift.id === 660 ||
      gift.id === 680 ||
      gift.id === 685 ||
      gift.id === 697 ||
      gift.id === 702 ||
      gift.id === 705 ||
      gift.id === 758
    ) {
      const request: CrmPointGiftingRequest = {
        userId: user.id,
        Transaction_Type__c: crmTxType.name,
        Type__c: 'Adding',
        Level_Points__c: 0,
        Redeem_Points__c: giftPoint,
        Transaction_External_ID__c: transactionExternalId,
        Transaction_Date__c: currentDateStr,
        Rule_Name__c: crmTxType.description,
        Campaign__c: crmTxType.campaignName,
        Tier__c: user.tierCode,
        Earned_Tier_Point__c: 0,
        Tier_Points__c: user.tierPoint,
        Transaction_Trigger_External_ID__c: transactionRefId,
      };

      requestOutboxMessage.push(request);
      // const newOutboxMessage = this.outboxMessageRepo.create({
      //   provider: SyncProvider.CRM,
      //   callType: CallType.SYNC,
      //   syncType: SyncType.IMMEDIATE,
      //   request: JSON.stringify(request),
      //   status: OutboxMessageStatus.PROCESSING,
      //   createdDate: getNowAtTimeHcm(),
      //   retryNumber: 0,
      // });
      // await this.outboxMessageRepo.save(newOutboxMessage);
      // const crmResult = await this.crmService.createTransactionPointGifting(
      //   request,
      // );

      // if (crmResult[0].status !== 0)
      //   throw new Error(
      //     `Call crm createTransactionPointGifting not success, body: ${JSON.stringify(
      //       request,
      //     )}, response: ${JSON.stringify(crmResult)}`,
      //   );
    } else if (gift.type === GiftType.GIFT || gift.type === GiftTypeNew.GIFT) {
      const request: CrmRedeemGiftRequest = {
        status_user_gift: UserGiftStatus.PENDING,
        user_gift_id: userGiftId.toString(),
        user_id: user.id,
        phoneNumber: user.phoneNumber,
        gift_id: gift.code,
        gift_name: gift.name,
        recipientName: '',
        recipientPhone: '',
        recipientAddress: '',
        action_type: crmTxType.code,
        Description__c: '',
        point: giftPoint,
        ref_id: transactionExternalId,
        Campaign__c: crmTxType.campaignName,
        quantity: '1',
        Expiry_date__c: '',
        Type__c: 'Gifting',
        Transaction_Type__c: crmTxType.name,
        Rule_Name__c: crmTxType.description,
        Transaction_Date__c: currentDateStr,
        order_id: '',
        Billing_District__c: '',
        Billing_Province__c: '',
        Billing_Street__c: '',
        Billing_Ward__c: '',
        Quality__c: '',
        Manufacturing_Date__c: '',
        Tier__c: user.tierCode,
        Tier_Points__c: user.tierPoint,
        Earned_Tier_Point__c: 0,
        Transaction_Trigger_External_ID__c: transactionRefId,
        Gifts_Source__c: sourceGift,
      };

      // requestOutboxMessage.push(request);

      const newOutboxMessage = this.outboxMessageRepo.create({
        provider: SyncProvider.CRM,
        callType: CallType.SYNC,
        syncType: SyncType.IMMEDIATE,
        request: JSON.stringify(request),
        status: OutboxMessageStatus.PROCESSING,
        createdDate: getNowAtTimeHcm(),
        retryNumber: 0,
      });
      // await this.outboxMessageRepo.save(newOutboxMessage);

      outboxMessageGift.push(newOutboxMessage);
      syncData3rdServiceToWhRequestDetails.push({
        code: JavaV4WhSyncTo3rdServiceCode.EXCHANGE_GIFT,
        destination: JavaV4WhSyncTo3rdServiceDestination.SF,
        payload: request,
      });

      // const crmResult = await this.crmService.createTransactionRedeemGift(
      //   request,
      // );

      // if (crmResult.status !== 0)
      //   throw new Error(
      //     `Call crm createTransactionRedeemGift not success, body: ${JSON.stringify(
      //       request,
      //     )}, response: ${JSON.stringify(crmResult)}`,
      //   );
    } else if (
      gift.type === GiftType.E_VOUCHER ||
      gift.type === GiftTypeNew.E_VOUCHER ||
      gift.type === GiftType.E_VOUCHER_SHOP ||
      gift.type === GiftTypeNew.E_VOUCHER_SHOP ||
      gift.type === GiftType.E_VOUCHER_SHOP_BKIDS ||
      gift.type === GiftTypeNew.E_VOUCHER_SHOP_BKIDS
    ) {
      const request: CrmRedeemVoucherRequest = {
        ref_id: transactionExternalId,
        user_id: user.id,
        phoneNumber: user.phoneNumber,
        productId: gift.eProductId
          ? gift.eProductId.toString()
          : gift.dynamicData?.eproductId?.toString(),
        productName: gift.eProductName
          ? gift.eProductName.toString()
          : gift.dynamicData?.eproductName?.toString(),
        priceValue: gift.priceValue
          ? gift.priceValue.toString()
          : gift.dynamicData?.priceValue?.toString(),
        productPriceId: gift.priceId
          ? gift.priceId.toString()
          : gift.dynamicData?.priceId?.toString(),
        evoucherId: gift.code || gift.dynamicData?.rewardAppGiftCode,
        quantity: '1',
        createdDate: '',
        status: 'NEW',
        Redeem_Points__c: giftPoint,
        Campaign__c: crmTxType.campaignName,
        Type__c: 'Gifting',
        Transaction_Type__c: crmTxType.name,
        Rule_Name__c: crmTxType.description,
        Transaction_Date__c: currentDateStr,
        Earned_Tier_Point__c: 0,
        Tier__c: user.tierCode,
        Tier_Points__c: user.tierPoint,
        Transaction_Trigger_External_ID__c: transactionRefId,
        User_Gift_Id__c: userGiftId,
        EV_Source__c: giftType,
      };

      // requestOutboxMessage.push(request);

      const newOutboxMessage = this.outboxMessageRepo.create({
        provider: SyncProvider.CRM,
        callType: CallType.SYNC,
        syncType: SyncType.IMMEDIATE,
        request: JSON.stringify(request),
        status: OutboxMessageStatus.PROCESSING,
        createdDate: getNowAtTimeHcm(),
        retryNumber: 0,
      });
      // await this.outboxMessageRepo.save(newOutboxMessage);
      outboxMessageGift.push(newOutboxMessage);
      syncData3rdServiceToWhRequestDetails.push({
        code: JavaV4WhSyncTo3rdServiceCode.EXCHANGE_VOUCHER,
        destination: JavaV4WhSyncTo3rdServiceDestination.SF,
        payload: request,
      });

      // const crmResult = await this.crmService.createTransactionRedeemVoucher(
      //   request,
      // );
      // if (crmResult.status !== 0)
      //   throw new Error(
      //     `Call crm createTransactionRedeemVoucher not success, body: ${JSON.stringify(
      //       request,
      //     )}, response: ${JSON.stringify(crmResult)}`,
      //   );
    } else if (
      (gift.type === GiftType.EV_VITA_CODE ||
        gift.type === GiftTypeNew.EV_VITA_CODE) &&
      vitaCode
    ) {
      // const expireDateStr = getFormattedCurrentDateAfterDays(
      //   90,
      //   TIME_FORMAT_CRM,
      // );
      const expireDateStr = getFormattedDateAfterDays(
        vitaCode.expiryDate,
        0,
        TIME_FORMAT_CRM,
      );
      const request: CrmRedeemVitaCodeRequest = {
        ref_id: transactionExternalId,
        user_id: user?.id?.toString(),
        phoneNumber: user?.phoneNumber,
        productId: gift.eProductId
          ? gift.eProductId.toString()
          : gift.dynamicData?.eproductId?.toString(),
        productName: gift.eProductName
          ? gift.eProductName.toString()
          : gift.dynamicData?.eproductName?.toString(),
        priceValue: gift.priceValue
          ? gift.priceValue.toString()
          : gift.price?.toString(),
        productPriceId: gift.priceId
          ? gift.priceId.toString()
          : gift.dynamicData?.priceId?.toString(),
        evoucherId: gift.code || gift.dynamicData?.rewardAppGiftCode,
        quantity: '1',
        // createdDate: '',
        status: 'NEW',
        Redeem_Points__c: giftPoint,
        Campaign__c: crmTxType?.campaignName,
        Type__c: 'Gifting',
        Transaction_Type__c: crmTxType?.name,
        Rule_Name__c: crmTxType?.description,
        Transaction_Date__c: currentDateStr,
        Tier__c: user.tierCode,
        Tier_Points__c: user.tierPoint,
        Earned_Tier_Point__c: 0,
        voucherCode: vitaCode.code,
        expiryDate: expireDateStr,
        createdDate: currentDateStr,
        Transaction_Trigger_External_ID__c: transactionRefId,
        User_Gift_Id__c: userGiftId,
        EV_Source__c: giftType,
      };

      // requestOutboxMessage.push(request);

      const newOutboxMessage = this.outboxMessageRepo.create({
        provider: SyncProvider.CRM,
        callType: CallType.SYNC,
        syncType: SyncType.IMMEDIATE,
        request: JSON.stringify(request),
        status: OutboxMessageStatus.PROCESSING,
        createdDate: getNowAtTimeHcm(),
        retryNumber: 0,
      });
      // await this.outboxMessageRepo.save(newOutboxMessage);
      outboxMessageGift.push(newOutboxMessage);
      syncData3rdServiceToWhRequestDetails.push({
        code: JavaV4WhSyncTo3rdServiceCode.EXCHANGE_VOUCHER,
        destination: JavaV4WhSyncTo3rdServiceDestination.SF,
        payload: request,
      });

      // const crmResult = await this.crmService.createTransactionRedeemVoucher(
      //   request,
      // );
      // if (crmResult.status !== 0)
      //   throw new Error(
      //     `Call crm createTransactionRedeemVoucher not success, body: ${JSON.stringify(
      //       request,
      //     )}, response: ${JSON.stringify(crmResult)}`,
      //   );
    }

    // Setup topup user gift if gift of event quy 3 2024 CLBB evoucher topup
    if (
      eventOrdinal.eventId >
        Number(this.configService.get('event.eventLatestId')) &&
      gift.type === GiftTypeNew.E_VOUCHER
    ) {
      postCommitActions.push(() =>
        this.reuseUserGift(userGiftId, user.id, token),
      );
    } else if (
      GiftIdInEvent.EV_CLBB_EVOUCHER_TOPUP_24_Q3_GIFT_TOPUP_50K == gift.id ||
      GiftIdInEvent.EV_CLBB_EVOUCHER_TOPUP_24_Q3_GIFT_TOPUP_20K == gift.id ||
      GiftIdInEvent.EV_WA_SBPS_2024_GIFT_TOPUP_20K == gift.id ||
      GiftIdInEvent.EV_WA_SBPS_2024_GIFT_TOPUP_10K == gift.id
    ) {
      postCommitActions.push(() =>
        this.reuseUserGift(userGiftId, user.id, token),
      );
    }
  }

  private async redeemGiftMayRuiEventQuy12024(
    userSessionData: UserSessionData,
    eventUser: EventUser,
    eventLimit: EventLimit,
    eventOrdinal: EventDetail,
    user: User,
    now: Date,
    eventUserLimit: EventUserLimit,
    reqData: TriggerEventMayRuiQ12024ReqDto,
    eventDetailProvince?: EventDetailProvince,
    token?: string,
  ) {
    // check user in reset identity time
    if (user.startFreezePoint && user.endFreezePoint) {
      const currentTime = getNowAtTimeZoneHcm();
      const isWithinFreezePeriod =
        compareDateBetweenFromDateIAndToDateInTimezone(
          currentTime,
          user.startFreezePoint,
          user.endFreezePoint,
        );
      if (isWithinFreezePeriod === 1) {
        throw new AppBaseExc(
          StatusCode.BLOCK_IDENTITY_SCAN,
          null,
          null,
          StatusCode.BLOCK_IDENTITY_SCAN,
        );
      }
    }

    const eventQuy12024MayRuiId = this.configService.get(
      'event.eventQuy12024MayRui',
    );
    this.decreaseWinrate(eventUser, eventLimit.downRate);
    let promisesSaveData: any[] = [];
    eventOrdinal.quantity--;
    promisesSaveData.push(this.eventDetailRepo.save(eventOrdinal));
    eventUserLimit.userLimit--;
    promisesSaveData.push(this.eventUserLimitRepo.save(eventUserLimit));
    if (eventDetailProvince) {
      eventDetailProvince.quantity--;
      promisesSaveData.push(
        this.eventDetailProvinceRepo.save(eventDetailProvince),
      );
    }
    await Promise.allSettled(promisesSaveData);

    const transactionExternalId = randomTransactionExternalId();
    const giftPoint = 0;
    const [crmTxType] = await Promise.all([
      this.crmTransactionTypeRepo.findOneBy({
        id: eventOrdinal.crmTransactionTypeId,
      }),
    ]);

    let gift: any;
    if (
      eventOrdinal.eventId >
      Number(this.configService.get('event.eventLatestId'))
    ) {
      gift = await this.vitaJavaService.getGiftById(
        token,
        eventOrdinal.gsGiftId,
      );
    } else {
      gift = await this.giftRepo.findOneBy({
        id: eventOrdinal.giftId,
      });
    }

    promisesSaveData = [];
    let vacxinCode = null;

    const popupCode = this.getPopupCode(eventOrdinal);
    // save user_gift
    const userGift = this.userGiftRepo.create({
      userId: user.id,
      giftId: gift.id,
      status: UserGiftStatus.PENDING,
      actionType: crmTxType.code,
      point: 0,
      version: 0,
      createdDate: now,
    });
    if (PopupV2Code.QUY1_2024_MAY_RUI_VACXIN == popupCode) {
      const requestData: HandleEventEvoucherVacxinReqDto = {
        eventSource: `EVQ_${eventQuy12024MayRuiId}`,
      };
      try {
        vacxinCode = await this.eventCustomerService.handleEventEvoucherVacxin(
          userSessionData,
          requestData,
          true,
        );
        userGift.status = UserGiftStatus.USED;
        if ('string' === typeof vacxinCode) {
          userGift.voucherCode = vacxinCode;
          const vitaCode = await this.vitaCodeRepo.findOneBy({
            code: vacxinCode,
          });
          if (vitaCode) {
            userGift.expiryDate = vitaCode.expiryDate;
          }
        }
      } catch (error) {
        vacxinCode = '';
      }
    }
    await this.userGiftRepo.save(userGift);

    // save history_point
    const historyPoint = this.historyPointRepo.create({
      customerId: user.id,
      customerName: user.name,
      customerPhone: user.phoneNumber,
      giftPoint: giftPoint,
      status: HistoryPointStatus.SUCCESS,
      transactionDate: now,
      type: HistoryPointType.GIFT,
      actionType: crmTxType.code,
      transactionExternalId: transactionExternalId,
      brand: null,
      cdpSyncUp: false,
      userGiftId: userGift.id,
    });
    await this.historyPointRepo.save(historyPoint);
    const historyPointAttribute = this.historyPointAttributeRepo.create({
      attributeCode: HistoryPointAttributeCode.REF_TRAN_ID,
      value: reqData.txnId,
      historyPointId: historyPoint.id,
    });
    await this.historyPointAttributeRepo.save(historyPointAttribute);

    // import to record of identity point service
    const actionTypeIdentity =
      IdentityPointActionType[crmTxType.code] || 'verifyPoint';
    const isDuplicateQr = false;

    await this.saveIdentityRecord(
      user,
      crmTxType,
      actionTypeIdentity,
      transactionExternalId,
      giftPoint,
      isDuplicateQr,
    );

    const eventPointHistory: EventPointHistory =
      this.eventPointHistoryRepo.create({
        userId: user.id,
        historyPointId: historyPoint.id,
        giftName: gift.name,
        eventDetailId: eventOrdinal.id,
      });
    promisesSaveData.push(this.eventPointHistoryRepo.save(eventPointHistory));

    // handle crm api
    if (gift.type === GiftType.GIFT || gift.type === GiftTypeNew.GIFT) {
      const request: CrmRedeemGiftRequest = {
        status_user_gift: UserGiftStatus.PENDING,
        user_gift_id: userGift.id.toString(),
        user_id: user.id,
        phoneNumber: user.phoneNumber,
        gift_id: gift.code,
        gift_name: gift.name,
        recipientName: '',
        recipientPhone: '',
        recipientAddress: '',
        action_type: crmTxType.code,
        Description__c: '',
        point: giftPoint,
        ref_id: transactionExternalId,
        Campaign__c: crmTxType.campaignName,
        quantity: '1',
        Expiry_date__c: '',
        Type__c: 'Gifting',
        Transaction_Type__c: crmTxType.name,
        Rule_Name__c: crmTxType.description,
        Transaction_Date__c: dayjs().tz(TIME_ZONE).format(TIME_FORMAT_CRM),
        order_id: '',
        Billing_District__c: '',
        Billing_Province__c: '',
        Billing_Street__c: '',
        Billing_Ward__c: '',
        Quality__c: '',
        Manufacturing_Date__c: '',
        Tier__c: user.tierCode,
        Tier_Points__c: user.tierPoint,
        Earned_Tier_Point__c: 0,
        Transaction_Trigger_External_ID__c: reqData.txnId,
      };

      const newOutboxMessage = this.outboxMessageRepo.create({
        provider: SyncProvider.CRM,
        callType: CallType.SYNC,
        syncType: SyncType.IMMEDIATE,
        request: JSON.stringify(request),
        status: OutboxMessageStatus.PROCESSING,
        createdDate: getNowAtTimeHcm(),
        retryNumber: 0,
      });
      promisesSaveData.push(this.outboxMessageRepo.save(newOutboxMessage));
    } else if (
      PopupV2Code.QUY1_2024_MAY_RUI_VACXIN == popupCode &&
      '' != vacxinCode
    ) {
      const request: CrmRedeemVitaCodeRequest = {
        ref_id: transactionExternalId,
        user_id: user?.id?.toString(),
        phoneNumber: user?.phoneNumber,
        productId: gift.eProductId
          ? gift.eProductId.toString()
          : gift.dynamicData?.eproductId?.toString(),
        productName: gift.eProductName
          ? gift.eProductName.toString()
          : gift.dynamicData?.eproductName?.toString(),
        priceValue: gift.priceValue
          ? gift.priceValue.toString()
          : gift.dynamicData?.priceValue?.toString(),
        productPriceId: gift.priceId
          ? gift.priceId.toString()
          : gift.dynamicData?.priceId?.toString(),
        evoucherId: gift.code || gift.dynamicData?.rewardAppGiftCode,
        quantity: '1',
        // createdDate: '',
        status: 'NEW',
        Redeem_Points__c: giftPoint,
        Campaign__c: crmTxType?.campaignName,
        Type__c: 'Gifting',
        Transaction_Type__c: crmTxType?.name,
        Rule_Name__c: crmTxType?.description,
        Transaction_Date__c: dayjs().tz(TIME_ZONE).format(TIME_FORMAT_CRM),
        Earned_Tier_Point__c: 0,
        Tier__c: user.tierCode,
        Tier_Points__c: user.tierPoint,
        Transaction_Trigger_External_ID__c: reqData.txnId,
        voucherCode: vacxinCode,
        createdDate: dayjs().tz(TIME_ZONE).format(TIME_FORMAT_CRM),
        expiryDate: null,
      };

      const newOutboxMessage = this.outboxMessageRepo.create({
        provider: SyncProvider.CRM,
        callType: CallType.SYNC,
        syncType: SyncType.IMMEDIATE,
        request: JSON.stringify(request),
        status: OutboxMessageStatus.PROCESSING,
        createdDate: getNowAtTimeHcm(),
        retryNumber: 0,
      });
      promisesSaveData.push(this.outboxMessageRepo.save(newOutboxMessage));
    }

    await Promise.allSettled(promisesSaveData);
  }

  async handleEvent4Lon(
    vitaCode: VitaCode,
    user: User,
    historyPoint: HistoryPoint,
  ) {
    console.log('on handle event 4 Lon');
    // TODO: Kiểm tra trường hợp lấy quà vita code theo provinceId
    // Hiện tại đang theo luồng lấy quà vita code theo eventSource
    // vitaCode.createdDate = dayjs().tz(TIME_ZONE).toDate();
    // vitaCode.expiryDate = dayjs().add(90, 'day').toDate();

    // Send vita code to user via sms
    await this.vgsService.sendVitaCodeSms(
      user.phoneNumber,
      SendZnsSource.ADD_CAN_EVENT_4_LON,
      vitaCode,
    );
    // Send vita code to google sheet
    try {
      await this.sendUserInfoToGoogleSheet(user, vitaCode);
    } catch (err) {
      this.logger.debug('handleEvent4Lon sendUserInfoToGoogleSheet fail');
      console.log(err);
    }
    // Update vita code
    // vitaCode.smsStatus = true;
    vitaCode.sentStatus = true;
    vitaCode.historyPointId = historyPoint.id;
    await this.vitaCodeRepo.save(vitaCode);
  }

  async triggerEvents(
    user: User,
    userUpdated: User,
    product: Product,
    spoon: Spoon,
    productData: ProductDataGotByQrDto,
    transactionExternalId: string,
    arrNoti: any[],
    appversionname: string,
    requestOutboxMessage: RequestOutboxMessage,
    token: string,
    outboxMessageGift: OutboxMessage[],
    syncData3rdServiceToWhRequestDetails: SyncData3rdServiceToWhRequestDetail[],
    typeAddPoint: string,
    source: ScanHistoryApiType,
    eventTrigger: EventType[],
    eventAddCanTypeTrigger: EventAddCanType[],
    arrTemplate2468Noti: any[],
    crmSyncUsingWh: boolean,
    historyPoint: HistoryPoint,
    dto: AddPointReqDto,
  ): Promise<any[]> {
    const arrNotiLength = arrNoti.length;
    const { spoonCode, qrCode } = dto;

    await this.eventEmitter.emitAsync(
      'event.Q2',
      userUpdated,
      product,
      spoon.spoonCode,
      productData,
      transactionExternalId,
      arrNoti,
      appversionname,
      requestOutboxMessage,
      token,
      typeAddPoint,
      outboxMessageGift,
      syncData3rdServiceToWhRequestDetails,
      eventTrigger,
    );

    let isEventQ32024ClbbMassSamplingTriggered = false;
    const arrNotiEventQ32024ClbbMassSampling: any[] = [];
    await this.eventEmitter.emitAsync(
      'event.Q32024ClbbMassSampling',
      userUpdated,
      product,
      spoon.spoonCode,
      productData,
      transactionExternalId,
      arrNotiEventQ32024ClbbMassSampling,
      appversionname,
      requestOutboxMessage,
      token,
      outboxMessageGift,
      syncData3rdServiceToWhRequestDetails,
      typeAddPoint,
    );
    if (arrNotiEventQ32024ClbbMassSampling.length) {
      isEventQ32024ClbbMassSamplingTriggered = true;
      arrNoti = arrNotiEventQ32024ClbbMassSampling;
    }

    await this.eventEmitter.emitAsync(
      EVENT_EMITTER_NAME.EVENT_2468,
      userUpdated,
      product,
      spoon.spoonCode,
      productData,
      transactionExternalId,
      arrTemplate2468Noti,
      appversionname,
      requestOutboxMessage,
      eventAddCanTypeTrigger,
      typeAddPoint,
    );

    let isEventThang52024BigC = false;
    let isEventQuy32024ClbbEvoucherTopup = false;
    for (const type of eventTrigger) {
      if (EventType.EV_BIGC_24_Q3 == type) {
        isEventThang52024BigC = true;
      }
      if (EventType.EV_CLBB_EVOUCHER_TOPUP_24_Q3 == type) {
        isEventQuy32024ClbbEvoucherTopup = true;
      }
    }
    const eventBigCHasPopupCode =
      isEventThang52024BigC && arrNotiLength != arrNoti.length;
    const eventQuy32024ClbbEvoucherTopupHasPopupCode =
      isEventQuy32024ClbbEvoucherTopup && arrNotiLength != arrNoti.length;
    let isEventCLBBQ32024Trigger = false;
    for (const type of eventAddCanTypeTrigger) {
      if (EventAddCanType.EVQ3_CLBB_24 == type) {
        isEventCLBBQ32024Trigger = true;
      }
    }
    if (!isEventQ32024ClbbMassSamplingTriggered) {
      if (eventQuy32024ClbbEvoucherTopupHasPopupCode) {
        arrNoti = arrNoti;
      } else if (isEventCLBBQ32024Trigger && arrTemplate2468Noti.length) {
        arrNoti = arrTemplate2468Noti;
      } else if (!eventBigCHasPopupCode) {
        arrNoti = arrNoti.concat(arrTemplate2468Noti);
      }
    }

    // hotline add point -> do not trigger event CLS
    if (!source || source != ScanHistoryApiType.HOTLINE) {
      const {
        notificationCodes = [],
        postCommitActions = [],
        // NOTE: comment for not add request receive gift
        // clsOutBoxMessageRequests = [],
        outBoxMessages = [],
        syncData3rdServiceToWhRequestDetailArr = [],
      } = await this.handleEventClsPhase1(
        new EventClsQ4Payload({
          productData,
          token,
          spoon,
          user,
          appversionname,
          product,
          transactionRefId: transactionExternalId,
          isInTransaction: true,
        }),
      );
      arrNoti = notificationCodes?.length ? notificationCodes : arrNoti;
      // NOTE: push outbox messages from CLS to outboxMessageGift for bulk insert
      outboxMessageGift.push(...outBoxMessages);
      syncData3rdServiceToWhRequestDetails.push(
        ...syncData3rdServiceToWhRequestDetailArr,
      );

      runOnTransactionCommit(() => {
        asyncMapSettled(postCommitActions, (action) => action());
      });

      // NOTE: add request receive gift
      // requestOutboxMessage.push(...clsOutBoxMessageRequests);
    }

    if (crmSyncUsingWh) {
      const syncAddPointData3rdServiceToWhRequestDetails: SyncData3rdServiceToWhRequestDetail[] =
        [];
      syncAddPointData3rdServiceToWhRequestDetails.push(
        this.vitaJavaService.generateSyncData3rdServiceToWhRequestDetail(
          requestOutboxMessage,
          JavaV4WhSyncTo3rdServiceDestination.SF,
          JavaV4WhSyncTo3rdServiceCode.ADD_POINT,
        ),
      );
      syncAddPointData3rdServiceToWhRequestDetails.push(
        this.vitaJavaService.generateSyncData3rdServiceToWhRequestDetail(
          requestOutboxMessage,
          JavaV4WhSyncTo3rdServiceDestination.SAP,
          JavaV4WhSyncTo3rdServiceCode.ADD_POINT_MAMUONG,
        ),
      );
      const whAddPointOutboxMessageRequestData =
        this.vitaJavaService.generateWhOutboxMessageSyncData3rdServiceRequestDataInBatch(
          token,
          syncAddPointData3rdServiceToWhRequestDetails,
        );
      const outboxAddPointSyncWh =
        this.outboxMessageRepo.createSyncWhSyncData3rdService(
          whAddPointOutboxMessageRequestData,
        );
      await this.outboxMessageRepo.save(outboxAddPointSyncWh);

      if (syncData3rdServiceToWhRequestDetails.length) {
        const whOutboxMessageRequestData =
          this.vitaJavaService.generateWhOutboxMessageSyncData3rdServiceRequestDataInBatch(
            token,
            syncData3rdServiceToWhRequestDetails,
          );
        const outboxSyncWh =
          this.outboxMessageRepo.createSyncWhSyncData3rdService(
            whOutboxMessageRequestData,
          );
        runOnTransactionCommit(() => {
          this.outboxMessageRepo.save(outboxSyncWh);
        });
      }
    } else {
      const newOutboxMessage = this.outboxMessageRepo.create({
        provider: SyncProvider.CRM,
        callType: CallType.SYNC,
        syncType: SyncType.IMMEDIATE,
        request: JSON.stringify(requestOutboxMessage),
        status: OutboxMessageStatus.PROCESSING,
        createdDate: getNowAtTimeHcm(),
        retryNumber: 0,
      });

      await this.outboxMessageRepo.save(newOutboxMessage);

      if (outboxMessageGift.length) {
        runOnTransactionCommit(() => {
          this.outboxMessageRepo.insert(outboxMessageGift);
        });
      }
    }

    console.log('arrNoti', arrNoti);
    //handle add transaction to Triplayz third party Transaction
    this.eventEmitter.emit(TRIPLAYZ_EVENT_NAME.ADD_TRANSACTION, {
      token,
      historyPoint,
      user,
      product,
      dto,
      productData,
      crmPointRequest: requestOutboxMessage?.[0],
    });

    const eventQuy32024UpRankData: EventQuy32024UpRankData = {
      user,
      requestOutboxMessage,
      product,
      token,
      spoonCode,
      qrCode,
    };
    const {
      postCommitActions = [],
      popupCode = '',
      gift = null,
      triggered = false,
    } = await this.handleEventQ32024UpRank(eventQuy32024UpRankData);
    if (triggered) {
      arrNoti = [];
      if (postCommitActions && postCommitActions.length) {
        runOnTransactionCommit(() => {
          asyncMapSettled(postCommitActions, (action) => action());
        });
      }
      await Promise.all([
        this.pushPopupV2(user.id, popupCode, arrNoti, appversionname),
        this.pushEventQ1Notification(user.id, gift),
      ]);
    }

    // Send noti enough point to finish pre order user gift
    if (user.giftPoint) {
      const request: GetUserGiftPreOrderEnoughPointToSendNotifyRequest = {
        userTotalPoint: user.giftPoint,
      };
      const response: GetUserGiftPreOrderEnoughPointToSendNotifyResponse =
        await this.vitaJavaService.getUserGiftPreOrderEnoughPointToSendNotify(
          token,
          request,
        );
      if (response) {
        const giftName = response?.data?.gift?.name;
        if (giftName) {
          try {
            const useNotiV3 = this.configService.get('useNotiV3');
            if (useNotiV3) {
              const outboxesMsg: OutboxMessage[] = [];
              for (const name of giftName) {
                const kafkaDto = new PushNotiKafkaDto({
                  userIds: [user.id],
                  version: PushNotiKafkaDtoVersion.V1,
                  notiDisplayTemplateParams: {
                    gift_name: name,
                  },
                  notiDisplayTemplateType:
                    NotiDisplayTemplateType.ENOUGH_POINT_TO_FINISH_PRE_ORDER_USER_GIFT,
                  featureNoti: FeatureNoti.NOTI_PRE_ORDER_GIFT,
                });

                const outboxMsg =
                  this.outboxMessageRepo.createPushNoti(kafkaDto);
                outboxesMsg.push(outboxMsg);
              }

              await this.outboxMessageRepo.save(outboxesMsg);
            }
          } catch (err) {
            console.log(
              `error when push notification finish pre order user gift,`,
              err,
            );
          }
        }
      }
    }

    return arrNoti;
  }

  private async sendUserInfoToGoogleSheet(user: User, vitaCode: VitaCode) {
    try {
      const dataToGoogleSheet: Event4LonToGoogleSheet = {
        code: vitaCode.code,
        createdDate: vitaCode.createdDate
          ? dayjs(vitaCode.createdDate).tz(TIME_ZONE).format('DD-MM-YYYY')
          : '',
        hospital: vitaCode.hospital,
        usedDate: '90',
        expiredDate: vitaCode.expiryDate
          ? dayjs(vitaCode.expiryDate).tz(TIME_ZONE).format('DD-MM-YYYY')
          : '',
        space: '',
        fullName: user.getFullName(),
        phoneNumber: user.phoneNumber,
      };
      await this.googleSheetService.writeData(
        dataToGoogleSheet,
        this.configService.get('googleSheet.event4LonSpreadSheetID'),
      );
    } catch (error) {
      console.log(error);
      throw new HttpException(`${error}`, HttpStatus.BAD_REQUEST);
    }
  }

  private async redeemGiftProduct01(
    eventUser: EventUser,
    eventLimit: EventLimit,
    eventOrdinal: EventDetail,
    product: Product,
    user: User,
    now: Date,
    eventUserLimit: EventUserLimit,
    transactionRefId: string,
    code: string,
    requestOutboxMessage: RequestOutboxMessage,
    eventDetailProvince?: EventDetailProvince,
    eventDetailToStore?: EventDetailToStore,
  ) {
    // check user in reset identity time
    if (user.startFreezePoint && user.endFreezePoint) {
      const currentTime = getNowAtTimeZoneHcm();
      const isWithinFreezePeriod =
        compareDateBetweenFromDateIAndToDateInTimezone(
          currentTime,
          user.startFreezePoint,
          user.endFreezePoint,
        );
      if (isWithinFreezePeriod === 1) {
        throw new AppBaseExc(
          StatusCode.BLOCK_IDENTITY_SCAN,
          null,
          null,
          StatusCode.BLOCK_IDENTITY_SCAN,
        );
      }
    }

    this.decreaseWinrate(eventUser, eventLimit.downRate);
    eventOrdinal.quantity--;
    await this.eventDetailRepo.save(eventOrdinal);
    eventUserLimit.userLimit--;
    await this.eventUserLimitRepo.save(eventUserLimit);
    if (eventDetailProvince) {
      eventDetailProvince.quantity--;
      await this.eventDetailProvinceRepo.save(eventDetailProvince);
    }
    if (eventDetailToStore) {
      eventDetailToStore.quantity--;
      await this.eventDetailToStoreRepo.save(eventDetailToStore);
    }

    const crmTxType = await this.crmTransactionTypeRepo.findOneBy({
      id: eventOrdinal.crmTransactionTypeId,
    });
    const transactionExternalId = randomTransactionExternalId();

    // gift
    const gift = await this.giftRepo.findOneBy({
      id: eventOrdinal.giftId,
    });

    const giftPoint = 0;
    // if (gift.id === 686) {
    //   giftPoint = 1.5;
    // } else if (gift.id === 687) {
    //   giftPoint = 2;
    // } else if (gift.id === 688) {
    //   giftPoint = 3;
    // } else if (gift.id === 689) {
    //   giftPoint = 4;
    // }

    // save user_gift
    const userGift = this.userGiftRepo.create({
      userId: user.id,
      giftId: gift.id,
      status: UserGiftStatus.PENDING,
      actionType: crmTxType.code,
      point: giftPoint,
      version: 0,
      createdDate: now,
    });

    // save history_point
    const historyPoint = this.historyPointRepo.create({
      customerId: user.id,
      customerName: user.name,
      customerPhone: user.phoneNumber,
      giftPoint: giftPoint,
      status: HistoryPointStatus.SUCCESS,
      transactionDate: now,
      type: HistoryPointType.GIFT,
      actionType: crmTxType.code,
      transactionExternalId: transactionExternalId,
      brand: product.brand,
      cdpSyncUp: false,
      userGiftId: userGift.id,
    });

    await this.historyPointRepo.save(historyPoint);

    const historyPointAttribute = this.historyPointAttributeRepo.create({
      attributeCode: HistoryPointAttributeCode.REF_TRAN_ID,
      value: transactionRefId,
      historyPointId: historyPoint.id,
    });

    await this.historyPointAttributeRepo.save(historyPointAttribute);

    // import to record of identity point service

    const isDuplicateQr = false;
    const actionTypeIdentity =
      IdentityPointActionType[crmTxType.code] || 'verifyPoint';

    await this.saveIdentityRecord(
      user,
      crmTxType,
      actionTypeIdentity,
      transactionExternalId,
      giftPoint,
      isDuplicateQr,
      product,
    );

    // update user point
    user.giftPoint += giftPoint;
    user.totalPoint += giftPoint;

    await this.userRepo.save(user);

    // save history_point_event
    const eventPointHistory = this.eventPointHistoryRepo.create({
      userId: user.id,
      productId: product.id,
      spoonCode: code,
      historyPointId: historyPoint.id,
      giftName: gift.name,
      eventDetailId: eventOrdinal.id,
    });

    await this.eventPointHistoryRepo.save(eventPointHistory);

    // handle crm api
    // if (
    //   gift.id === 686 ||
    //   gift.id === 687 ||
    //   gift.id === 688 ||
    //   gift.id === 689
    // ) {
    //   const request: CrmPointGiftingRequest = {
    //     userId: user.id,
    //     Transaction_Type__c: crmTxType.name,
    //     Type__c: 'Adding',
    //     Level_Points__c: 0,
    //     Redeem_Points__c: giftPoint,
    //     Transaction_External_ID__c: transactionExternalId,
    //     Transaction_Date__c: dayjs().tz(TIME_ZONE).format(TIME_FORMAT_CRM),
    //     Rule_Name__c: crmTxType.description,
    //     Campaign__c: crmTxType.campaignName,
    //     Tier__c: user.tierCode,
    //     Earned_Tier_Point__c: 0,
    //     Tier_Points__c: user.tierPoint,
    //     Transaction_Trigger_External_ID__c: transactionRefId,
    //   };

    //   requestOutboxMessage.push(request);
    //   // const newOutboxMessage = this.outboxMessageRepo.create({
    //   //   provider: SyncProvider.CRM,
    //   //   callType: CallType.SYNC,
    //   //   syncType: SyncType.IMMEDIATE,
    //   //   request: JSON.stringify(request),
    //   //   status: OutboxMessageStatus.PROCESSING,
    //   //   createdDate: getNowAtTimeHcm(),
    //   //   retryNumber: 0,
    //   // });
    //   // await this.outboxMessageRepo.save(newOutboxMessage);
    // }
  }

  private async getUserType(user: User, startDate: Date) {
    const store = await this.storeRepo.findOneBy({
      phoneNumber: user.phoneNumber,
    });
    if (store) return UserTypeV2.STORE;
    const isNewUser = await this.isNewUser(user, startDate);
    return isNewUser ? UserTypeV2.NEW_USER : UserTypeV2.CUSTOMER;
  }

  private async isNewUser(user: User, startDate: Date) {
    //return dayjs(user.createdDate).isAfter(startDate);
    // Type of createdDate is timestamp without timezone
    // We need convert to string to make sure dayjs parsing to tz work well
    // Type of startDate is timestamp with timezone
    // Don't need do this
    const compare = compareDateWithDateInTimezoneNewVersion(
      user.createdDate.toUTCString(),
      startDate,
    );
    return 1 == compare;
  }

  private async increaseWinrate(eventUser: EventUser, uprate: number) {
    const newWinRate = eventUser.winRate + uprate;
    if (newWinRate > 100) {
      eventUser.winRate = 100;
    } else {
      eventUser.winRate = newWinRate;
    }
    await this.eventUserRepo.save(eventUser);
  }

  private async decreaseWinrate(eventUser: EventUser, downrate: number) {
    const newWinRate = eventUser.winRate - downrate;
    eventUser.winRate = newWinRate;
    await this.eventUserRepo.save(eventUser);
  }

  private async countNumOfCans(
    skus: Array<string>,
    startTime: Date,
    userId: number,
  ) {
    const numberOfCans = await this.historyPointRepo
      .createQueryBuilder('hp')
      .innerJoinAndSelect('hp.historyPointAttributes', 'hpa')
      .where('hp.customerId = :customerId', { customerId: userId })
      .andWhere('hp.transactionDate >= :startTime', { startTime })
      .andWhere('hp.type = :type', { type: 'ADD_POINT' })
      .andWhere('hp.actionType IN(:...actionTypes)', {
        actionTypes: ['QR_CODE', 'FIRST_SCAN'],
      })
      .andWhere('hpa.attributeCode = :code', { code: 'PRODUCT_CODE' })
      .andWhere('hpa.value IN(:...values)', {
        values: skus,
      })
      .getCount();
    return numberOfCans;
  }

  async createAddPointTransaction(
    dto: AddPointReqDto,
    user: User,
    product: Product,
    productData: ProductDataGotByQrDto,
    isFirstScan: boolean,
    spoon: Spoon,
    token: string,
    transactionExternalId: string,
    requestOutboxMessage: RequestOutboxMessage,
    isDuplicateQr = false,
  ) {
    const { qrCode, spoonCode } = dto;

    const [crmTransType, crmFirstScanTransType] = await Promise.all([
      this.crmTransactionTypeRepo.findOneBy({
        mainCode: CrmTransactionTypeMainCode.QR_CODE,
      }),
      isFirstScan &&
        this.crmTransactionTypeRepo.findOneBy({
          mainCode: CrmTransactionTypeMainCode.FIRST_SCAN,
        }),
    ]);

    // const transactionExternalId = randomTransactionExternalId();

    const request: CreateAddPointTransactionReqDto = {
      userId: user.id,
      Transaction_Type__c: crmTransType.name,
      Type__c: CrmTransactionTypeRequest.ADDING,
      Level_Points__c: isDuplicateQr ? 0 : product.levelPoint,
      Redeem_Points__c: isDuplicateQr ? 0 : product.point,
      Transaction_External_ID__c: transactionExternalId,
      Transaction_Date__c: dayjs().tz(TIME_ZONE).format(TIME_FORMAT_CRM),
      Point_Transfer_Status__c: PointTransferStatus.TRANSFERRED,
      Rule_Name__c: isFirstScan
        ? crmFirstScanTransType.description
        : crmTransType.description,
      Program_Name__c: this.configService.get('crm.auth.programName'),
      Manufacturing_Date__c:
        productData.manufactureDate ||
        dayjs().tz(TIME_ZONE).format(TIME_FORMAT_CRM),
      Distributor_Code__c: productData.distributorCode,
      Distributor_Name__c: productData.distributorName,
      Distributor_Address__c: productData.distributorAddress,
      Store_ID__c: productData.storeId,
      Store_Name__c: productData.storeName,
      Store_Address__c: productData.storeAddress,
      Spoon_Code_Create_Date__c: dayjs(spoon.createdDate).format('YYYY-MM-DD'),
      Product_Name__c: product.name,
      Product_Code__c: product.code,
      Amount__c: Math.round(product.money),
      Expiry_date__c: productData.expireDate,
      QR_Code__c: qrCode,
      Spoon_Code__c: spoonCode,
      Campaign__c: crmTransType.campaignName,
      Tier__c: user.tierCode,
      Earned_Tier_Point__c: isDuplicateQr ? 0 : product.levelPoint,
      Tier_Points__c: user.tierPoint,
    };

    requestOutboxMessage.push(request);

    // const newOutboxMessage = this.outboxMessageRepo.create({
    //   provider: SyncProvider.CRM,
    //   callType: CallType.SYNC,
    //   syncType: SyncType.IMMEDIATE,
    //   request: JSON.stringify(request),
    //   status: OutboxMessageStatus.PROCESSING,
    //   createdDate: getNowAtTimeHcm(),
    //   retryNumber: 0,
    // });

    // await this.outboxMessageRepo.save(newOutboxMessage);

    // let crmResult: BaseResponseTransaction[];
    // const saveExternalApiRequest: SaveExternalApiRequest = {};
    // try {
    //   crmResult = await this.crmService.createAddPointTransaction(
    //     request,
    //     saveExternalApiRequest,
    //   );
    //   if (crmResult[0].status !== 0)
    //     throw new Error(
    //       `Call crm createAddPointTransaction not success, body: ${JSON.stringify(
    //         request,
    //       )}, response: ${JSON.stringify(crmResult)}`,
    //     );
    // } catch (error) {
    //   Logger.error('Call crm error', error);

    //   saveExternalApiRequest.isSuccess = false;
    //   const response = await this.vitaGoService.saveExternalApi(
    //     saveExternalApiRequest,
    //     token,
    //   );

    //   if (response) {
    //     console.log('Error saveExternalApi, response: ', response);
    //     throw new Error(`Call crm failed, and saveExternalApi failed`);
    //   }
    // }

    const historyPoint = await this.createAddPointHistoryPoint({
      dto,
      user,
      product,
      productData,
      isFirstScan,
      spoon,
      transactionExternalId,
      isDuplicateQr,
    });

    if (!isDuplicateQr) {
      await Promise.all([
        this.handleStoreInvitation(historyPoint, isFirstScan, user),
        this.pushAddPointNotification(user.id, product, dto),
      ]);
    }

    return historyPoint;
  }

  private async handleStoreInvitation(
    historyPoint: HistoryPoint,
    isFirstScan: boolean,
    user: User,
  ) {
    if (!isFirstScan) return;

    const storeInvation = await this.storeInvitationRepo.findOneBy({
      userId: user.id,
    });
    if (storeInvation) {
      storeInvation.historyPointId = historyPoint.id;
      await this.storeInvitationRepo.save(storeInvation);
    }
  }

  async pushAddPointToPubsub(
    user: User,
    dto: AddPointReqDto,
    productData: ProductDataGotByQrDto,
    product: Product,
    historyPoint: HistoryPoint,
  ) {
    const { qrCode, spoonCode } = dto;

    const pubsubData: AddPoint = {
      userId: user.id,
      qrCode: spoonCode,
      spoonCode: qrCode,
      productCode: product.code,
      exportDate: productData.exportDate,
      manufactureDate: productData.manufactureDate,
      provinceId: product.id,
      giftPoint: productData.point,
      fistScan: false,
      txId: historyPoint.transactionExternalId,
      historyPointId: historyPoint.id,
      brand: product.brand,
      Weight: productData.weight,
      tierUp: false,
      expireDate: productData.expireDate,
      levelPoint: product.levelPoint,
    };

    const addPointMessage = await load(
      join(
        __dirname + '/../../../node_modules/vtd-common-v3/proto/message.proto',
      ),
    ).then((root) => root.lookupType(`${protobufPackage}.AddPoint`));
    const buffer = addPointMessage.encode(pubsubData).finish();
    await this.gcPubsubService.pushAddPointToPubsub(buffer);
  }

  private async createAddPointHistoryPoint(data: {
    dto: AddPointReqDto;
    user: User;
    product: Product;
    productData: ProductDataGotByQrDto;
    isFirstScan: boolean;
    spoon: Spoon;
    transactionExternalId: string;
    isDuplicateQr: boolean;
  }) {
    const {
      dto,
      isFirstScan,
      product,
      productData,
      spoon,
      transactionExternalId,
      user,
      isDuplicateQr,
    } = data;
    const { qrCode, spoonCode, feSource } = dto;

    const actionType = isFirstScan
      ? 'FIRST_SCAN'
      : CrmTransactionTypeMainCode.QR_CODE;

    const historyPoint = this.historyPointRepo.create({
      customerId: user.id,
      customerName: user.getFullName(),
      customerPhone: user.phoneNumber,
      giftPoint: isDuplicateQr ? 0 : product.point,
      tierPoint: isDuplicateQr ? 0 : product.levelPoint,
      status: isDuplicateQr
        ? HistoryPointStatus.FAILED
        : HistoryPointStatus.SUCCESS,
      transactionDate: getNowAtTimeZoneHcm(),
      type: HistoryPointType.ADD_POINT,
      actionType,
      money: Math.round(product.money),
      transactionExternalId,
      brand: product.brand,
      cdpSyncUp: false,
    });

    await this.historyPointRepo.save(historyPoint);

    const historyPointAttributes: HistoryPointAttribute[] = [
      this.historyPointAttributeRepo.create({
        attributeCode: HistoryPointAttributeCode.QR_CODE,
        value: qrCode,
        historyPointId: historyPoint.id,
      }),
      this.historyPointAttributeRepo.create({
        attributeCode: HistoryPointAttributeCode.SPOON_CODE,
        value: spoonCode,
        historyPointId: historyPoint.id,
      }),
      this.historyPointAttributeRepo.create({
        attributeCode: HistoryPointAttributeCode.PRODUCT_ID,
        value: String(product.id),
        historyPointId: historyPoint.id,
      }),
      this.historyPointAttributeRepo.create({
        attributeCode: HistoryPointAttributeCode.PRODUCT_CODE,
        value: product.code,
        historyPointId: historyPoint.id,
      }),
      this.historyPointAttributeRepo.create({
        attributeCode: HistoryPointAttributeCode.PRODUCT_NAME,
        value: product.name,
        historyPointId: historyPoint.id,
      }),
      this.historyPointAttributeRepo.create({
        attributeCode: HistoryPointAttributeCode.PRODUCT_MONEY,
        value: String(product.money),
        historyPointId: historyPoint.id,
      }),
      this.historyPointAttributeRepo.create({
        attributeCode: HistoryPointAttributeCode.FIRST_SCAN,
        value: String(isFirstScan),
        historyPointId: historyPoint.id,
      }),
      this.historyPointAttributeRepo.create({
        attributeCode: HistoryPointAttributeCode.SPOON_CREATE_DATE,
        value: dayjs(spoon.createdDate).tz(TIME_ZONE).format('YYYY-MM-DD'),
        historyPointId: historyPoint.id,
      }),
      this.historyPointAttributeRepo.create({
        attributeCode: HistoryPointAttributeCode.SOURCE,
        value: String(feSource),
        historyPointId: historyPoint.id,
      }),
      // crmResult?.[0]?.sf_id &&
      //   this.historyPointAttributeRepo.create({
      //     attributeCode: HistoryPointAttributeCode.SF_ID,
      //     value: crmResult[0].sf_id,
      //     historyPointId: historyPoint.id,
      //   }),
      productData.storeAddress &&
        this.historyPointAttributeRepo.create({
          attributeCode: HistoryPointAttributeCode.STORE_ADDRESS,
          value: productData.storeAddress,
          historyPointId: historyPoint.id,
        }),
      productData.storeName &&
        this.historyPointAttributeRepo.create({
          attributeCode: HistoryPointAttributeCode.STORE_NAME,
          value: productData.storeName,
          historyPointId: historyPoint.id,
        }),
      productData.storeId &&
        this.historyPointAttributeRepo.create({
          attributeCode: HistoryPointAttributeCode.STORE_ID,
          value: productData.storeId,
          historyPointId: historyPoint.id,
        }),
      productData.distributorCode &&
        this.historyPointAttributeRepo.create({
          attributeCode: HistoryPointAttributeCode.DISTRIBUTOR_CODE,
          value: productData.distributorCode,
          historyPointId: historyPoint.id,
        }),
      productData.distributorName &&
        this.historyPointAttributeRepo.create({
          attributeCode: HistoryPointAttributeCode.DISTRIBUTOR_NAME,
          value: productData.distributorName,
          historyPointId: historyPoint.id,
        }),
      productData.distributorAddress &&
        this.historyPointAttributeRepo.create({
          attributeCode: HistoryPointAttributeCode.DISTRIBUTOR_ADDRESS,
          value: productData.distributorAddress,
          historyPointId: historyPoint.id,
        }),
    ];

    if (!isDuplicateQr) {
      historyPointAttributes.push(
        this.historyPointAttributeRepo.create({
          attributeCode: HistoryPointAttributeCode.POINT,
          value: String(product.point),
          historyPointId: historyPoint.id,
        }),
        this.historyPointAttributeRepo.create({
          attributeCode: HistoryPointAttributeCode.LEVEL_POINT,
          value: String(product.levelPoint),
          historyPointId: historyPoint.id,
        }),
      );
    }

    await this.historyPointAttributeRepo.save(
      historyPointAttributes.filter(Boolean),
    );

    return historyPoint;
  }

  private async createScanHistory(
    user: User,
    dto: AddPointReqDto,
    status: ScanHistoryStatus,
    errorCode?: string,
    source?: ScanHistoryApiType,
  ) {
    const { qrCode, spoonCode } = dto;

    const scanHistory = this.scanHistoryRepo.create({
      userId: user.id,
      userName: user.getFullName(),
      phoneNumber: user.phoneNumber,
      request: JSON.stringify({ spoonCode, qrCode }),
      qrCode,
      spoonCode,
      status,
      apiType:
        source && source == ScanHistoryApiType.HOTLINE
          ? ScanHistoryApiType.HOTLINE
          : ScanHistoryApiType.APP,
      errorCode,
    });
    await this.scanHistoryRepo.save(scanHistory);
  }

  async createTranGiftPoint(
    user: User,
    transactionRefId: string,
    actionType: string,
    point: number,
    crmCode: string,
    requestOutboxMessage: RequestOutboxMessage,
    notiDisplayTemplateType: any,
    notiParams: Record<string, any>,
    totalPointBefore?: number,
  ) {
    const transactionExternalId = randomTransactionExternalId();
    const crmTxType = await this.crmTransactionTypeRepo.findOneBy({
      code: crmCode,
    });

    // check user in reset identity time
    if (user.startFreezePoint && user.endFreezePoint) {
      const currentTime = getNowAtTimeZoneHcm();
      const isWithinFreezePeriod =
        compareDateBetweenFromDateIAndToDateInTimezone(
          currentTime,
          user.startFreezePoint,
          user.endFreezePoint,
        );
      if (isWithinFreezePeriod === 1) {
        throw new AppBaseExc(
          StatusCode.BLOCK_IDENTITY_SCAN,
          null,
          null,
          StatusCode.BLOCK_IDENTITY_SCAN,
        );
      }
    }

    const history = this.historyPointRepo.create({
      type: HistoryPointType.GIFT,
      customerId: user.id,
      customerName: user.getFullName(),
      customerPhone: user.phoneNumber,
      status: HistoryPointStatus.SUCCESS,
      transactionDate: getNowAtTimeZoneHcm(),
      transactionExternalId: transactionExternalId,
      cdpSyncUp: false,
      actionType,
      giftPoint: point,
    });

    await this.historyPointRepo.save(history);
    const historyPointAttributes: HistoryPointAttribute[] = [
      this.historyPointAttributeRepo.create({
        attributeCode: HistoryPointAttributeCode.POINT,
        value: String(point),
        historyPointId: history.id,
      }),
      this.historyPointAttributeRepo.create({
        attributeCode: HistoryPointAttributeCode.REF_TRAN_ID,
        value: transactionRefId,
        historyPointId: history.id,
      }),
    ];
    // await this.historyPointAttributeRepo.save(historyPointAttributes);

    // await this.pushUpdateRankNotification(user.id, title, content, description);

    // import to record of identity point service

    const historyPointCategory = mapHistoryPointType(
      HistoryPointType.GIFT,
      point,
    );

    const identityRecord =
      this.ddxTriggerCalculateUserResetGiftPointRepo.create({
        customerId: String(user.id),
        customerPhone: user.phoneNumber || 'Null',
        customerName: user.firstName + user.lastName || 'Undefined',
        totalPointBefore: Math.max((user.giftPoint ?? 0) - point, 0),
        totalPointAfter: user.giftPoint ?? 0,
        numberPoint: point,
        contentTransaction:
          IdentityPointContent[crmTxType.code] || 'No content',
        type: historyPointCategory,
        actionType: IdentityPointActionType[crmTxType.code] || 'verifyPoint',
        transactionTime: getNowAtTimeHcm(),
        transactionExternalId,
        account: {
          created_at: user.createdDate,
          last_time_login: user.lastLoginDate,
        },
      });

    await this.ddxTriggerCalculateUserResetGiftPointRepo.save(identityRecord);

    // if ([135309, 135306].includes(user.id)) {
    //   throw new AppBaseExc(
    //     StatusCode.SB_DEFAULT_ERROR,
    //     '',
    //     true,
    //     StatusCode.SB_DEFAULT_ERROR,
    //     this.request,
    //   );
    // }

    await Promise.all([
      this.historyPointAttributeRepo.save(historyPointAttributes),
      this.pushGiftPointNotification(user, notiDisplayTemplateType, notiParams),
    ]);

    const request: CrmPointGiftingRequest = {
      userId: user.id,
      Transaction_Type__c: crmTxType.name,
      Type__c: 'Adding',
      Level_Points__c: 0,
      Redeem_Points__c: point,
      Transaction_External_ID__c: transactionExternalId,
      Transaction_Date__c: dayjs().tz(TIME_ZONE).format(TIME_FORMAT_CRM),
      Rule_Name__c: crmTxType.description,
      Campaign__c: crmTxType.campaignName,
      Earned_Tier_Point__c: 0,
      Tier__c: user.tierCode,
      Tier_Points__c: user.tierPoint,
      Transaction_Trigger_External_ID__c: transactionRefId,
    };
    requestOutboxMessage.push(request);

    // const newOutboxMessage = this.outboxMessageRepo.create({
    //   provider: SyncProvider.CRM,
    //   callType: CallType.SYNC,
    //   syncType: SyncType.IMMEDIATE,
    //   request: JSON.stringify(request),
    //   status: OutboxMessageStatus.PROCESSING,
    //   createdDate: getNowAtTimeHcm(),
    //   retryNumber: 0,
    // });
    // await this.outboxMessageRepo.save(newOutboxMessage);

    // const crmResult = await this.crmService.createTransactionPointGifting(
    //   request,
    // );

    // if (crmResult[0].status !== 0)
    //   throw new Error(
    //     `Call crm createTransactionPointGifting not success, body: ${JSON.stringify(
    //       request,
    //     )}, response: ${JSON.stringify(crmResult)}`,
    //   );
  }

  async updateUserPoint(
    user: User,
    product: Product,
    transactionExternalId: string,
    isFirstScan?: boolean,
  ) {
    user.totalPoint = user.totalPoint + product.point;
    user.giftPoint = Number(user.giftPoint) + product.point;
    user.tierPoint = user.tierPoint + product.levelPoint;
    user.tierPointAddedAfterUpRank += product.levelPoint;
    user.lastScanDate = getNowAtTimeZoneHcm();
    user.gaveFirstScan = true;

    // save time one
    const actionType = isFirstScan
      ? CrmTransactionTypeMainCode.FIRST_SCAN
      : CrmTransactionTypeMainCode.QR_CODE;

    await this.saveIdentityRecord(
      user,
      null,
      actionType,
      transactionExternalId,
      product.point,
      false,
      product,
    );

    const listTransGiftPoint = [];

    const currentTier = await this.tierRepo.findOneBy({ code: user.tierCode });
    if (!currentTier)
      return Promise.reject(
        new AppBaseExc(
          StatusCode.SB_DEFAULT_ERROR,
          '',
          true,
          StatusCode.API_FAILED_UNKNOWN,
          this.request,
        ),
      );

    if (Object.keys(PER_REDEEM_POINT).includes(user.tierCode)) {
      const point = (product.point * PER_REDEEM_POINT[user.tierCode]) / 100;
      user.giftPoint += point;
      user.totalPoint += point;
      const oldTierCode = user.tierCode;
      if (user.tierPoint >= currentTier.maxPoint && currentTier.nextTierCode) {
        user.tierCode = currentTier.nextTierCode;
      }
      // await this.createTranGiftPoint(
      //   user,
      //   transactionExternalId,
      //   CRM_TRANS_TYPE_CODE[user.tierCode],
      //   point,
      //   CRM_TRANS_TYPE_CODE[oldTierCode],
      //   `THƯỞNG XU THÀNH CÔNG.`,
      //   `Bạn được thưởng ${point} xu với hạng ${oldTierCode}. Hãy sử dụng Xu tích luỹ để tham gia đổi quà.`,
      //   `"Bạn được thưởng ${point} xu với hạng ${oldTierCode}. Hãy sử dụng Xu tích luỹ để tham gia đổi quà."`,
      // );

      listTransGiftPoint.push({
        user,
        transactionExternalId,
        actionType: CRM_TRANS_TYPE_CODE[user.tierCode],
        point,
        crmCode: CRM_TRANS_TYPE_CODE[oldTierCode],
        title: `THƯỞNG XU THÀNH CÔNG.`,
        content: `Bạn được thưởng ${point} xu với hạng ${oldTierCode}. Hãy sử dụng Xu tích luỹ để tham gia đổi quà.`,
        description: `"Bạn được thưởng ${point} xu với hạng ${oldTierCode}. Hãy sử dụng Xu tích luỹ để tham gia đổi quà."`,
        notiDisplayTemplateType: NotiDisplayTemplateType.QR_CODE_TIER_2023,
        notiParams: {
          gifted_point_amount: point,
          gifted_point_rank: oldTierCode,
        },
        totalPoint: user.giftPoint,
      });
    }

    let isUpdateRank = false;
    let isUpdateRankGiftPoint = false;
    const startOfCurrentYear = getStartOfCurrentYearAtTimeHcm(true);
    const endOfCurrentYear = getEndOfCurrentYearAtTimeHcm(true);

    if (user.tierPoint >= currentTier.maxPoint && currentTier.nextTierCode) {
      user.tierCode = currentTier.nextTierCode;
      user.tierRankUpdatedDate = new Date();
      user.tierPointAddedAfterUpRank = 0;
      isUpdateRank = true;

      if (user.tierCode === USER_RANK.GOLD) {
        const historyPoint = await this.historyPointRepo.count({
          where: {
            actionType: CRM_TRANS_TYPE_CODE.TIER_UP_GOLD_2023,
            type: HistoryPointType.GIFT,
            customerId: user.id,
            transactionDate: Between(startOfCurrentYear, endOfCurrentYear),
          },
        });
        if (historyPoint <= 0) {
          isUpdateRankGiftPoint = true;
          user.giftPoint += GIFT_POINT.FIRST_UPGRADE_RANK_GOLD;
          user.totalPoint += GIFT_POINT.FIRST_UPGRADE_RANK_GOLD;
          // await this.createTranGiftPoint(
          //   user,
          //   transactionExternalId,
          //   CRM_TRANS_TYPE_CODE.TIER_UP_GOLD_2023,
          //   GIFT_POINT.FIRST_UPGRADE_RANK_GOLD,
          //   CRM_TRANS_TYPE_CODE.TIER_UP_GOLD_2023,
          //   `THĂNG HẠNG ${user.tierCode} THÀNH CÔNG.`,
          //   `Bạn đã được tặng ${GIFT_POINT.FIRST_UPGRADE_RANK_GOLD} xu khi thăng hạng ${user.tierCode} thành công.`,
          //   `"Bạn đã được tặng ${GIFT_POINT.FIRST_UPGRADE_RANK_GOLD} xu khi thăng hạng ${user.tierCode} thành công."`,
          // );
          listTransGiftPoint.push({
            user,
            transactionExternalId,
            actionType: CRM_TRANS_TYPE_CODE.TIER_UP_GOLD_2023,
            point: GIFT_POINT.FIRST_UPGRADE_RANK_GOLD,
            crmCode: CRM_TRANS_TYPE_CODE.TIER_UP_GOLD_2023,
            title: `THĂNG HẠNG ${user.tierCode} THÀNH CÔNG.`,
            content: `Bạn đã được tặng ${GIFT_POINT.FIRST_UPGRADE_RANK_GOLD} xu khi thăng hạng ${user.tierCode} thành công.`,
            description: `"Bạn đã được tặng ${GIFT_POINT.FIRST_UPGRADE_RANK_GOLD} xu khi thăng hạng ${user.tierCode} thành công."`,
            notiDisplayTemplateType: NotiDisplayTemplateType.TIER_UP_2023,
            notiParams: {
              rank: user.tierCode,
              point: GIFT_POINT.FIRST_UPGRADE_RANK_GOLD,
            },
            totalPoint: user.giftPoint,
          });
        }
      } else if (user.tierCode === USER_RANK.PLATINUM) {
        const historyPoint = await this.historyPointRepo.count({
          where: {
            actionType: CRM_TRANS_TYPE_CODE.TIER_UP_PLATINUM_2023,
            type: HistoryPointType.GIFT,
            customerId: user.id,
            transactionDate: Between(startOfCurrentYear, endOfCurrentYear),
          },
        });
        if (historyPoint <= 0) {
          isUpdateRankGiftPoint = true;
          user.giftPoint += GIFT_POINT.FIRST_UPGRADE_RANK_PLATIUM;
          user.totalPoint += GIFT_POINT.FIRST_UPGRADE_RANK_PLATIUM;
          // await this.createTranGiftPoint(
          //   user,
          //   transactionExternalId,
          //   CRM_TRANS_TYPE_CODE.TIER_UP_PLATINUM_2023,
          //   GIFT_POINT.FIRST_UPGRADE_RANK_PLATIUM,
          //   CRM_TRANS_TYPE_CODE.TIER_UP_PLATINUM_2023,
          //   `THĂNG HẠNG ${user.tierCode} THÀNH CÔNG.`,
          //   `Bạn đã được tặng ${GIFT_POINT.FIRST_UPGRADE_RANK_PLATIUM} xu khi thăng hạng ${user.tierCode} thành công.`,
          //   `"Bạn đã được tặng ${GIFT_POINT.FIRST_UPGRADE_RANK_PLATIUM} xu khi thăng hạng ${user.tierCode} thành công."`,
          // );
          listTransGiftPoint.push({
            user,
            transactionExternalId,
            actionType: CRM_TRANS_TYPE_CODE.TIER_UP_PLATINUM_2023,
            point: GIFT_POINT.FIRST_UPGRADE_RANK_PLATIUM,
            crmCode: CRM_TRANS_TYPE_CODE.TIER_UP_PLATINUM_2023,
            title: `THĂNG HẠNG ${user.tierCode} THÀNH CÔNG.`,
            content: `Bạn đã được tặng ${GIFT_POINT.FIRST_UPGRADE_RANK_PLATIUM} xu khi thăng hạng ${user.tierCode} thành công.`,
            description: `"Bạn đã được tặng ${GIFT_POINT.FIRST_UPGRADE_RANK_PLATIUM} xu khi thăng hạng ${user.tierCode} thành công."`,
            notiDisplayTemplateType: NotiDisplayTemplateType.TIER_UP_2023,
            notiParams: {
              rank: user.tierCode,
              point: GIFT_POINT.FIRST_UPGRADE_RANK_PLATIUM,
            },
            totalPoint: user.giftPoint,
          });
        }
      }
    }

    //console.log('user before convertLeadToAccount');
    //console.log(user);
    const leadId = await this.crmService.convertLeadToAccount(user);
    //console.log('leadId');
    //console.log(leadId);
    if (leadId) {
      user.crmUserId = leadId;
      user.userType = UserType.ACCOUNT;
    }
    //console.log('user after convertLeadToAccount');
    //console.log(user);

    if (
      isUpdateRank &&
      (user.tier === USER_RANK.TITAN || !isUpdateRankGiftPoint)
    ) {
      // const [, userUpdate] = await Promise.all([
      //   this.pushUpdateRankNotification(
      //     user.id,
      //     `THĂNG HẠNG ${user.tierCode} THÀNH CÔNG.`,
      //     `Bạn đã thăng hạng ${user.tierCode} thành công.`,
      //     `"Bạn đã thăng hạng ${user.tierCode} thành công."`,
      //   ),
      //   this.userRepo.save(user),
      // ]);
      const notiUpRank = {
        userId: user.id,
        title: `THĂNG HẠNG ${user.tierCode} THÀNH CÔNG.`,
        content: `Bạn đã thăng hạng ${user.tierCode} thành công.`,
        description: `"Bạn đã thăng hạng ${user.tierCode} thành công."`,
      };
      const userUpdate = await this.userRepo.save(user);
      return { userUpdate, notiUpRank, listTransGiftPoint };
    } else {
      const userUpdate = await this.userRepo.save(user);
      return { userUpdate, listTransGiftPoint };
    }
  }

  private async pushEventQ1Notification(userId: number, eventOrdinal: any) {
    const useNotiV3 = this.configService.get('useNotiV3');
    const lastestEventIdNotApplyImprove = this.configService.get(
      'event.eventLatestId',
    );
    if (
      useNotiV3 &&
      eventOrdinal.notiDisplayTemplateId &&
      eventOrdinal.eventId > lastestEventIdNotApplyImprove
    ) {
      const kafkaDto = new PushNotiKafkaDto({
        userIds: [userId],
        version: PushNotiKafkaDtoVersion.V1,
        notiDisplayTemplateId: eventOrdinal.notiDisplayTemplateId,
        featureNoti: FeatureNoti.NOTI_EVENT,
      });
      //console.log('kafkaDto pushEventCallingNotification');
      //console.log(kafkaDto);

      const outboxMsg = this.outboxMessageRepo.createPushNoti(kafkaDto);
      await this.outboxMessageRepo.save(outboxMsg);
      return;
    }

    const topic = `USER_${userId}_${dayjs().unix()}`;
    // const notificationData = NotificationData[giftId];
    const notificationData = this.getNotificationData(eventOrdinal);
    console.log(
      `logging notification for user ${userId} with data ${JSON.stringify(
        notificationData,
      )}`,
    );
    const title = notificationData.title;
    const content = notificationData.content;
    const description = notificationData.description;
    const userSessions = await this.userSessionRepo.find({
      where: { userId, deviceToken: Not(IsNull()) },
    });
    const tokens = userSessions.map((item) => item.deviceToken);

    const request: PushNotificationRequest = {
      message: content,
      title: title,
      topic: topic,
      type: 2,
      tokenOfTopic: tokens,
    };

    const notificationUser = this.notificationUserRepo.create({
      userId,
      status: NotificationUserStatus.UNREAD,
      firebaseStatus: NotificationUserFirebaseStatus.SENT,
      title,
      content,
      description,
      deepLink: 'https://vitadairyvietnam.vn/oggi/',
    });

    await this.vitaJavaService.pushNotification(request);
    await this.notificationUserRepo.save(notificationUser);
  }

  private async pushEventCallingNotification(
    userId: number,
    notify: any,
    notiDisplayTemplateType: NotiDisplayTemplateType,
  ) {
    const useNotiV3 = this.configService.get('useNotiV3');
    this.logger.log(`useNotiV3: ${useNotiV3}`);

    if (useNotiV3) {
      const kafkaDto = new PushNotiKafkaDto({
        userIds: [userId],
        version: PushNotiKafkaDtoVersion.V1,
        notiDisplayTemplateType: notiDisplayTemplateType as any,
        featureNoti: FeatureNoti.NOTI_EVENT,
      });
      //console.log('kafkaDto pushEventCallingNotification');
      //console.log(kafkaDto);

      const outboxMsg = this.outboxMessageRepo.createPushNoti(kafkaDto);
      await this.outboxMessageRepo.save(outboxMsg);
      return;
    }

    const topic = `USER_${userId}_${dayjs().unix()}`;
    const title = notify.title;
    const content = notify.content;
    const description = notify.description;
    const userSessions = await this.userSessionRepo.find({
      where: { userId, deviceToken: Not(IsNull()) },
    });
    const tokens = userSessions.map((item) => item.deviceToken);

    const request: PushNotificationRequest = {
      message: content,
      title: title,
      topic: topic,
      type: 2,
      tokenOfTopic: tokens,
    };
    const notificationUser = this.notificationUserRepo.create({
      userId,
      status: NotificationUserStatus.UNREAD,
      firebaseStatus: NotificationUserFirebaseStatus.SENT,
      title,
      content,
      description,
    });

    await this.vitaJavaService.pushNotification(request);
    await this.notificationUserRepo.save(notificationUser);
  }

  private async pushPopupV2(
    userId: number,
    code: string,
    arrNoti: string[],
    appversionname: string,
  ) {
    if (
      code === PopupV2Code.QUY3_OPTI_KHONG_TRUNG_GIAI ||
      code === PopupV2Code.QUY3_DHA_KHONG_TRUNG_GIAI ||
      code === PopupV2Code.EVBIGC_KHONG_TRUNG_GIAI_2024
      // code === PopupV2Code.QUY4_SBPS_X2_TICH_XU_KHONG_TRUNG_GIAI
    ) {
      console.log(`Don't push popup code: ${code} for user`, userId);
      return;
    }
    console.log(`Push popup code: ${code} for user`, userId);
    const appversion = this.configService.get('app.appVersionName');
    if (appversionname >= appversion) {
      arrNoti.push(code);
    } else {
      const userSessions = await this.userSessionRepo.find({
        where: { userId: userId, deviceToken: Not(IsNull()) },
      });
      const deviceTokens = userSessions.map((item) => item.deviceToken);
      await Promise.all(
        deviceTokens.map(async (deviceToken) => {
          const request: PushPopupRequestV2 = {
            message: PUSH_POP_UP_REQUEST_MESSAGE,
            title: PUSH_POP_UP_REQUEST_TITLE,
            type: 1,
            hmData: {
              code,
              notification_title: PUSH_POP_UP_REQUEST_NOTI_TITLE,
            },
            token: deviceToken,
          };

          await this.vitaJavaService.pushPopup(request);
        }),
      );
    }
  }

  async webAppGetQrInformation(reqData: WebAppGetQrInformationReqDto) {
    const [{ productData, saveQrCallback }] = await Promise.all([
      this.getProductByQr(reqData.qrCode),
    ]);
    await saveQrCallback();
    if (!productData) {
      throw new AppBaseExc(
        StatusCode.SB_DEFAULT_ERROR,
        '',
        true,
        StatusCode.API_FAILED_UNKNOWN,
        this.request,
      );
    }
    const productMstGet = await this.productMstRepo.findOneBy({
      code: productData.code,
    });

    return {
      ...productData,
      enable:
        productMstGet && productMstGet.webAppEnable
          ? BooleanValueEnum.TRUE
          : BooleanValueEnum.FALSE,
    };
  }

  private async pushAddPointNotification(
    userId: number,
    product: Product,
    dto: AddPointReqDto,
  ) {
    const useNotiV3 = this.configService.get('useNotiV3');
    this.logger.log(`useNotiV3: ${useNotiV3}`);

    if (useNotiV3) {
      let notiDisplayTemplateType: NotiDisplayTemplateType =
        NotiDisplayTemplateType.QR_CODE;
      if (PRODUCT_CODE_CBB_PEDIA == product.code) {
        notiDisplayTemplateType = NotiDisplayTemplateType.QR_CODE_CBB_PEDIA;
      }
      /*console.log('kafkaDto raw pushAddPointNotification');
      console.log({
        userIds: [userId],
        version: PushNotiKafkaDtoVersion.V1,
        notiDisplayTemplateType: NotiDisplayTemplateType.QR_CODE,
        notiDisplayTemplateParams: {
          added_point_amount: product.point,
        },
        source: (dto.feSource === 'CLS'
          ? NotificationUserSource.CLS
          : NotificationUserSource.MB) as any,
        featureNoti: FeatureNoti.NOTI_ADD_POINT,
      });*/
      const kafkaDto = new PushNotiKafkaDto({
        userIds: [userId],
        version: PushNotiKafkaDtoVersion.V1,
        notiDisplayTemplateType,
        notiDisplayTemplateParams: {
          added_point_amount: product.point,
        },
        source: (dto.feSource === 'CLS'
          ? NotificationUserSource.CLS
          : NotificationUserSource.MB) as any,
        featureNoti: FeatureNoti.NOTI_ADD_POINT,
      });
      //console.log('kafkaDto pushAddPointNotification');
      //console.log(kafkaDto);

      const outboxMsg = this.outboxMessageRepo.createPushNoti(kafkaDto);
      await this.outboxMessageRepo.save(outboxMsg);
      return;
    }

    const topic = `USER_${userId}_${dayjs().unix()}`;
    const title = 'Quét mã và tích xu thành công.';
    const content = `Bạn đã tích lũy thành công ${product.point} xu. Hãy sử dụng Xu tích lũy để tham gia đổi quà`;
    const description = `"Bạn đã tích lũy thành công ${product.point} xu. Hãy sử dụng Xu tích lũy để tham gia đổi quà"`;
    const userSessions = await this.userSessionRepo.find({
      where: { userId, deviceToken: Not(IsNull()) },
    });
    const tokens = userSessions.map((item) => item.deviceToken);

    const request: PushNotificationRequest = {
      message: content,
      title: title,
      topic: topic,
      type: 2,
      tokenOfTopic: tokens,
    };
    const notification = this.notificationUserRepo.create({
      userId,
      status: NotificationUserStatus.UNREAD,
      firebaseStatus: NotificationUserFirebaseStatus.SENT,
      title,
      content,
      description,
      source:
        dto.feSource === 'CLS'
          ? NotificationUserSource.CLS
          : NotificationUserSource.MB,
    });

    await this.vitaJavaService.pushNotification(request);
    await this.notificationUserRepo.save(notification);
  }

  async pushUpdateRankNotification(
    userId: number,
    title: string,
    content: string,
    description: string,
    user: User,
  ) {
    const useNotiV3 = this.configService.get('useNotiV3');
    this.logger.log(`useNotiV3: ${useNotiV3}`);

    if (useNotiV3) {
      const kafkaDto = new PushNotiKafkaDto({
        userIds: [userId],
        version: PushNotiKafkaDtoVersion.V1,
        notiDisplayTemplateType: NotiDisplayTemplateType.TIER_UP,
        notiDisplayTemplateParams: {
          up_rank: user.tierCode,
        },
        featureNoti: FeatureNoti.NOTI_USER_RANKING,
      });
      //console.log('kafkaDto pushUpdateRankNotification');
      //console.log(kafkaDto);

      const outboxMsg = this.outboxMessageRepo.createPushNoti(kafkaDto);
      await this.outboxMessageRepo.save(outboxMsg);
      return;
    }

    const topic = `USER_${userId}_${dayjs().unix()}`;
    // const title = `THĂNG HẠNG ${rank} THÀNH CÔNG.`;
    // const content = `Bạn đã thăng hạng ${rank} thành công.`;
    // const description = `"Bạn đã thăng hạng ${rank} thành công."`;
    const userSessions = await this.userSessionRepo.find({
      where: { userId, deviceToken: Not(IsNull()) },
    });
    const tokens = userSessions.map((item) => item.deviceToken);

    const request: PushNotificationRequest = {
      message: content,
      title: title,
      topic: topic,
      type: 2,
      tokenOfTopic: tokens,
    };
    const notification = this.notificationUserRepo.create({
      userId,
      status: NotificationUserStatus.UNREAD,
      firebaseStatus: NotificationUserFirebaseStatus.SENT,
      title,
      content,
      description,
    });

    await this.vitaJavaService.pushNotification(request);
    await this.notificationUserRepo.save(notification);
  }

  private async pushGiftPointNotification(
    user: User,
    notiDisplayTemplateType: NotiDisplayTemplateType,
    notiParams: Record<string, any>,
  ) {
    const kafkaDto = new PushNotiKafkaDto({
      userIds: [user.id],
      version: PushNotiKafkaDtoVersion.V1,
      notiDisplayTemplateType: notiDisplayTemplateType as any,
      notiDisplayTemplateParams: notiParams,
      featureNoti: FeatureNoti.NOTI_EVENT,
    });
    //console.log('kafkaDto pushGiftPointNotification');
    //console.log(kafkaDto);

    const outboxMsg = this.outboxMessageRepo.createPushNoti(kafkaDto);
    await this.outboxMessageRepo.save(outboxMsg);
    return;
  }

  async createOrUpdateUserNumberScan(
    userId: number,
    userNumberScan: UserNumberScan,
  ) {
    if (userNumberScan) {
      const startOrCurrentDay = getStartOfNowInTimezone();
      const startOfCurrentMonth = getStartOfCurrentMonthInTimezone();
      if (
        compareDateWithDateInTimezone(
          userNumberScan.scannedAt,
          startOrCurrentDay,
        ) < 0
      ) {
        userNumberScan.numberScanInDay = 1;
      } else {
        userNumberScan.numberScanInDay += 1;
      }

      if (
        compareDateWithDateInTimezone(
          userNumberScan.scannedAt,
          startOfCurrentMonth,
        ) < 0
      ) {
        userNumberScan.numberScanInMonth = 1;
      } else {
        userNumberScan.numberScanInMonth += 1;
      }

      userNumberScan.numberScan += 1;
      userNumberScan.scannedAt = new Date();

      await this.userNumberScanRepo.save(userNumberScan);
    } else {
      await this.userNumberScanRepo.save({
        userId,
        numberScan: 1,
        numberScanInDay: 1,
        numberScanInMonth: 1,
      });
    }
  }

  private getQrType(qrCode: string): QrType | undefined {
    const enableGetProductOnSap = this.configService.get<boolean>(
      'sap.feature.enableGetProduct',
    );
    if (enableGetProductOnSap) {
      return QrType.SAP;
    }

    if (qrCode.length === 16) {
      return QrType.BRAVO;
    }

    if (qrCode.length === 17) {
      return QrType.BRAVO;
    }

    if (qrCode.length === 18) {
      return QrType.VITA;
    }
  }

  private async useSpoon(
    spoon: Spoon,
    spoonOnSap: SapProductBySpoonCodeResponse,
    qrManufactureDate: string,
    qrCode: string,
  ) {
    if (!spoon) {
      spoon = this.spoonRepo.create({
        spoonCode: spoonOnSap.code,
        createdDate: spoonOnSap.applied_date_obj,
      });
    }

    spoon.status = SpoonStatus.USED;
    spoon.scanDate = getNowAtTimeZoneHcm();
    spoon.qrCode = qrCode;
    spoon.qrManufactureDate = new Date(
      formatDayjsToTimeZoneZero(dayjs(qrManufactureDate, 'YYYY-MM-DD')),
    );

    return await this.spoonRepo.save(spoon);
  }

  private async getSpoonAndCheckSpoonWithQr(spoonCode: string, qrCode: string) {
    const enableGetProductOnSap = this.configService.get<boolean>(
      'sap.feature.enableGetProduct',
    );
    // check format of spoonCode
    if (!enableGetProductOnSap) {
      if (!(spoonCode.length >= 7 && spoonCode.length <= 9)) {
        throw new AppBaseExc(
          StatusCode.SB_SPOON_FORERR,
          '',
          true,
          StatusCode.SB_SPOON_FORERR,
          this.request,
        );
      }
    } else {
      if (!(spoonCode.length >= 8 && spoonCode.length <= 10)) {
        throw new AppBaseExc(
          StatusCode.SB_SPOON_FORERR,
          '',
          true,
          StatusCode.SB_SPOON_FORERR,
          this.request,
        );
      }
    }

    const spoon = await this.spoonRepo
      .createQueryBuilder('spoon')
      .where('spoon.spoonCode = :spoonCode', { spoonCode })
      .setLock('pessimistic_write')
      .maxExecutionTime(60000) // lock timeout 60s
      .getOne();

    let spoonOnSap: SapProductBySpoonCodeResponse = null;
    if (!spoon) {
      if (!enableGetProductOnSap) {
        throw new AppBaseExc(
          StatusCode.SB_SPOON_NOEXIST,
          '',
          true,
          StatusCode.SPOON_CODE_NOT_EXIST,
          this.request,
        );
      }
    }
    if (enableGetProductOnSap) {
      spoonOnSap = await this.getProductBySpoonOnSap(spoonCode);
    }

    if (spoon && spoon.qrCode) {
      throw new AppBaseExc(
        StatusCode.SB_SPOON_USED,
        '',
        true,
        StatusCode.SPOON_CODE_ALREADY_IN_USE,
        this.request,
      );
    }

    const spoonQr = await this.spoonRepo.findOneBy({ qrCode });
    if (spoonQr) {
      const qrByHotline = await this.hotlineHistoryPointRepo
        .createQueryBuilder('hhp')
        .leftJoinAndSelect('hhp.historyPoint', 'hp')
        .leftJoinAndSelect('hp.historyPointAttributes', 'hpa')
        .where('hpa.attributeCode = :attributeCode', {
          attributeCode: HistoryPointAttributeCode.QR_CODE,
        })
        .andWhere('hpa.value = :value', { value: qrCode })
        .getOne();

      if (qrByHotline) {
        throw new AppBaseExc(StatusCode.SPOON_CODE_ALREADY_USED_BY_HOTLINE);
      } else
        throw new AppBaseExc(
          StatusCode.SB_QR_USED,
          '',
          true,
          StatusCode.QR_ALREADY_IN_USE,
          this.request,
        );
    }

    return { spoon, spoonOnSap };
  }

  private applySpoonQrRule(
    product: Product,
    productData: ProductDataGotByQrDto,
    spoon: Spoon,
    spoonOnSap: SapProductBySpoonCodeResponse,
    manufactureDateStr: string,
    qrCode: string,
  ) {
    const enableGetProductOnSap = this.configService.get<boolean>(
      'sap.feature.enableGetProduct',
    );
    if (enableGetProductOnSap) {
      if (!spoonOnSap.CommandCode) {
        throw new AppBaseExc(
          StatusCode.SB_SPOON_CMD_NULL,
          '',
          true,
          StatusCode.SB_SPOON_CMD_NULL,
          this.request,
        );
      }
      if (!productData.commandCode) {
        throw new AppBaseExc(
          StatusCode.SB_QR_CMD_NULL,
          '',
          true,
          StatusCode.SB_QR_CMD_NULL,
          this.request,
        );
      }
      if (spoonOnSap.CommandCode != productData.commandCode) {
        throw new AppBaseExc(
          StatusCode.SB_SPOON_CMD_MISMATCH,
          '',
          true,
          StatusCode.SB_SPOON_CMD_MISMATCH,
          this.request,
        );
      }
      if (!productData.manufactureDate) {
        throw new AppBaseExc(
          StatusCode.SB_QR_MANUFACTURE_DATE_MISS,
          '',
          true,
          StatusCode.SB_QR_MANUFACTURE_DATE_MISS,
          this.request,
        );
      }
      if (!productData.expireDate) {
        throw new AppBaseExc(
          StatusCode.SB_QR_EXPIRE_DATE_MISS,
          '',
          true,
          StatusCode.SB_QR_EXPIRE_DATE_MISS,
          this.request,
        );
      }
      if (!spoonOnSap.Applied_date) {
        throw new AppBaseExc(
          StatusCode.SB_SPOON_APPLIED_DATE_MISS,
          '',
          true,
          StatusCode.SB_SPOON_APPLIED_DATE_MISS,
          this.request,
        );
      }

      return;
    }

    // if (this.configService.get('nodeEnv') === NodeEnv.DEVELOPMENT) return;
    if (qrCode.length === 18) return;

    if (!manufactureDateStr)
      throw new AppBaseExc(
        StatusCode.SB_DEFAULT_ERROR,
        '',
        true,
        StatusCode.API_FAILED_UNKNOWN,
        this.request,
      );

    const manufactureDate = dayjs(manufactureDateStr, 'YYYY-MM-DD').toDate();
    const beforeDateApplyNewSpoon30Days = dayjs(DATE_APPLY_NEW_SPOON)
      .subtract(30, 'day')
      .toDate();

    if (
      !spoon.createdDate ||
      (manufactureDate > DATE_APPLY_NEW_SPOON &&
        spoon.createdDate < beforeDateApplyNewSpoon30Days) ||
      (manufactureDate < beforeDateApplyNewSpoon30Days &&
        spoon.createdDate > DATE_APPLY_NEW_SPOON)
    ) {
      throw new AppBaseExc(
        StatusCode.SB_DEFAULT_ERROR,
        null,
        null,
        StatusCode.API_FAILED_UNKNOWN,
        this.request,
      );
    }

    if (
      spoon.createdDate > DATE_APPLY_NEW_SPOON ||
      manufactureDate > DATE_APPLY_NEW_SPOON
    ) {
      if (product.prefixBrandCode !== spoon.spoonCode[0]) {
        throw new AppBaseExc(
          StatusCode.SB_SPOON_UNMATCH_BRAND,
          null,
          null,
          StatusCode.SPOON_CODE_BRAND_ERROR_MAPPING,
          this.request,
        );
      }
      if (product.prefixWeightCode !== spoon.spoonCode[1]) {
        throw new AppBaseExc(
          StatusCode.SB_SPOON_UNMATCH_WEIGHT,
          null,
          null,
          StatusCode.SPOON_CODE_WEIGHT_ERROR_MAPPING,
          this.request,
        );
      }
    }

    const monthDiff = calMonthDiff(spoon.createdDate, manufactureDate);

    const manufactureYear = dayjs(manufactureDate).year();
    const manufactureMonth = dayjs(manufactureDate).month();

    if (
      (manufactureYear >= 2023 && manufactureMonth >= 1) ||
      manufactureYear >= 2024
    ) {
      if (
        monthDiff > 0 ||
        (!dayjs(spoon.createdDate).isBefore(manufactureDate) &&
          !dayjs(spoon.createdDate).isSame(manufactureDate, 'day'))
      ) {
        throw new AppBaseExc(
          StatusCode.SB_DATE_AFTER223,
          '',
          true,
          StatusCode.SPOON_CODE_MANUFACTURE_DATE_ERROR_MAPPING,
          this.request,
        );
      }
    } else if (
      (manufactureYear >= 2022 && manufactureMonth >= 6 && monthDiff > 0) ||
      (manufactureYear >= 2023 && manufactureMonth >= 0 && monthDiff > 0)
    ) {
      throw new AppBaseExc(
        StatusCode.SB_DATE_AFTER722,
        '',
        true,
        StatusCode.SPOON_CODE_MANUFACTURE_DATE_ERROR_MAPPING,
        this.request,
      );
    } else if (manufactureYear > 2021 && monthDiff > 1) {
      throw new AppBaseExc(
        StatusCode.SB_DATE_1722,
        '',
        true,
        StatusCode.SPOON_CODE_MANUFACTURE_DATE_ERROR_MAPPING,
        this.request,
      );
    }
  }

  private async getProductByQr(qrCode: string): Promise<{
    productData: ProductDataGotByQrDto;
    saveQrCallback: () => Promise<ZenposQr | BravoQr | VitaQr | boolean>;
  }> {
    const qrType = this.getQrType(qrCode);
    let productData: ProductDataGotByQrDto;
    let saveQrCallback: () => Promise<ZenposQr | BravoQr | VitaQr | boolean>;
    if (qrType === QrType.ZENPOS) {
      const productZenposRes = await this.getProductByZenposQr(qrCode);
      const zenposQr =
        this.zenposQrRepo.createFromProductResByZenposQr(productZenposRes);

      productData = ProductDataGotByQrDto.fromZenposQrRes(productZenposRes);
      saveQrCallback = () => this.zenposQrRepo.save(zenposQr);
    }

    if (qrType === QrType.BRAVO) {
      const productBravoRes = await this.getProductByBravoQr(qrCode);
      const productMst = await this.productMstRepo.findOneBy({
        code: productBravoRes.ProductCode,
      });

      const bravoQr = this.bravoQrRepo.createFromBravoQrRes(productBravoRes);
      productData = ProductDataGotByQrDto.fromBravoQrRes(
        productBravoRes,
        productMst,
      );

      saveQrCallback = () => this.bravoQrRepo.save(bravoQr);
    }

    if (qrType === QrType.VITA) {
      const vitaQr = await this.getProductByVitaQr(qrCode);

      productData = ProductDataGotByQrDto.fromVitaQr(vitaQr);
      // TODO: Change vitaQr status from raw string to enum, to avoid typo error
      saveQrCallback = () =>
        this.vitaQrRepo.save({ ...vitaQr, status: 'USED' });
    }

    if (qrType === QrType.SAP) {
      const productOnSap = await this.getProductByQrOnSap(qrCode);
      productData = ProductDataGotByQrDto.fromSapRes(productOnSap);
      saveQrCallback = () => this.saveQrSap();
    }

    const isExpired = productData?.expireDate
      ? dayjs(
          productData?.expireDate,
          ProductDataGotByQrDto.DATE_FORMAT,
        ).isBefore(dayjs())
      : true;

    if (!qrType)
      throw new AppBaseExc(
        StatusCode.SB_QR_ERROR,
        '',
        true,
        StatusCode.QR_CODE_INVALID,
        this.request,
      );

    if (!productData?.code)
      throw new AppBaseExc(
        StatusCode.SB_QR_NOEXIST,
        '',
        true,
        StatusCode.QR_CODE_INVALID,
        this.request,
      );

    // if (qrType !== QrType.SAP) {
    if (isExpired)
      throw new AppBaseExc(
        StatusCode.SB_EXPIRE,
        '',
        true,
        StatusCode.QR_CODE_INVALID,
        this.request,
      );
    // }

    return { productData, saveQrCallback };
  }

  private async getProductByZenposQr(qrCode: string) {
    try {
      const resData = await this.zenposQrService.getProductByQrCode(qrCode);
      if (
        resData.ManufactureDate.length < 10 ||
        resData.ExpiryDate.length < 10
      ) {
        throw new AppBaseExc(
          StatusCode.SB_QR_NOEXIST,
          null,
          null,
          StatusCode.API_FAILED_UNKNOWN,
          this.request,
        );
      }

      return resData;
    } catch (error) {
      Logger.error('Get product by zenpos qr error', error);
      throw new AppBaseExc(
        StatusCode.SB_QR_SYSTEM_ERROR,
        '',
        true,
        StatusCode.QR_CODE_ERROR_16,
        this.request,
      );
    }
  }

  private async getProductByBravoQr(qrCode: string) {
    try {
      const resData = await this.bravoQrService.getProductByQrCode(qrCode);
      if (!resData.Data || resData.Data.Products.length < 1)
        throw new AppBaseExc(
          StatusCode.SB_QR_NOEXIST,
          '',
          true,
          StatusCode.API_FAILED_UNKNOWN,
          this.request,
        );

      const productRes = resData.Data.Products[0];

      if (
        !resData.Data ||
        productRes.ProductDate.length < 10 ||
        productRes.ExpireDate.length < 10 ||
        (productRes.StockOutDate && productRes.StockOutDate.length < 10)
      ) {
        throw new AppBaseExc(
          StatusCode.SB_QR_NOEXIST,
          'BravoQr validation fails',
          true,
          StatusCode.API_FAILED_UNKNOWN,
          this.request,
        );
      }

      return productRes;
    } catch (error) {
      Logger.error('Get product by bravo qr error', error);

      switch (error.status) {
        case StatusCode.SB_QR_NOEXIST.status:
          throw error;
        default:
          throw new AppBaseExc(
            StatusCode.SB_QR_SYSTEM_ERROR,
            '',
            true,
            StatusCode.QR_CODE_ERROR_17,
            this.request,
          );
      }
    }
  }

  private async getProductByVitaQr(qrCode: string) {
    try {
      const vitaQr = await this.vitaQrRepo.findOneBy({ code: qrCode });
      if (!vitaQr)
        throw new AppBaseExc(
          StatusCode.SB_QR_NOEXIST,
          '',
          true,
          StatusCode.API_FAILED_UNKNOWN,
          this.request,
        );

      return vitaQr;
    } catch (error) {
      Logger.error('Get product by vita qr error', error);
      throw new AppBaseExc(
        StatusCode.SB_QR_SYSTEM_ERROR,
        '',
        true,
        StatusCode.API_FAILED_UNKNOWN,
        this.request,
      );
    }
  }

  async checkFeatureScanQr() {
    const featureScanQr = await this.featureRepo.getFeatureScanQr();

    if (!featureScanQr)
      throw new AppBaseExc(
        StatusCode.SB_DEFAULT_ERROR,
        '',
        true,
        StatusCode.API_FAILED_UNKNOWN,
        this.request,
      );

    if (featureScanQr.status === FeatureStatus.INACTIVE)
      throw new AppBaseExc(
        StatusCode.SB_DEFAULT_ERROR,
        '',
        true,
        StatusCode.API_FAILED_UNKNOWN,
        this.request,
      );

    if (featureScanQr.status === FeatureStatus.MAINTAIN)
      throw new AppBaseExc(
        StatusCode.SB_DEFAULT_ERROR,
        '',
        true,
        StatusCode.API_FAILED_UNKNOWN,
        this.request,
      );
  }

  private async checkScanSameQrFailedManyTimes(user: User, qrCode: string) {
    try {
      const limitNumberOfScanSameQrFailedEntity =
        await this.systemConfigRepo.getLimitNumberOfScanSameQrFailed();
      if (!limitNumberOfScanSameQrFailedEntity) return '';

      const limitNumberOfScanSameQrFailed = Number(
        limitNumberOfScanSameQrFailedEntity.value,
      );

      const curentTime = new Date(
        dayjs().tz(TIME_ZONE).format(TIME_FORMAT_DAY),
      );

      let timeCheckScan: Date;

      if (
        !user.blockedScanExpiryDate ||
        dayjs(user.blockedScanExpiryDate).isBefore(curentTime)
      ) {
        timeCheckScan = curentTime;
      } else {
        timeCheckScan = user.blockedScanExpiryDate;
      }

      // const numOfScanSameQrFailed = await this.scanHistoryRepo.countBy({
      //   userId: user.id,
      //   qrCode,
      //   status: ScanHistoryStatus.FAILED,
      //   createdDate: MoreThan(user.blockedScanDate),
      // });

      const numOfScanSameQrFailed = await this.scanHistoryRepo
        .createQueryBuilder('scanHistory')
        .where((qb) => {
          qb.where('scanHistory.userId = :userId', { userId: user.id })
            .andWhere('scanHistory.qrCode = :qrCode', { qrCode })
            // .andWhere('scanHistory.errorCode IN (:...errorCodes)', {
            //   errorCodes: SB_SCAN_FAIL_VALID_ERROR_CODE,
            // })
            .andWhere(
              '((scanHistory.errorCode IN (:...errorCodes) AND scanHistory.apiType != :apiType) OR (scanHistory.errorCode = :errorCode AND scanHistory.apiType = :apiType))',
              {
                errorCodes: SB_SCAN_FAIL_VALID_ERROR_CODE,
                errorCode: StatusCode.SB_DEFAULT_ERROR.error,
                apiType: ScanHistoryApiType.HOTLINE,
              },
            )
            .andWhere('scanHistory.status = :status', {
              status: ScanHistoryStatus.FAILED,
            })
            .andWhere('scanHistory.spoonCode is not null');
          if (timeCheckScan)
            qb.andWhere('scanHistory.createdDate > :timeCheckScan', {
              timeCheckScan,
            });
        })
        .getCount();

      if (numOfScanSameQrFailed < limitNumberOfScanSameQrFailed) return '';

      const configBlockScanSameQrTimeMinutes =
        await this.systemConfigRepo.getBlockedScanSameQrFailedTimeMinutes();
      const numbBlockScanSameQrTimeMinutes =
        Number(configBlockScanSameQrTimeMinutes) || 1440;

      const blockedScanHistory = this.blockedHistoryRepo.create({
        userId: user.id,
        userName: user.name,
        phoneNumber: user.phoneNumber,
        actionDate: getNowAtTimeZoneHcm(),
        blockedTime: numbBlockScanSameQrTimeMinutes,
        reason: 'Scan same qr failed many times',
        type: BlockedHistoryType.BLOCKED_SCAN_SAME_QR,
        cdpSyncUp: false,
      });
      await this.blockedHistoryRepo.save(blockedScanHistory);

      return BlockedHistoryType.BLOCKED_SCAN_SAME_QR;
    } catch (error) {
      Logger.error('checkScanSameQrFailedManyTimes error', error);
      return '';
    }
  }
  private async checkScanQrFailedManyTimes(user: User) {
    try {
      const configLimitNumberOfScanQrFailed =
        await this.systemConfigRepo.getLimitNumberOfScanQrFailed();
      if (!configLimitNumberOfScanQrFailed) return '';
      const limitNumberOfScanQrFailed = isNaN(
        Number(configLimitNumberOfScanQrFailed.value),
      )
        ? 15
        : Number(configLimitNumberOfScanQrFailed.value);

      const currentTime = new Date(
        dayjs().tz(TIME_ZONE).format(TIME_FORMAT_DAY),
      );

      let timeCheckScan: Date;

      if (
        !user.blockedScanExpiryDate ||
        dayjs(user.blockedScanExpiryDate).isBefore(currentTime)
      ) {
        timeCheckScan = currentTime;
      } else {
        timeCheckScan = user.blockedScanExpiryDate;
      }

      const numOfScanFailed = await this.scanHistoryRepo
        .createQueryBuilder('scanHistory')
        .where((qb) => {
          qb.where('scanHistory.userId = :userId', {
            userId: user.id,
          })
            .andWhere('scanHistory.status = :status', {
              status: ScanHistoryStatus.FAILED,
            })
            // .andWhere('scanHistory.errorCode IN (:...errorCodes)', {
            //   errorCodes: SB_SCAN_FAIL_VALID_ERROR_CODE,
            // })
            // .andWhere('scanHistory.apiType != :apiType', {
            //   apiType: ScanHistoryApiType.HOTLINE,
            // })
            .andWhere(
              '((scanHistory.errorCode IN (:...errorCodes) AND scanHistory.apiType != :apiType) OR (scanHistory.errorCode = :errorCode AND scanHistory.apiType = :apiType))',
              {
                errorCodes: SB_SCAN_FAIL_VALID_ERROR_CODE,
                errorCode: StatusCode.SB_DEFAULT_ERROR.error,
                apiType: ScanHistoryApiType.HOTLINE,
              },
            )
            .andWhere('scanHistory.spoonCode is not null');
          if (timeCheckScan)
            qb.andWhere('scanHistory.createdDate > :timeCheckScan', {
              timeCheckScan,
            });
        })
        .getCount();

      if (numOfScanFailed < limitNumberOfScanQrFailed) return '';

      const blockedScanQrFailedTimeMinutes = 1440;
      const blockedHistory = this.blockedHistoryRepo.create({
        userId: user.id,
        userName: user.getFullName(),
        phoneNumber: user.phoneNumber,
        actionDate: getNowAtTimeZoneHcm(),
        blockedTime: blockedScanQrFailedTimeMinutes,
        reason: 'Scan qr failed many times',
        type: BlockedHistoryType.BLOCKED_SCAN_QR,
        cdpSyncUp: false,
      });

      await this.blockedHistoryRepo.save(blockedHistory);
      return BlockedHistoryType.BLOCKED_SCAN_QR;
    } catch (error) {
      Logger.error('checkScanQrFailedManyTimes', error);
      return '';
    }
  }

  private async checkScanManyTimes(user: User) {
    try {
      const limitNumberOfBlockedScanConfig =
        await this.systemConfigRepo.getLimitNumberOfBlockedScan();
      const limitNumberOfBlockedScanValue = Number(
        limitNumberOfBlockedScanConfig.value,
      );

      if (!limitNumberOfBlockedScanValue) return '';

      if (user.numberOfBlockedScan < limitNumberOfBlockedScanValue) return '';
      user.numberOfBlockedAccount++;

      const blockedScanHistory = this.blockedHistoryRepo.create({
        userId: user.id,
        userName: user.getFullName(),
        phoneNumber: user.phoneNumber,
        actionDate: getNowAtTimeZoneHcm(),
        blockedTime: null,
        reason: 'Blocked scan many times',
        type: BlockedHistoryType.BLOCKED_ACCOUNT_WHEN_SCAN_FAILED,
        cdpSyncUp: false,
      });

      await Promise.all([
        this.userRepo.save(user),
        this.blockedHistoryRepo.save(blockedScanHistory),
      ]);
      return BlockedHistoryType.BLOCKED_ACCOUNT_WHEN_SCAN_FAILED;
    } catch (error) {
      Logger.error('checkScanManyTimes', error);
      return '';
    }
  }

  async checkLimitNumberOfScan(userId: number): Promise<UserNumberScan> {
    const codesConfig = [
      SystemConfigCode.LIMIT_NUMBER_OF_SCAN_QR_SUCCESS_IN_MONTH,
      SystemConfigCode.LIMIT_NUMBER_OF_SCAN_QR_SUCCESS_IN_DAY,
    ];
    const [limitConfigs, userNumberScan] = await Promise.all([
      this.systemConfigRepo.findBy({
        code: In(codesConfig),
      }),
      this.userNumberScanRepo
        .createQueryBuilder('uns')
        .where('uns.userId = :userId', { userId })
        .setLock('pessimistic_write')
        .maxExecutionTime(60000) // lock timeout 60s
        .getOne(),
    ]);
    const mapLimitConfisByCode = objectify(
      limitConfigs,
      (config) => config.code,
    );
    const limitInMonthConfig =
      mapLimitConfisByCode[
        SystemConfigCode.LIMIT_NUMBER_OF_SCAN_QR_SUCCESS_IN_MONTH
      ];
    const limitInDayConfig =
      mapLimitConfisByCode[
        SystemConfigCode.LIMIT_NUMBER_OF_SCAN_QR_SUCCESS_IN_DAY
      ];

    if (!limitInMonthConfig || !limitInDayConfig) {
      throw new AppBaseExc(
        StatusCode.SB_DEFAULT_ERROR,
        '',
        true,
        StatusCode.API_FAILED_UNKNOWN,
        this.request,
      );
    }

    if (!userNumberScan || !userNumberScan.scannedAt) {
      return userNumberScan;
    }

    const startOfCurrentMonth = getStartOfCurrentMonthInTimezone();
    if (
      compareDateWithDateInTimezone(
        userNumberScan.scannedAt,
        startOfCurrentMonth,
      ) < 0
    ) {
      return userNumberScan;
    }
    if (limitInMonthConfig.isActive) {
      if (
        userNumberScan.numberScanInMonth >= Number(limitInMonthConfig.value)
      ) {
        throw new AppBaseExc(
          StatusCode.SB_OVERADDPOINT,
          '',
          true,
          StatusCode.API_FAILED_UNKNOWN,
          this.request,
        );
      }
    }

    const startOfCurrentDay = getStartOfNowInTimezone();
    if (
      compareDateWithDateInTimezone(
        userNumberScan.scannedAt,
        startOfCurrentDay,
      ) < 0
    ) {
      return userNumberScan;
    }
    if (limitInDayConfig.isActive) {
      if (userNumberScan.numberScanInDay >= Number(limitInDayConfig.value)) {
        throw new AppBaseExc(
          StatusCode.SB_OVERADDPOINT,
          '',
          true,
          StatusCode.API_FAILED_UNKNOWN,
          this.request,
        );
      }
    }

    return userNumberScan;
  }

  async getUserGiftByGiftIds(
    user: UserSessionData,
    getUserGiftDto: UserGiftReqDto,
  ) {
    const { giftIds } = getUserGiftDto;

    try {
      const response = await this.userGiftRepo.find({
        where: {
          userId: user.userId,
          giftId: In(giftIds),
          status: In([UserGiftStatus.PENDING]),
        },
        relations: { gift: { giftTransportInfo: true } },
        order: { createdDate: 'DESC' },
      });

      return new AppResponseDto(response);
    } catch (error) {
      throw new AppBaseExc(
        StatusCode.SB_DEFAULT_ERROR,
        '',
        true,
        StatusCode.API_FAILED_UNKNOWN,
        this.request,
      );
    }
  }
  public async handleEventClsPhase1(payload: EventClsQ4Payload) {
    try {
      this.logger.debug(`Starting EventClsQ4Payload handle!`);
      const data = await this._eventClsQ4Service.handle(payload);

      this.logger.debug(`Done EventClsQ4Payload handle!`);
      return data;
    } catch (error) {
      this.logger.error('Error when handle eventClsQ4', error.message);
      throw error;
      /*return {
        notificationCodes: [],
        postCommitActions: [],
        clsOutBoxMessageRequests: [],
        outBoxMessages: [],
      };*/
    }
  }

  public async handleEventQ32024UpRank(data: EventQuy32024UpRankData) {
    try {
      const rs = await this._eventQ32024UpRankService.handle(data);
      return rs;
    } catch (error) {
      this.logger.error('Error when handle eventQ32024UpRank', error.message);
      throw error;
      /*return {
        //notificationCodes: [],
        postCommitActions: [],
        popupCode: '',
        gift: null,
        triggered: false,
      };*/
    }
  }

  private async reuseUserGift(
    userGiftId: number,
    userId: number,
    token: string,
  ) {
    const request: UserGiftReuseRequest = {
      userGiftId,
    };
    if (!token) {
      request.userId = userId;
    }
    const data = await this.vitaJavaService.userGiftReuse(
      token,
      request,
      async (error) => {
        this.logger.error('Error when call reuse user gift ', error.message);
      },
    );
    this.logger.debug('Response when call reuse user gift ', data);
    return data;
  }

  async getProductByQrOnSap(qrCode: string): Promise<SapProductByQrResponse> {
    let response: SapCallApiGetProductByQrResponse = null;

    try {
      response = await this.sapService.getProductByQr(qrCode);
      if (!response) {
        throw new AppBaseExc(
          StatusCode.SB_QR_NOEXIST,
          '',
          true,
          StatusCode.API_FAILED_UNKNOWN,
          this.request,
        );
      }
      if (204 == response.sapResStatus) {
        throw new AppBaseExc(
          StatusCode.SB_QR_NOT_FOUND,
          '',
          true,
          StatusCode.SB_QR_NOT_FOUND,
          this.request,
        );
      }
      const products = response.sapResult?.Data?.Products;
      if (!products || !products.length) {
        throw new AppBaseExc(
          StatusCode.SB_QR_NOEXIST,
          '',
          true,
          StatusCode.API_FAILED_UNKNOWN,
          this.request,
        );
      }
      /*const outboxMessageLog: OutboxMessage =
        this.outboxMessageRepo.createLogGetProductOnSap(
          { qrCode },
          response,
          OutboxMessageStatus.SUCCESS,
        );
      await this.outboxMessageRepo.save(outboxMessageLog);*/

      return products[0];
    } catch (err) {
      if (err instanceof AppBaseExc) {
        throw err;
      }
      if (408 == err?.response?.status) {
        throw new AppBaseExc(
          StatusCode.SB_SAP_NORESPONSE,
          '',
          true,
          StatusCode.SB_SAP_NORESPONSE,
          this.request,
        );
      }
      /*const outboxMessageLog: OutboxMessage =
        this.outboxMessageRepo.createLogGetProductOnSap(
          { qrCode },
          err,
          OutboxMessageStatus.FAILED,
        );
      await this.outboxMessageRepo.save(outboxMessageLog);*/

      throw new AppBaseExc(
        StatusCode.SB_QR_SYSTEM_ERROR,
        '',
        true,
        StatusCode.API_FAILED_UNKNOWN,
        this.request,
      );
    }
  }

  private async getProductBySpoonOnSap(
    spoonCode: string,
  ): Promise<SapProductBySpoonCodeResponse> {
    let response: SapCallApiGetProductBySpoonCodeResponse = null;

    try {
      response = await this.sapService.getProductBySpoon(spoonCode);
      if (!response) {
        throw new AppBaseExc(
          StatusCode.SB_SPOON_NOEXIST,
          '',
          true,
          StatusCode.SB_SPOON_NOEXIST,
          this.request,
        );
      }
      if (204 == response.sapResStatus) {
        throw new AppBaseExc(
          StatusCode.SB_SPOON_NOT_FOUND,
          '',
          true,
          StatusCode.SB_SPOON_NOT_FOUND,
          this.request,
        );
      }
      const products = response.sapResult?.Data?.response;
      if (!products || !products.length) {
        throw new AppBaseExc(
          StatusCode.SB_SPOON_NOEXIST,
          '',
          true,
          StatusCode.SB_SPOON_NOEXIST,
          this.request,
        );
      }

      const spoonOnSap: SapProductBySpoonCodeResponse = products[0];
      // Field Applied_date has no data -> set null to throw error
      // Ref ticket 3155
      if (!spoonOnSap?.Applied_date) {
        throw new AppBaseExc(
          StatusCode.SB_SPOON_APPLIED_DATE_MISS,
          '',
          true,
          StatusCode.SB_SPOON_APPLIED_DATE_MISS,
          this.request,
        );
      }
      spoonOnSap.code = spoonCode;
      // SAP TODO: kiểm tra format từ SAP
      spoonOnSap.applied_date_obj = makeDateIsDateAtTimeHcm(
        spoonOnSap.Applied_date,
      );
      /*const outboxMessageLog: OutboxMessage =
        this.outboxMessageRepo.createLogGetProductOnSap(
          { spoonCode },
          response,
          OutboxMessageStatus.SUCCESS,
        );
      await this.outboxMessageRepo.save(outboxMessageLog);*/

      return spoonOnSap;
    } catch (err) {
      if (err instanceof AppBaseExc) {
        throw err;
      }
      if (408 == err?.response?.status) {
        throw new AppBaseExc(
          StatusCode.SB_SAP_NORESPONSE,
          '',
          true,
          StatusCode.SB_SAP_NORESPONSE,
          this.request,
        );
      }
      /*const outboxMessageLog: OutboxMessage =
        this.outboxMessageRepo.createLogGetProductOnSap(
          { spoonCode },
          err,
          OutboxMessageStatus.FAILED,
        );
      await this.outboxMessageRepo.save(outboxMessageLog);*/

      throw new AppBaseExc(
        StatusCode.SB_SPOON_NOEXIST,
        '',
        true,
        StatusCode.API_FAILED_UNKNOWN,
        this.request,
      );
    }
  }

  private async saveQrSap(): Promise<boolean> {
    return true;
  }

  private getPopupCode(eventOrdinal: any) {
    const eventId = eventOrdinal.eventId;
    const giftId = eventOrdinal.giftId || eventOrdinal.id;
    let popupCode = GiftID[giftId];
    if (eventId > Number(this.configService.get('event.eventLatestId'))) {
      const eventDetailPopup = eventOrdinal.eventDetailPopup;
      if (eventDetailPopup) {
        popupCode = eventDetailPopup.code;
      }
    }

    return popupCode;
  }

  private getNotificationData(eventOrdinal: any) {
    const eventId = eventOrdinal.eventId;
    const giftId = eventOrdinal.giftId || eventOrdinal.id;
    let notificationData = NotificationData[giftId];

    if (eventId > Number(this.configService.get('event.eventLatestId'))) {
      notificationData = {
        title: eventOrdinal.notificationTitle,
        content: eventOrdinal.notificationContent,
        description: eventOrdinal.notificationDescription,
      };
    }

    return notificationData;
  }

  private canRedeemGiftToday(
    evenlyHistory: Record<string, EvenlyHistory>,
    remainQuantity: number,
  ): boolean {
    const today = getFormatCurrentDateInTimezone();
    const latestDate = this.getLatestDateInEvenlyHistory(evenlyHistory);
    const history = evenlyHistory[today];

    if (today === latestDate) return remainQuantity > 0;

    return !history || history.usedQuantity < history.evenQuantity;
  }

  private getLatestDateInEvenlyHistory(
    evenlyHistory: Record<string, EvenlyHistory>,
  ) {
    const sortedDates = sortByAttribute(
      Object.keys(evenlyHistory).map((date) => ({ date })),
      'date',
      false,
    );
    return sortedDates?.[0]?.date;
  }

  /**
   * Determines product weight category (e.g. 400g, 800g)
   * based on either product prefixWeightCode or SKU structure.
   *
   * @param sku - The SKU string of the product
   * @param prefixWeightCode - Optional prefix weight code from product entity
   *
   * @returns EnumProductWeightIdentity or null if undetermined
   */
  private determineProductWeight(
    sku: string,
    prefixWeightCode?: string,
  ): EnumProductWeightIdentity {
    // Use prefixWeightCode if available
    if (prefixWeightCode === '4') {
      return EnumProductWeightIdentity.WEIGHT_400_GRAM;
    }

    if (prefixWeightCode === '8') {
      return EnumProductWeightIdentity.WEIGHT_800_GRAM;
    }

    // Fallback: Determine from SKU format
    const isEightCharSku = sku.length === 8;
    const isNineCharSku = sku.length === 9;

    const fifthChar = isEightCharSku ? sku[4] : undefined;
    const sixthChar = isNineCharSku ? sku[5] : undefined;

    const weightIndicator = fifthChar || sixthChar;

    if (weightIndicator === '4') {
      return EnumProductWeightIdentity.WEIGHT_400_GRAM;
    }

    if (weightIndicator === '8') {
      return EnumProductWeightIdentity.WEIGHT_800_GRAM;
    }

    // Unknown or unsupported weight
    return null;
  }

  private async validateMatchSetupEventTimeGiftingGift(
    user: User,
    event: Event,
    eventDetail: EventDetail,
  ): Promise<IResultValidateMatchSetupEventTimeGiftingGift> {
    const rs: IResultValidateMatchSetupEventTimeGiftingGift = {
      result: true,
    };

    if (!user || !event || !eventDetail) {
      return rs;
    }

    if (!eventDetail.enableTimeGiftingGift) {
      return rs;
    }

    const now = getNowAtTimezone();
    const setupEventDetailTimeGiftingMatch =
      await this.eventDetailTimeGiftingGiftRepo.findOneBy({
        eventDetailId: eventDetail.id,
        startDate: LessThan(now),
        endDate: MoreThan(now),
      });
    if (!setupEventDetailTimeGiftingMatch) {
      return rs;
    }
    rs.eventDetailTimeGiftingGift = setupEventDetailTimeGiftingMatch;

    const historyUserGiftingGiftEvent =
      await this.historyUserGiftingGiftEventRepo.findOneBy({
        userId: user.id,
        eventDetailTimeGiftingGiftId: setupEventDetailTimeGiftingMatch.id,
      });
    if (!historyUserGiftingGiftEvent) {
      return rs;
    }
    rs.historyUserGiftingGiftEvent = historyUserGiftingGiftEvent;
    if (
      historyUserGiftingGiftEvent.total < setupEventDetailTimeGiftingMatch.total
    ) {
      return rs;
    }

    rs.result = false;
    return rs;
  }

  private async saveIdentityRecord(
    user: User,
    crmTxType: CrmTransactionType,
    actionType: string,
    transactionExternalId: string,
    giftPoint: number,
    isDuplicateQr: boolean,
    product?: Product,
  ) {
    const pointAdd = isDuplicateQr ? 0 : giftPoint;
    const historyPointCategory = mapHistoryPointType(
      HistoryPointType.GIFT,
      pointAdd,
    );

    let contentTransaction =
      IdentityPointContent[crmTxType?.code ?? actionType] ?? 'Không xác định';

    if (
      ['FIRST_SCAN', 'QR_CODE', 'FIRST_SCAN_SBPS', 'QR_CODE_SBPS'].includes(
        actionType,
      )
    ) {
      contentTransaction = `${contentTransaction}${
        product?.name ?? 'Không xác định'
      }`;

      const actionTypeIdentity =
        IdentityPointActionType[crmTxType?.code ?? actionType] ?? 'verifyPoint';

      const identityRecord =
        this.ddxTriggerCalculateUserResetGiftPointRepo.create({
          customerId: String(user.id),
          customerPhone: user.phoneNumber || 'Null',
          customerName: user.firstName + user.lastName || 'Undefined',
          totalPointBefore: Math.max((user.giftPoint ?? 0) - pointAdd, 0),
          totalPointAfter: user.giftPoint ?? 0,
          numberPoint: pointAdd,
          contentTransaction,
          type: historyPointCategory,
          actionType: actionTypeIdentity,
          transactionTime: getNowAtTimeHcm(),
          transactionExternalId,
          account: {
            created_at: user.createdDate,
            last_time_login: user.lastLoginDate,
          },
        });

      await this.ddxTriggerCalculateUserResetGiftPointRepo.save(identityRecord);
    }
  }

  private async countNumberOf400GrAnd800GrCans(
    user: User,
    eventProducts: EventProduct[],
    typeAddPoint: string,
  ): Promise<CountNumberOf400GrAnd800GrCansInterface> {
    const rs: CountNumberOf400GrAnd800GrCansInterface = {
      numberOf400GrCans: 0,
      numberOf800GrCans: 0,
    };
    const skus400g = [];
    const skus800g = [];
    if (!eventProducts || !eventProducts.length || !typeAddPoint) {
      return rs;
    }
    const event = eventProducts[0].event;
    const eventProductSkus = eventProducts.map((item) => item.sku);
    const products = await this.productRepo.findBy({
      code: In(eventProductSkus),
    });
    const mapProductsBySku = objectify(products, (item) => item.code);

    for (const eventProduct of eventProducts) {
      let weightDetermine: EnumProductWeightIdentity = null;

      if (typeAddPoint === 'SB') {
        const product = mapProductsBySku[eventProduct.sku] ?? null;
        if (product) {
          weightDetermine = this.determineProductWeight(
            eventProduct.sku,
            product?.prefixWeightCode,
          );
        }
      }

      switch (weightDetermine) {
        case EnumProductWeightIdentity.WEIGHT_400_GRAM:
          skus400g.push(eventProduct.sku);
          break;
        case EnumProductWeightIdentity.WEIGHT_800_GRAM:
          skus800g.push(eventProduct.sku);
          break;
      }
    }
    if (!skus400g.length && !skus800g.length) {
      return rs;
    }

    const productCodes = [...skus400g, ...skus800g];
    const historyPointsGetByProductCodes = await this.historyPointRepo
      .createQueryBuilder('hp')
      .innerJoinAndSelect('hp.historyPointAttributes', 'hpa')
      .where('hp.customerId = :customerId', { customerId: user.id })
      .andWhere('hp.transactionDate >= :startTime', {
        startTime: event.startDate,
      })
      .andWhere('hp.type = :type', { type: 'ADD_POINT' })
      .andWhere('hp.actionType IN(:...actionTypes)', {
        actionTypes: ['QR_CODE', 'FIRST_SCAN'],
      })
      .andWhere('hpa.attributeCode = :code', { code: 'PRODUCT_CODE' })
      .andWhere('hpa.value IN(:...values)', {
        values: productCodes,
      })
      .getMany();
    for (const hp of historyPointsGetByProductCodes) {
      if (skus400g.includes(hp.historyPointAttributes[0].value)) {
        rs.numberOf400GrCans++;
      }
      if (skus800g.includes(hp.historyPointAttributes[0].value)) {
        rs.numberOf800GrCans++;
      }
    }

    return rs;
  }
}
