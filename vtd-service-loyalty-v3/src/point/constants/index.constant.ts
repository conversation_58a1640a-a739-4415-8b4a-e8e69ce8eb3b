import dayjs from 'dayjs';
import { TIME_ZONE } from '../../common/constants/index.constant';
import { StatusCode } from '../../common/constants/status-code.constant';
import { HistoryPointType } from '../enums/history-point.enum';

export const DATE_APPLY_NEW_SPOON = dayjs('2022-01-01', 'YYYY-MM-DD').toDate();
export const END_TIME_EVENT_MUM = dayjs(
  '2023-04-30 23:59:59',
  'YYYY-MM-DD HH:mm:ss',
).toDate();
export const START_TIME_ID_PHASE2 = dayjs(
  '2023-04-04 00:00:00',
  'YYYY-MM-DD HH:mm:ss',
).toDate();

export const END_TIME_ID_PHASE2 = dayjs(
  '2023-07-01T00:00:00', // 2023-07-01 00:00:00
  'YYYY-MM-DDTHH:mm:ss',
  TIME_ZONE,
).format('YYYY-MM-DDTHH:mm:ss');
export const START_TIME_NEW_SKU = dayjs('2022-09-15', 'YYYY-MM-DD');
export const NEW_SKUS = [
  'VMCG4M02',
  'VMCG8M01',
  'VMCG4M03',
  'VMCG8M02',
  'VMCG4M01',
  'VMCG8M03',
  'VMCBI4M01',
  'VMCBI8M01',
  'VMCBI4M02',
  'VMCBI8M02',
  'VMCBI4M03',
  'VMCBI8M03',
  'VMIQ4M01',
  'VMIQ8M01',
  'VMIQ4M02',
  'VMIQ8M04',
  'VMIQ4M03',
  'VMIQ8M03',
  'VCLG8S02',
];

export const TYPE_HOSTORY_POINT = {
  GIFTING: 'GIFTING',
};

export const SB_SCAN_FAIL_VALID_ERROR_CODE = [
  'SB_SPOON_FORERR',
  'SB_SPOON_NOEXIST',
  'error.spoon_code_not_exist',
  'error.invalid_spoon_code',
  'SB_SPOON_USED',
  'error.spoon_code_already_in_use',
  'error.spoon_code_is_used',
  'SB_SPOON_UNMATCH_BRAND',
  'error.product_not_applicable',
  'error.spoone_code_brand_error_mapping',
  'SB_SPOON_UNMATCH',
  'error.spoone_code_weight_error_mapping',
  'error.product_not_applicable',
  'SPOON_CODE_WEIGHT_ERROR_MAPPING',
  'SB_SPOON_UNMATCH_WEIGHT',
  'SPOON_CODE_MANUFACTURE_DATE_ERROR_MAPPING',
  'error.spoon_code_manufacture_date_error_mapping',
  'SB_DATE_1722',
  'SB_DATE_AFTER722',
  'SB_DATE_AFTER223',
  'SB_DEFAULT_ERROR',
  'SB_SPOON_NOT_FOUND',
  'SB_SPOON_CMD_MISMATCH',
  'SB_SPOON_APPLIED_DATE_MISS',
  'SB_SPOON_CMD_NULL',
  'ERROR',
];

export enum TrackingSyncStatus {
  PENDING = 'PENDING',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
}

export const sbpsErrorCodes = [
  StatusCode.SBPS_QR_USED.error,
  StatusCode.SBPS_QR_FORERR.error,
  StatusCode.SBPS_QR_NOEXIST.error,
  StatusCode.SBPS_DEFAULT_ERROR.error,
];

export const sbErrorCodes = [
  StatusCode.SB_DEFAULT_ERROR.error,
  StatusCode.SB_SPOON_NOT_FOUND.error,
  StatusCode.SB_SPOON_CMD_MISMATCH.error,
  StatusCode.SB_SPOON_APPLIED_DATE_MISS.error,
  StatusCode.SB_SPOON_CMD_NULL.error,
  StatusCode.SB_SPOON_FORERR.error,
  StatusCode.SB_SPOON_NOEXIST.error,
  StatusCode.SB_SPOON_USED.error,
  StatusCode.API_FAILED_UNKNOWN.error,
  StatusCode.SB_SPOON_UNMATCH_BRAND.error,
  StatusCode.SB_BLOCK_SCANQR.error,
];

export const PRODUCT_CODE_CBB_PEDIA = '510000077';
