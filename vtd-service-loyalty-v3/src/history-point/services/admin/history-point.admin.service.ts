import { Injectable } from '@nestjs/common';
import { paginate } from 'nestjs-typeorm-paginate';
import { Brackets, SelectQueryBuilder } from 'typeorm';
import { QueryFilter } from '../../../common/decorators/query-filter.decorator';
import { HistoryPoint } from '../../../point/entities/history-point.entity';
import { HistoryPointRepository } from '../../../point/repositories/history-point.repository';
import { GetHistoryPointsAdminReqDto } from '../../dtos/req/admin/get-history-points.admin.req.dto';
import { isArray, isString, toNumber, trim } from 'lodash';

@Injectable()
export class HistoryPointAdminService {
  constructor(private readonly historyPointRepo: HistoryPointRepository) {}

  async getHistoryPoints(dto: GetHistoryPointsAdminReqDto) {
    // Build and execute the query
    const queryBuilder = await this.buildHistoryPointsQuery(dto);
    const { items, meta } = await paginate(queryBuilder, {
      limit: dto.limit,
      page: dto.page,
    });

    return {
      items,
      meta,
    };
  }

  /**
   * Builds the query for fetching history points with all filters applied
   */
  private async buildHistoryPointsQuery(
    dto: GetHistoryPointsAdminReqDto,
  ): Promise<SelectQueryBuilder<HistoryPoint>> {
    const queryBuilder = this.historyPointRepo
      .createQueryBuilder('hp')
      .leftJoin('hp.user', 'customer')
      .select([
        'hp.id',
        'hp.customerId',
        'hp.customerName',
        'hp.customerPhone',
        'hp.giftPoint',
        'hp.tierPoint',
        'hp.type',
        'hp.actionType',
        'hp.status',
        'hp.brand',
        'hp.transactionExternalId',
        'hp.transactionDate',
      ])
      .orderBy('hp.transactionDate', 'DESC');

    // Apply filters via reusable pipeline in BaseRepository
    await this.historyPointRepo.applyAllFilters(queryBuilder, dto, this);

    return queryBuilder;
  }

  /**
   * Applies branch filter
   */
  @QueryFilter({ order: 10 })
  private applyBrandFilter(
    queryBuilder: SelectQueryBuilder<HistoryPoint>,
    dto: GetHistoryPointsAdminReqDto,
  ): void {
    if (isString(dto.brands)) {
      queryBuilder.andWhere('hp.brand = :brand', { brand: dto.brands });
    }

    if (isArray(dto.brands) && dto.brands.length) {
      queryBuilder.andWhere('hp.brand IN (:...brands)', {
        brands: dto.brands,
      });
    }
  }

  /**
   * Applies status filter
   */
  @QueryFilter({ order: 20 })
  private applyStatusFilter(
    queryBuilder: SelectQueryBuilder<HistoryPoint>,
    dto: GetHistoryPointsAdminReqDto,
  ): void {
    if (dto.statuses?.length) {
      queryBuilder.andWhere('hp.status IN (:...statuses)', {
        statuses: dto.statuses,
      });
    }
  }

  /**
   * Applies customer search filter (searches in name and phone)
   */
  @QueryFilter({ order: 30 })
  private applyCustomerSearchFilter(
    queryBuilder: SelectQueryBuilder<HistoryPoint>,
    dto: GetHistoryPointsAdminReqDto,
  ): void {
    const searchText = trim(dto.customerSearchText);
    if (!searchText) return;

    queryBuilder.andWhere(
      new Brackets((qb) => {
        const customerId = toNumber(searchText);

        if (!isNaN(customerId)) {
          qb.where('hp.customerId = :customerId', { customerId });
        } else {
          qb.where('customer.lastName ILIKE :customerSearchText', {
            customerSearchText: `${searchText}%`,
          });
        }
      }),
    );
  }

  /**
   * Applies transaction external ID filter
   */
  @QueryFilter({ order: 40 })
  private applyTransactionExternalIdFilter(
    queryBuilder: SelectQueryBuilder<HistoryPoint>,
    dto: GetHistoryPointsAdminReqDto,
  ): void {
    if (!dto.transactionExternalId) return;

    queryBuilder.andWhere(
      'hp.transactionExternalId ILIKE :transactionExternalId',
      {
        transactionExternalId: `${dto.transactionExternalId}%`,
      },
    );
  }

  /**
   * Applies date range filter with default 30-day window if no dates provided
   */
  @QueryFilter({ order: 50 })
  private applyDateRangeFilter(
    queryBuilder: SelectQueryBuilder<HistoryPoint>,
    dto: GetHistoryPointsAdminReqDto,
  ): void {
    if (!dto.startDate && !dto.endDate) {
      // Default filter: last 30 days
      queryBuilder.andWhere(`hp.transactionDate >= NOW() - INTERVAL '30 days'`);
      return;
    }

    if (dto.startDate) {
      queryBuilder.andWhere('hp.transactionDate >= :startDate', {
        startDate: dto.startDate,
      });
    }

    if (dto.endDate) {
      queryBuilder.andWhere('hp.transactionDate <= :endDate', {
        endDate: dto.endDate,
      });
    }
  }
}
