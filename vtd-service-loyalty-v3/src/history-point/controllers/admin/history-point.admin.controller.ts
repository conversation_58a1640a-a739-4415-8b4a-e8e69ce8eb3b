import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Prefix } from '../../../common/constants/index.constant';
import { UseAdmin } from '../../../common/decorators/user.decorator';
import { AppResponseDto } from '../../../common/dtos/app-response.dto';
import { GetHistoryPointsAdminReqDto } from '../../dtos/req/admin/get-history-points.admin.req.dto';
import { HistoryPointAdminResDto } from '../../dtos/res/history-point.admin.res.dto';
import { HistoryPointAdminService } from '../../services/admin/history-point.admin.service';

@Controller({ version: '1', path: `${Prefix.ADMIN}/history-points` })
@ApiTags('History Point Admin Controller')
@UseAdmin()
export class HistoryPointAdminController {
  constructor(
    private readonly historyPointAdminService: HistoryPointAdminService,
  ) {}

  @Get()
  async getHistoryPoints(@Query() dto: GetHistoryPointsAdminReqDto) {
    const { items, meta } =
      await this.historyPointAdminService.getHistoryPoints(dto);
    return AppResponseDto.fromNestJsPagination(
      items.map((item) => new HistoryPointAdminResDto(item)),
      meta,
    );
  }
}
