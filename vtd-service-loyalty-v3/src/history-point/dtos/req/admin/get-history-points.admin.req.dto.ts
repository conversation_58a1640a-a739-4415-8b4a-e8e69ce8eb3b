import {
  IsValidArrayEnum,
  IsValidArrayString,
  IsValidDate,
  IsValidText,
} from '../../../../common/decorators/custom-validator.decorator';
import { PaginationReqDto } from '../../../../common/dtos/pagination.dto';
import { HistoryPointStatus } from '../../../../point/enums/history-point.enum';

export class GetHistoryPointsAdminReqDto extends PaginationReqDto {
  @IsValidArrayString({ required: false })
  brands?: string[];

  @IsValidArrayEnum({ required: false }, HistoryPointStatus)
  statuses?: HistoryPointStatus[];

  @IsValidText({ required: false })
  customerSearchText?: string;

  @IsValidText({ required: false })
  transactionExternalId?: string;

  @IsValidDate({ required: false })
  startDate?: Date;

  @IsValidDate({ required: false })
  endDate?: Date;
}
