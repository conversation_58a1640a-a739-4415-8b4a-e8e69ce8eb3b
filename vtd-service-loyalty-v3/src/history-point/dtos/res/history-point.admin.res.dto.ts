import { AutoMapDecorator } from '../../../common/decorators/automap.decorator';
import { BaseMapperDto } from '../../../common/dtos/base-mapper.dto';

export class HistoryPointAdminResDto extends BaseMapperDto {
  @AutoMapDecorator()
  id: number;

  @AutoMapDecorator()
  customerId: number;

  @AutoMapDecorator()
  customerName: string;

  @AutoMapDecorator()
  customerPhone: string;

  @AutoMapDecorator()
  giftPoint: number;

  @AutoMapDecorator()
  tierPoint: number;

  @AutoMapDecorator()
  type: string;

  @AutoMapDecorator()
  actionType: string;

  @AutoMapDecorator()
  status: string;

  @AutoMapDecorator()
  brand: string;

  @AutoMapDecorator()
  transactionExternalId: string;

  @AutoMapDecorator()
  transactionDate: Date;
}
