import { Module } from '@nestjs/common';
import { AuthModule } from '../auth/auth.module';
import { HistoryPointRepository } from '../point/repositories/history-point.repository';
import { HistoryPointAdminController } from './controllers/admin/history-point.admin.controller';
import { HistoryPointAdminService } from './services/admin/history-point.admin.service';

@Module({
  imports: [AuthModule],
  controllers: [HistoryPointAdminController],
  providers: [HistoryPointAdminService, HistoryPointRepository],
})
export class HistoryPointModule {}
