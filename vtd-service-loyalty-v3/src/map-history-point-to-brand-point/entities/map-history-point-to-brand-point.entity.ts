import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { BaseEntityOnlyCreatedAt } from '../../common/entities/base.entity';

@Entity({ name: 'map_history_to_brand_points' })
export class MapHistoryPointToBrandPointEntity extends BaseEntityOnlyCreatedAt {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'history_point_id', type: 'int4' })
  historyPointId: number;

  @Column({ name: 'brand_id', type: 'int4' })
  brandId: number;

  @Column({ name: 'event_id', type: 'int8', nullable: true })
  eventId: number;

  @Column({ name: 'event_add_can_id', type: 'int4', nullable: true })
  eventAddCanId: number;

  @Column({ name: 'brand_point', type: 'float8' })
  brandPoint: number;
}
