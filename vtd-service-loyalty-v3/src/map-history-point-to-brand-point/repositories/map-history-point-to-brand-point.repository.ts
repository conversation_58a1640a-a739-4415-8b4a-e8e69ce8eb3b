import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { MapHistoryPointToBrandPointEntity } from '../entities/map-history-point-to-brand-point.entity';

@Injectable()
export class MapHistoryPointToBrandPointRepository extends BaseRepository<MapHistoryPointToBrandPointEntity> {
  constructor(dataSource: DataSource) {
    super(MapHistoryPointToBrandPointEntity, dataSource);
  }
}
