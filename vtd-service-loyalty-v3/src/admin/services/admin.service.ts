import { Injectable } from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import { paginate } from 'nestjs-typeorm-paginate';
import { Transactional } from 'typeorm-transactional';
import { AccountToAdminRoleRepository } from '../../admin_authorization/repositories/account-to-admin-role.repository';
import { AdminAclRepository } from '../../admin_authorization/repositories/admin-acl.repository';
import { AppResponseDto } from '../../common/dtos/app-response.dto';
import {
  NotFoundExc,
  BadRequestExc,
} from '../../common/exceptions/custom-http.exception';
import { generateRandomString } from '../../common/utils';
import { EmailService } from '../../qr-code-sbps/services/email.service';
import {
  CreateAdminRequestDto,
  GetListAdminRequestDto,
  UpdateAdminRequestDto,
} from '../dtos/requests/admin.request.dto';
import { AdminRepository } from '../repositories/admin.repository';
import { AccountData } from '../../proto/account.pb';

@Injectable()
export class AdminService {
  constructor(
    private readonly adminRepo: AdminRepository,
    private readonly accountToAdminRoleRepo: AccountToAdminRoleRepository,
    private readonly adminAclRepo: AdminAclRepository,
    private readonly emailService: EmailService,
  ) {}

  async getList(dto: GetListAdminRequestDto) {
    const { page, limit, searchText } = dto;

    const queryBuilder = this.adminRepo.createQueryBuilder('admin');
    queryBuilder.orderBy('admin.id', 'DESC');

    if (searchText) {
      queryBuilder.andWhere(
        '(admin.firstName LIKE :searchText OR admin.lastName LIKE :searchText OR admin.email LIKE :searchText)',
        {
          searchText: `%${searchText}%`,
        },
      );
    }

    const { items, meta } = await paginate(queryBuilder, {
      limit,
      page,
    });

    return AppResponseDto.fromNestJsPagination(items, meta);
  }

  async getById(id: number) {
    const admin = await this.adminRepo.findOne({
      where: { id },
    });

    if (!admin) {
      throw new NotFoundExc('Không tìm thấy Admin');
    }

    return new AppResponseDto(admin);
  }

  @Transactional()
  async create(dto: CreateAdminRequestDto) {
    const { firstName, lastName, email, status, permission } = dto;

    const existingAdmin = await this.adminRepo.findOne({
      where: { email },
    });
    if (existingAdmin) {
      throw new NotFoundExc('Email đã tồn tại');
    }

    const randomPassword = generateRandomString(6);
    const hashPassword = await bcrypt.hash(randomPassword, 10);
    const newAdmin = this.adminRepo.create({
      firstName,
      lastName,
      email,
      status,
      permission,
      password: hashPassword,
    });

    await this.adminRepo.save(newAdmin);

    await this.emailService.sendEmail(
      newAdmin.email,
      '[VitaDairy] - Chào mừng bạn đến với hệ thống VitaDairy dành cho quản lý',
      'VitaDairy',
      { name: 'VitaDairy' },
      `<p>Chào bạn,</p>
        <p>
        Bạn đã được cấp tài khoản để đăng nhập vào hệ thống VitaDairy dành cho quản lý.<br />
        Dưới đây là thông tin đăng nhập:
        </p>

        <p>
        <strong>Email:</strong>
        <a href="mailto:${newAdmin.email}">${newAdmin.email}</a><br />
        <strong>Mật khẩu:</strong> ${randomPassword}<br />
        </p>

        <p>Trân trọng,<br />VitaDairy Admin</p>`,
    );

    return new AppResponseDto(newAdmin);
  }

  @Transactional()
  async updateById(id: number, dto: UpdateAdminRequestDto) {
    const { firstName, lastName, email, status, permission } = dto;

    const existingAdmin = await this.adminRepo.findOne({
      where: { id },
    });
    if (!existingAdmin) {
      throw new NotFoundExc('Không tìm thấy Admin');
    }

    const updatedAdmin = this.adminRepo.create({
      ...existingAdmin,
      firstName,
      lastName,
      email,
      status,
      // permission,
    });

    await this.adminRepo.save(updatedAdmin);

    return new AppResponseDto(updatedAdmin);
  }

  @Transactional()
  async deleteById(id: number, admin: AccountData) {
    if (id == admin.accountId) {
      throw new BadRequestExc('Không được xóa chính mình');
    }

    const existingAdmin = await this.adminRepo.findOne({
      where: { id },
    });

    if (!existingAdmin) {
      throw new NotFoundExc('Không tìm thấy Admin');
    }

    await Promise.all([
      this.accountToAdminRoleRepo.delete({ accountId: id }),
      this.adminAclRepo.delete({ accountId: id }),
    ]);

    await this.adminRepo.delete({ id });

    return new AppResponseDto('ok');
  }
}
