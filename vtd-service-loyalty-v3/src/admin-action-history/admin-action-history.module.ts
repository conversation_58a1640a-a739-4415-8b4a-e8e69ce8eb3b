import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { join } from 'path';
import { AuthModule } from '../auth/auth.module';
import {
  ACCOUNT_PACKAGE_NAME,
  ACCOUNT_SERVICE_NAME,
} from '../proto/account.pb';
import { AdminActionHistoryRepository } from './repositories/admin-action-history.repository';
import { AdminActionHistoryController } from './controllers/admin-action-history.controller';
import { AdminActionHistoryService } from './services/admin-action-history.service';
import { AdminAuthorizationModule } from '../admin_authorization/admin_authorization.module';

@Module({
  imports: [
    AuthModule,
    ClientsModule.register([
      {
        name: ACCOUNT_SERVICE_NAME,
        transport: Transport.GRPC,
        options: {
          url: 'vtd-service-user-v3:50052',
          package: ACCOUNT_PACKAGE_NAME,
          protoPath: join(
            __dirname +
              '/../../../node_modules/vtd-common-v3/proto/account.proto',
          ),
        },
      },
    ]),
    AdminAuthorizationModule,
  ],
  controllers: [AdminActionHistoryController],
  providers: [AdminActionHistoryRepository, AdminActionHistoryService],
  // Export to use repository in other controller, need import AdminActionHistoryModule first
  exports: [AdminActionHistoryRepository],
})
export class AdminActionHistoryModule {}
