import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Transactional } from 'typeorm-transactional';
import { GlobalConfig } from '../../common/config/global.config';
import { AdminActionHistoryRepository } from '../repositories/admin-action-history.repository';

@Injectable()
export class AdminActionHistoryCronJob {
  private readonly logger = new Logger(AdminActionHistoryCronJob.name);

  private readonly limitInOneRunCronJob: number;
  private readonly numberOfSplitBatches: number;
  private readonly daysToKeep: number;

  constructor(
    private readonly adminActionHistoryRepo: AdminActionHistoryRepository,
    private readonly configSer: ConfigService<GlobalConfig>,
  ) {
    const limitConfig = this.configSer.get<number>(
      'adminActionHistory.cronJob.removeOldData.limitInOneRunCronJob',
    );
    const splitConfig = this.configSer.get<number>(
      'adminActionHistory.cronJob.removeOldData.numberOfSplitBatchs',
    );
    const daysConfig = this.configSer.get<number>(
      'adminActionHistory.cronJob.removeOldData.daysToKeep',
    );

    this.limitInOneRunCronJob = Number(limitConfig ?? 0);
    this.numberOfSplitBatches = Number(splitConfig ?? 0);
    this.daysToKeep = Number(daysConfig ?? 0);
  }

  /**
   * Cron entrypoint to remove old admin action history records in batches.
   */
  async removeOldData(): Promise<void> {
    this.logger.debug('Start processing old admin action history records.');

    if (
      this.daysToKeep <= 0 ||
      this.limitInOneRunCronJob <= 0 ||
      this.numberOfSplitBatches <= 0
    ) {
      this.logger.warn('Wrong configuration. Skip removing old data.');
      return;
    }

    const limitPerBatch = Math.ceil(
      this.limitInOneRunCronJob / this.numberOfSplitBatches,
    );

    for (
      let batchIndex = 0;
      batchIndex < this.numberOfSplitBatches;
      batchIndex++
    ) {
      this.logger.debug(
        `Executing batch ${batchIndex + 1}/${this.numberOfSplitBatches}`,
      );

      try {
        const deletedCount = await this.processRemoveOldData(limitPerBatch);

        if (deletedCount === 0) {
          this.logger.debug('No more old data to delete. Stopping early.');
          break;
        }

        this.logger.verbose(
          `Batch ${batchIndex + 1} removed ${deletedCount} records.`,
        );
      } catch (error) {
        this.logger.error(
          `Error while removing old data in batch ${batchIndex + 1}:`,
          error,
        );
        break;
      }
    }

    this.logger.debug('Finished processing old data.');
  }

  /**
   * Remove a limited number of old records in one transactional batch.
   * Uses a subquery to safely limit and delete by ID.
   *
   * @param limit Number of records to delete in this batch
   *
   * @returns number of records deleted
   */
  @Transactional()
  private async processRemoveOldData(limit: number): Promise<number> {
    this.logger.debug(`Start deleting old records. Limit: ${limit}`);

    if (limit <= 0) {
      return 0;
    }

    const result = await this.adminActionHistoryRepo.query(
      `
      DELETE FROM admin_action_histories
      WHERE id IN (
        SELECT id
        FROM admin_action_histories
        WHERE created_at < NOW() - INTERVAL '${this.daysToKeep} days'
        ORDER BY created_at
        LIMIT ${limit}
      )
      RETURNING id;
      `,
    );
    const [deletedRows, deletedCount] = result;
    this.logger.debug('Deleted records: ', JSON.stringify(deletedRows));

    // const numberOfRowsDeleted = result.length;

    this.logger.debug(`Deleted ${deletedCount} records.`);
    return deletedCount;
  }
}
