import {
  Column,
  Entity,
  Index,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { BaseEntityV2 } from '../../common/entities/base-v2.entity';
import { Noti } from './noti.entity';
import { NotiDisplayTemplateType } from '../enums/noti-display-template.enum';

@Entity()
@Index(['type'], {
  unique: true,
  where: `type != '${NotiDisplayTemplateType.CUSTOM}' AND deleted_at IS NULL`,
})
export class NotiDisplayTemplate extends BaseEntityV2 {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'name' })
  name: string;

  @Column()
  title: string;

  @Column()
  content: string;

  @Column()
  desc: string;

  @Column()
  type: NotiDisplayTemplateType;

  @OneToMany(() => Noti, (n) => n.notiDisplayTemplate, { persistence: false })
  noties: Noti[];

  constructor(data: any = null) {
    super();
    if (data) {
      this.id = data.id;
      this.name = data.name;
      this.type = data.type;
      this.title = data.title;
      this.content = data.content;
      this.desc = data.desc;
    }
  }
}
