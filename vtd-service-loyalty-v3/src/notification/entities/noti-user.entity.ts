import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { User } from '../../auth/entities/user.entity';
import { PartialIndexWithSoftDelete } from '../../common/decorators/typeorm.decorator';
import { Noti } from './noti.entity';
import { BaseEntityV2 } from '../../common/entities/base-v2.entity';

@Entity()
@PartialIndexWithSoftDelete(['notiId', 'userId'], { unique: true })
export class NotiUser extends BaseEntityV2 {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  // join noti
  @Column({ name: 'noti_id' })
  notiId: number;

  @ManyToOne(() => Noti, (n) => n.notiUsers, { persistence: false })
  @JoinColumn({ name: 'noti_id' })
  noti: Noti;
  //end join noti

  // join user
  @Column({ name: 'user_id' })
  userId: number;

  @ManyToOne(() => User, (user) => user.notiUsers, { persistence: false })
  @JoinColumn({ name: 'user_id' })
  user: User;
  //end join user

  @Column({ type: 'timestamptz', name: 'read_at', nullable: true })
  readAt: Date;
}
