import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON>in<PERSON><PERSON>umn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { BaseEntityV2 } from '../../common/entities/base-v2.entity';
import { File } from '../../file/entities/file.entity';
import { NotiLinkTypeDto } from '../dtos/common/types/noti.type.dto';
import { NotiSource } from '../enums/noti.enum';
import { NotiUser } from './noti-user.entity';
import { NotiDisplayTemplate } from './noti-display-template.entity';

@Entity()
export class Noti extends BaseEntityV2 {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({ type: 'jsonb', name: 'link', nullable: true })
  link: NotiLinkTypeDto;

  @Column({
    type: 'jsonb',
    name: 'noti_display_template_params',
    nullable: true,
  })
  notiDisplayTemplateParams: any;

  @Column({ default: NotiSource.MB })
  source: NotiSource;

  @Column({ nullable: true, name: 'notification_user_id' })
  notificationUserId: number;

  @Column({ nullable: true, name: 'old_image_link' })
  oldImageLink: string;

  // join image
  @Column({ name: 'image_id', nullable: true })
  imageId: number;

  @Column({ nullable: true, name: 'noti_display_template_id' })
  notiDisplayTemplateId: number;

  @ManyToOne(() => File, (file) => file.noties, { persistence: false })
  @JoinColumn({ name: 'image_id' })
  image: File;

  @OneToMany(() => NotiUser, (nu) => nu.noti, { persistence: false })
  notiUsers: NotiUser[];

  @ManyToOne(() => NotiDisplayTemplate, (ndt) => ndt.noties, {
    persistence: false,
  })
  @JoinColumn({ name: 'noti_display_template_id' })
  notiDisplayTemplate: NotiDisplayTemplate;
}
