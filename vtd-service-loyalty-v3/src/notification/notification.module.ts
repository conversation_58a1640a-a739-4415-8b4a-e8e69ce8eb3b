import { Module } from '@nestjs/common';
import { NotificationChatbotController } from './controllers/notification.chatbot.controller';
import { NotificationUserRepository } from './repositories/notification-user.repository';
import { NotificationHistoryRepository } from './repositories/notification-history.repository';
import { NotificationUserPhysicalServerDevRepository } from './repositories/notification-user-physical-server-dev.repository';
import { NotificationUserPhysicalServerProdRepository } from './repositories/notification-user-physical-server-prod.repository';
import { NotificationUserPhysicalServerUatRepository } from './repositories/notification-user-physical-server-uat.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NotificationUserPhysicalServerDev } from './entities/notification-user-physical-server-dev.entity.ra';
import { NotificationUserController } from './controllers/notification.user.controller';
import { AuthService } from '../auth/auth.service';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { USER_PACKAGE_NAME, USER_SERVICE_NAME } from '../proto/user.pb';
import { join } from 'path';
import {
  ACCOUNT_PACKAGE_NAME,
  ACCOUNT_SERVICE_NAME,
} from '../proto/account.pb';
import { NotificationUserService } from './services/notification.user.service';
import { NotificationUser } from './entities/notification-user.entity';
import { NotificationChatbotService } from './services/notification.chatbot.service';
import { PartnerNotificationRepository } from './repositories/partner-notification.repository';
import { UserRepository } from '../auth/repositories/user.repository';
import { AuthChatbotService } from '../auth/services/auth.chatbot.service';
import { NotificationRepository } from './repositories/notification.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([NotificationUser]),
    ClientsModule.register([
      {
        name: USER_SERVICE_NAME,
        transport: Transport.GRPC,
        options: {
          url: 'vtd-service-user-v3:50051',
          package: USER_PACKAGE_NAME,
          protoPath: join(
            __dirname + '/../../../node_modules/vtd-common-v3/proto/user.proto',
          ),
        },
      },
      {
        name: ACCOUNT_SERVICE_NAME,
        transport: Transport.GRPC,
        options: {
          url: 'vtd-service-user-v3:50052',
          package: ACCOUNT_PACKAGE_NAME,
          protoPath: join(
            __dirname +
              '/../../../node_modules/vtd-common-v3/proto/account.proto',
          ),
        },
      },
    ]),
  ],
  controllers: [NotificationUserController, NotificationChatbotController],
  providers: [
    NotificationUserRepository,
    NotificationHistoryRepository,
    NotificationUserPhysicalServerDevRepository,
    NotificationUserPhysicalServerUatRepository,
    NotificationUserPhysicalServerProdRepository,
    AuthService,
    NotificationUserService,
    NotificationChatbotService,
    PartnerNotificationRepository,
    UserRepository,
    AuthChatbotService,
    NotificationRepository,
  ],
  exports: [
    NotificationUserRepository,
    NotificationHistoryRepository,
    NotificationUserPhysicalServerDevRepository,
    NotificationUserPhysicalServerUatRepository,
    NotificationUserPhysicalServerProdRepository,
  ],
})
export class NotificationModule {}
