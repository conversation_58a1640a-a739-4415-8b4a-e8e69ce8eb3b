import {
  IsValidArrayEnum,
  IsValidArrayString,
  IsValidDate,
  IsValidText,
} from '../../../../common/decorators/custom-validator.decorator';
import { PaginationReqDto } from '../../../../common/dtos/pagination.dto';
import {
  NotificationHistoryStatus,
  NotificationHistoryType,
} from '../../../enums/notification-history.enum';

export class GetNotificationHistoriesAdminReqDto extends PaginationReqDto {
  @IsValidArrayEnum({ required: false }, NotificationHistoryStatus)
  statuses?: NotificationHistoryStatus[];

  @IsValidArrayEnum({ required: false }, NotificationHistoryType)
  types?: NotificationHistoryType[];

  @IsValidArrayString({ required: false })
  sources?: string[];

  @IsValidText({ required: false })
  recipient?: string;

  @IsValidDate({ required: false })
  startDate?: Date;

  @IsValidDate({ required: false })
  endDate?: Date;
}
