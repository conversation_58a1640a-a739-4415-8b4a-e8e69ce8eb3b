import { Transform } from 'class-transformer';
import { IsValidText } from '../../../../common/decorators/custom-validator.decorator';

export class NotiLinkTypeDto {
  @IsValidText({ required: false, trim: true })
  @Transform(({ value }) => value || null)
  mb?: string;

  @IsValidText({ required: false, trim: true })
  web?: string;

  constructor(data: any = null) {
    if (data) {
      this.mb = data.mb;
      this.web = data.web;
    }
  }
}
