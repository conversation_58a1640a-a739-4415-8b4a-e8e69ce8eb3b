import { PickType } from '@nestjs/swagger';
import dayjs from 'dayjs';
import { FileResDto } from '../../../../file/dtos/common/res/file.res.dto';
import { Noti } from '../../../entities/noti.entity';
import { TIME_ZONE_HCM } from '../../../../common/constants/index.constant';

interface NotiResDtoParams {
  data: Noti;
  title?: string;
  content?: string;
  readAt?: Date;
  desc?: string;
}

export class NotiResDto extends PickType(Noti, [
  'id',
  'link',
  'notiDisplayTemplateId',
  'notiDisplayTemplateParams',
  'imageId',
]) {
  readAt?: Date | string;
  title?: string;
  content?: string;
  image?: FileResDto;
  desc?: string;
  createdDate?: Date | string;

  static mapProperty(
    dto: NotiResDto,
    { data, content, title, desc, readAt }: NotiResDtoParams,
    source = 'user',
  ) {
    dto.id = data.id;
    dto.link = null;
    dto.createdDate = data.createdDate;
    dto.title = title;
    dto.content = content;
    dto.desc = desc;
    dto.readAt = readAt;
    // TODO: hide noti link with noti of event quy 3 2024 mass sampling
    // TODO: can not find why noti of event quy 3 2024 mass sampling
    // TODO: has set link in table noti
    // TODO: remove after find out how to fix issue
    // TODO: force update null for all case
    if ('user' != source) {
      //dto.link = data.link;
      dto.link = null;
    }
  }

  static forUser(params: NotiResDtoParams) {
    const { data, readAt } = params;
    const result = new NotiResDto();

    if (!data) return null;
    this.mapProperty(result, params);

    result.image = FileResDto.forCustomer({ data: data.image });

    if (data.createdDate) {
      result.createdDate = dayjs(data.createdDate)
        .tz(TIME_ZONE_HCM)
        .format('YYYY-MM-DD HH:mm:ss');
    }
    if (readAt) {
      result.readAt = dayjs(readAt)
        .tz(TIME_ZONE_HCM)
        .format('YYYY-MM-DD HH:mm:ss');
    }

    return result;
  }

  static forAdmin(params: NotiResDtoParams) {
    const { data } = params;
    const result = new NotiResDto();
    if (!data) return null;
    this.mapProperty(result, params, 'admin');

    result.image = FileResDto.forAdmin({ data: data.image });

    return result;
  }
}
