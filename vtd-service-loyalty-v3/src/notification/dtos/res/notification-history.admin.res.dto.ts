import { AutoMapDecorator } from '../../../common/decorators/automap.decorator';
import { BaseMapperDto } from '../../../common/dtos/base-mapper.dto';

export class NotificationHistoryAdminResDto extends BaseMapperDto {
  @AutoMapDecorator()
  id: number;

  @AutoMapDecorator()
  recipient: string;

  @AutoMapDecorator()
  content: string;

  @AutoMapDecorator()
  sentTime: Date;

  @AutoMapDecorator()
  status: string;

  @AutoMapDecorator()
  type: string;

  @AutoMapDecorator()
  source: string;

  @AutoMapDecorator()
  response: string;
}
