import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { Noti } from '../entities/noti.entity';

@Injectable()
export class NotiRepository extends BaseRepository<Noti> {
  constructor(dataSource: DataSource) {
    super(Noti, dataSource);
  }

  public async createMulti(listNoti: Array<Noti>) {
    const notiEntities = this.create(listNoti);
    await this.insert(notiEntities);
    return notiEntities;
  }
}
