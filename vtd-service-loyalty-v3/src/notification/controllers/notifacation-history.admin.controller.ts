import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Prefix } from '../../common/constants/index.constant';
import { UseAdmin } from '../../common/decorators/user.decorator';
import { NotificationHistoryAdminService } from '../services/notification-history.admin.service';
import { AppResponseDto } from '../../common/dtos/app-response.dto';
import { NotificationHistoryAdminResDto } from '../dtos/res/notification-history.admin.res.dto';
import { GetNotificationHistoriesAdminReqDto } from '../dtos/req/admin/get-notification-histories.admin.req.dto';

@Controller({ version: '1', path: `${Prefix.ADMIN}/notification-histories` })
@ApiTags('Notification History Admin Controller')
@UseAdmin()
export class NotificationHistoryAdminController {
  constructor(
    private readonly notificationHistoryAdminService: NotificationHistoryAdminService,
  ) {}

  @Get()
  async getNotificationHistories(
    @Query() dto: GetNotificationHistoriesAdminReqDto,
  ) {
    const { items, meta } =
      await this.notificationHistoryAdminService.getNotificationHistories(dto);
    return AppResponseDto.fromNestJsPagination(
      items.map((item) => new NotificationHistoryAdminResDto(item)),
      meta,
    );
  }
}
