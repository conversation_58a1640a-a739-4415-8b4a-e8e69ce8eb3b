import { Injectable, Logger } from '@nestjs/common';
import { paginate } from 'nestjs-typeorm-paginate';
import { In } from 'typeorm';
import { StatusCode } from '../../common/constants/status-code.constant';
import { AppResponseDto } from '../../common/dtos/app-response.dto';
import { File } from '../../file/entities/file.entity';
import { UserSessionData } from '../../proto/user.pb';
import { NotiResDto } from '../dtos/common/res/noti.res.dto';
import { NotiLinkTypeDto } from '../dtos/common/types/noti.type.dto';
import { GetListNotiUserReqDto } from '../dtos/user/req/noti.user.req.dto';
import { Noti } from '../entities/noti.entity';
import { NotificationUserStatus } from '../enums/notification-user.enum';
import { PartnerNotificationStatus } from '../enums/partner-notification.enum';
import { NotiRepository } from '../repositories/noti.repository';
import { NotificationUserRepository } from '../repositories/notification-user.repository';
import { PartnerNotificationRepository } from '../repositories/partner-notification.repository';
import { RenderNotiService } from './render-noti.service';
import { NotiDisplayTemplateType } from '../enums/noti-display-template.enum';

const LOGGER = new Logger('NotificationService');
@Injectable()
export class NotificationUserService {
  constructor(
    private readonly notificationUserRepository: NotificationUserRepository,
    private partnerNotificationRepo: PartnerNotificationRepository,
    private renderNotiSer: RenderNotiService,
    private notiRepo: NotiRepository,
  ) {}

  async getListNoti(
    dto: GetListNotiUserReqDto,
    userSession: UserSessionData,
  ): Promise<AppResponseDto> {
    const { limit, page, source } = dto;

    const qb = this.notiRepo
      .createQueryBuilder('n')
      .innerJoin('n.notiUsers', 'nu')
      .where('nu.userId = :userId', { userId: userSession.userId })
      .groupBy('n.id')
      .select('n.id')
      .orderBy('n.id', 'DESC');

    if (source) {
      qb.andWhere('n.source = :source', { source });
    }

    const { items, meta } = await paginate(qb, { page, limit });

    const noties = await this.notiRepo.find({
      where: {
        id: In(items.map((item) => item.id)),
        notiUsers: { userId: userSession.userId },
      },
      relations: {
        notiDisplayTemplate: true,
        notiUsers: { user: true },
        image: true,
      },
    });

    const notificationUserIds = noties.map((el) => el.notificationUserId);
    const notificationUsers = await this.notificationUserRepository.find({
      where: {
        id: In(notificationUserIds),
      },
    });
    const mapNotiIdToNoti: Record<string, Noti> = {};
    for (const noti of noties) {
      mapNotiIdToNoti[noti.id] = noti;
    }
    const result = items.map((item) => {
      const noti = mapNotiIdToNoti[item.id];

      const _notiDisplayTemplate = noti.notiDisplayTemplate;
      if (
        noti?.notiDisplayTemplate?.name ==
        NotiDisplayTemplateType.THIRD_PARTY_TPZ
      ) {
        const match = notificationUsers.find(
          (el) => el.id == noti.notificationUserId,
        );

        _notiDisplayTemplate.title = match.title;
        _notiDisplayTemplate.content = match.content;
        _notiDisplayTemplate.desc = match.description || '';
        let bucket = '';
        let filename = '';
        if (this._isHttpValid(match?.image) == true) {
          const url = new URL(match.image);
          const pathParts = url.pathname.split('/');
          bucket = pathParts[1];
          filename = pathParts.slice(2).join('/');
          noti.image =
            new File({ url: match.image, bucket, name: filename }) || null;
        }
        noti.link = new NotiLinkTypeDto({
          mb: match?.deepLink || null,
          web: match?.deepLink || null,
        });
      }

      const { content, title, desc } = this.renderNotiSer.render({
        noti,
        notiDisplayTemplate: _notiDisplayTemplate,
        user: noti.notiUsers[0].user,
      });
      return NotiResDto.forUser({
        data: noti,
        content,
        title,
        desc,
        readAt: noti.notiUsers[0].readAt,
      });
    });

    return AppResponseDto.fromNestJsPagination(result, meta);
  }

  async getCountUnread(
    userSessionData: UserSessionData,
  ): Promise<AppResponseDto> {
    const notificationCount: number =
      await this.countUnreadNotificationByUserId(userSessionData.userId);

    return {
      meta: StatusCode.SUCCESS,
      response: { notificationCount },
    } as AppResponseDto;
  }

  private async countUnreadNotificationByUserId(
    userId: number,
  ): Promise<number> {
    try {
      return this.notificationUserRepository.countByUserIdAndStatus(
        userId,
        NotificationUserStatus.UNREAD,
      );
    } catch (error) {
      LOGGER.error('countUnreadNotificationByUserId error : ', error);
      return 0;
    }
  }

  async getPartnerNotificationStatus(user: UserSessionData) {
    const parnerNoti = await this.partnerNotificationRepo.findOneBy({
      userId: user.userId,
    });

    if (!parnerNoti) {
      return new AppResponseDto(PartnerNotificationStatus.READ);
    }

    return new AppResponseDto(parnerNoti.status);
  }

  async updatePartnerNotification(user: UserSessionData) {
    const parnerNoti = await this.partnerNotificationRepo.findOneBy({
      userId: user.userId,
    });

    if (!parnerNoti) return new AppResponseDto(PartnerNotificationStatus.READ);

    if (parnerNoti.status == PartnerNotificationStatus.READ)
      return new AppResponseDto(parnerNoti.status);

    parnerNoti.status = PartnerNotificationStatus.READ;
    await this.partnerNotificationRepo.save(parnerNoti);

    return new AppResponseDto(parnerNoti.status);
  }

  private _isHttpValid(str) {
    try {
      const newUrl = new URL(str);
      return newUrl.protocol === 'http:' || newUrl.protocol === 'https:';
    } catch (err) {
      return false;
    }
  }
}
