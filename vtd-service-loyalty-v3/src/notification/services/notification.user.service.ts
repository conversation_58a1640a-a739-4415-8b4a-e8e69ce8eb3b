import { Injectable, Logger } from '@nestjs/common';
import { paginate } from 'nestjs-typeorm-paginate';
import { StatusCode } from '../../common/constants/status-code.constant';
import { AppResponseDto } from '../../common/dtos/app-response.dto';
import { UserSessionData } from '../../proto/user.pb';
import { GetListNotiUserReqDto } from '../dtos/user/req/noti.user.req.dto';
import { NotificationUserStatus } from '../enums/notification-user.enum';
import { PartnerNotificationStatus } from '../enums/partner-notification.enum';
import { NotificationUserRepository } from '../repositories/notification-user.repository';
import { PartnerNotificationRepository } from '../repositories/partner-notification.repository';

const LOGGER = new Logger('NotificationService');
@Injectable()
export class NotificationUserService {
  constructor(
    private readonly notificationUserRepository: NotificationUserRepository,
    private partnerNotificationRepo: PartnerNotificationRepository,
  ) {}

  async getListNoti(
    dto: GetListNotiUserReqDto,
    userSession: UserSessionData,
  ): Promise<AppResponseDto> {
    const { limit, page, source } = dto;

    const qb = this.notificationUserRepository
      .createQueryBuilder('nu')
      .andWhere('nu.userId = :userId', { userId: userSession.userId })
      .orderBy('nu.id', 'DESC');

    if (source) {
      qb.andWhere('nu.source = :source', { source });
    }

    const { items, meta } = await paginate(qb, { page, limit });

    return AppResponseDto.fromNestJsPagination(items, meta);
  }

  async getCountUnread(
    userSessionData: UserSessionData,
  ): Promise<AppResponseDto> {
    const notificationCount: number =
      await this.countUnreadNotificationByUserId(userSessionData.userId);

    return {
      meta: StatusCode.SUCCESS,
      response: { notificationCount },
    } as AppResponseDto;
  }

  private async countUnreadNotificationByUserId(
    userId: number,
  ): Promise<number> {
    try {
      return this.notificationUserRepository.countByUserIdAndStatus(
        userId,
        NotificationUserStatus.UNREAD,
      );
    } catch (error) {
      LOGGER.error('countUnreadNotificationByUserId error : ', error);
      return 0;
    }
  }

  async getPartnerNotificationStatus(user: UserSessionData) {
    const parnerNoti = await this.partnerNotificationRepo.findOneBy({
      userId: user.userId,
    });

    if (!parnerNoti) {
      return new AppResponseDto(PartnerNotificationStatus.READ);
    }

    return new AppResponseDto(parnerNoti.status);
  }

  async updatePartnerNotification(user: UserSessionData) {
    const parnerNoti = await this.partnerNotificationRepo.findOneBy({
      userId: user.userId,
    });

    if (!parnerNoti) return new AppResponseDto(PartnerNotificationStatus.READ);

    if (parnerNoti.status == PartnerNotificationStatus.READ)
      return new AppResponseDto(parnerNoti.status);

    parnerNoti.status = PartnerNotificationStatus.READ;
    await this.partnerNotificationRepo.save(parnerNoti);

    return new AppResponseDto(parnerNoti.status);
  }
}
