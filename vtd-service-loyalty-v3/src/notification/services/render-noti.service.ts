import { Injectable } from '@nestjs/common';
import dayjs from 'dayjs';
import { render } from 'mustache';
import { TIME_ZONE_HCM } from '../../common/constants/index.constant';
import { NotiDisplayTemplate } from '../entities/noti-display-template.entity';
import { Noti } from '../entities/noti.entity';
import { User } from '../../auth/entities/user.entity';

export const DYNAMIC_PARAMS = [
  'user_name',
  'user_birthday',
  'user_phone',
  'user_gift_point',
  'user_tier_code',
  'user_last_login_date',
];

@Injectable()
export class RenderNotiService {
  constructor() {}

  render(params: RenderParams): RenderNotiResult {
    const { noti, notiDisplayTemplate, user } = params;

    const notiParams = {
      ...noti.notiDisplayTemplateParams,
      ...(user?.firstName &&
        user?.lastName && {
          user_name: `${user.firstName} ${user.lastLoginDate}`,
        }),
      ...(user?.birthDay && { user_birthday: user.birthDay }),
      ...(user?.phoneNumber && { user_phone: user.phoneNumber }),
      ...(user?.giftPoint && { user_gift_point: user.giftPoint }),
      ...(user?.tierCode && { user_tier_code: user.tierCode }),
      ...(user?.lastLoginDate && {
        user_last_login_date: dayjs(user.lastLoginDate)
          .tz(TIME_ZONE_HCM, true)
          .format('DD/MM/YYYY'),
      }),
    };
    const renderedTitle = render(notiDisplayTemplate.title, notiParams);
    const renderedContent = render(notiDisplayTemplate.content, notiParams);
    const renderedDesc = render(notiDisplayTemplate.desc, notiParams);

    return {
      title: renderedTitle,
      content: renderedContent,
      desc: renderedDesc,
    };
  }

  isNeedUserInfo(notiDisplayTemplate: NotiDisplayTemplate) {
    for (const dynamicParam of DYNAMIC_PARAMS) {
      if (notiDisplayTemplate.title.includes(dynamicParam)) return true;
      if (notiDisplayTemplate.content.includes(dynamicParam)) return true;
      if (notiDisplayTemplate.desc.includes(dynamicParam)) return true;
    }

    return false;
  }
}

export type RenderParams = {
  noti: Pick<Noti, 'notiDisplayTemplateParams'>;
  notiDisplayTemplate: NotiDisplayTemplate;
  user?: User;
};

export type RenderNotiResult = {
  title: string;
  content: string;
  desc: string;
};
