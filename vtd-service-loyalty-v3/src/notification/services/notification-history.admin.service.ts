import { Injectable } from '@nestjs/common';
import { isArray, isString } from 'lodash';
import { paginate } from 'nestjs-typeorm-paginate';
import { SelectQueryBuilder } from 'typeorm';
import { QueryFilter } from '../../common/decorators/query-filter.decorator';
import { GetNotificationHistoriesAdminReqDto } from '../dtos/req/admin/get-notification-histories.admin.req.dto';
import { NotificationHistory } from '../entities/notification-history.entity';
import { NotificationHistoryRepository } from '../repositories/notification-history.repository';

@Injectable()
export class NotificationHistoryAdminService {
  constructor(
    private readonly notificationHistoryRepo: NotificationHistoryRepository,
  ) {}

  async getNotificationHistories(dto: GetNotificationHistoriesAdminReqDto) {
    const queryBuilder = await this.buildNotificationHistoriesQuery(dto);

    return paginate(queryBuilder, {
      limit: dto.limit,
      page: dto.page,
    });
  }

  private async buildNotificationHistoriesQuery(
    dto: GetNotificationHistoriesAdminReqDto,
  ): Promise<SelectQueryBuilder<NotificationHistory>> {
    const queryBuilder = this.notificationHistoryRepo
      .createQueryBuilder('nh')
      .orderBy('nh.sentTime', 'DESC');

    await this.notificationHistoryRepo.applyAllFilters(queryBuilder, dto, this);

    return queryBuilder;
  }

  @QueryFilter({ order: 10 })
  private applyStatusFilter(
    queryBuilder: SelectQueryBuilder<NotificationHistory>,
    dto: GetNotificationHistoriesAdminReqDto,
  ): void {
    if (dto.statuses?.length) {
      queryBuilder.andWhere('nh.status IN (:...statuses)', {
        statuses: dto.statuses,
      });
    }
  }

  @QueryFilter({ order: 20 })
  private applyTypeFilter(
    queryBuilder: SelectQueryBuilder<NotificationHistory>,
    dto: GetNotificationHistoriesAdminReqDto,
  ): void {
    if (dto.types?.length) {
      queryBuilder.andWhere('nh.type IN (:...types)', {
        types: dto.types,
      });
    }
  }

  @QueryFilter({ order: 30 })
  private applySourceFilter(
    queryBuilder: SelectQueryBuilder<NotificationHistory>,
    dto: GetNotificationHistoriesAdminReqDto,
  ): void {
    if (isString(dto.sources)) {
      queryBuilder.andWhere('nh.source = :source', { source: dto.sources });
    }

    if (isArray(dto.sources) && dto.sources.length) {
      queryBuilder.andWhere('nh.source IN (:...sources)', {
        sources: dto.sources,
      });
    }
  }

  @QueryFilter({ order: 40 })
  private applyRecipientFilter(
    queryBuilder: SelectQueryBuilder<NotificationHistory>,
    dto: GetNotificationHistoriesAdminReqDto,
  ): void {
    if (dto.recipient) {
      queryBuilder.andWhere('nh.recipient ILIKE :recipient', {
        recipient: `${dto.recipient}%`,
      });
    }
  }

  @QueryFilter({ order: 50 })
  private applyDateRangeFilter(
    queryBuilder: SelectQueryBuilder<NotificationHistory>,
    dto: GetNotificationHistoriesAdminReqDto,
  ): void {
    if (!dto.startDate && !dto.endDate) {
      queryBuilder.andWhere(`nh.sentTime >= NOW() - INTERVAL '30 days'`);
      return;
    }

    if (dto.startDate) {
      queryBuilder.andWhere('nh.sentTime >= :startDate', {
        startDate: dto.startDate,
      });
    }

    if (dto.endDate) {
      queryBuilder.andWhere('nh.sentTime <= :endDate', {
        endDate: dto.endDate,
      });
    }
  }
}
