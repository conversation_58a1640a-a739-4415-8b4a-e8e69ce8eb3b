import { Injectable } from '@nestjs/common';
import { BaseRepository } from 'src/common/repositories/base.repositories';
import { DataSource } from 'typeorm';
// import { ProductMst } from '../entities/product-mst.entity';
import { Product } from '../../product/entities/product.entity';

@Injectable()
export class ProductMstRepository extends BaseRepository<Product> {
  constructor(dataSource: DataSource) {
    super(Product, dataSource);
  }
}
