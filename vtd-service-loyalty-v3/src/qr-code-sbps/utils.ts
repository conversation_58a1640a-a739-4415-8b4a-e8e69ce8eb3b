import secureRandom = require('random-number-csprng');
import {
  CHARACTERS,
  FACTORY,
  FORMAT_DATE_CREATE,
  SKU,
  TIMESTAMP_BEGIN_20250901_STRING,
  WEIGHT,
} from './constants';
// import { createCanvas, registerFont } from 'canvas';
import QRCode = require('qrcode');
import dayjs from 'dayjs';
// registerFont('src/common/font/SansSerifFLF.otf', { family: 'SansSerifFLF' });
const Jimp = require('jimp');
const qrCode = require('qrcode-reader');
import { VitaQr } from './entities/vita-qr.entity';
import {
  TIMESTAMP_BEGIN_20241001_STRING,
  TIMESTAMP_BEGIN_20250601_STRING,
} from './constants';
import { WebAppList } from '../common/enums/webapp-list.enum';
import {
  compareDateWithDateInTimezoneNewVersion,
  isValueInEnum,
} from '../common/utils';
import { AppBaseExc } from '../common/exceptions/custom-app.exception';
import { StatusCode } from '../common/constants/status-code.constant';
import { AppMetaDto } from '../common/dtos/app-response.dto';
import { EventSbpsSkuEntity } from '../event-sbps-sku/entities/event-sbps-sku.entity';
import { EVENT_CODE } from '../common/constants/index.constant';

export function genListUniqueRandomNumber(
  amout: number,
  min: number,
  max: number,
) {
  const result = new Set<number>();

  while (result.size < amout) {
    // const randomNumb = await secureRandom(min, max);
    const randomNumb = Math.floor(Math.random() * (max - min) + min);
    result.add(randomNumb);
  }

  return result;
}

export function genMonthQr(month: number, year: number) {
  // start month = 12 start year = 2022
  const index =
    ((month % 12) + (Math.floor(month / 12) + year - 2022 - 1) * 12) %
    CHARACTERS.length;
  // if (index < 0) return 0; // date < 12/2022
  return index;
}

export function genQrCode(text: string) {
  const canvas: any = null;
  QRCode.toCanvas(canvas, text, {
    errorCorrectionLevel: 'H',
    margin: 1,
    width: 400,
    color: {
      dark: '#000000',
      light: '#ffffff',
    },
  });
  // const ctx = canvas.getContext('2d');
  // ctx.font = '30px SansSerifFLF';
  // ctx.textAlign = 'center';
  // ctx.fillText(text, 200, 380);
  return canvas.toBuffer();
}

export async function urlToString(buffer: Buffer) {
  const getCode = () => {
    return new Promise((resolve, reject) => {
      Jimp.read(buffer, function (err, image) {
        if (err) {
          reject(err);
        }
        const qrcode = new qrCode();
        qrcode.callback = function (err, value) {
          if (err) {
            reject(err);
          }
          resolve(value?.result);
        };

        qrcode.decode(image?.bitmap);
      });
    });
  };
  return await getCode();
}

export const genFileId = (
  sku: string,
  weight: number,
  factory: string,
  indexCharacter: number,
  manufactureDate: Date,
) => {
  const time = dayjs(manufactureDate);
  const currentTime = dayjs().valueOf();
  const fileId = `${SKU[sku]}_${WEIGHT[weight]}_${FACTORY[factory]}_${
    CHARACTERS[indexCharacter]
  }_${time.format(FORMAT_DATE_CREATE)}*${currentTime.toString(16)}`;
  return fileId;
};

export const genFileNameExport = (factoryName: string, fileId: string) => {
  return `${factoryName}_${fileId.split('*')[0]}`;
};

export function getObjectKey(obj, value) {
  return Object.keys(obj).find((key) => obj[key] === value);
}

export const validateDataQrManufactureDate = (
  dataQr: VitaQr,
  callFromWebApp = false,
): boolean => {
  if (!dataQr) return false;

  if (callFromWebApp) {
    // Type of createdAt is timestamp without timezone
    // We need convert to string to make sure dayjs parsing to tz work well
    if (
      -1 ==
      compareDateWithDateInTimezoneNewVersion(
        dataQr.createdAt.toUTCString(),
        TIMESTAMP_BEGIN_20241001_STRING,
      )
    ) {
      return false;
    }
  }

  return true;
};

export const validateCallFromWebApp = (webapp_name: WebAppList): boolean => {
  let callFromWebApp = false;
  if (webapp_name) {
    // Check webapp name valid
    if (!isValueInEnum(WebAppList, webapp_name)) {
      throw new AppBaseExc(StatusCode.SBPS_DEFAULT_ERROR, '', true);
    }
    callFromWebApp = true;
  }

  return callFromWebApp;
};

export const validateAllowAddPoint = (
  dataQr: VitaQr,
  oggiOrClgSkuGet: EventSbpsSkuEntity,
  webapp_name: WebAppList = WebAppList.WA_SBPS_24,
): boolean => {
  if (!dataQr) {
    throw new AppBaseExc(StatusCode.SBPS_DEFAULT_ERROR, '', true);
  }

  const isDataQrManufactureDateBefore20241001 =
    -1 ==
    compareDateWithDateInTimezoneNewVersion(
      dataQr.createdAt.toUTCString(),
      TIMESTAMP_BEGIN_20241001_STRING,
    );
  const isDataQrManufactureDateBefore20250601 =
    -1 ==
    compareDateWithDateInTimezoneNewVersion(
      dataQr.createdAt.toUTCString(),
      TIMESTAMP_BEGIN_20250601_STRING,
    );
  const isDataQrManufactureBefore20250901 =
    -1 ==
    compareDateWithDateInTimezoneNewVersion(
      dataQr.createdAt.toUTCString(),
      TIMESTAMP_BEGIN_20250901_STRING,
    );
  const isUsingOggiOrClgSku = oggiOrClgSkuGet ? true : false;

  if (isDataQrManufactureDateBefore20241001) {
    return true;
  }
  if (isDataQrManufactureDateBefore20250601) {
    return false;
  }
  if (isDataQrManufactureBefore20250901) {
    return false;
  }
  if (isUsingOggiOrClgSku) {
    return false;
  }

  return true;
};

export const validateAllowJoinEvent = (
  dataQr: VitaQr,
  oggiOrClgSkuGet: EventSbpsSkuEntity,
  webapp_name: WebAppList = WebAppList.WA_SBPS_24,
): boolean => {
  if (!dataQr) {
    throw new AppBaseExc(StatusCode.SBPS_DEFAULT_ERROR, '', true);
  }

  const isDataQrManufactureDateBefore20241001 =
    -1 ==
    compareDateWithDateInTimezoneNewVersion(
      dataQr.createdAt.toUTCString(),
      TIMESTAMP_BEGIN_20241001_STRING,
    );
  const isDataQrManufactureDateBefore20250601 =
    -1 ==
    compareDateWithDateInTimezoneNewVersion(
      dataQr.createdAt.toUTCString(),
      TIMESTAMP_BEGIN_20250601_STRING,
    );
  const isDataQrManufactureBefore20250901 =
    -1 ==
    compareDateWithDateInTimezoneNewVersion(
      dataQr.createdAt.toUTCString(),
      TIMESTAMP_BEGIN_20250901_STRING,
    );
  const isUsingOggiOrClgSku = oggiOrClgSkuGet ? true : false;

  if (isDataQrManufactureDateBefore20241001) {
    return false;
  }
  if (isDataQrManufactureDateBefore20250601) {
    return false;
  }
  if (isDataQrManufactureBefore20250901) {
    return true;
  }
  if (isUsingOggiOrClgSku) {
    return true;
  }

  return true;
};

export const validateAllowRunFlowHandleScanQrFail = (
  dataQr: VitaQr,
  error: any,
): boolean => {
  if (!dataQr || !error) {
    return true;
  }

  // Type of createdAt is timestamp without timezone
  // We need convert to string to make sure dayjs parsing to tz work well
  const isDataQrManufactureDateAfter20241001 =
    -1 !=
    compareDateWithDateInTimezoneNewVersion(
      dataQr.createdAt.toUTCString(),
      TIMESTAMP_BEGIN_20241001_STRING,
    );
  if (error instanceof AppBaseExc && isDataQrManufactureDateAfter20241001) {
    const errorExcMeta: AppMetaDto = error.toMeta();
    if (StatusCode.SBPS_OUTOF_QUANTITY.error == errorExcMeta.error) {
      return false;
    }
  }

  return true;
};

export const validateEventCodeIsSbps = (eventCode: string) => {
  return eventCode === EVENT_CODE.SBPS;
};

export const mappingDefaultErrorCode = (eventCode: string) => {
  if (validateEventCodeIsSbps(eventCode)) {
    return StatusCode.SBPS_DEFAULT_ERROR;
  } else {
    return StatusCode.SB_DEFAULT_ERROR;
  }
};
