import {
  Body,
  Controller,
  Get,
  Headers,
  HttpCode,
  Param,
  Post,
  Query,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Prefix } from '../../common/constants/index.constant';
import {
  AuthUser,
  CurrentToken,
  UseAuth,
} from '../../common/decorators/user.decorator';
import { AuthUserDto } from '../../common/dtos/authUser.dto';
import { ScanProductDto } from '../dtos/req/scan-product.dto';
import { ScanQrDto } from '../dtos/req/scan-qr.dto';
import { UserQrCodeSbpsService } from '../services/user-qr-code-sbps.service';
import { HeaderAppDto } from '../../common/dtos/header.dto';
import { UpdateStatusQrCodeDto } from '../dtos/req/update-status-qr-code.dto';

@Controller({ version: '1', path: `${Prefix.USER}/qr-code` })
@ApiTags('User qr-code')
@UseAuth()
export class UserQrCodeSbpsController {
  constructor(private userQrCodeSbpsService: UserQrCodeSbpsService) {}
  @Get('check-user-scan/:eventCode')
  checkUserScan(
    @Param('eventCode') eventCode: string,
    @AuthUser() authUser: AuthUserDto,
  ) {
    return this.userQrCodeSbpsService.checkUserScan(eventCode, authUser.userId);
  }

  @Get('qr-information')
  @HttpCode(200)
  getQrInformation(
    @Query() body: ScanQrDto,
    @AuthUser() authUser: AuthUserDto,
    @Headers() { appversionname, webappname }: HeaderAppDto,
  ) {
    return this.userQrCodeSbpsService.getQrInformation(
      body,
      authUser.userId,
      webappname,
    );
  }

  @Post('scan-qr/:eventCode')
  @HttpCode(200)
  scanQr(
    @Param('eventCode') eventCode: string,
    @Body() body: ScanQrDto,
    @AuthUser() authUser: AuthUserDto,
  ) {
    return this.userQrCodeSbpsService.scanQr(eventCode, body, authUser.userId);
  }

  @Post('scan-product/:eventCode')
  scanProduct(
    @Param('eventCode') eventCode: string,
    @Body() body: ScanProductDto,
    @AuthUser() authUser: AuthUserDto,
    @CurrentToken() token: string,
    @Headers() { appversionname, webappname }: HeaderAppDto,
  ) {
    return this.userQrCodeSbpsService.scanProduct(
      authUser.userId,
      token,
      eventCode,
      body,
      appversionname,
      webappname,
    );
  }

  @Post('qr-code/:eventCode/status')
  updateStatusQrCode(
    @Param('eventCode') eventCode: string,
    @Body() body: UpdateStatusQrCodeDto,
    @AuthUser() authUser: AuthUserDto,
    @CurrentToken() token: string,
    @Headers() { appversionname, webappname }: HeaderAppDto,
  ) {
    return this.userQrCodeSbpsService.handleUpdateStatusQrCode(
      authUser.userId,
      eventCode,
      body,
      appversionname,
      webappname,
    );
  }
}
