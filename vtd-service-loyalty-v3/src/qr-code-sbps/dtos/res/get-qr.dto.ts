import { Expose, Transform, Type } from 'class-transformer';
import { ValidateNested } from 'class-validator';
import dayjs from 'dayjs';
import { PUBLIC_URL, STATUS } from 'src/qr-code-sbps/constants';
import { VitaQrFile } from 'src/qr-code-sbps/entities/qr-vita-file.entity';

class QrRes {
  @Expose()
  id: number;

  @Expose()
  code: string;

  @Expose()
  image: string;

  @Expose()
  createdAt: string;

  @Expose()
  isActive: boolean;

  @Expose()
  status: STATUS;

  @Expose()
  prefix: string;

  @Expose()
  sku: string;

  @Expose()
  isAllowRa: boolean | null;

  @Expose()
  weight: number;

  @Expose()
  description: string;

  @Expose()
  expiredDate: string;
}

export class GetQrResDto {
  @Expose()
  @ValidateNested({ each: true })
  @Type(() => QrRes)
  @Transform(({ value }) => {
    value = value.map((item) => ({
      id: item?.id,
      code: item?.vitaQr?.code,
      manufactureDate: item?.vitaQr?.createdAt,
      isActive: item?.vitaQr?.isActive,
      status: item?.vitaQr?.status,
      prefix: item?.vitaQr?.prefix,
      sku: item?.vitaQr?.sku,
      isAllowRa: item?.product?.isAllowRa ?? null,
      weight: item?.vitaQr?.weight,
      description: item?.vitaQr?.description,
      image: `${PUBLIC_URL}${item?.file?.bucket}/${item?.file?.name}`,
      expiredDate: dayjs(item?.vitaQr?.createdAt).add(9, 'month'),
    }));
    return [...value];
  })
  data: QrRes[];

  @Expose()
  total: number;
}
