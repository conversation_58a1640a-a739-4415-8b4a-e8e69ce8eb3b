import { IsDateString, IsOptional } from 'class-validator';
import {
  IsValidBoolean,
  IsValidDate,
  IsValidNumber,
  IsValidText,
} from 'src/common/decorators/custom-validator.decorator';
import { PaginationReqDto } from 'src/common/dtos/pagination.dto';

export class ListQrReqDto extends PaginationReqDto {
  @IsValidText({ trim: true, required: false })
  searchText?: string;

  // @IsValidText({ trim: true, required: false })
  // fileId?: string;
  @IsValidNumber({ required: false })
  fileId?: number;

  @IsDateString()
  @IsOptional()
  startDate?: Date;

  @IsDateString()
  @IsOptional()
  endDate?: Date;

  @IsValidBoolean({ required: false })
  isAllowRa?: boolean;
}
