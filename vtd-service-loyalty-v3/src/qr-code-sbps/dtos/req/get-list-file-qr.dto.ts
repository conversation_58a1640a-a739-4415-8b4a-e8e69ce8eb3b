import { IsDateString, IsOptional } from 'class-validator';

import {
  IsValidBoolean,
  IsValidText,
} from 'src/common/decorators/custom-validator.decorator';
import { PaginationReqDto } from 'src/common/dtos/pagination.dto';

export class ListFileQrReqDto extends PaginationReqDto {
  @IsValidText({ trim: true, required: false })
  searchText?: string;

  @IsDateString()
  @IsOptional()
  startDate?: Date;

  @IsDateString()
  @IsOptional()
  endDate?: Date;

  @IsValidBoolean({ required: false })
  isAllowRa?: boolean;
}
