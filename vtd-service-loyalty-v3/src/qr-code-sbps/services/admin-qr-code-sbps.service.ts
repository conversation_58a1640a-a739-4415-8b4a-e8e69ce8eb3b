import { Bucket, Storage } from '@google-cloud/storage';
import { Injectable } from '@nestjs/common';
import dayjs from 'dayjs';
import StorageConfig from 'src/common/config/ggcloud-storage-config';
import { v4 as uuidv4 } from 'uuid';

import { In } from 'typeorm';
import * as xlsx from 'xlsx';
import { EVENT_CODE } from '../../common/constants/index.constant';
import { StatusCode } from '../../common/constants/status-code.constant';
import { AppResponseDto } from '../../common/dtos/app-response.dto';
import { PaginationReqDto } from '../../common/dtos/pagination.dto';
import { AppBaseExc } from '../../common/exceptions/custom-app.exception';
import {
  BadRequestExc,
  ConflictExc,
  InternalServerErrorExc,
  NotFoundExc,
} from '../../common/exceptions/custom-http.exception';
import { VitaJavaService } from '../../external/services/vita-java.service';
import {
  CHARACTERS,
  FACTORY,
  FileRequestStatus,
  FORMAT_DATE_CREATE,
  NUMBER_BATCHING,
  PUBLIC_URL,
  SKU,
  STATUS,
  WEIGHT,
} from '../constants';
import { CreateQrReqDTO } from '../dtos/req/create-qr.dto';
import { ExportQrReqDto } from '../dtos/req/export-qr.dto';
import { ListFileQrReqDto } from '../dtos/req/get-list-file-qr.dto';
import { ListQrReqDto } from '../dtos/req/list-qr.dto';
import { RejectFileReqDTO } from '../dtos/req/reject-file-request.dto';
import { UpdateQrReqDto } from '../dtos/req/update-qr.dto';
import { FileRequestCode } from '../entities/file-request-code.entity';
import { VitaQrFile } from '../entities/qr-vita-file.entity';
import { FileRequestCodeRepository } from '../repositories/file-request-code.repository';
import { FileRepository } from '../../file/repositories/file.repository';
import { VitaQrRepository } from '../repositories/qr-code-sbps.repository';
import { VitaQrFileRepository } from '../repositories/qr-vita-file.repository';
import {
  genFileId,
  genFileNameExport,
  genListUniqueRandomNumber,
  genMonthQr,
  genQrCode,
  getObjectKey,
} from '../utils';
import { EmailService } from './email.service';
import { InjectQueue } from '@nestjs/bull';
import { Job, Queue } from 'bull';
import { Transactional } from 'typeorm-transactional';
import { AdminActionHistoryRepository } from '../../admin-action-history/repositories/admin-action-history.repository';
import { EnumNameAdminMenuModule } from '../../admin_authorization/common/enums/admin-menu-module.enum';
import { AccountData } from 'src/proto/account.pb';
import { QrSkuSetupRepository } from '../repositories/qr-sku-setup.repository';
import { ProductAdminService } from '../../product/services/admin/product.admin.service';
import { GetProductsByEventCodeAdminRequestDto } from '../dtos/req/admin-qr-code-sbps.req.dto';
import { GetProductsAdminRequestDto } from '../../product/dtos/requests/admin/get-products.admin.request.dto';
import { LoggerService } from 'src/core';
import { ProductMstRepository } from '../repositories/product-mst.repository';
import { uniqueArray } from '../../common/utils';

@Injectable()
export class AdminQrCodeSbpsService {
  private bucket: Bucket;
  private storage: Storage;
  private _logger = new LoggerService(AdminQrCodeSbpsService.name);
  constructor(
    @InjectQueue('message-queue') private queue: Queue,
    private vitaQrRepo: VitaQrRepository,
    private fileRepo: FileRepository,
    private vitaQrFileRepo: VitaQrFileRepository,
    private vitaJavaService: VitaJavaService,
    private readonly emailService: EmailService,
    private fileRequestCodeRepo: FileRequestCodeRepository,
    private readonly adminActionHistoryRepo: AdminActionHistoryRepository,
    private readonly qrSkuSetupRepo: QrSkuSetupRepository,
    private readonly productAdminService: ProductAdminService,
    private readonly productMstRepo: ProductMstRepository,
  ) {
    this.storage = new Storage({
      projectId: StorageConfig.projectId,
      credentials: {
        client_email: StorageConfig.clientEmail,
        private_key: StorageConfig.privateKey,
      },
    });
    this.bucket = this.storage.bucket(StorageConfig.mediaBucket);
  }

  @Transactional()
  async requestFileCode(
    eventCode: string,
    body: CreateQrReqDTO,
    admin: AccountData,
  ) {
    const {
      amount,
      factory,
      isActive,
      sku,
      weight,
      description,
      manufactureDate,
      fileName,
    } = body;

    await this.getQrSkuSetupBySku(sku);

    if (!dayjs(manufactureDate).isValid()) {
      throw new BadRequestExc(`Thời gian sản xuất không hợp lệ`);
    }

    if (eventCode !== EVENT_CODE.SBPS) {
      throw new BadRequestExc(`Mã sự kiện không đúng`);
    }

    if (!Object.keys(FACTORY).includes(factory)) {
      throw new BadRequestExc(`Nhà máy không đúng`);
    }

    if (!Object.keys(WEIGHT).includes(weight.toString())) {
      throw new BadRequestExc(`Khối lượng không đúng`);
    }

    const time = dayjs(manufactureDate);
    const month = time.month();
    const year = time.year();
    // index month start 0
    const indexCharacterMonth = genMonthQr(month + 1, year);

    if (indexCharacterMonth < 0)
      throw new BadRequestExc(`Thời gian sản xuất nhỏ hơn 12/2022`);
    try {
      const [fileRequest] = await Promise.all([
        this.fileRequestCodeRepo.save({
          factory: body.factory,
          codeQuantity: amount,
          manufactureDate: manufactureDate,
          sku: sku,
          name: fileName,
          accRequestId: admin.accountId,
          isActive: isActive,
          weight: weight,
          status: FileRequestStatus.NOT_APPROVE,
          nameUserRequest: admin.email,
          type: 'SBPS',
          description: description,
        }),
        this.adminActionHistoryRepo.loggingCreateAction(
          admin.email,
          EnumNameAdminMenuModule.QUAN_LY_MA_QR_SBPS,
          fileName,
        ),
      ]);

      return fileRequest;
    } catch (error) {
      this._logger.error('error', error);
      throw new InternalServerErrorExc('Lỗi khi tạo yêu cầu mã QR');
    }

    // this.createQr(body, user, fileRequestCode, indexCharacterMonth);

    // return fileRequestCode;
  }
  @Transactional()
  async createQr(
    body: CreateQrReqDTO,
    admin: AccountData,
    fileRequestCode: FileRequestCode,
    indexCharacterMonth: number,
    numBatching: number,
  ) {
    const {
      amount,
      factory,
      isActive,
      sku,
      weight,
      description,
      manufactureDate,
    } = body;

    // const numCode = await this.vitaQrFileRepo.countBy({
    //   fileRequestId: fileRequestCode.id,
    // });

    // console.log('sbps_numCode: ', numCode);
    const timeId = Date.now();
    console.log('sbps_create_code_start: ', new Date().toString());

    const numGenCode = numBatching;

    const qrSkuSetup = await this.getQrSkuSetupBySku(sku);

    const prefix =
      qrSkuSetup.firstCharacter +
      WEIGHT[weight] +
      FACTORY[factory] +
      CHARACTERS[indexCharacterMonth];

    if (prefix.length !== 4)
      throw new InternalServerErrorExc(`Tiền tố không chính xác`);

    const fileId = genFileId(
      sku,
      weight,
      factory,
      indexCharacterMonth,
      manufactureDate,
    );

    // let numberBatching = NUMBER_BATCHING;
    // if (numGenCode <= NUMBER_BATCHING) {
    //   numberBatching = numGenCode;
    // }

    const listSuffix = new Set<string>();
    const listCode = (await this.vitaQrRepo.findBy({ code: `${prefix}%` })).map(
      (item) => item.code,
    );

    while (listSuffix.size < numGenCode) {
      const listNumbers = genListUniqueRandomNumber(8, 0, 28);

      let suffix = '';
      for (const item of listNumbers) {
        suffix += CHARACTERS[item];
      }
      if (!listCode.includes(prefix + suffix)) listSuffix.add(suffix);
    }

    const isError = false;
    // for (let i = 0; i <= Math.floor(listSuffix.size / numberBatching); i++) {
    //   let numBatching;
    //   if (i < Math.floor(listSuffix.size / numberBatching)) {
    //     numBatching = numberBatching;
    //   } else {
    //     numBatching = listSuffix.size - i * numberBatching;
    //   }
    // try {
    // await Promise.all(
    await Promise.allSettled(
      Array.from(Array(listSuffix.size).keys()).map(async (number) => {
        const dataQr = {
          isActive: isActive,
          status: STATUS.UNUSED,
          description,
          sku,
          weight,
          prefix,
          code: prefix + [...listSuffix][number],
          createdAt: dayjs(fileRequestCode.manufactureDate).add(7, 'hour'), // add 7 hour to get HCM time
        };
        // REQUEST CHANGE - REMOVE QR image
        // const dataImg = genQrCode(dataQr.code);
        // const [file, vitaQr] = await Promise.all([
        //   // this.saveFile(dataQr.code, dataImg, fileId),
        //   this.vitaQrRepo.save(dataQr),
        // ]);
        const vitaQr = await this.vitaQrRepo.save(dataQr);
        return this.vitaQrFileRepo.save({
          // file,
          vitaQr,
          // fileId: file.id,
          qrCode: vitaQr.code,
          fileRequestCode: fileRequestCode,
        });
      }),
    ).then((results) =>
      results.forEach((result) => {
        if (result.status === 'rejected') {
          throw result.reason;
        }
      }),
    );
    // );
    // } catch (e) {
    //   console.log('error: ', e);
    //   isError = true;
    // }
    // }
    // const total = await this.vitaQrFileRepo.countBy({
    //   fileRequestId: fileRequestCode.id,
    // });
    // await this.fileRequestCodeRepo.save({
    //   ...fileRequestCode,
    //   codeQuantityCreated: total,
    // });
    // if (isError || total < amount) {
    //   await this.emailService.sendEmail(
    //     user.email,
    //     '[VitaDairy] - Quá trình tạo mã QR SBPS bị lỗi',
    //     'VitaDairy',
    //     { name: 'VitaDairy' },
    //     `<p>Chào bạn,</p></br><p>Quá trình tạo mã QR SBPS của bạn bị lỗi, số lượng mã QR sẽ không đủ so với với số lượng bạn điền vào. Bạn vui lòng kiểm tra lại thông tin file QR.</p></br><p>Dưới đây là thông tin file QR bị lỗi ${fileRequestCode.name}</p></br><p>Trân trọng,</p></br><p>VitaDairy Admin</p>`,
    //   );
    // }
    console.log('sbps_create_code_end: ', new Date().toString());
  }

  async updateCodeCreated(fileId: number, email: string) {
    const jobs = await this.queue.getJobs(['active', 'waiting']);

    const jobsGroup = jobs.map((job) =>
      job.id.toString().includes(`${fileId}_`),
    );
    // console.log('jobsGroup: ', jobsGroup.length);
    const total = await this.vitaQrFileRepo.countBy({
      fileRequestId: fileId,
    });
    const fileRequestCode = await this.fileRequestCodeRepo.findOneBy({
      id: fileId,
    });
    if (!jobsGroup.length) {
      if (total < fileRequestCode.codeQuantity) {
        console.log('send mail: ', email);
        await this.emailService.sendEmail(
          email,
          '[VitaDairy] - Quá trình tạo mã QR SBPS bị lỗi',
          'VitaDairy',
          { name: 'VitaDairy' },
          `<p>Chào bạn,</p></br><p>Quá trình tạo mã QR SBPS của bạn bị lỗi, số lượng mã QR sẽ không đủ so với với số lượng bạn điền vào. Bạn vui lòng kiểm tra lại thông tin file QR.</p></br><p>Dưới đây là thông tin file QR bị lỗi ${fileRequestCode.name}</p></br><p>Trân trọng,</p></br><p>VitaDairy Admin</p>`,
        );
        return this.fileRequestCodeRepo.save({
          ...fileRequestCode,
          codeQuantityCreated: total,
          status: FileRequestStatus.ERROR,
        });
      } else if (total === fileRequestCode.codeQuantity) {
        return this.fileRequestCodeRepo.save({
          ...fileRequestCode,
          codeQuantityCreated: total,
          status: FileRequestStatus.SUCCESS,
        });
      } else {
        return this.fileRequestCodeRepo.save({
          ...fileRequestCode,
          codeQuantityCreated: total,
          status: FileRequestStatus.ERROR,
        });
      }
    } else {
      return this.fileRequestCodeRepo.save({
        ...fileRequestCode,
        codeQuantityCreated: total,
      });
    }
  }

  async createJobFail(
    body: CreateQrReqDTO,
    admin: AccountData,
    file: FileRequestCode,
    indexCharacterMonth: number,
    numBatching: number,
    retry: number,
  ) {
    if (retry >= 3) return;
    await this.queue.add(
      {
        body: body,
        user: admin,
        file: file,
        indexCharacterMonth: indexCharacterMonth,
        numBatching,
        retry: retry + 1,
      },
      {
        attempts: 1,
        delay: 1000,
        removeOnComplete: 1000,
        removeOnFail: 1000,
        jobId: `${file.id}_${uuidv4()}`,
        lifo: true,
      },
    );
  }

  async saveFile(code: string, image: Buffer, fileId: string) {
    const file = this.bucket.file(`vita-qr-sbps/${fileId}/${code}.png`);

    await file.save(image, {
      resumable: true,
      validation: true,
    });

    const fileImg = this.fileRepo.create({
      bucket: this.bucket.name,
      name: file.name,
      type: 'IMAGE',
      size: file?.metadata?.size,
    });
    return this.fileRepo.save(fileImg);
  }

  async get(query: ListQrReqDto, eventCode: string) {
    if (eventCode !== EVENT_CODE.SBPS)
      throw new BadRequestExc(`Mã sự kiện không đúng`);
    const { limit, page, searchText, startDate, endDate, fileId, isAllowRa } =
      query;
    const skip = (page - 1) * limit;

    let allowRaCodes: string[] = [];
    const productByCode = new Map<
      string,
      { code: string; isAllowRa: boolean }
    >();

    // Two-step filter: fetch allowed codes first, then use column = ANY(:codes).
    // Reason: "column IN (SELECT ... FROM other_table WHERE ...)" lets the planner
    // misestimate cardinality and pick a bad join order (e.g. full scan then filter),
    // causing huge perf gaps between different filter values. Fetching the list in app
    // and using ANY(:codes) gives the planner a known array → stable, fast plans.
    // Also avoids PG parameter limit when the list is large (ANY = one array param).
    if (isAllowRa !== undefined && isAllowRa !== null) {
      const allowRaProducts = await this.productMstRepo.find({
        where: { isAllowRa },
        select: ['code', 'isAllowRa'],
      });
      allowRaCodes = allowRaProducts.map((p) => p.code);
      allowRaProducts.forEach((p) =>
        productByCode.set(p.code, { code: p.code, isAllowRa: p.isAllowRa }),
      );
      if (allowRaCodes.length === 0) {
        return { data: [], total: 0 };
      }
    }

    const queryBuilder = this.vitaQrFileRepo
      .createQueryBuilder('vitaQrFile')
      .leftJoinAndSelect('vitaQrFile.vitaQr', 'vitaQr')
      .leftJoinAndSelect('vitaQrFile.file', 'file')
      .where((qb) => {
        if (searchText) {
          qb.where(
            '(vitaQr.code ILIKE :searchText OR vitaQr.sku ILIKE :searchText )',
            {
              searchText: `%${searchText}%`,
            },
          );
        }
        if (startDate) {
          qb.andWhere('vitaQr.createdAt >= :startDate', { startDate });
        }
        if (endDate) {
          qb.andWhere('vitaQr.createdAt <= :endDate', { endDate });
        }
        if (fileId) {
          qb.andWhere('vitaQrFile.fileRequestId = :fileRequestId', {
            fileRequestId: fileId,
          });
        }
      });

    if (allowRaCodes.length > 0) {
      queryBuilder.andWhere('vitaQr.sku = ANY(:allowRaCodes)', {
        allowRaCodes,
      });
    }

    const [data, total] = await queryBuilder
      .orderBy('vitaQrFile.id', 'DESC')
      .take(limit)
      .skip(skip)
      .getManyAndCount();

    if (productByCode.size === 0) {
      const skus = uniqueArray(
        data.map((f) => f.vitaQr?.sku).filter(Boolean),
      ) as string[];
      if (skus.length > 0) {
        const products = await this.productMstRepo.find({
          where: { code: In(skus) },
          select: ['code', 'isAllowRa'],
        });
        products.forEach((p) =>
          productByCode.set(p.code, { code: p.code, isAllowRa: p.isAllowRa }),
        );
      }
    }

    const dataWithProduct = data.map((item) => ({
      ...item,
      product: productByCode.get(item.vitaQr?.sku ?? '') ?? null,
    }));

    return { data: dataWithProduct, total };
  }

  async export(eventCode: string, body: ExportQrReqDto) {
    if (eventCode !== EVENT_CODE.SBPS)
      throw new BadRequestExc(`Mã sự kiện không đúng`);
    const { fileId } = body;

    const fileRquestCode = await this.fileRequestCodeRepo.findOneBy({
      id: fileId,
    });

    if (!fileRquestCode) throw new NotFoundExc(`Không tìm thấy file`);

    if (
      fileRquestCode.status === FileRequestStatus.NOT_APPROVE ||
      fileRquestCode.status === FileRequestStatus.REJECTED
    )
      throw new BadRequestExc('file không được chấp nhận hoặc bị từ chối');

    const product = await this.productMstRepo.findOne({
      where: { code: fileRquestCode.sku },
      select: ['isAllowRa', 'urlDomain'],
    });

    const workbook = xlsx.utils.book_new();

    const [data, total] = await this.vitaQrFileRepo
      .createQueryBuilder('vitaQrFile')
      .leftJoinAndSelect('vitaQrFile.file', 'file')
      .where((qb) => {
        qb.where('vitaQrFile.fileRequestId = :fileRequestId', {
          fileRequestId: fileId,
        });
      })
      .orderBy('vitaQrFile.id', 'ASC')
      .getManyAndCount();

    const urlDomain = product?.urlDomain?.replace(/\/$/, '') ?? '';
    const allowRaText =
      product?.isAllowRa === true
        ? 'Có'
        : product?.isAllowRa === false
        ? 'Không'
        : '';

    // 1 sheet has 2000 rows
    const limit = 2000;
    for (let i = 0; i < Math.ceil(total / limit); i++) {
      const listData = data.slice(i * limit, (i + 1) * limit);
      const list = listData.map((item, index) => {
        return {
          STT: index + 1,
          'Ma QR': item.qrCode,
          'ID QR': item.id,
          'Cho phép tích trên RA': allowRaText,
          URL: product?.urlDomain ?? '',
          'Link URL+QR': urlDomain ? `${urlDomain}/${item.qrCode}` : '',
        };
      });

      const worksheet = xlsx.utils.json_to_sheet(list);
      xlsx.utils.book_append_sheet(workbook, worksheet, `sheet${i + 1}`, true);
    }
    if (!total) {
      const worksheet = xlsx.utils.json_to_sheet([]);
      xlsx.utils.book_append_sheet(workbook, worksheet);
    }

    return { workbook, fileName: fileRquestCode.name };
  }

  @Transactional()
  async update(eventCode: string, body: UpdateQrReqDto, admin: AccountData) {
    const { isActive, qrCodes } = body;
    if (eventCode !== EVENT_CODE.SBPS)
      throw new BadRequestExc(`Mã sự kiện không đúng`);
    const listQr = await this.vitaQrRepo.find({
      where: {
        code: In(qrCodes),
      },
    });

    const updateQr = listQr.map((qr) => {
      return {
        ...qr,
        isActive,
      };
    });
    try {
      const [updateQrResult] = await Promise.all([
        this.vitaQrRepo.save(updateQr),
        this.adminActionHistoryRepo.loggingUpdateAction(
          admin.email,
          EnumNameAdminMenuModule.QUAN_LY_MA_QR_SBPS,
          qrCodes.join(','),
        ),
      ]);

      return updateQrResult;
    } catch (error) {
      this._logger.error('error', error);
      throw new InternalServerErrorExc('Lỗi khi cập nhật mã QR');
    }
  }

  async getListFileId(eventCode: string, query: ListFileQrReqDto) {
    const { limit, page, endDate, startDate, searchText, isAllowRa } = query;
    const skip = (page - 1) * limit;
    if (eventCode !== EVENT_CODE.SBPS)
      throw new AppBaseExc(StatusCode.EVENT_CODE_INVALID);

    let allowRaCodes: string[] = [];
    const productMap = new Map<string, boolean>();

    // Two-step filter: fetch allowed codes first, then use column = ANY(:codes).
    // Reason: "column IN (SELECT ... FROM other_table WHERE ...)" lets the planner
    // misestimate cardinality and pick a bad join order (e.g. full scan then filter),
    // causing huge perf gaps between different filter values. Fetching the list in app
    // and using ANY(:codes) gives the planner a known array → stable, fast plans.
    // Also avoids PG parameter limit when the list is large (ANY = one array param).
    if (isAllowRa !== undefined && isAllowRa !== null) {
      const allowRaProducts = await this.productMstRepo.find({
        where: { isAllowRa },
        select: ['code', 'isAllowRa'],
      });
      allowRaCodes = allowRaProducts.map((p) => p.code);
      allowRaProducts.forEach((p) => productMap.set(p.code, p.isAllowRa));
      if (allowRaCodes.length === 0) {
        return new AppResponseDto({ total: 0, data: [] });
      }
    }

    const queryBuilder = this.fileRequestCodeRepo
      .createQueryBuilder('fileRequestCode')
      .where((qb) => {
        if (startDate)
          qb.where('fileRequestCode.createdAt >= :startDate', { startDate });

        if (endDate)
          qb.where('fileRequestCode.createdAt <= :endDate', { endDate });

        if (searchText)
          qb.where('fileRequestCode.name ILIKE :name', {
            name: `%${searchText}%`,
          });
      });

    if (allowRaCodes.length > 0) {
      queryBuilder.andWhere('fileRequestCode.sku = ANY(:allowRaCodes)', {
        allowRaCodes,
      });
    }

    const [data, total] = await queryBuilder
      .orderBy('fileRequestCode.createdAt', 'DESC')
      .take(limit)
      .skip(skip)
      .getManyAndCount();

    if (productMap.size === 0) {
      const skus = uniqueArray(data.map((f) => f.sku).filter(Boolean));
      if (skus.length > 0) {
        const products = await this.productMstRepo.find({
          where: { code: In(skus) },
          select: ['code', 'isAllowRa'],
        });
        products.forEach((p) => productMap.set(p.code, p.isAllowRa));
      }
    }

    const dataWithIsAllowRa = data.map((item) => ({
      ...item,
      isAllowRa: productMap.get(item.sku) ?? null,
    }));

    return new AppResponseDto({
      total,
      data: dataWithIsAllowRa,
    });
  }

  async getDetailFile(eventCode: string, fileId: number) {
    if (eventCode !== EVENT_CODE.SBPS)
      throw new AppBaseExc(StatusCode.EVENT_CODE_INVALID);

    return this.fileRequestCodeRepo.findOneBy({ id: fileId });
  }

  @Transactional()
  async approveFileRequest(
    eventCode: string,
    fileId: number,
    admin: AccountData,
  ) {
    if (eventCode !== EVENT_CODE.SBPS)
      throw new AppBaseExc(StatusCode.EVENT_CODE_INVALID);

    const fileRequest = await this.fileRequestCodeRepo.findOneBy({
      id: fileId,
    });
    if (!fileRequest) throw new NotFoundExc('Không tìm thấy file');

    await this.getQrSkuSetupBySku(fileRequest.sku);

    if (fileRequest.status === FileRequestStatus.APPROVED) return fileRequest;

    const time = dayjs(fileRequest.manufactureDate);
    const month = time.month();
    const year = time.year();
    // index month start 0
    const indexCharacterMonth = genMonthQr(month + 1, year);

    const body: CreateQrReqDTO = {
      fileName: fileRequest.name,
      amount: fileRequest.codeQuantity,
      factory: fileRequest.factory,
      isActive: fileRequest.isActive,
      manufactureDate: fileRequest.manufactureDate,
      sku: fileRequest.sku,
      weight: fileRequest.weight,
      description: fileRequest.description,
    };
    const file = await this.fileRequestCodeRepo.save({
      ...fileRequest,
      status: FileRequestStatus.APPROVED,
      accApproveId: admin.accountId,
      approvedDate: dayjs(),
    });

    const listQueue = [];

    for (
      let i = 0;
      i <= Math.floor(fileRequest.codeQuantity / NUMBER_BATCHING);
      i++
    ) {
      let numBatching;
      if (i < Math.floor(fileRequest.codeQuantity / NUMBER_BATCHING)) {
        numBatching = NUMBER_BATCHING;
      } else {
        numBatching = fileRequest.codeQuantity - i * NUMBER_BATCHING;
      }

      listQueue.push(
        this.queue.add(
          {
            body: body,
            user: admin,
            file: file,
            indexCharacterMonth: indexCharacterMonth,
            numBatching,
            retry: 0,
          },
          {
            attempts: 1,
            delay: 1000,
            removeOnComplete: 1000,
            removeOnFail: 1000,
            jobId: `${file.id}_${uuidv4()}`,
          },
        ),
      );
    }

    const results = await Promise.all(listQueue);
    console.log('sbps_queue_results', results);

    // this.createQr(body, user, file, indexCharacterMonth);

    await this.adminActionHistoryRepo.loggingUpdateAction(
      admin.email,
      EnumNameAdminMenuModule.QUAN_LY_MA_QR_SBPS,
      file.name,
    );

    return file;
  }

  @Transactional()
  async rejectFileRequest(
    eventCode: string,
    body: RejectFileReqDTO,
    admin: AccountData,
  ) {
    const { fileId, rejectDescription } = body;
    if (eventCode !== EVENT_CODE.SBPS)
      throw new AppBaseExc(StatusCode.EVENT_CODE_INVALID);

    const fileRequest = await this.fileRequestCodeRepo.findOneBy({
      id: fileId,
    });
    if (!fileRequest) throw new NotFoundExc('Không tìm thấy file');

    if (fileRequest.status === FileRequestStatus.APPROVED)
      throw new BadRequestExc('file đã được chấp nhận');

    const [rejectFileResult] = await Promise.all([
      this.fileRequestCodeRepo.save({
        ...fileRequest,
        status: FileRequestStatus.REJECTED,
        accApproveId: admin.accountId,
        rejectDescription: rejectDescription,
        approvedDate: dayjs(),
      }),
      this.adminActionHistoryRepo.loggingUpdateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_MA_QR_SBPS,
        fileRequest.name,
      ),
    ]);

    return rejectFileResult;
  }

  @Transactional()
  async creatErrorCode(eventCode: string, fileId: number, admin: AccountData) {
    if (eventCode !== EVENT_CODE.SBPS)
      throw new AppBaseExc(StatusCode.EVENT_CODE_INVALID);

    const fileRequest = await this.fileRequestCodeRepo.findOneBy({
      id: fileId,
    });
    if (!fileRequest) throw new NotFoundExc('Không tìm thấy file');

    if (
      fileRequest.status === FileRequestStatus.NOT_APPROVE ||
      fileRequest.status === FileRequestStatus.REJECTED
    )
      throw new BadRequestExc('file không được chấp nhận hoặc bị từ chối');

    const numCode = await this.vitaQrFileRepo.countBy({
      fileRequestId: fileRequest.id,
    });

    if (numCode === fileRequest.codeQuantity) {
      if (numCode !== fileRequest.codeQuantityCreated) {
        return this.fileRequestCodeRepo.save({
          ...fileRequest,
          codeQuantityCreated: numCode,
          status: FileRequestStatus.SUCCESS,
        });
      } else {
        return fileRequest;
      }
    } else if (numCode < fileRequest.codeQuantity) {
      const numGenCode = fileRequest.codeQuantity - numCode;

      const time = dayjs(fileRequest.manufactureDate);
      const month = time.month();
      const year = time.year();
      const indexCharacterMonth = genMonthQr(month + 1, year);

      const body: CreateQrReqDTO = {
        fileName: fileRequest.name,
        amount:
          fileRequest.codeQuantity - Number(fileRequest.codeQuantityCreated),
        factory: fileRequest.factory,
        isActive: fileRequest.isActive,
        manufactureDate: fileRequest.manufactureDate,
        sku: fileRequest.sku,
        weight: fileRequest.weight,
        description: fileRequest.description,
      };

      const file = await this.fileRequestCodeRepo.save({
        ...fileRequest,
        status: FileRequestStatus.APPROVED,
        accApproveId: admin.accountId,
        approvedDate: dayjs(),
      });

      const listQueue = [];

      for (let i = 0; i <= Math.floor(numGenCode / NUMBER_BATCHING); i++) {
        let numBatching;
        if (i < Math.floor(numGenCode / NUMBER_BATCHING)) {
          numBatching = NUMBER_BATCHING;
        } else {
          numBatching = numGenCode - i * NUMBER_BATCHING;
        }

        listQueue.push(
          this.queue.add(
            {
              body: body,
              user: admin,
              file: file,
              indexCharacterMonth: indexCharacterMonth,
              numBatching,
              retry: 0,
            },
            {
              attempts: 1,
              delay: 1000,
              removeOnComplete: 1000,
              removeOnFail: 1000,
              jobId: `${file.id}_${uuidv4()}`,
            },
          ),
        );
      }

      await Promise.all(listQueue);

      await this.adminActionHistoryRepo.loggingUpdateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_MA_QR_SBPS,
        file.name,
      );

      return file;
    } else if (numCode > fileRequest.codeQuantity) {
      this.deleteQrCode(numCode, fileRequest);
      return;
    }
  }

  @Transactional()
  async deleteQrCode(numCode: number, fileRequest: FileRequestCode) {
    const numDeleteCode = numCode - fileRequest.codeQuantity;
    const codesDelete = await this.vitaQrFileRepo
      .createQueryBuilder('vitaQrFile')
      .where('vitaQrFile.fileRequestId = :fileRequestId', {
        fileRequestId: fileRequest.id,
      })
      .limit(numDeleteCode)
      .getMany();

    await this.vitaQrFileRepo.delete({
      id: In(codesDelete.map((qr) => qr.id)),
    });
    await this.vitaQrRepo.delete({
      code: In(codesDelete.map((qr) => qr.qrCode)),
    });
    return this.fileRequestCodeRepo.save({
      ...fileRequest,
      codeQuantityCreated: fileRequest.codeQuantity,
      status: FileRequestStatus.SUCCESS,
    });
  }

  async deleteJob() {
    await this.queue.removeJobs('create-sbps-code');
  }

  private async getQrSkuSetupBySku(sku: string) {
    const qrSkuSetup = await this.qrSkuSetupRepo.findOneBy({ sku });

    if (!qrSkuSetup) {
      throw new BadRequestExc(`Không tìm thấy Quy tắc gen mã với SKU này`);
    }

    if (!qrSkuSetup.firstCharacter) {
      throw new BadRequestExc(`Ký tự đầu tiên không được để trống`);
    }

    return qrSkuSetup;
  }

  async getProductsByEventCode(
    eventCode: string,
    dto: GetProductsByEventCodeAdminRequestDto,
  ) {
    const request: GetProductsAdminRequestDto = {
      ...dto,
      type1: eventCode,
    };

    return this.productAdminService.getProducts(request);
  }
}
