import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import dayjs from 'dayjs';
import { Between, IsNull, Not, MoreThan } from 'typeorm';
import {
  runInTransaction,
  runOnTransactionCommit,
} from 'typeorm-transactional';
import {
  NotiDisplayTemplateType,
  PushNotiKafkaDto,
  PushNotiKafkaDtoVersion,
  FeatureNoti,
  IdentityPointContent,
  IdentityPointActionType,
  EventAddCanType,
} from 'vtd-common-v3';
import { User } from '../../auth/entities/user.entity';
import { UserType } from '../../auth/enums/user.enum';
import { UserRepository } from '../../auth/repositories/user.repository';
import { GlobalConfig } from '../../common/config/global.config';
import {
  CRM_TRANS_TYPE_CODE,
  EVENT_CODE,
  EVENT_EMITTER_NAME,
  GIFT_POINT,
  PER_REDEEM_POINT,
  TIME_FORMAT_CRM,
  TIME_FORMAT_DAY,
  TIME_ZONE,
  USER_RANK,
} from '../../common/constants/index.constant';
import { PopupV2Code } from './../../external/constants/index.constant';
import {
  SBPS_SCAN_FAIL_VALID_ERROR_CODE,
  TIMESTAMP_BEGIN_20241001_STRING,
} from '../constants';
import { StatusCode } from '../../common/constants/status-code.constant';
import { AppResponseDto } from '../../common/dtos/app-response.dto';
import { AppBaseExc } from '../../common/exceptions/custom-app.exception';
import {
  getNowAtTimeHcm,
  getNowAtTimeZoneHcm,
  randomTransactionExternalId,
  getStartOfCurrentYearAtTimeHcm,
  getEndOfCurrentYearAtTimeHcm,
  compareDateWithDateInTimezoneNewVersion,
  makeDateIsDateInTimeZoneAndFormatToString,
  addMonthToDateAndMakeDateIsDateInTimeZoneAndFormatToString,
  compareDateBetweenFromDateIAndToDateInTimezone,
  getStartOfDateInTimezone,
  sortByAttribute,
} from '../../common/utils';
import {
  validateDataQrManufactureDate,
  validateCallFromWebApp,
  validateAllowAddPoint,
  validateAllowRunFlowHandleScanQrFail,
  validateAllowJoinEvent,
  validateEventCodeIsSbps,
  mappingDefaultErrorCode,
} from '../utils';
import { asyncMapSettled } from '../../common/helpers';
import { SystemConfigRepository } from '../../config/repositories/system-config.repository';
import {
  CrmTransactionTypeRequest,
  PointTransferStatus,
} from '../../external/enums/crm.enum';
import {
  CreateAddPointSBPSTransactionReqDto,
  CrmPointGiftingRequest,
} from '../../external/interfaces/crm.interface';
import {
  GetUserGiftPreOrderEnoughPointToSendNotifyRequest,
  GetUserGiftPreOrderEnoughPointToSendNotifyResponse,
  PushNotificationRequest,
  SyncData3rdServiceToWhRequestDetail,
} from '../../external/interfaces/vita-java.interface';
import { CrmService } from '../../external/services/crm.service';
import { VitaJavaService } from '../../external/services/vita-java.service';
import {
  NotificationUserFirebaseStatus,
  NotificationUserStatus,
} from '../../notification/enums/notification-user.enum';
import { NotificationUserRepository } from '../../notification/repositories/notification-user.repository';
import { ProductDataGotByQrDto } from '../../point/dto/misc/product-data-got-by-qr.dto';
import { BlockedHistoryType } from '../../point/enums/block-history.enum';
import { HistoryPointAttributeCode } from '../../point/enums/history-point-attribute.enum';
import {
  HistoryPointStatus,
  HistoryPointType,
} from '../../point/enums/history-point.enum';
import {
  CallType,
  OutboxMessageStatus,
  SyncProvider,
  SyncType,
} from '../../point/enums/outbox-message.enum';
import { ScanHistoryApiType } from '../../point/enums/scan-history.enum';
import { RequestOutboxMessage } from '../../point/interfaces/outbox-message.interface';
import { CrmTransactionTypeRepository } from '../../point/repositories/crm-transaction-type.repository';
import { OutboxMessageRepository } from '../../point/repositories/outbox-message.repository';
import { TierRepository } from '../../tier/repositories/tier.repository';
import { GROUP, SKU, STATUS, STATUS_SCAN_HIS } from '../constants';
import { ScanProductDto } from '../dtos/req/scan-product.dto';
import { ScanQrDto } from '../dtos/req/scan-qr.dto';
import { HistoryPointAttribute } from '../entities/history-point-attribute.entity';
import { BlockedHistoryRepository } from '../repositories/blocked-history.repository';
import { BlockedScanRepository } from '../repositories/blocked-scan.repository';
import { EventRepository } from '../repositories/event.repository';
import { HistoryPointAttributeRepository } from '../repositories/history-point-attribute.repository';
import { HistoryPointRepository } from '../repositories/history-point.repository';
import { ProductLineRepository } from '../repositories/product-line.repository';
import { ProductMstRepository } from '../repositories/product-mst.repository';
import { ProductPreviewRepository } from '../repositories/product-preview.repository';
import { VitaQrRepository } from '../repositories/qr-code-sbps.repository';
import { ScanHistoryRepository } from '../repositories/scan-history.repository';
import { UserNumberScanSbpsRepository } from '../repositories/user-number-scan-sbps.repository';
import { UserNumberScanSbps } from '../entities/user-number-scan-sbps.entity';
import { UserSessionRepository } from '../repositories/user-session.repository';
import { TRIPLAYZ_EVENT_NAME } from '../../triplayz/type';
import { EventQ32024UpRankService } from '../../point/services/event-quy-3-2024-up-rank/event-quy-3-2024-up-rank.service';
import { EventQuy32024UpRankData } from '../../point/services/event-quy-3-2024-up-rank/types';
import { OutboxMessage } from '../../point/entities/outbox-message.entity';
import { WebAppList } from '../../common/enums/webapp-list.enum';
import { VitaQr } from '../entities/vita-qr.entity';
import { EventRepository as NewestEventRepository } from '../../event/repositories/event.repository';
import { EventDetailRepository } from '../../event/repositories/event-detail.repository';
import { QrSbpsBlackListRepository } from '../repositories/qr-sbps-black-list.repository';
import { EventType } from '../../point/enums/event.enum';
import {
  JavaV4WhSyncTo3rdServiceCode,
  JavaV4WhSyncTo3rdServiceDestination,
} from '../../external/constants/java_v4.constant';
import { EventCommonPopupUserService } from '../../event-common/services/user/event-common-popup.user.service';
import { DDXTriggerCalculateUserResetGiftPointRepository } from 'src/point/repositories/ddx_trigger_calculate_user_reset_gift_point.repository';
import { mapHistoryPointType } from 'src/point/helpers';
import { UserSessionData } from '../../proto/user.pb';
import { SystemConfigCode } from '../../point/enums/system-config.enum';
import {
  sbErrorCodes,
  sbpsErrorCodes,
} from '../../point/constants/index.constant';
import {
  compareDateWithDateInTimezone,
  getNowAtTimezone,
  getStartOfCurrentMonthInTimezone,
  getStartOfNowInTimezone,
} from '../../common/datetime.util';
import { EventSbpsSkuRepository } from '../../event-sbps-sku/repositories/event-sbps-sku.repository';
import { ProvinceUserService } from '../../provinces/services/user/province.user.service';
import { QrSkuSetupRepository } from '../repositories/qr-sku-setup.repository';
import { EventAddCanRepository } from '../../event-add-can/repositories/event-add-can.repository';
import { MapHistoryPointToBrandPointRepository } from '../../map-history-point-to-brand-point/repositories/map-history-point-to-brand-point.repository';
import { MapHistoryPointToBrandPointEntity } from '../../map-history-point-to-brand-point/entities/map-history-point-to-brand-point.entity';
import { Event } from '../../event/entities/event.entity';
import { EventAddCan } from '../../event-add-can/entities/event-add-can.entity';
import { MapUserToBrandPointRepository } from '../../map-user-to-brand-point/repositories/map-user-to-brand-point.repository';
import { MapUserToBrandPointEntity } from '../../map-user-to-brand-point/entities/map-user-to-brand-point.entity';
import { generateIdSendSfUnique } from '../../map-user-to-brand-point/utils/map-user-to-brand-point.util';
import { UpdateStatusQrCodeDto } from '../dtos/req/update-status-qr-code.dto';

@Injectable()
export class UserQrCodeSbpsService {
  private logger = new Logger(UserQrCodeSbpsService.name);
  private crmSyncUsingWh = false;

  constructor(
    private configSer: ConfigService<GlobalConfig>,

    private eventEmitter: EventEmitter2,
    private userRepo: UserRepository,
    private vitaQrRepo: VitaQrRepository,
    private scanHistoryRepo: ScanHistoryRepository,
    private productMstRepo: ProductMstRepository,
    private blockedHistoryRepo: BlockedHistoryRepository,
    private productPreview: ProductPreviewRepository,
    private blockedScanRepo: BlockedScanRepository,
    private productLineRepo: ProductLineRepository,
    private userNumberScanSbpsRepo: UserNumberScanSbpsRepository,
    private eventRepo: EventRepository,
    private historyPointRepo: HistoryPointRepository,
    private historyPointAttributeRepo: HistoryPointAttributeRepository,
    private httpService: HttpService,
    private vitaJavaService: VitaJavaService,
    private notificationUserRepo: NotificationUserRepository,
    private userSessionRepo: UserSessionRepository,
    private crmService: CrmService,
    private outboxMessageRepo: OutboxMessageRepository,
    private tierRepo: TierRepository,
    private systemConfigRepo: SystemConfigRepository,
    private crmTransactionTypeRepo: CrmTransactionTypeRepository,
    private newestEventRepo: NewestEventRepository,
    private eventDetailRepo: EventDetailRepository,
    private qrSbpsBlackListRepo: QrSbpsBlackListRepository,
    private readonly eventSbpsSkuRepo: EventSbpsSkuRepository,
    private _eventQ32024UpRankService: EventQ32024UpRankService,
    private eventCommonPopupUserService: EventCommonPopupUserService,
    private ddxTriggerCalculateUserResetGiftPointRepo: DDXTriggerCalculateUserResetGiftPointRepository,
    private readonly provinceUserService: ProvinceUserService,
    private readonly qrSkuSetupRepo: QrSkuSetupRepository,
    private readonly eventAddCanRepo: EventAddCanRepository,
    private readonly mapHistoryPointToBrandPointRepo: MapHistoryPointToBrandPointRepository,
    private readonly mapUserToBrandPointRepo: MapUserToBrandPointRepository,
  ) {
    this.crmSyncUsingWh = this.configSer.get('crm.options.syncUsingWh');
  }

  async checkUserScan(eventCode: string, userId: number) {
    if (!validateEventCodeIsSbps(eventCode))
      throw new AppBaseExc(StatusCode.SBPS_DEFAULT_ERROR, '', true);

    const isBlocked = await this.userIsBlocked(eventCode, userId);
    return new AppResponseDto({
      blockScanSbps: isBlocked,
    });
  }

  async scanQr(eventCode: string, body: ScanQrDto, userId: number) {
    try {
      const result = await runInTransaction(() =>
        this.handleScanQr(eventCode, body, userId),
      );
      return result;
    } catch (error) {
      await runInTransaction(() =>
        this.handleScanQrFail(userId, body.qrCode, error, eventCode),
      );

      if (error instanceof AppBaseExc) throw error;
      throw new AppBaseExc(
        mappingDefaultErrorCode(eventCode),
        '',
        true,
        StatusCode.API_FAILED_UNKNOWN,
      );
    }
  }

  async scanProduct(
    userId: number,
    token: string,
    eventCode: string,
    body: ScanProductDto,
    appversionname: string,
    webapp_name?: WebAppList,
  ) {
    try {
      const result = await runInTransaction(() =>
        this.handleScanProduct(
          userId,
          token,
          eventCode,
          body,
          appversionname,
          webapp_name,
        ),
      );
      return result;
    } catch (error) {
      console.log('error');
      console.log(error);
      const canRunFlowHandleScanQrFail =
        await this.validateAllowRunFlowHandleScanQrFail(body.qrCode, error);
      if (canRunFlowHandleScanQrFail) {
        await runInTransaction(() =>
          this.handleScanQrFail(userId, body.qrCode, error, eventCode),
        );
      }

      const scanFailErrorCount = await this.scanProductErrorCount(userId);

      if (error instanceof AppBaseExc) {
        if (sbpsErrorCodes.includes(error.error)) {
          error.subInfo = scanFailErrorCount.response;
        }

        if (
          validateEventCodeIsSbps(eventCode) &&
          error.error === StatusCode.SB_DEFAULT_ERROR.error
        ) {
          throw new AppBaseExc(
            StatusCode.SBPS_DEFAULT_ERROR,
            '',
            true,
            StatusCode.API_FAILED_UNKNOWN,
          );
        }

        throw error;
      }

      throw new AppBaseExc(
        mappingDefaultErrorCode(eventCode),
        '',
        true,
        StatusCode.API_FAILED_UNKNOWN,
      );
    }
  }

  async scanProductErrorCount(userId: number) {
    const currentUser = await this.userRepo.findOne({
      where: {
        id: userId,
      },
    });

    const startOfNextDay = getStartOfDateInTimezone(
      dayjs().add(1, 'day').toDate(),
    );

    const sbpsConfigLimit = await this.systemConfigRepo.findOne({
      where: {
        code: SystemConfigCode.LIMIT_NUMBER_OF_SCAN_SBPS_FAILED_IN_DAY,
      },
    });

    const sbConfigLimit = await this.systemConfigRepo.findOne({
      where: {
        code: SystemConfigCode.LIMIT_NUMBER_OF_SCAN_SAME_QR_FAILED,
      },
    });

    const currentTime = getNowAtTimezone();
    if (currentTime > currentUser.dateCompareResetFailedAddPoint) {
      currentUser.sbpsFailAddPointCounter = 0;
      currentUser.sbFailAddPointCounter = 0;
      currentUser.dateCompareResetFailedAddPoint = startOfNextDay;
      await this.userRepo.save(currentUser);
      return new AppResponseDto({
        sbps_fail_count: 0,
        sbps_remain_count: Number(sbpsConfigLimit.value),
      });
    }

    return new AppResponseDto({
      sbps_fail_count: currentUser.sbpsFailAddPointCounter,
      sbps_remain_count: Math.max(
        0,
        Number(sbpsConfigLimit.value) - currentUser.sbpsFailAddPointCounter,
      ),
    });
  }

  private async handleScanQr(
    eventCode: string,
    body: ScanQrDto,
    userId: number,
  ) {
    let { qrCode } = body;
    if (qrCode.startsWith('https://storage.googleapis.com')) {
      qrCode = this.getCodeFromUrl(qrCode) as unknown as string;
    }
    // const sku =
    //   Object.keys(SKU)[Object.values(SKU).indexOf(qrCode[0] as unknown as SKU)];

    const [dataQr, user] = await Promise.all([
      this.vitaQrRepo.findOne({
        where: { code: qrCode },
      }),
      this.userRepo.findOne({
        where: {
          id: userId,
        },
      }),
    ]);

    if (!dataQr || !dataQr.isActive) {
      throw new AppBaseExc(StatusCode.SBPS_QR_NOEXIST, '', true);
    }
    if (dataQr.status === STATUS.USED) {
      throw new AppBaseExc(StatusCode.SBPS_QR_USED, '', true);
    }

    const [blockAcc, userProvince] = await Promise.all([
      this.checkBlockAccount(user),
      this.provinceUserService.getUserNewProvice(user.id, user.phoneNumber),
    ]);

    if (blockAcc === StatusCode.SBPS_DEFAULT_ERROR.error) {
      throw new AppBaseExc(StatusCode.SBPS_DEFAULT_ERROR);
    } else if (
      blockAcc === StatusCode.BLOCKED_ACCOUNT_WHEN_SCAN_FAILED.error ||
      blockAcc === StatusCode.SBPS_ACCBLOCK_ADDPOINT.error
    ) {
      throw new AppBaseExc(StatusCode.SBPS_ACCBLOCK_ADDPOINT);
    }

    const isBlocked = await this.userIsBlocked(eventCode, userId);

    if (isBlocked) {
      throw new AppBaseExc(StatusCode.SBPS_BLOCK_ADDPOINT);
    } else if (qrCode.length !== 12) {
      throw new AppBaseExc(StatusCode.SBPS_QR_FORERR);
    }

    // if (!user?.provinceId) {
    //   throw new AppBaseExc(StatusCode.SPBS_ADD_EMPT, '', true);
    // }
    if (!userProvince || !userProvince.checkUserUpdatedNewProvince()) {
      throw new AppBaseExc(StatusCode.SPBS_ADD_EMPT, '', true);
    }

    const sku = dataQr.sku;
    const [product, productLine, userNumberScan, monthExpiry] =
      await Promise.all([
        this.productMstRepo
          .createQueryBuilder('productMst')
          .where((qb) => {
            qb.where('productMst.code = :sku', { sku });
          })
          .getOne(),
        this.productLineRepo.findOne({
          where: { code: EVENT_CODE.SBPS },
        }),
        this.userNumberScanSbpsRepo
          .createQueryBuilder('userNumberScanSbps')
          .where('userNumberScanSbps.userId = :userId', { userId })
          .getOne(),
        this.getMonthExpiryBySku(sku),
      ]);

    if (!productLine) {
      throw new AppBaseExc(StatusCode.SBPS_DEFAULT_ERROR, '', true);
    }
    const createdAtObj = dayjs(dataQr?.createdAt);
    const expireDateObj = createdAtObj.add(Number(monthExpiry), 'month');

    const isExpired = dataQr ? dayjs().isAfter(expireDateObj) : true;

    const limitInDayConfig =
      await this.systemConfigRepo.getLimitNumberOfScanSbpsSuccessInDay();

    const limitInDay = Number(limitInDayConfig.value) || 3;
    const startOfCurrentDay = getStartOfNowInTimezone();

    if (!product) {
      throw new AppBaseExc(StatusCode.SBPS_QR_UNMATCH, '', true);
    }
    if (isExpired) {
      throw new AppBaseExc(StatusCode.SBPS_EXPIRE, '', true);
    }
    if (
      userNumberScan &&
      compareDateWithDateInTimezone(
        userNumberScan.scannedAt,
        startOfCurrentDay,
      ) >= 0 &&
      userNumberScan?.numberScanInDaySbps >= limitInDay
    ) {
      throw new AppBaseExc(StatusCode.SBPS_OVERADDPOINT, '', true);
    }

    const previewProduct = await this.productPreview.findOne({
      where: { code: sku },
    });
    if (!previewProduct) {
      throw new AppBaseExc(StatusCode.SBPS_QR_UNMATCH, '', true);
    }

    return new AppResponseDto({
      code: dataQr.code,
      sku: dataQr.sku,
      id: product.id,
      name: product.previewName,
      weigh: previewProduct.weigh,
      weighType: previewProduct.weighType,
      created_at: createdAtObj.format('YYYY-MM-DD'),
      expired_at: expireDateObj.format('YYYY-MM-DD'),
    });
  }

  async getQrInformation(
    body: ScanQrDto,
    userId: number,
    webapp_name?: WebAppList,
  ) {
    const callFromWebApp = validateCallFromWebApp(webapp_name);
    let { qrCode } = body;
    if (qrCode.startsWith('https://storage.googleapis.com')) {
      qrCode = this.getCodeFromUrl(qrCode) as unknown as string;
    }
    if (qrCode.length !== 12) {
      throw new AppBaseExc(StatusCode.SBPS_QR_FORERR);
    }
    // const sku =
    //   Object.keys(SKU)[Object.values(SKU).indexOf(qrCode[0] as unknown as SKU)];

    const dataQr = await this.vitaQrRepo.findOne({
      where: { code: qrCode },
    });
    if (!dataQr || !dataQr.isActive) {
      throw new AppBaseExc(StatusCode.SBPS_QR_NOEXIST, '', true);
    }

    const sku = dataQr.sku;

    const [product, previewProduct, monthExpiry] = await Promise.all([
      this.productMstRepo
        .createQueryBuilder('productMst')
        .where((qb) => {
          qb.where('productMst.code = :sku', { sku });
        })
        .getOne(),
      this.productPreview.findOne({
        where: { code: sku },
      }),
      this.getMonthExpiryBySku(sku),
    ]);
    if (!product || !previewProduct) {
      throw new AppBaseExc(StatusCode.SBPS_QR_UNMATCH, '', true);
    }
    /*const rsValidate = await this.validateDataQrBeforeProcess(
      dataQr,
      callFromWebApp,
    );
    if (rsValidate) {
      return rsValidate;
    }*/

    const createdAtObj = dayjs(dataQr?.createdAt);
    const expireDateObj = createdAtObj.add(Number(monthExpiry), 'month');

    return new AppResponseDto({
      code: dataQr.code,
      sku: dataQr.sku,
      id: product.id,
      name: product.previewName,
      weigh: previewProduct.weigh,
      weighType: previewProduct.weighType,
      created_at: createdAtObj.format('YYYY-MM-DD'),
      expired_at: expireDateObj.format('YYYY-MM-DD'),
    });
  }

  async handleScanProduct(
    userId: number,
    token: string,
    eventCode: string,
    body: ScanProductDto,
    appversionname: string,
    webapp_name?: WebAppList,
  ) {
    const { sku } = body;
    let { qrCode } = body;
    // const callFromWebApp = validateCallFromWebApp(webapp_name);

    if (qrCode.startsWith('https://storage.googleapis.com')) {
      qrCode = this.getCodeFromUrl(qrCode) as unknown as string;
    }

    if (!validateEventCodeIsSbps(eventCode))
      throw new AppBaseExc(StatusCode.SBPS_DEFAULT_ERROR, '', true);

    const [vitaQr, user] = await Promise.all([
      this.vitaQrRepo
        .createQueryBuilder('vitaQr')
        .where('vitaQr.code = :qrCode', { qrCode })
        .setLock('pessimistic_write')
        .maxExecutionTime(60000)
        .getOne(),
      this.userRepo
        .createQueryBuilder('user')
        .where('user.id = :userId', { userId })
        .setLock('pessimistic_write')
        .maxExecutionTime(60000)
        .getOne(),
    ]);

    const [blockAcc, isBlocked, validateUserSetupNewProvinceOnly] =
      await Promise.all([
        this.checkBlockAccount(user),
        this.userIsBlocked(eventCode, userId),
        this.provinceUserService.validateUserSetupNewProvinceOnly(user.id),
      ]);

    if (blockAcc === StatusCode.SBPS_DEFAULT_ERROR.error) {
      throw new AppBaseExc(StatusCode.SBPS_DEFAULT_ERROR);
    } else if (blockAcc === StatusCode.BLOCK_IDENTITY_SCAN.error) {
      throw new AppBaseExc(StatusCode.BLOCK_IDENTITY_SCAN);
    } else if (blockAcc === StatusCode.BLOCKED_ACCOUNT_WHEN_SCAN_FAILED.error) {
      throw new AppBaseExc(StatusCode.SBPS_ACCBLOCK_ADDPOINT);
    }

    // const isBlocked = await this.userIsBlocked(eventCode, userId);

    if (isBlocked) {
      throw new AppBaseExc(StatusCode.SBPS_BLOCK_ADDPOINT);
    } else if (qrCode.length !== 12) {
      throw new AppBaseExc(StatusCode.SBPS_QR_FORERR);
    }

    // if (!user?.provinceId) {
    //   throw new AppBaseExc(StatusCode.SPBS_ADD_EMPT, '', true);
    // }
    if (!validateUserSetupNewProvinceOnly) {
      throw new AppBaseExc(StatusCode.SPBS_ADD_EMPT, '', true);
    }

    if (!vitaQr || !vitaQr.isActive) {
      throw new AppBaseExc(StatusCode.SBPS_QR_NOEXIST, '', true);
    }

    if (vitaQr.status !== STATUS.UNUSED) {
      throw new AppBaseExc(StatusCode.SBPS_QR_USED, '', true);
    }

    if (vitaQr.sku !== sku) {
      throw new AppBaseExc(StatusCode.SBPS_QR_UNMATCH, '', true);
    }

    // const rsValidate = await this.validateDataQrBeforeProcess(
    //   vitaQr,
    //   callFromWebApp,
    // );
    // if (rsValidate) {
    //   return rsValidate;
    // }

    const time = dayjs();

    const [
      userNumberScan,
      // oggiOrClgSkuGet,
      limitInDayConfig,
      product,
      // dataFirstScan,
    ] = await Promise.all([
      this.userNumberScanSbpsRepo
        .createQueryBuilder('userNumberScanSbps')
        .where('userNumberScanSbps.userId = :userId', { userId })
        .setLock('pessimistic_write')
        .maxExecutionTime(60000)
        .getOne(),
      // this.eventRepo
      //   .createQueryBuilder('event')
      //   .leftJoinAndSelect('event.eventProduct', 'eventProduct')
      //   .where((qb) => {
      //     qb.where('event.startDate <= :time AND event.endDate >= :time', {
      //       time,
      //     }).andWhere('eventProduct.sku LIKE :sku', { sku });
      //   })
      //   .getMany(),
      // this.eventSbpsSkuRepo.findOneBy({
      //   sku: vitaQr.sku,
      // }),
      this.systemConfigRepo.getLimitNumberOfScanSbpsSuccessInDay(),
      this.productMstRepo
        .createQueryBuilder('productMst')
        .where((qb) => {
          qb.where('productMst.code LIKE :sku', { sku });
        })
        .getOne(),
      // this.historyPointRepo
      //   .createQueryBuilder('historyPoint')
      //   .where('historyPoint.customerId = :customerId', {
      //     customerId: user.id,
      //   })
      //   .andWhere('historyPoint.actionType = :actionType', {
      //     actionType: 'FIRST_SCAN_SBPS',
      //   })
      //   .getOne(),
    ]);

    if (product) {
      if (!product.isAllowRa) {
        throw new AppBaseExc(StatusCode.SBPS_QR_NOT_EXISTS_RA, '', true);
      } else {
        const rsValidate = await this.validateDataQrBeforeProcess(
          vitaQr,
          false,
        );
        if (rsValidate) {
          return rsValidate;
        }
      }
    }

    // Check over limit
    const startOfCurrentDay = getStartOfNowInTimezone();

    const limitInDay = Number(limitInDayConfig.value) || 3;

    if (
      userNumberScan &&
      compareDateWithDateInTimezone(
        userNumberScan.scannedAt,
        startOfCurrentDay,
      ) >= 0 &&
      userNumberScan?.numberScanInDaySbps >= limitInDay
    ) {
      throw new AppBaseExc(StatusCode.SBPS_OVERADDPOINT, '', true);
    }
    //check event

    if (!product) {
      throw new AppBaseExc(StatusCode.SBPS_QR_UNMATCH, '', true);
    }
    let crmTransTypeCodeFirstScan = 'FIRST_SCAN_SBPS';
    let crmTransTypeCodeNotFirstScan = 'QR_CODE_SBPS';
    // Check product is product's brand
    if (product.checkIsProductBrand()) {
      // Check product belong to any active events
      const [activeEventsContainSkuGet, activeEventAddCansContainSkuGet] =
        await Promise.all([
          this.newestEventRepo.getAnyActiveEventContainSku(product.code),
          this.eventAddCanRepo.getAnyActiveEventContainSku(product.code),
        ]);
      if (
        (!activeEventsContainSkuGet || !activeEventsContainSkuGet.length) &&
        (!activeEventAddCansContainSkuGet ||
          !activeEventAddCansContainSkuGet.length)
      ) {
        return new AppResponseDto({}, null, {
          code: [PopupV2Code.WA_SBPS_2024_NOEVENT],
          notification_title: 'SYSTEM_MESSAGE',
          title: 'SYSTEM_MESSAGE',
        });
      }
      // crmTransTypeCodeFirstScan = 'FIRST_SCAN_SBPS_BRAND';
      // crmTransTypeCodeNotFirstScan = 'QR_CODE_SBPS_BRAND';
    }

    // const dataFirstScan = await this.historyPointRepo
    //   .createQueryBuilder('historyPoint')
    //   .where('historyPoint.customerId = :customerId', {
    //     customerId: user.id,
    //   })
    //   .andWhere('historyPoint.actionType = :actionType', {
    //     actionType: crmTransTypeCodeFirstScan,
    //   })
    //   .getOne();
    // const type = dataFirstScan
    //   ? crmTransTypeCodeNotFirstScan
    //   : crmTransTypeCodeFirstScan;
    const oggiOrClgSkuGet = null;
    const allowAddingPoint = validateAllowAddPoint(
      vitaQr,
      oggiOrClgSkuGet,
      webapp_name,
    );
    const allowJoinEvent = validateAllowJoinEvent(
      vitaQr,
      oggiOrClgSkuGet,
      webapp_name,
    );

    // const [crmTxType, monthExpiry] = await Promise.all([
    const [monthExpiry] = await Promise.all([
      // this.crmTransactionTypeRepo.findOneBy({
      //   code: type,
      // }),
      this.getMonthExpiryBySku(sku),
    ]);
    const isExpired = vitaQr
      ? dayjs().isAfter(
          dayjs(vitaQr?.createdAt).add(Number(monthExpiry), 'month'),
        )
      : true;
    if (isExpired) {
      throw new AppBaseExc(StatusCode.SBPS_EXPIRE, '', true);
    }

    const currentTier = await this.tierRepo.findOneBy({ code: user.tierCode });
    if (!currentTier) {
      throw new AppBaseExc(StatusCode.SBPS_DEFAULT_ERROR, '', true);
    }

    let productPoint = product.point;
    let productLevelPoint = product.levelPoint;
    const brandPoint = product.brandPoint;
    if (!allowAddingPoint || product.checkIsProductBrand()) {
      productPoint = 0;
      productLevelPoint = 0;
    }
    user.giftPoint = user.giftPoint + productPoint;
    user.tierPoint = user.tierPoint + productLevelPoint;
    user.tierPointAddedAfterUpRank += productLevelPoint;
    user.totalPoint = user.totalPoint + productPoint;
    user.lastScanDate = getNowAtTimeZoneHcm();
    const transactionalExternalId = randomTransactionExternalId();
    const contentTransaction = `Sữa uống dinh dưỡng - ${product.previewName}`;
    // const [scanHistory, , historyPoint] = await Promise.all([
    const [scanHistory] = await Promise.all([
      this.scanHistoryRepo.save({
        userId: user.id,
        userName: user.name,
        phoneNumber: user.phoneNumber,
        qrCode,
        request: JSON.stringify({ qrCode }),
        createdDate: getNowAtTimeZoneHcm(),
        status: STATUS_SCAN_HIS.SUCCESS,
      }),
      this.createOrUpdateUserNumberScanSbps(userId, userNumberScan),
      // this.historyPointRepo.save({
      //   customerId: user.id,
      //   customerName: user.firstName + user.lastName,
      //   customerPhone: user.phoneNumber,
      //   giftPoint: productPoint,
      //   tierPoint: productLevelPoint,
      //   status: 'SUCCESS',
      //   transactionDate: getNowAtTimeZoneHcm(),
      //   type: 'ADD_POINT',
      //   money: product.money,
      //   brand: product.brand,
      //   cdpSyncUp: false,
      //   isGiftReceived: false,
      //   actionType: type,
      //   transactionExternalId: transactionalExternalId,
      // }),

      this.vitaQrRepo.save({
        ...vitaQr,
        status: STATUS.USED,
      }),
    ]);

    const promisesSaveData: any[] = [
      // this.historyPointAttributeRepo.save({
      //   attributeCode: 'QR_CODE',
      //   value: scanHistory.qrCode,
      //   historyPointId: historyPoint.id,
      // }),
      // this.historyPointAttributeRepo.save({
      //   attributeCode: 'PRODUCT_ID',
      //   value: product.id.toString(),
      //   historyPointId: historyPoint.id,
      // }),
      // this.historyPointAttributeRepo.save({
      //   attributeCode: 'PRODUCT_NAME',
      //   value: product.name,
      //   historyPointId: historyPoint.id,
      // }),
    ];
    // save to identity service
    if (!product.checkIsProductBrand()) {
      promisesSaveData.push(
        this.ddxTriggerCalculateUserResetGiftPointRepo.save({
          customerId: String(user.id),
          customerPhone: user.phoneNumber || 'Null',
          customerName: user.firstName + user.lastName || 'Undefined',
          totalPointBefore: Math.max((user.giftPoint ?? 0) - productPoint, 0),
          totalPointAfter: user.giftPoint ?? 0,
          numberPoint: productPoint,
          contentTransaction: contentTransaction,
          type: 'add_point',
          actionType: 'verifyPoint',
          transactionTime: getNowAtTimeHcm(),
          transactionExternalId: transactionalExternalId,
          account: {
            created_at: user.createdDate,
            last_time_login: user.lastLoginDate,
          },
        }),
      );
    }
    if (promisesSaveData.length) {
      await Promise.all(promisesSaveData);
    }

    const leadId = await this.crmService.convertLeadToAccount(user);
    if (leadId) {
      user.crmUserId = leadId;
      user.userType = UserType.ACCOUNT;
    }

    const previewProduct = await this.productPreview.findOne({
      where: { code: sku },
    });

    const requestOutboxMessage: RequestOutboxMessage = [];

    let newUser: User;
    if (
      productPoint > 0 &&
      Object.keys(PER_REDEEM_POINT).includes(user.tierCode)
    ) {
      const point = (productPoint * PER_REDEEM_POINT[user.tierCode]) / 100;
      user.giftPoint += point;
      user.totalPoint += point;
      const oldTierCode = user.tierCode;
      let newTier = user.tierCode;
      if (user.tierPoint >= currentTier.maxPoint && currentTier.nextTierCode) {
        newTier = currentTier.nextTierCode;
      }

      const useNotiV3 = this.configSer.get('useNotiV3');
      this.logger.log(`useNotiV3: ${useNotiV3}`);
      if (useNotiV3) {
        const kafkaDto = new PushNotiKafkaDto({
          userIds: [userId],
          version: PushNotiKafkaDtoVersion.V1,
          notiDisplayTemplateType: NotiDisplayTemplateType.QR_CODE_TIER_2023,
          notiDisplayTemplateParams: {
            gifted_point_amount: point,
            gifted_point_rank: oldTierCode,
          },
          featureNoti: FeatureNoti.NOTI_EVENT,
        });

        await this.createTranGiftPointV2(
          user,
          transactionalExternalId,
          requestOutboxMessage,
          CRM_TRANS_TYPE_CODE[newTier],
          point,
          CRM_TRANS_TYPE_CODE[oldTierCode],
          kafkaDto,
        );
      } else {
        await this.createTranGiftPoint(
          user,
          transactionalExternalId,
          requestOutboxMessage,
          CRM_TRANS_TYPE_CODE[newTier],
          point,
          CRM_TRANS_TYPE_CODE[oldTierCode],
          `THƯỞNG XU THÀNH CÔNG.`,
          `Bạn được thưởng ${point} xu với hạng ${oldTierCode}. Hãy sử dụng Xu tích luỹ để tham gia đổi quà.`,
          `"Bạn được thưởng ${point} xu với hạng ${oldTierCode}. Hãy sử dụng Xu tích luỹ để tham gia đổi quà."`,
        );
      }
    }
    if (user.tierPoint >= currentTier.maxPoint && currentTier.nextTierCode) {
      user.tierCode = currentTier.nextTierCode;
      user.tierRankUpdatedDate = new Date();
      user.tierPointAddedAfterUpRank = 0;

      await this.userRepo.save(user);

      newUser = await this.redeemPointUpgradeRank(
        user,
        transactionalExternalId,
        requestOutboxMessage,
      );
    } else {
      await this.userRepo.save(user);
    }

    // const newOutboxMessage = this.outboxMessageRepo.create({
    //   provider: SyncProvider.CRM,
    //   callType: CallType.SYNC,
    //   syncType: SyncType.IMMEDIATE,
    //   request: JSON.stringify(request),
    //   status: OutboxMessageStatus.PROCESSING,
    //   createdDate: getNowAtTimeHcm(),
    //   retryNumber: 0,
    // });

    const useNotiV3 = this.configSer.get('useNotiV3');
    this.logger.log(`useNotiV3: ${useNotiV3}`);
    if (productPoint || brandPoint) {
      if (useNotiV3) {
        const kafkaDto = new PushNotiKafkaDto({
          userIds: [userId],
          version: PushNotiKafkaDtoVersion.V1,
          notiDisplayTemplateType: NotiDisplayTemplateType.QR_CODE_SBPS,
          notiDisplayTemplateParams: {
            added_point_amount: productPoint || brandPoint,
          },
          featureNoti: FeatureNoti.NOTI_ADD_POINT,
        });

        const outboxMsg = this.outboxMessageRepo.createPushNoti(kafkaDto);
        await this.outboxMessageRepo.save(outboxMsg);
      } else {
        // await Promise.all([
        //   // this.outboxMessageRepo.save(newOutboxMessage),
        //   this.pushNotification(
        //     user.id,
        //     'QUÉT MÃ VÀ TÍCH XU THÀNH CÔNG SBPS',
        //     `Bạn đã tích luỹ thành công ${productPoint} xu. Hãy sử dụng xu tích luỹ để tham gia đổi quà.`,
        //     `Bạn đã tích luỹ thành công ${productPoint} xu. Hãy sử dụng xu tích luỹ để tham gia đổi quà.`,
        //   ),
        // ]);
      }
    }

    let arrNoti = [];
    const arrTemplate2468Noti = [];

    newUser = await this.userRepo.findOneBy({ id: user.id });
    const outboxMessageGift: OutboxMessage[] = [];
    const syncData3rdServiceToWhRequestDetails: SyncData3rdServiceToWhRequestDetail[] =
      [];
    const eventTrigger: EventType[] = [];

    let eventsTriggered: Event[] = [];
    let eventAddCansTriggered: EventAddCan[] = [];
    if (allowJoinEvent) {
      const rs = await this.eventEmitter.emitAsync(
        'event.Q2',
        newUser,
        product,
        qrCode,
        ProductDataGotByQrDto.fromVitaQrSbps(
          vitaQr,
          previewProduct,
          monthExpiry,
        ),
        transactionalExternalId,
        arrNoti,
        appversionname,
        requestOutboxMessage,
        token,
        'SBPS',
        outboxMessageGift,
        syncData3rdServiceToWhRequestDetails,
        eventTrigger,
        vitaQr,
        webapp_name,
      );
      eventsTriggered = rs[0];
    }

    /*let isEventWaSbps2024 = false;
    for (const type of eventTrigger) {
      if (EventType.EV_WA_SBPS_2024 == type) {
        isEventWaSbps2024 = true;
      }
    }
    const popupCodeTemplateEventQ2 = arrNoti.length ? arrNoti[0] : null;
    if (
      !isEventWaSbps2024 ||
      (isEventWaSbps2024 && PopupV2Code.WA_SBPS_2024_HETGIAI == popupCodeTemplateEventQ2)
    ) {
      
    }*/

    const eventAddCanTypeTrigger: EventAddCanType[] = [];
    if (allowJoinEvent) {
      const rs = await this.eventEmitter.emitAsync(
        EVENT_EMITTER_NAME.EVENT_2468,
        user,
        product,
        qrCode,
        ProductDataGotByQrDto.fromVitaQrSbps(
          vitaQr,
          previewProduct,
          monthExpiry,
        ),
        transactionalExternalId,
        arrTemplate2468Noti,
        appversionname,
        requestOutboxMessage,
        eventAddCanTypeTrigger,
        'SBPS',
      );
      eventAddCansTriggered = rs[0];
      let isEventCLBB3BrandQ42024 = false;
      for (const type of eventAddCanTypeTrigger) {
        if (EventAddCanType.EV_24_Q4_3BRAND == type) {
          isEventCLBB3BrandQ42024 = true;
        }
      }
      if (!arrNoti.length && arrTemplate2468Noti.length) {
        arrNoti = arrTemplate2468Noti;
      }
    }

    let eventAddCanTriggered: EventAddCan = null;
    let eventTriggered: Event = null;
    let mapUserToBrandPoint: MapUserToBrandPointEntity = null;
    let isFirstScan = false;
    if (
      product.checkIsProductBrand() &&
      (eventsTriggered.length || eventAddCansTriggered.length)
    ) {
      if (eventsTriggered.length) {
        eventsTriggered = sortByAttribute(eventsTriggered, 'endDate', false);
        eventTriggered = eventsTriggered[0];
      }
      if (eventAddCansTriggered.length) {
        eventAddCansTriggered = sortByAttribute(
          eventAddCansTriggered,
          'endDate',
          false,
        );
        eventAddCanTriggered = eventAddCansTriggered[0];
      }

      mapUserToBrandPoint = await this.mapUserToBrandPointRepo.findOneBy({
        userId: userId,
        brandId: product.brandId,
        eventId: eventTriggered ? eventTriggered.id : null,
        eventAddCanId: eventAddCanTriggered ? eventAddCanTriggered.id : null,
      });

      isFirstScan = mapUserToBrandPoint ? false : true;
      crmTransTypeCodeFirstScan = 'FIRST_SCAN_SBPS_BRAND';
      crmTransTypeCodeNotFirstScan = 'QR_CODE_SBPS_BRAND';
    } else {
      const dataFirstScan = await this.historyPointRepo
        .createQueryBuilder('historyPoint')
        .where('historyPoint.customerId = :customerId', {
          customerId: user.id,
        })
        .andWhere('historyPoint.actionType = :actionType', {
          actionType: crmTransTypeCodeFirstScan,
        })
        .getOne();
      isFirstScan = dataFirstScan ? false : true;
    }

    const type = isFirstScan
      ? crmTransTypeCodeFirstScan
      : crmTransTypeCodeNotFirstScan;

    const [crmTxType, historyPoint] = await Promise.all([
      this.crmTransactionTypeRepo.findOneBy({
        code: type,
      }),
      this.historyPointRepo.save({
        customerId: user.id,
        customerName: user.firstName + user.lastName,
        customerPhone: user.phoneNumber,
        giftPoint: productPoint,
        tierPoint: productLevelPoint,
        status: 'SUCCESS',
        transactionDate: getNowAtTimeZoneHcm(),
        type: 'ADD_POINT',
        money: product.money,
        brand: product.brand,
        cdpSyncUp: false,
        isGiftReceived: false,
        actionType: type,
        transactionExternalId: transactionalExternalId,
      }),
    ]);
    await Promise.all([
      this.historyPointAttributeRepo.save({
        attributeCode: 'QR_CODE',
        value: scanHistory.qrCode,
        historyPointId: historyPoint.id,
      }),
      this.historyPointAttributeRepo.save({
        attributeCode: 'PRODUCT_ID',
        value: product.id.toString(),
        historyPointId: historyPoint.id,
      }),
      this.historyPointAttributeRepo.save({
        attributeCode: 'PRODUCT_NAME',
        value: product.name,
        historyPointId: historyPoint.id,
      }),
    ]);

    const request: CreateAddPointSBPSTransactionReqDto = {
      userId: user.id,
      Transaction_Type__c: crmTxType.name,
      Type__c: CrmTransactionTypeRequest.ADDING,
      Level_Points__c: historyPoint.tierPoint,
      Redeem_Points__c: historyPoint.giftPoint,
      Transaction_External_ID__c: historyPoint.transactionExternalId,
      Transaction_Date__c: dayjs().tz(TIME_ZONE).format(TIME_FORMAT_CRM),
      Point_Transfer_Status__c: PointTransferStatus.TRANSFERRED,
      Product_Name__c: product.name,
      Product_Code__c: product.code,
      Amount__c: Math.round(product.money),
      QR_Code__c: qrCode,
      Tier__c: user.tierCode,
      Earned_Tier_Point__c: productLevelPoint,
      Tier_Points__c: user.tierPoint,
      Rule_Name__c: crmTxType.description,
      /*Expiry_date__c: dayjs(vitaQr?.createdAt)
        .add(Number(monthExpiry), 'month')
        .tz(TIME_ZONE)
        .format('YYYY-MM-DDTHH:mm:ss.SSS+00:00'),
      Manufacturing_Date__c: dayjs(vitaQr?.createdAt)
        .tz(TIME_ZONE)
        .format('YYYY-MM-DDTHH:mm:ss.SSS+00:00'),*/
      // Type of createdDate in user is timestamp without timezone
      // We need convert to string to make sure dayjs parsing to tz work well
      Expiry_date__c:
        addMonthToDateAndMakeDateIsDateInTimeZoneAndFormatToString(
          vitaQr?.createdAt.toUTCString(),
          Number(monthExpiry),
          TIME_ZONE,
          TIME_FORMAT_DAY,
        ),
      // Type of createdDate in user is timestamp without timezone
      // We need convert to string to make sure dayjs parsing to tz work well
      Manufacturing_Date__c: makeDateIsDateInTimeZoneAndFormatToString(
        vitaQr?.createdAt.toUTCString(),
        TIME_ZONE,
        TIME_FORMAT_DAY,
      ),
    };

    let loyaltyProgramSfId = '';
    let idSendSfUnique = '';
    if (
      product.checkIsProductBrand() &&
      (eventTriggered || eventAddCanTriggered)
    ) {
      const promisesUpdateAddPointBrandProduct: any[] = [];

      const mapHistoryPointToBrandPoint =
        new MapHistoryPointToBrandPointEntity();
      mapHistoryPointToBrandPoint.historyPointId = historyPoint.id;
      mapHistoryPointToBrandPoint.brandId = product.brandId;
      mapHistoryPointToBrandPoint.brandPoint = product.brandPoint;
      if (eventsTriggered.length) {
        loyaltyProgramSfId = eventTriggered.loyaltyProgramSfId;
        mapHistoryPointToBrandPoint.eventId = eventTriggered.id;
      }
      if (eventAddCansTriggered.length) {
        loyaltyProgramSfId = eventAddCanTriggered.loyaltyProgramSfId;
        mapHistoryPointToBrandPoint.eventAddCanId = eventAddCanTriggered.id;
      }
      promisesUpdateAddPointBrandProduct.push(
        this.mapHistoryPointToBrandPointRepo.save(mapHistoryPointToBrandPoint),
      );

      if (mapUserToBrandPoint) {
        const rsUpdateUserBrandPoint =
          await this.mapUserToBrandPointRepo.increaseBrandPoint(
            mapUserToBrandPoint,
            product.brandPoint,
          );
        if (!rsUpdateUserBrandPoint || !rsUpdateUserBrandPoint.affected) {
          throw new AppBaseExc(StatusCode.SBPS_DEFAULT_ERROR);
        }
      } else {
        mapUserToBrandPoint = new MapUserToBrandPointEntity();
        mapUserToBrandPoint.userId = userId;
        mapUserToBrandPoint.brandId = product.brandId;
        mapUserToBrandPoint.brandPoint = product.brandPoint;
        if (eventAddCanTriggered) {
          mapUserToBrandPoint.eventAddCanId = eventAddCanTriggered.id;
        }
        if (eventTriggered) {
          mapUserToBrandPoint.eventId = eventTriggered.id;
        }

        promisesUpdateAddPointBrandProduct.push(
          this.mapUserToBrandPointRepo.save(mapUserToBrandPoint),
        );
      }

      await Promise.all(promisesUpdateAddPointBrandProduct);

      idSendSfUnique = generateIdSendSfUnique(mapUserToBrandPoint);
      if (!idSendSfUnique) {
        throw new AppBaseExc(StatusCode.SBPS_DEFAULT_ERROR);
      }
      request.Program_Name__c = loyaltyProgramSfId;
      request.Loyalty_Member__c = idSendSfUnique;
      request.Redeem_Points__c = product.brandPoint;
    }

    requestOutboxMessage.unshift(request);

    if (this.crmSyncUsingWh) {
      const syncAddPointData3rdServiceToWhRequestDetails: SyncData3rdServiceToWhRequestDetail[] =
        [];
      syncAddPointData3rdServiceToWhRequestDetails.push(
        this.vitaJavaService.generateSyncData3rdServiceToWhRequestDetail(
          requestOutboxMessage,
          JavaV4WhSyncTo3rdServiceDestination.SF,
          JavaV4WhSyncTo3rdServiceCode.ADD_POINT,
        ),
      );
      syncAddPointData3rdServiceToWhRequestDetails.push(
        this.vitaJavaService.generateSyncData3rdServiceToWhRequestDetail(
          requestOutboxMessage,
          JavaV4WhSyncTo3rdServiceDestination.SAP,
          JavaV4WhSyncTo3rdServiceCode.ADD_POINT_MATHECAO,
        ),
      );
      const whAddPointOutboxMessageRequestData =
        this.vitaJavaService.generateWhOutboxMessageSyncData3rdServiceRequestDataInBatch(
          token,
          syncAddPointData3rdServiceToWhRequestDetails,
        );
      const outboxAddPointSyncWh =
        this.outboxMessageRepo.createSyncWhSyncData3rdService(
          whAddPointOutboxMessageRequestData,
        );
      // TODO: Stop sync WH if add point for brand product
      // TODO: process flow create loyalty member program first
      // Process done. Revert
      if (product.checkIsProductBrand()) {
        // outboxAddPointSyncWh.retryNumber = 90;
      }
      await this.outboxMessageRepo.save(outboxAddPointSyncWh);

      if (syncData3rdServiceToWhRequestDetails.length) {
        if (product.checkIsProductBrand() && eventTriggered) {
          for (const syncData3rdServiceToWhRequestDetail of syncData3rdServiceToWhRequestDetails) {
            syncData3rdServiceToWhRequestDetail.payload.Program_Name__c =
              loyaltyProgramSfId;
            syncData3rdServiceToWhRequestDetail.payload.Loyalty_Member__c =
              idSendSfUnique;
          }
        }
        const whOutboxMessageRequestData =
          this.vitaJavaService.generateWhOutboxMessageSyncData3rdServiceRequestDataInBatch(
            token,
            syncData3rdServiceToWhRequestDetails,
          );
        const outboxSyncWh =
          this.outboxMessageRepo.createSyncWhSyncData3rdService(
            whOutboxMessageRequestData,
          );
        // TODO: Stop sync WH if gifting gift when add point for brand product
        // TODO: process flow create loyalty member program first
        // Process done. Revert
        if (product.checkIsProductBrand()) {
          // outboxSyncWh.retryNumber = 90;
        }
        runOnTransactionCommit(async () => {
          try {
            await this.outboxMessageRepo.save(outboxSyncWh);
          } catch (error) {
            this.logger.error(
              `Error when save outbox message for sync data 3rd service,
              request: ${JSON.stringify(outboxSyncWh)},
              error: ${JSON.stringify(error)}`,
            );
          }
        });
      }
    } else {
      const newOutboxMessage = this.outboxMessageRepo.create({
        provider: SyncProvider.CRM,
        callType: CallType.SYNC,
        syncType: SyncType.IMMEDIATE,
        request: JSON.stringify(requestOutboxMessage),
        status: OutboxMessageStatus.PROCESSING,
        createdDate: getNowAtTimeHcm(),
        retryNumber: 0,
      });

      await this.outboxMessageRepo.save(newOutboxMessage);

      if (outboxMessageGift.length) {
        runOnTransactionCommit(async () => {
          try {
            await this.outboxMessageRepo.save(outboxMessageGift);
          } catch (error) {
            this.logger.error(
              `Error when save outbox message for sync data 3rd service,
              request: ${JSON.stringify(outboxMessageGift)},
              error: ${JSON.stringify(error)}`,
            );
          }
        });
      }
    }

    this.eventEmitter.emit(TRIPLAYZ_EVENT_NAME.ADD_TRANSACTION, {
      token,
      historyPoint,
      user,
      product,
      dto: { qrCode },
      productData: null,
      crmPointRequest: requestOutboxMessage?.[0],
    });

    if (allowJoinEvent) {
      const eventQuy32024UpRankData: EventQuy32024UpRankData = {
        user,
        requestOutboxMessage,
        qrCode,
        token,
      };
      const {
        postCommitActions = [],
        popupCode = '',
        gift = null,
        triggered = false,
      } = await this.handleEventQ32024UpRank(eventQuy32024UpRankData);
      if (triggered) {
        arrNoti = [popupCode];
        if (postCommitActions && postCommitActions.length) {
          runOnTransactionCommit(() => {
            asyncMapSettled(postCommitActions, (action) => action());
          });
        }
        const useNotiV3 = this.configSer.get('useNotiV3');
        if (useNotiV3) {
          const kafkaDto = new PushNotiKafkaDto({
            userIds: [userId],
            version: PushNotiKafkaDtoVersion.V1,
            notiDisplayTemplateType:
              NotiDisplayTemplateType.EVENT_QUY_3_2024_UP_RANK,
            featureNoti: FeatureNoti.NOTI_USER_RANKING,
          });

          const outboxMsg = this.outboxMessageRepo.createPushNoti(kafkaDto);
          await this.outboxMessageRepo.save(outboxMsg);
        }
      }
    }

    // Send noti enough point to finish pre order user gift
    if (user.giftPoint && !product.checkIsProductBrand()) {
      const request: GetUserGiftPreOrderEnoughPointToSendNotifyRequest = {
        userTotalPoint: user.giftPoint,
      };
      const response: GetUserGiftPreOrderEnoughPointToSendNotifyResponse =
        await this.vitaJavaService.getUserGiftPreOrderEnoughPointToSendNotify(
          token,
          request,
        );
      if (response) {
        const giftName = response?.data?.gift?.name;
        if (giftName) {
          try {
            const useNotiV3 = this.configSer.get('useNotiV3');
            if (useNotiV3) {
              const outboxesMsg: OutboxMessage[] = [];
              for (const name of giftName) {
                const kafkaDto = new PushNotiKafkaDto({
                  userIds: [user.id],
                  version: PushNotiKafkaDtoVersion.V1,
                  notiDisplayTemplateParams: {
                    gift_name: name,
                  },
                  notiDisplayTemplateType:
                    NotiDisplayTemplateType.ENOUGH_POINT_TO_FINISH_PRE_ORDER_USER_GIFT,
                  featureNoti: FeatureNoti.NOTI_PRE_ORDER_GIFT,
                });

                const outboxMsg =
                  this.outboxMessageRepo.createPushNoti(kafkaDto);
                outboxesMsg.push(outboxMsg);
              }

              await this.outboxMessageRepo.save(outboxesMsg);
            }
          } catch (err) {
            console.log(
              `error when push notification finish pre order user gift,`,
              err,
            );
          }
        }
      }
    }

    if ([137135, 137136, 137137].includes(userId)) {
      throw new AppBaseExc(StatusCode.SBPS_DEFAULT_ERROR, '', true);
    }

    arrNoti =
      await this.eventCommonPopupUserService.getPopupCodeByPriorityHighest(
        arrNoti,
      );

    return new AppResponseDto(
      { addPoint: productPoint, addBrandPoint: brandPoint },
      null,
      {
        code: arrNoti,
        notification_title: 'SYSTEM_MESSAGE',
        title: 'SYSTEM_MESSAGE',
      },
    );
  }

  async handleUpdateStatusQrCode(
    userId: number,
    eventCode: string,
    body: UpdateStatusQrCodeDto,
    appversionname: string,
    webapp_name?: WebAppList,
  ) {
    const { sku, status } = body;
    let { qrCode } = body;

    qrCode = this.extractQrCode(qrCode);

    if (!validateEventCodeIsSbps(eventCode)) {
      throw new AppBaseExc(StatusCode.SBPS_DEFAULT_ERROR, '', true);
    }

    if (status !== STATUS.USED) {
      throw new AppBaseExc(StatusCode.SBPS_DEFAULT_ERROR, '', true);
    }

    const [vitaQr, product] = await Promise.all([
      this.vitaQrRepo
        .createQueryBuilder('vitaQr')
        .where('vitaQr.code = :qrCode', { qrCode })
        // .andWhere('vitaQr.sku = :sku', { sku })
        .setLock('pessimistic_write')
        .maxExecutionTime(60000)
        .getOne(),
      this.productMstRepo
        .createQueryBuilder('productMst')
        .where((qb) => {
          qb.where('productMst.code LIKE :sku', { sku });
        })
        .getOne(),
    ]);

    if (!vitaQr || !vitaQr.isActive) {
      throw new AppBaseExc(StatusCode.SBPS_QR_NOEXIST, '', true);
    }

    if (vitaQr.status !== STATUS.UNUSED) {
      throw new AppBaseExc(StatusCode.SBPS_QR_USED, '', true);
    }

    if (vitaQr.sku !== sku || !product) {
      throw new AppBaseExc(StatusCode.SBPS_QR_UNMATCH, '', true);
    }

    if (product.isAllowRa) {
      throw new AppBaseExc(StatusCode.SBPS_QR_NOEXIST, '', true);
    }

    vitaQr.status = status;
    await this.vitaQrRepo.save(vitaQr);

    return new AppResponseDto('ok');
  }

  private async handleScanQrFail(
    userId: number,
    qrCode: string,
    error: AppBaseExc,
    eventCode: string,
  ) {
    if (qrCode.startsWith('https://storage.googleapis.com')) {
      qrCode = this.getCodeFromUrl(qrCode) as unknown as string;
    }
    const user = await this.userRepo.findOne({
      where: {
        id: userId,
      },
    });

    await this.scanHistoryRepo.save({
      userId: user.id,
      userName: user.name,
      phoneNumber: user.phoneNumber,
      qrCode,
      request: JSON.stringify({ qrCode }),
      createdDate: getNowAtTimeZoneHcm(),
      status: STATUS_SCAN_HIS.FAILED,
      errorCode: error.error,
    });

    if (
      error.error === StatusCode.BLOCKED_ACCOUNT_WHEN_SCAN_FAILED.error ||
      error.error === StatusCode.SBPS_ACCBLOCK_ADDPOINT.error
    ) {
      return;
    }

    console.log('user.id: ', user.id);

    const blockedStatus = await Promise.all([
      this.checkScanFailManyTime(eventCode, user),
      //this.checkBlockedManyTime(user),
    ]);
    console.log('blockedStatus', blockedStatus);
    blockedStatus.forEach((item) => {
      switch (item) {
        case BlockedHistoryType.SBPS_BLOCK_ADDPOINT:
          user.numberOfBlockedScanSbps += 1;
          break;

        /*case BlockedHistoryType.SBPS_ACCBLOCK_ADDPOINT:
          user.blockedAccountType =
            BlockedHistoryType.BLOCKED_ACCOUNT_WHEN_SCAN_FAILED;
          user.blockedAccount = true;
          user.blockedAccountDate = getNowAtTimeZoneHcm();
          break;*/
      }
    });
    const blockedAccount = await this.checkBlockedManyTime(user);
    if (BlockedHistoryType.SBPS_ACCBLOCK_ADDPOINT == blockedAccount) {
      user.blockedAccountType =
        BlockedHistoryType.BLOCKED_ACCOUNT_WHEN_SCAN_FAILED;
      user.blockedAccount = true;
      user.blockedAccountDate = getNowAtTimeZoneHcm();
    }

    if (sbpsErrorCodes.includes(error.error)) {
      user.sbpsFailAddPointCounter += 1;
    }

    if (sbErrorCodes.includes(error.error)) {
      user.sbFailAddPointCounter += 1;
    }

    const startOfNextDay = getStartOfDateInTimezone(
      dayjs().add(1, 'day').toDate(),
    );
    user.dateCompareResetFailedAddPoint = startOfNextDay;

    await this.userRepo.save(user);
  }

  private async checkScanFailManyTime(eventCode: string, user: User) {
    const startTime = dayjs().tz('UTC').startOf('day').toDate();
    const endTimeFilter = dayjs(startTime).tz('UTC').add(1, 'day');
    const endTime = dayjs().tz(TIME_ZONE).startOf('day').add(1, 'day');

    const isBlocked = await this.userIsBlocked(eventCode, user.id);
    let total = 0;

    if (!isBlocked) {
      const [dataScan, totalCount] = await this.scanHistoryRepo
        .createQueryBuilder('scanHistory')
        .where((qb) => {
          qb.where('scanHistory.userId = :userId', { userId: user.id })
            .andWhere('scanHistory.status = :status', {
              status: STATUS_SCAN_HIS.FAILED,
            })
            .andWhere('scanHistory.errorCode IN (:...errorCodes)', {
              errorCodes: SBPS_SCAN_FAIL_VALID_ERROR_CODE,
            })
            .andWhere('scanHistory.apiType is distinct from :apiType', {
              apiType: ScanHistoryApiType.HOTLINE,
            })
            .andWhere(
              'scanHistory.createdDate >= :startTime AND scanHistory.createdDate < :endTime',
              {
                startTime,
                endTime: endTimeFilter,
              },
            )
            .andWhere('scanHistory.spoonCode is null');
        })
        .getManyAndCount();

      total = totalCount;
    }

    const limitInDayConfig =
      await this.systemConfigRepo.getLimitNumberOfScanSbpsFailedInDay();

    const limitInDay = Number(limitInDayConfig.value) || 5;
    const productLine = await this.productLineRepo.findOne({
      where: { code: EVENT_CODE.SBPS },
    });
    if (!productLine)
      throw new AppBaseExc(StatusCode.SBPS_DEFAULT_ERROR, '', true);

    if (total < limitInDay) return ' ';

    await this.blockedHistoryRepo.save({
      userId: user.id,
      userName: user.getFullName(),
      phoneNumber: user.phoneNumber,
      blockedTime: 1440,
      type: BlockedHistoryType.SBPS_BLOCK_ADDPOINT,
      reason: 'Scan qr SBPS failed many times',
      cdpSyncUp: false,
      actionDate: getNowAtTimeZoneHcm(),
    });
    const blockedScan = await this.blockedScanRepo.findOne({
      where: {
        userId: user.id,
        productLineId: productLine.id,
      },
    });

    if (blockedScan) {
      await Promise.all([
        this.blockedScanRepo.save({
          ...blockedScan,
          blockScan: true,
          updatedDate: getNowAtTimeHcm(),
          expiryDate: endTime,
        }),
      ]);
    } else {
      await Promise.all([
        this.blockedScanRepo.save({
          userId: user.id,
          productLine: productLine,
          blockScan: true,
          updatedDate: getNowAtTimeHcm(),
          expiryDate: endTime,
        }),
      ]);
    }

    return BlockedHistoryType.SBPS_BLOCK_ADDPOINT;
  }

  private async checkBlockedManyTime(user: User) {
    const limitNumberOfBlockedScanConfig =
      await this.systemConfigRepo.getLimitNumberOfBlockedScan();
    const limitNumberOfBlockedScanValue =
      Number(limitNumberOfBlockedScanConfig.value) || 3;

    if (user.numberOfBlockedScanSbps < limitNumberOfBlockedScanValue) return '';
    user.numberOfBlockedAccount++;

    const blockedScanHistory = this.blockedHistoryRepo.create({
      userId: user.id,
      userName: user.getFullName(),
      phoneNumber: user.phoneNumber,
      actionDate: getNowAtTimeZoneHcm(),
      blockedTime: null,
      reason: 'Blocked scan sbps many times',
      type: BlockedHistoryType.SBPS_ACCBLOCK_ADDPOINT,
      cdpSyncUp: false,
    });
    await Promise.all([
      this.userRepo.save(user),
      this.blockedHistoryRepo.save(blockedScanHistory),
    ]);
    return BlockedHistoryType.SBPS_ACCBLOCK_ADDPOINT;
  }

  private async userIsBlocked(eventCode: string, userId: number) {
    const productLine = await this.productLineRepo.findOne({
      where: {
        code: eventCode,
      },
    });
    if (!productLine) {
      throw new AppBaseExc(StatusCode.SBPS_DEFAULT_ERROR, '', true);
    }

    const userBlock = await this.blockedScanRepo
      .createQueryBuilder('blockedScan')
      .where((qb) => {
        qb.where('blockedScan.userId = :userId', { userId })
          .andWhere('blockedScan.productLineId = :productLineId', {
            productLineId: productLine.id,
          })
          .andWhere('blockedScan.expiryDate >= :currentTime', {
            //currentTime: dayjs().format(FORMAT_DATE),
            // Do not using dayjs().format(), it cause timezone issue.
            currentTime: dayjs(),
          });
      })
      .getOne();

    return !!userBlock?.blockScan;
  }

  private async checkBlockAccount(user: User) {
    // freeze user
    if (user.startFreezePoint && user.endFreezePoint) {
      const currentTime = getNowAtTimeZoneHcm();
      const isWithinFreezePeriod =
        compareDateBetweenFromDateIAndToDateInTimezone(
          currentTime,
          user.startFreezePoint,
          user.endFreezePoint,
        );
      if (isWithinFreezePeriod === 1) {
        return StatusCode.BLOCK_IDENTITY_SCAN.error;
      }
    }

    //
    if (!user.blockedAccount) return;

    const blockedScanHistory = await this.blockedHistoryRepo.findOne({
      where: { userId: user.id },
      order: { actionDate: 'DESC', id: 'DESC' },
    });
    if (!blockedScanHistory) return StatusCode.SBPS_DEFAULT_ERROR.error;

    if (blockedScanHistory.type === BlockedHistoryType.SBPS_ACCBLOCK_ADDPOINT) {
      return StatusCode.SBPS_ACCBLOCK_ADDPOINT.error;
    }

    if (
      blockedScanHistory.type ===
      BlockedHistoryType.BLOCKED_ACCOUNT_WHEN_SCAN_FAILED
    ) {
      return StatusCode.BLOCKED_ACCOUNT_WHEN_SCAN_FAILED.error;
    }
    return;
  }

  private async pushNotification(
    userId: number,
    title: string,
    content: string,
    description: string,
  ) {
    const topic = `USER_${userId}_${dayjs().unix()}`;
    const userSessions = await this.userSessionRepo.find({
      where: { userId, deviceToken: Not(IsNull()) },
    });
    const tokens = userSessions.map((item) => item.deviceToken);

    const request: PushNotificationRequest = {
      message: content,
      title: title,
      topic: topic,
      type: 2,
      tokenOfTopic: tokens,
    };
    const notification = this.notificationUserRepo.create({
      userId,
      status: NotificationUserStatus.UNREAD,
      firebaseStatus: NotificationUserFirebaseStatus.SENT,
      title,
      content,
      description,
    });

    await this.vitaJavaService.pushNotification(request);
    await this.notificationUserRepo.save(notification);
  }

  private getCodeFromUrl(url: string) {
    return url?.split('/')?.[url?.split('/')?.length - 1]?.split('.')?.[0];
  }

  async redeemPointUpgradeRank(
    user: User,
    transactionExternalId: string,
    requestOutboxMessage: RequestOutboxMessage,
  ) {
    let isUpdateRankGiftPoint = false;
    const startOfCurrentYear = getStartOfCurrentYearAtTimeHcm(true);
    const endOfCurrentYear = getEndOfCurrentYearAtTimeHcm(true);
    if (user.tierCode === USER_RANK.GOLD) {
      const historyPoint = await this.historyPointRepo.count({
        where: {
          actionType: CRM_TRANS_TYPE_CODE.TIER_UP_GOLD_2023,
          type: HistoryPointType.GIFT,
          customerId: user.id,
          transactionDate: Between(startOfCurrentYear, endOfCurrentYear),
        },
      });
      if (historyPoint <= 0) {
        isUpdateRankGiftPoint = true;
        user.giftPoint += GIFT_POINT.FIRST_UPGRADE_RANK_GOLD;
        user.totalPoint += GIFT_POINT.FIRST_UPGRADE_RANK_GOLD;

        const useNotiV3 = this.configSer.get('useNotiV3');
        this.logger.log(`useNotiV3: ${useNotiV3}`);
        if (useNotiV3) {
          const pushNotiKafkaDto = new PushNotiKafkaDto({
            userIds: [user.id],
            version: PushNotiKafkaDtoVersion.V1,
            notiDisplayTemplateType: NotiDisplayTemplateType.TIER_UP_2023,
            notiDisplayTemplateParams: {
              point: GIFT_POINT.FIRST_UPGRADE_RANK_GOLD,
              rank: user.tierCode,
            },
            featureNoti: FeatureNoti.NOTI_USER_RANKING,
          });

          await this.createTranGiftPointV2(
            user,
            transactionExternalId,
            requestOutboxMessage,
            CRM_TRANS_TYPE_CODE.TIER_UP_GOLD_2023,
            GIFT_POINT.FIRST_UPGRADE_RANK_GOLD,
            CRM_TRANS_TYPE_CODE.TIER_UP_GOLD_2023,
            pushNotiKafkaDto,
          );
        } else {
          await this.createTranGiftPoint(
            user,
            transactionExternalId,
            requestOutboxMessage,
            CRM_TRANS_TYPE_CODE.TIER_UP_GOLD_2023,
            GIFT_POINT.FIRST_UPGRADE_RANK_GOLD,
            CRM_TRANS_TYPE_CODE.TIER_UP_GOLD_2023,
            `THĂNG HẠNG ${user.tierCode} THÀNH CÔNG.`,
            `Bạn đã được tặng ${GIFT_POINT.FIRST_UPGRADE_RANK_GOLD} xu khi thăng hạng ${user.tierCode} thành công.`,
            `"Bạn đã được tặng ${GIFT_POINT.FIRST_UPGRADE_RANK_GOLD} xu khi thăng hạng ${user.tierCode} thành công."`,
          );
        }
      }
    } else if (user.tierCode === USER_RANK.PLATINUM) {
      const historyPoint = await this.historyPointRepo.count({
        where: {
          actionType: CRM_TRANS_TYPE_CODE.TIER_UP_PLATINUM_2023,
          type: HistoryPointType.GIFT,
          customerId: user.id,
          transactionDate: Between(startOfCurrentYear, endOfCurrentYear),
        },
      });
      if (historyPoint <= 0) {
        isUpdateRankGiftPoint = true;
        user.giftPoint += GIFT_POINT.FIRST_UPGRADE_RANK_PLATIUM;
        user.totalPoint += GIFT_POINT.FIRST_UPGRADE_RANK_PLATIUM;

        const useNotiV3 = this.configSer.get('useNotiV3');
        this.logger.log(`useNotiV3: ${useNotiV3}`);

        if (useNotiV3) {
          const pushNotiKafkaDto = new PushNotiKafkaDto({
            link: '',
            userIds: [user.id],
            version: PushNotiKafkaDtoVersion.V1,
            notiDisplayTemplateType: NotiDisplayTemplateType.TIER_UP_2023,
            notiDisplayTemplateParams: {
              point: GIFT_POINT.FIRST_UPGRADE_RANK_PLATIUM,
              rank: user.tierCode,
            },
            featureNoti: FeatureNoti.NOTI_USER_RANKING,
          });

          await this.createTranGiftPointV2(
            user,
            transactionExternalId,
            requestOutboxMessage,
            CRM_TRANS_TYPE_CODE.TIER_UP_PLATINUM_2023,
            GIFT_POINT.FIRST_UPGRADE_RANK_PLATIUM,
            CRM_TRANS_TYPE_CODE.TIER_UP_PLATINUM_2023,
            pushNotiKafkaDto,
          );
        } else {
          await this.createTranGiftPoint(
            user,
            transactionExternalId,
            requestOutboxMessage,
            CRM_TRANS_TYPE_CODE.TIER_UP_PLATINUM_2023,
            GIFT_POINT.FIRST_UPGRADE_RANK_PLATIUM,
            CRM_TRANS_TYPE_CODE.TIER_UP_PLATINUM_2023,
            `THĂNG HẠNG ${user.tierCode} THÀNH CÔNG.`,
            `Bạn đã được tặng ${GIFT_POINT.FIRST_UPGRADE_RANK_PLATIUM} xu khi thăng hạng ${user.tierCode} thành công.`,
            `"Bạn đã được tặng ${GIFT_POINT.FIRST_UPGRADE_RANK_PLATIUM} xu khi thăng hạng ${user.tierCode} thành công."`,
          );
        }
      }
    }

    if (user.tier === USER_RANK.TITAN || !isUpdateRankGiftPoint) {
      const useNotiV3 = this.configSer.get('useNotiV3');
      this.logger.log(`useNotiV3: ${useNotiV3}`);

      if (useNotiV3) {
        const kafkaDto = new PushNotiKafkaDto({
          link: '',
          userIds: [user.id],
          version: PushNotiKafkaDtoVersion.V1,
          notiDisplayTemplateType: NotiDisplayTemplateType.TIER_UP,
          notiDisplayTemplateParams: {
            up_rank: user.tierCode,
          },
          featureNoti: FeatureNoti.NOTI_USER_RANKING,
        });

        const outboxMsg = this.outboxMessageRepo.createPushNoti(kafkaDto);
        await this.outboxMessageRepo.save(outboxMsg);
      } else {
        await this.pushNotification(
          user.id,
          `THĂNG HẠNG ${user.tierCode} THÀNH CÔNG.`,
          `Bạn đã thăng hạng ${user.tierCode} thành công.`,
          `"Bạn đã thăng hạng ${user.tierCode} thành công."`,
        );
      }
    }
    const newUser = await this.userRepo.save(user);
    return newUser;
  }

  private async createTranGiftPoint(
    user: User,
    transactionRefId: string,
    requestOutboxMessage: RequestOutboxMessage,
    actionType: string,
    point: number,
    crmCode: string,
    title: string,
    content: string,
    description: string,
  ) {
    const transactionExternalId = randomTransactionExternalId();
    const crmTxType = await this.crmTransactionTypeRepo.findOneBy({
      code: crmCode,
    });

    //save to identity service
    const historyPointCategory = mapHistoryPointType(
      HistoryPointType.GIFT,
      point,
    );

    const identityPoint = this.ddxTriggerCalculateUserResetGiftPointRepo.create(
      {
        customerId: String(user.id),
        customerPhone: user.phoneNumber || 'Null',
        customerName: user.firstName + user.lastName || 'Undefined',
        totalPointBefore: Math.max((user.giftPoint ?? 0) - point, 0),
        totalPointAfter: user.giftPoint ?? 0,
        numberPoint: point,
        contentTransaction:
          IdentityPointContent[crmTxType.code] || 'Content empty',
        type: historyPointCategory,
        actionType: IdentityPointActionType[crmTxType.code] || 'verifyPoint',
        transactionTime: getNowAtTimeHcm(),
        transactionExternalId: transactionExternalId,
        account: {
          created_at: user.createdDate,
          last_time_login: user.lastLoginDate,
        },
      },
    );

    const history = this.historyPointRepo.create({
      type: HistoryPointType.GIFT,
      customerId: user.id,
      customerName: user.getFullName(),
      customerPhone: user.phoneNumber,
      status: HistoryPointStatus.SUCCESS,
      transactionDate: getNowAtTimeZoneHcm(),
      transactionExternalId: transactionExternalId,
      cdpSyncUp: false,
      actionType,
      giftPoint: point,
    });

    await this.historyPointRepo.save(history);
    await this.ddxTriggerCalculateUserResetGiftPointRepo.save(identityPoint);
    const historyPointAttributes: HistoryPointAttribute[] = [
      this.historyPointAttributeRepo.create({
        attributeCode: HistoryPointAttributeCode.POINT,
        value: String(point),
        historyPointId: history.id,
      }),
      this.historyPointAttributeRepo.create({
        attributeCode: HistoryPointAttributeCode.REF_TRAN_ID,
        value: transactionRefId,
        historyPointId: history.id,
      }),
    ];

    await Promise.all([
      this.historyPointAttributeRepo.save(historyPointAttributes),
      // push noti v2 in function createTranGiftPointV2 below
      this.pushNotification(user.id, title, content, description),
    ]);

    const request: CrmPointGiftingRequest = {
      userId: user.id,
      Transaction_Type__c: crmTxType.name,
      Type__c: 'Adding',
      Level_Points__c: 0,
      Redeem_Points__c: point,
      Transaction_External_ID__c: transactionExternalId,
      Transaction_Date__c: dayjs().tz(TIME_ZONE).format(TIME_FORMAT_CRM),
      Rule_Name__c: crmTxType.description,
      Campaign__c: crmTxType.campaignName,
      Earned_Tier_Point__c: 0,
      Tier__c: user.tierCode,
      Tier_Points__c: user.tierPoint,
      Transaction_Trigger_External_ID__c: transactionRefId,
    };

    // const newOutboxMessage = this.outboxMessageRepo.create({
    //   provider: SyncProvider.CRM,
    //   callType: CallType.SYNC,
    //   syncType: SyncType.IMMEDIATE,
    //   request: JSON.stringify(request),
    //   status: OutboxMessageStatus.PROCESSING,
    //   createdDate: getNowAtTimeHcm(),
    //   retryNumber: 0,
    // });
    // await this.outboxMessageRepo.save(newOutboxMessage);
    requestOutboxMessage.push(request);
  }

  private async createTranGiftPointV2(
    user: User,
    transactionRefId: string,
    requestOutboxMessage: RequestOutboxMessage,
    actionType: string,
    point: number,
    crmCode: string,
    pushNotiKafkaDto: PushNotiKafkaDto,
  ) {
    const transactionExternalId = randomTransactionExternalId();
    const crmTxType = await this.crmTransactionTypeRepo.findOneBy({
      code: crmCode,
    });

    //save to identity service
    const historyPointCategory = mapHistoryPointType(
      HistoryPointType.GIFT,
      point,
    );

    const identityPoint = this.ddxTriggerCalculateUserResetGiftPointRepo.create(
      {
        customerId: String(user.id),
        customerPhone: user.phoneNumber || 'Null',
        customerName: user.lastName + ' ' + user.firstName || 'Undefined',
        totalPointBefore: Math.max((user.giftPoint ?? 0) - point, 0),
        totalPointAfter: user.giftPoint ?? 0,
        numberPoint: point,
        contentTransaction:
          IdentityPointContent[crmTxType.code] ?? 'Unknow content',
        type: historyPointCategory,
        actionType: IdentityPointActionType[crmTxType.code] || 'verifyPoint',
        transactionTime: getNowAtTimeHcm(),
        transactionExternalId: transactionExternalId,
        account: {
          created_at: user.createdDate,
          last_time_login: user.lastLoginDate,
        },
      },
    );

    const history = this.historyPointRepo.create({
      type: HistoryPointType.GIFT,
      customerId: user.id,
      customerName: user.getFullName() || 'underfine',
      customerPhone: user.phoneNumber || 'Null',
      status: HistoryPointStatus.SUCCESS,
      transactionDate: getNowAtTimeZoneHcm(),
      transactionExternalId: transactionExternalId,
      cdpSyncUp: false,
      actionType,
      giftPoint: point,
    });

    await Promise.all([
      this.historyPointRepo.save(history),
      this.ddxTriggerCalculateUserResetGiftPointRepo.save(identityPoint),
    ]);
    const historyPointAttributes: HistoryPointAttribute[] = [
      this.historyPointAttributeRepo.create({
        attributeCode: HistoryPointAttributeCode.POINT,
        value: String(point),
        historyPointId: history.id,
      }),
      this.historyPointAttributeRepo.create({
        attributeCode: HistoryPointAttributeCode.REF_TRAN_ID,
        value: transactionRefId,
        historyPointId: history.id,
      }),
    ];

    await Promise.all([
      this.historyPointAttributeRepo.save(historyPointAttributes),
    ]);

    const outboxMsg = this.outboxMessageRepo.createPushNoti(pushNotiKafkaDto);
    await this.outboxMessageRepo.save(outboxMsg);

    const request: CrmPointGiftingRequest = {
      userId: user.id,
      Transaction_Type__c: crmTxType.name,
      Type__c: 'Adding',
      Level_Points__c: 0,
      Redeem_Points__c: point,
      Transaction_External_ID__c: transactionExternalId,
      Transaction_Date__c: dayjs().tz(TIME_ZONE).format(TIME_FORMAT_CRM),
      Rule_Name__c: crmTxType.description,
      Campaign__c: crmTxType.campaignName,
      Earned_Tier_Point__c: 0,
      Tier__c: user.tierCode,
      Tier_Points__c: user.tierPoint,
      Transaction_Trigger_External_ID__c: transactionRefId,
    };

    // const newOutboxMessage = this.outboxMessageRepo.create({
    //   provider: SyncProvider.CRM,
    //   callType: CallType.SYNC,
    //   syncType: SyncType.IMMEDIATE,
    //   request: JSON.stringify(request),
    //   status: OutboxMessageStatus.PROCESSING,
    //   createdDate: getNowAtTimeHcm(),
    //   retryNumber: 0,
    // });
    // await this.outboxMessageRepo.save(newOutboxMessage);
    requestOutboxMessage.push(request);
  }

  public async handleEventQ32024UpRank(data: EventQuy32024UpRankData) {
    try {
      const rs = await this._eventQ32024UpRankService.handle(data);
      return rs;
    } catch (error) {
      this.logger.error('Error when handle eventQ32024UpRank', error.message);
      return {
        //notificationCodes: [],
        postCommitActions: [],
        popupCode: '',
        gift: null,
        triggered: false,
      };
    }
  }

  private async validateDataQrBeforeProcess(
    dataQr: VitaQr,
    callFromWebApp: boolean,
  ): Promise<any> {
    const isValidateDataQrManufactureDate = validateDataQrManufactureDate(
      dataQr,
      callFromWebApp,
    );
    // Type of createdAt is timestamp without timezone
    // We need convert to string to make sure dayjs parsing to tz work well
    const isDataQrManufactureDateBefore20241001 =
      -1 ==
      compareDateWithDateInTimezoneNewVersion(
        dataQr.createdAt.toUTCString(),
        TIMESTAMP_BEGIN_20241001_STRING,
      );
    if (isDataQrManufactureDateBefore20241001) {
      return null;
    }

    if (callFromWebApp) {
      if (!isValidateDataQrManufactureDate) {
        return new AppResponseDto({}, null, {
          code: [PopupV2Code.WA_SBPS_2024_WEBAPP],
          notification_title: 'SYSTEM_MESSAGE',
          title: 'SYSTEM_MESSAGE',
        });
      }
    }

    // const eventWaSbps2024Id = this.configSer.get('event.eventQuy42024WaSbps');
    // let isEventWaSbps2024NotActive = true;
    // if (eventWaSbps2024Id) {
    //   const eventWaSbps2024 = await this.newestEventRepo.findActiveEvent(
    //     eventWaSbps2024Id,
    //   );
    //   if (eventWaSbps2024) {
    //     /*const eventDetailOfEventWaSbps2024 =
    //       await this.eventDetailRepo.findOneBy({
    //         eventId: eventWaSbps2024Id,
    //         quantity: MoreThan(0),
    //       });
    //     if (eventDetailOfEventWaSbps2024) {
    //       isEventWaSbps2024NotActive = false;
    //     }*/
    //     isEventWaSbps2024NotActive = false;
    //   }
    // }
    // if (isEventWaSbps2024NotActive) {
    const [eventSbpsSkuGetBySku, activeEventsContainSkuGet] = await Promise.all(
      [
        this.eventSbpsSkuRepo.findOneBy({
          sku: dataQr.sku,
        }),
        this.newestEventRepo.getAnyActiveEventContainSku(dataQr.sku),
      ],
    );
    if (!eventSbpsSkuGetBySku) {
      if (callFromWebApp) {
        return new AppResponseDto({}, null, {
          code: [PopupV2Code.WA_SBPS_2024_HETGIAI],
          notification_title: 'SYSTEM_MESSAGE',
          title: 'SYSTEM_MESSAGE',
        });
      } else {
        if (!isDataQrManufactureDateBefore20241001) {
          return new AppResponseDto({}, null, {
            code: [PopupV2Code.WA_SBPS_2024_HETGIAI],
            notification_title: 'SYSTEM_MESSAGE',
            title: 'SYSTEM_MESSAGE',
          });
        }
      }
    } else {
      if (!activeEventsContainSkuGet || !activeEventsContainSkuGet.length) {
        return new AppResponseDto({}, null, {
          code: [PopupV2Code.WA_SBPS_2024_HETGIAI],
          notification_title: 'SYSTEM_MESSAGE',
          title: 'SYSTEM_MESSAGE',
        });
      }

      let totalGiftQuantity = 0;
      for (const activeEventContainSkuGet of activeEventsContainSkuGet) {
        totalGiftQuantity += activeEventContainSkuGet.eventDetails.reduce(
          (sum, item) => {
            return sum + item.quantity;
          },
          0,
        );
      }
      if (!totalGiftQuantity) {
        return new AppResponseDto({}, null, {
          code: [PopupV2Code.WA_SBPS_2024_HETGIAI],
          notification_title: 'SYSTEM_MESSAGE',
          title: 'SYSTEM_MESSAGE',
        });
      }

      if (!callFromWebApp) {
        const qrSbpsBlackList = await this.qrSbpsBlackListRepo.findOneBy({
          qrCode: dataQr.code,
        });
        if (qrSbpsBlackList) {
          return new AppResponseDto({}, null, {
            code: [PopupV2Code.WA_SBPS_2024_CHUCMAYMAN],
            notification_title: 'SYSTEM_MESSAGE',
            title: 'SYSTEM_MESSAGE',
          });
        }
      }
    }

    if (!isDataQrManufactureDateBefore20241001) {
      // const validateSkuBelongToAnyActiveEvents =
      //   await this.newestEventRepo.validateSkuBelongToAnyActiveEvents(
      //     dataQr.sku,
      //   );
      if (!activeEventsContainSkuGet || !activeEventsContainSkuGet.length) {
        return new AppResponseDto({}, null, {
          code: [PopupV2Code.WA_SBPS_2024_NOEVENT],
          notification_title: 'SYSTEM_MESSAGE',
          title: 'SYSTEM_MESSAGE',
        });
      }
    }

    return null;
  }

  private async validateAllowRunFlowHandleScanQrFail(
    qrCode: string,
    excError: any,
  ): Promise<boolean> {
    if (!qrCode || !excError) {
      return true;
    }

    const qrData = await this.vitaQrRepo
      .createQueryBuilder('vitaQr')
      .where('vitaQr.code = :qrCode', { qrCode })
      .getOne();
    if (!qrData) {
      return true;
    }

    return validateAllowRunFlowHandleScanQrFail(qrData, excError);
  }

  async createOrUpdateUserNumberScanSbps(
    userId: number,
    userNumberScan: UserNumberScanSbps,
  ) {
    // const userNumberScan = await this.userNumberScanSbpsRepo
    //   .createQueryBuilder('uns')
    //   .where('uns.userId = :userId', { userId })
    //   .setLock('pessimistic_write')
    //   .maxExecutionTime(60000) // lock timeout 60s
    //   .getOne();

    if (userNumberScan) {
      const startOfCurrentDay = getStartOfNowInTimezone();
      const startOfCurrentMonth = getStartOfCurrentMonthInTimezone();
      if (
        compareDateWithDateInTimezone(
          userNumberScan.scannedAt,
          startOfCurrentDay,
        ) < 0
      ) {
        userNumberScan.numberScanInDaySbps = 1;
      } else {
        userNumberScan.numberScanInDaySbps += 1;
      }

      if (
        compareDateWithDateInTimezone(
          userNumberScan.scannedAt,
          startOfCurrentMonth,
        ) < 0
      ) {
        userNumberScan.numberScanInMonthSbps = 1;
      } else {
        userNumberScan.numberScanInMonthSbps += 1;
      }

      userNumberScan.numberScanSbps += 1;
      userNumberScan.scannedAt = new Date();

      await this.userNumberScanSbpsRepo.save(userNumberScan);
    } else {
      await this.userNumberScanSbpsRepo.save({
        userId,
        numberScanSbps: 1,
        numberScanInDaySbps: 1,
        numberScanInMonthSbps: 1,
      });
    }
  }

  private async getMonthExpiryBySku(sku: string): Promise<number> {
    const qrSkuSetup = await this.qrSkuSetupRepo.findOneBy({
      sku: sku,
    });

    if (!qrSkuSetup) {
      throw new AppBaseExc(StatusCode.QR_SKU_SETUP_EXPIRED_MONTH_INVALID);
    }

    return qrSkuSetup.expireMonth;
  }

  private generateResponse(errorCodes: string[]): AppResponseDto {
    return new AppResponseDto({}, null, {
      code: errorCodes,
      notification_title: 'SYSTEM_MESSAGE',
      title: 'SYSTEM_MESSAGE',
    });
  }

  private extractQrCode(qrCode: string): string {
    if (qrCode.startsWith('https://storage.googleapis.com')) {
      return this.getCodeFromUrl(qrCode) as unknown as string;
    }

    return qrCode;
  }
}
