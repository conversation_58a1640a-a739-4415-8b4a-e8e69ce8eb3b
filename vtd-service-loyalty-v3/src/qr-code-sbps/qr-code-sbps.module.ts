import { MailerModule } from '@nestjs-modules/mailer';
import { HttpModule } from '@nestjs/axios';
import { BullModule } from '@nestjs/bull';
import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { TypeOrmModule } from '@nestjs/typeorm';
import { join } from 'path';
import { FileRepository } from 'src/file/repositories/file.repository';
import { VitaQrFileRepository } from 'src/qr-code-sbps/repositories/qr-vita-file.repository';
import { AuthSpoonService } from '../auth/auth-spoon.service';
import { AuthService } from '../auth/auth.service';
import { ProvinceRepository } from '../auth/repositories/province.repository';
import { UserRepository } from '../auth/repositories/user.repository';
import { SystemConfigRepository } from '../config/repositories/system-config.repository';
import { CrmService } from '../external/services/crm.service';
import { VitaJavaService } from '../external/services/vita-java.service';
import { NotificationUserRepository } from '../notification/repositories/notification-user.repository';
import { CrmTransactionTypeRepository } from '../point/repositories/crm-transaction-type.repository';
import { OutboxMessageRepository } from '../point/repositories/outbox-message.repository';
import {
  ACCOUNT_PACKAGE_NAME,
  ACCOUNT_SERVICE_NAME,
} from '../proto/account.pb';
import { USER_PACKAGE_NAME, USER_SERVICE_NAME } from '../proto/user.pb';
import { TierRepository } from '../tier/repositories/tier.repository';
import { AdminQrCodeSbpsController } from './controllers/admin-qr-code-sbps.controller';
import { UserQrCodeSbpsController } from './controllers/user-qr-code-sbps.controller';
import { BlockedHistoryRepository } from './repositories/blocked-history.repository';
import { BlockedScanRepository } from './repositories/blocked-scan.repository';
import { EventProductRepository } from './repositories/event-product.repository';
import { EventUsersRepository } from './repositories/event-users.repository';
import { EventRepository } from './repositories/event.repository';
import { EventRepository as NewestEventRepository } from '../event/repositories/event.repository';
import { EventDetailRepository } from '../event/repositories/event-detail.repository';
import { FileRequestCodeRepository } from './repositories/file-request-code.repository';
import { HistoryPointAttributeRepository } from './repositories/history-point-attribute.repository';
import { HistoryPointRepository } from './repositories/history-point.repository';
import { ProductLineRepository } from './repositories/product-line.repository';
import { ProductMstRepository } from './repositories/product-mst.repository';
import { ProductPreviewRepository } from './repositories/product-preview.repository';
import { VitaQrRepository } from './repositories/qr-code-sbps.repository';
import { ScanHistoryRepository } from './repositories/scan-history.repository';
import { UserNumberScanSbpsRepository } from './repositories/user-number-scan-sbps.repository';
import { UserSessionRepository } from './repositories/user-session.repository';
import { AdminQrCodeSbpsService } from './services/admin-qr-code-sbps.service';
import { MessageConsumer } from './services/consumer.service';
import { TasksService } from './services/cron-reset-number-scan-sbps.service';
import { EmailService } from './services/email.service';
import { NotificationService } from './services/notification.service';
import { UserQrCodeSbpsService } from './services/user-qr-code-sbps.service';
import { BlockedScanUserController } from './controllers/blocked-scan.user.controller';
import { BlockedScanUserService } from './services/blocked-scan.user.service';
import { UserNumberScanRepository } from '../point/repositories/user-number-scan.repository';
import { QrSbpsBlackListRepository } from './repositories/qr-sbps-black-list.repository';
import { EventSbpsSkuRepository } from '../event-sbps-sku/repositories/event-sbps-sku.repository';
import { PointModule } from '../point/point.module';
import { ProvinceModule } from '../provinces/province.module';
import { EventCommonModule } from '../event-common/event-common.module';
import { UserProvinceRepository } from '../provinces/repositories/user-province.repository';
import { AdminActionHistoryModule } from '../admin-action-history/admin-action-history.module';
import { AdminAuthorizationModule } from '../admin_authorization/admin_authorization.module';
import { AdminQrSkuSetupService } from './services/admin-qr-sku-setup.service';
import { QrSkuSetupRepository } from './repositories/qr-sku-setup.repository';
import { AdminQrSkuSetupSbpsController } from './controllers/admin-qr-sku-setup.controller';
import { ProductModule } from '../product/product.module';
import { EventAddCanRepository } from '../event-add-can/repositories/event-add-can.repository';
import { MapHistoryPointToBrandPointRepository } from '../map-history-point-to-brand-point/repositories/map-history-point-to-brand-point.repository';
import { MapUserToBrandPointRepository } from '../map-user-to-brand-point/repositories/map-user-to-brand-point.repository';

@Module({
  imports: [
    BullModule.registerQueue({
      name: 'message-queue',
      settings: {
        lockDuration: 1000 * 60 * 3,
        lockRenewTime: 1000 * 60 * 1.5, // half of lockDuration
        maxStalledCount: 0,
      },
    }),
    MailerModule.forRoot({
      transport: {
        host: 'smtp.gmail.com',
        port: 465,
        secure: true,
        auth: {
          user: process.env.GMAIL,
          pass: process.env.GMAIL_PASS,
        },
      },
      defaults: {
        from: process.env.GMAIL_PASS,
      },
    }),
    TypeOrmModule.forFeature([
      VitaQrRepository,
      FileRepository,
      VitaQrFileRepository,
      UserRepository,
      ScanHistoryRepository,
      ProductMstRepository,
      BlockedHistoryRepository,
      ProductPreviewRepository,
      BlockedScanRepository,
      ProductLineRepository,
      UserNumberScanSbpsRepository,
      EventProductRepository,
      EventRepository,
      NewestEventRepository,
      EventDetailRepository,
      HistoryPointRepository,
      HistoryPointAttributeRepository,
      EventUsersRepository,
      UserSessionRepository,
      NotificationUserRepository,
      ProvinceRepository,
      FileRequestCodeRepository,
      TierRepository,
      SystemConfigRepository,
      QrSbpsBlackListRepository,
      EventSbpsSkuRepository,
      EventAddCanRepository,
      MapHistoryPointToBrandPointRepository,
      MapUserToBrandPointRepository,
    ]),
    HttpModule,
    ClientsModule.register([
      {
        name: USER_SERVICE_NAME,
        transport: Transport.GRPC,
        options: {
          url: 'localhost:50051',
          package: USER_PACKAGE_NAME,
          protoPath: join(
            __dirname + '/../../../node_modules/vtd-common-v3/proto/user.proto',
          ),
        },
      },
      {
        name: ACCOUNT_SERVICE_NAME,
        transport: Transport.GRPC,
        options: {
          url: 'localhost:50052',
          package: ACCOUNT_PACKAGE_NAME,
          protoPath: join(
            __dirname +
              '/../../../node_modules/vtd-common-v3/proto/account.proto',
          ),
        },
      },
    ]),
    PointModule,
    ProvinceModule,
    EventCommonModule,
    AdminAuthorizationModule,
    AdminActionHistoryModule,
    ProductModule,
  ],
  controllers: [
    AdminQrCodeSbpsController,
    UserQrCodeSbpsController,
    // NotificationController,
    BlockedScanUserController,
    AdminQrSkuSetupSbpsController,
  ],
  providers: [
    MessageConsumer,
    TasksService,
    CrmService,
    AdminQrCodeSbpsService,
    UserQrCodeSbpsService,
    NotificationService,
    VitaQrRepository,
    FileRepository,
    VitaQrFileRepository,
    UserRepository,
    ScanHistoryRepository,
    ProductMstRepository,
    BlockedHistoryRepository,
    ProductPreviewRepository,
    BlockedScanRepository,
    ProductLineRepository,
    UserNumberScanSbpsRepository,
    EventProductRepository,
    EventRepository,
    NewestEventRepository,
    EventDetailRepository,
    HistoryPointRepository,
    HistoryPointAttributeRepository,
    EventUsersRepository,
    UserSessionRepository,
    NotificationUserRepository,
    AuthService,
    VitaJavaService,
    ProvinceRepository,
    EmailService,
    AuthSpoonService,
    FileRequestCodeRepository,
    OutboxMessageRepository,
    SystemConfigRepository,
    TierRepository,
    CrmTransactionTypeRepository,
    BlockedScanUserService,
    UserNumberScanRepository,
    QrSbpsBlackListRepository,
    EventSbpsSkuRepository,
    UserProvinceRepository,
    AdminQrSkuSetupService,
    QrSkuSetupRepository,
    EventAddCanRepository,
    MapHistoryPointToBrandPointRepository,
    MapUserToBrandPointRepository,
  ],
})
export class QrCodeSbpsModule {}
