import { Column, <PERSON>ti<PERSON>, PrimaryGeneratedColumn } from 'typeorm';

@Entity('user_number_scan_sbps')
export class UserNumberScanSbps {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'number_scan_in_day_sbps' })
  numberScanInDaySbps: number;

  @Column({ name: 'number_scan_in_month_sbps' })
  numberScanInMonthSbps: number;

  @Column({ name: 'number_scan_sbps' })
  numberScanSbps: number;

  @Column({ name: 'number_point_used_in_day_sbps' })
  numberPointUsedInDaySbps: number;
}
