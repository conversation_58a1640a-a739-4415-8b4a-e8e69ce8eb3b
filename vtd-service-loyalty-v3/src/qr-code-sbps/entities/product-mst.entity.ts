import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

// tại sao lại có 2 table giống nhau làm logic event 2468 chưa được tối ưu do confuse
// có 2 loại table product
@Entity('product_mst')
export class ProductMst {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  name: string;

  @Column({ name: 'preview_name', nullable: true })
  previewName: string;

  @Column()
  image: string;

  @Column({ name: 'brand_code' })
  brandCode: string;

  @Column()
  description: string;

  @Column({ name: 'brand_name' })
  brandName: string;

  @Column()
  terms: string;

  @Column()
  link: string;

  @Column({ name: 'is_active' })
  isActive: boolean;

  @Column()
  code: string;

  @Column()
  money: number;

  @Column()
  status: string;

  @Column()
  point: number;

  @Column({ name: 'level_point' })
  levelPoint: number;

  @Column()
  brand: string;

  @Column({ name: 'web_app_enable', type: 'boolean', default: false })
  webAppEnable: boolean;

  @Column({ name: 'is_allow_ra', default: true, type: 'boolean' })
  isAllowRa: boolean;

  @Column({ name: 'url_domain', type: 'varchar', length: 255, nullable: true })
  urlDomain: string;
}
