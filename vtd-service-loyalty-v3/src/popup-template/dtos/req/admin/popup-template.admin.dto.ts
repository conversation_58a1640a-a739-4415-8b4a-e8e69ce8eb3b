import { PartialType } from '@nestjs/swagger';
import {
  IsValidBoolean,
  IsValidNumber,
  IsValidText,
} from 'src/common/decorators/custom-validator.decorator';
import { PaginationReqDto } from '../../../../common/dtos/pagination.dto';

export class CreatePopupTemplateAdminReqDto {
  @IsValidText({ maxLength: 20 })
  code: string;

  @IsValidText({ maxLength: 255 })
  title: string;

  @IsValidText()
  content: string;

  @IsValidText({ maxLength: 255 })
  text1: string;

  @IsValidText()
  link1: string;

  @IsValidText({ maxLength: 20 })
  type1: string;

  @IsValidText({ maxLength: 255 })
  text2: string;

  @IsValidText()
  link2: string;

  @IsValidText({ maxLength: 20 })
  type2: string;

  @IsValidText({ required: false })
  defaultImage: string;

  @IsValidBoolean()
  isShow: boolean;
}

export class UpdatePopupTemplateAdminReqDto extends PartialType(
  CreatePopupTemplateAdminReqDto,
) {}

export class GetListPopupTemplateAdminReqDto extends PaginationReqDto {}
