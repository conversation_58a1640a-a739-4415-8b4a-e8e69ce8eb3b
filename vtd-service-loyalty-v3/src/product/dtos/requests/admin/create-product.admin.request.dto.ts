import {
  IsValidBoolean,
  IsValidNumber,
  IsValidText,
} from '../../../../common/decorators/custom-validator.decorator';

export class CreateProductAdminRequestDto {
  @IsValidText()
  name: string;

  @IsValidText()
  previewName: string;

  @IsValidText()
  code: string;

  // @IsValidText()
  // brand: string;

  @IsValidNumber({
    min: 0,
  })
  money: number;

  @IsValidNumber({
    min: 0,
  })
  point: number;

  @IsValidNumber({
    min: 0,
  })
  levelPoint: number;

  @IsValidNumber({
    min: 1,
  })
  brandId: number;

  @IsValidNumber({
    min: 0,
  })
  brandPoint: number;

  @IsValidNumber({
    min: 0,
    required: false,
  })
  total?: number;

  @IsValidText()
  unitRetail: string;

  @IsValidText({ required: false })
  unitWrapper?: string;

  @IsValidText()
  prefixBrandCode: string;

  @IsValidText()
  prefixWeightCode: string;

  @IsValidBoolean()
  ignorePoint: boolean;

  @IsValidBoolean()
  isActive: boolean;

  @IsValidBoolean()
  webAppEnable: boolean;

  @IsValidText()
  type1: string;

  @IsValidText({ required: false })
  typeName1?: string;

  @IsValidText({ required: false })
  type2?: string;

  @IsValidText({ required: false })
  typeName2?: string;

  @IsValidText({ required: false })
  image?: string;

  @IsValidText({
    required: false,
  })
  link?: string;

  @IsValidText({
    required: false,
  })
  status?: string;

  @IsValidText({
    required: false,
  })
  sfId?: string;

  @IsValidText({
    required: false,
  })
  description?: string;
}
