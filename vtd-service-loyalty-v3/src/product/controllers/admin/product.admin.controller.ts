import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import {
  EnumCodeAdminMenuModule,
  EnumCodeActionOnAdminMenuModule,
} from 'vtd-common-v3';
import { Prefix } from '../../../common/constants/index.constant';
import {
  AuthAdmin,
  UseAdminWithAuthorizeBySetupAdminMenuModuleAcl,
} from '../../../common/decorators/user.decorator';
import { AdminMenuModuleDecorator } from '../../../admin_authorization/common/decorators/admin-menu-module.decorator';
import { ActionsOnAdminMenuModuleDecorator } from '../../../admin_authorization/common/decorators/action-on-admin-menu-module.decorator';
import { ProductAdminService } from '../../services/admin/product.admin.service';
import { GetProductsAdminRequestDto } from '../../dtos/requests/admin/get-products.admin.request.dto';
import { CreateProductAdminRequestDto } from '../../dtos/requests/admin/create-product.admin.request.dto';
import { UpdateProductAdminRequestDto } from '../../dtos/requests/admin/update-product.admin.request.dto';
import { CreateProductPreviewAdminRequestDto } from '../../dtos/requests/admin/create-product-preview.admin.request.dto';
import { UpdateProductPreviewAdminRequestDto } from '../../dtos/requests/admin/update-product-preview.admin.request.dto';
import { AccountData } from '../../../proto/account.pb';

@Controller({ version: '1', path: `${Prefix.ADMIN}/products` })
@UseAdminWithAuthorizeBySetupAdminMenuModuleAcl()
@AdminMenuModuleDecorator(EnumCodeAdminMenuModule.QUAN_LY_SAN_PHAM)
@ApiTags('Admin Manage Products Controller')
export class ProductAdminController {
  constructor(private readonly productAdminService: ProductAdminService) {}

  @Get()
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.READ,
  )
  async getProducts(@Query() dto: GetProductsAdminRequestDto) {
    return await this.productAdminService.getProducts(dto);
  }

  @Get('brands')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.READ,
  )
  // TODO: using request with paginate when number of brands grow up
  async getBrands() {
    return await this.productAdminService.getBrands();
  }

  @Get(':id')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.READ,
  )
  async getProduct(@Param('id', ParseIntPipe) id: number) {
    return await this.productAdminService.getProduct(id);
  }

  @Post()
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.CREATE,
  )
  async createProduct(
    @Body() dto: CreateProductAdminRequestDto,
    @AuthAdmin() admin: AccountData,
  ) {
    return await this.productAdminService.createProduct(dto, admin);
  }

  @Post('sync-brand')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.CREATE,
  )
  async syncBrandInProducts() {
    return await this.productAdminService.syncBrandInProducts();
  }

  @Put(':id')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.UPDATE,
  )
  async updateProduct(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateProductAdminRequestDto,
    @AuthAdmin() admin: AccountData,
  ) {
    return await this.productAdminService.updateProduct(id, dto, admin);
  }

  @Get(':id/preview')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.READ,
  )
  async getProductPreview(@Param('id', ParseIntPipe) id: number) {
    return await this.productAdminService.getProductPreview(id);
  }

  @Post(':id/preview')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.CREATE,
  )
  async createProductPreview(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: CreateProductPreviewAdminRequestDto,
    @AuthAdmin() admin: AccountData,
  ) {
    return await this.productAdminService.createProductPreview(id, dto, admin);
  }

  @Put(':id/preview/:previewId')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.UPDATE,
  )
  async updateProductPreview(
    @Param('id', ParseIntPipe) id: number,
    @Param('previewId', ParseIntPipe) previewId: number,
    @Body() dto: UpdateProductPreviewAdminRequestDto,
    @AuthAdmin() admin: AccountData,
  ) {
    return await this.productAdminService.updateProductPreview(
      id,
      previewId,
      dto,
      admin,
    );
  }
}
