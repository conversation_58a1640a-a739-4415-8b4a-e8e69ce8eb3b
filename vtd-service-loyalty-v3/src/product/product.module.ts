import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { join } from 'path';
import { AuthModule } from '../auth/auth.module';
import { AuthService } from '../auth/auth.service';
import { BravoQrService } from '../external/services/bravo-qr.service';
import { ZenposQrService } from '../external/services/zenpos-qr.service';
import { SapService } from '../external/services/sap.service';
import { BravoQrRepository } from '../point/repositories/bravo-qr.repository';
import { ZenposQrRepository } from '../point/repositories/zenpos-qr.repository';
import {
  ACCOUNT_PACKAGE_NAME,
  ACCOUNT_SERVICE_NAME,
} from '../proto/account.pb';
import { USER_PACKAGE_NAME, USER_SERVICE_NAME } from '../proto/user.pb';
import { VitaQrRepository } from '../qr-code-sbps/repositories/qr-code-sbps.repository';
import { SpoonRepository } from '../spoon/repositories/spoon.repository';
import { ProductController } from './product.controller';
import { ProductService } from './product.service';
import { ProductRepository } from './repositories/product.repository';
import { OutboxMessageRepository } from '../point/repositories/outbox-message.repository';
import { ProductMstRepository } from '../qr-code-sbps/repositories/product-mst.repository';
import { ProductCategoryRepository } from './repositories/product-category.repository';
import { ProductPreviewRepository } from '../qr-code-sbps/repositories/product-preview.repository';
import { ProductCategoryService } from './product-category.service';
import { ElasticSearchModule } from '../elasticSearch/elasticSearch.module';
import { AdminAuthorizationModule } from '../admin_authorization/admin_authorization.module';
import { AdminActionHistoryModule } from '../admin-action-history/admin-action-history.module';
import { ProductAdminController } from './controllers/admin/product.admin.controller';
import { ProductAdminService } from './services/admin/product.admin.service';
import { BrandRepository } from '../brand/repositories/brand.repository';

@Module({
  imports: [
    HttpModule,
    ClientsModule.register([
      {
        name: USER_SERVICE_NAME,
        transport: Transport.GRPC,
        options: {
          url: 'vtd-service-user-v3:50051',
          package: USER_PACKAGE_NAME,
          protoPath: join(
            __dirname + '/../../../node_modules/vtd-common-v3/proto/user.proto',
          ),
        },
      },
      {
        name: ACCOUNT_SERVICE_NAME,
        transport: Transport.GRPC,
        options: {
          url: 'vtd-service-user-v3:50052',
          package: ACCOUNT_PACKAGE_NAME,
          protoPath: join(
            __dirname +
              '/../../../node_modules/vtd-common-v3/proto/account.proto',
          ),
        },
      },
    ]),
    AuthModule,
    ElasticSearchModule,
    AdminAuthorizationModule,
    AdminActionHistoryModule,
  ],
  controllers: [ProductController, ProductAdminController],
  providers: [
    SpoonRepository,
    ZenposQrRepository,
    BravoQrRepository,
    VitaQrRepository,
    ZenposQrService,
    BravoQrService,
    SapService,
    ProductRepository,
    OutboxMessageRepository,
    ProductService,
    AuthService,
    ProductMstRepository,
    ProductCategoryRepository,
    ProductPreviewRepository,
    ProductCategoryService,
    ProductAdminService,
    BrandRepository,
  ],
  exports: [
    ProductAdminService,
    ProductRepository,
    ProductCategoryRepository,
    OutboxMessageRepository,
  ],
})
export class ProductModule {}
