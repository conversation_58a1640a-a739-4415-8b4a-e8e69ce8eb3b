import { Injectable } from '@nestjs/common';
import { paginate } from 'nestjs-typeorm-paginate';
import { AppResponseDto } from '../../../common/dtos/app-response.dto';
import {
  ConflictExc,
  NotFoundExc,
  BadRequestExc,
} from '../../../common/exceptions/custom-http.exception';
import { ProductRepository } from '../../repositories/product.repository';
import { ProductPreviewRepository } from '../../../qr-code-sbps/repositories/product-preview.repository';
import { EnumNameAdminMenuModule } from '../../../admin_authorization/common/enums/admin-menu-module.enum';
import { AdminActionHistoryRepository } from '../../../admin-action-history/repositories/admin-action-history.repository';
import { GetProductsAdminRequestDto } from '../../dtos/requests/admin/get-products.admin.request.dto';
import { CreateProductAdminRequestDto } from '../../dtos/requests/admin/create-product.admin.request.dto';
import { UpdateProductAdminRequestDto } from '../../dtos/requests/admin/update-product.admin.request.dto';
import { CreateProductPreviewAdminRequestDto } from '../../dtos/requests/admin/create-product-preview.admin.request.dto';
import { UpdateProductPreviewAdminRequestDto } from '../../dtos/requests/admin/update-product-preview.admin.request.dto';
import { AccountData } from '../../../proto/account.pb';
import { Transactional } from 'typeorm-transactional';
import { Not } from 'typeorm';
import { BrandRepository } from '../../../brand/repositories/brand.repository';

@Injectable()
export class ProductAdminService {
  constructor(
    private readonly productRepo: ProductRepository,
    private readonly productPreviewRepo: ProductPreviewRepository,
    private readonly adminActionHistoryRepo: AdminActionHistoryRepository,
    private readonly brandRepo: BrandRepository,
  ) {}

  async getProducts(req: GetProductsAdminRequestDto) {
    const { page, limit } = req;

    const queryBuilder = this.productRepo
      .createQueryBuilder('product')
      .leftJoinAndSelect('product.brandEntity', 'productBrandEntity');
    if (req.search) {
      queryBuilder.andWhere(
        '(product.name ILIKE :search OR product.code ILIKE :search)',
        {
          search: `%${req.search}%`,
        },
      );
    }
    if (req.brand) {
      queryBuilder.andWhere('product.brand = :brand', {
        brand: req.brand,
      });
    }
    if (req.type1) {
      queryBuilder.andWhere('product.type1 = :type1', {
        type1: req.type1,
      });
    }
    queryBuilder.orderBy('product.id', 'DESC');

    const { items, meta } = await paginate(queryBuilder, {
      limit,
      page,
    });

    return AppResponseDto.fromNestJsPagination(items, meta);
  }

  async getProduct(id: number) {
    const product = await this.getProductById(id);

    return new AppResponseDto(product);
  }

  async getProductPreview(productId: number) {
    const product = await this.getProductById(productId);
    const productPreviewGet = await this.productPreviewRepo.findOneBy({
      code: product.code,
    });

    return new AppResponseDto({
      ...productPreviewGet,
      name: product.previewName,
    });
  }

  @Transactional()
  async createProduct(data: CreateProductAdminRequestDto, admin: AccountData) {
    const [productByCodeGet, brandGet] = await Promise.all([
      this.productRepo.findOneBy({
        code: data.code,
      }),
      this.brandRepo.findOneBy({
        id: data.brandId,
      }),
    ]);
    if (productByCodeGet) {
      throw new BadRequestExc('Code duplicate');
    }
    if (!brandGet) {
      throw new NotFoundExc('Brand not found');
    }

    const [createdProduct] = await Promise.all([
      this.productRepo.save({
        ...data,
        brand: brandGet.name,
      }),
      this.adminActionHistoryRepo.loggingCreateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SAN_PHAM,
        data.name,
      ),
    ]);

    return new AppResponseDto(createdProduct);
  }

  @Transactional()
  async createProductPreview(
    productId: number,
    data: CreateProductPreviewAdminRequestDto,
    admin: AccountData,
  ) {
    const product = await this.getProductById(productId);
    const productPreviewByCodeGet = await this.productPreviewRepo.findOneBy({
      code: product.code,
    });
    if (productPreviewByCodeGet) {
      throw new BadRequestExc('Code duplicate');
    }

    const [createdProductPreview] = await Promise.all([
      this.productPreviewRepo.save({
        ...data,
        code: product.code,
        name: product.previewName,
      }),
      this.adminActionHistoryRepo.loggingCreateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SAN_PHAM,
        product.name,
      ),
    ]);

    return new AppResponseDto(createdProductPreview);
  }

  @Transactional()
  async updateProduct(
    id: number,
    data: UpdateProductAdminRequestDto,
    admin: AccountData,
  ) {
    const [product, otherProductByCodeGet, brandGet] = await Promise.all([
      this.getProductById(id),
      this.productRepo.findOneBy({
        code: data.code,
        id: Not(id),
      }),
      this.brandRepo.findOneBy({
        id: data.brandId,
      }),
    ]);
    if (otherProductByCodeGet) {
      throw new BadRequestExc('Code duplicate');
    }
    if (!brandGet) {
      throw new NotFoundExc('Brand not found');
    }

    const [updatedProduct] = await Promise.all([
      this.productRepo.save({
        ...data,
        id: product.id,
        brand: brandGet.name,
      }),
      this.adminActionHistoryRepo.loggingUpdateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SAN_PHAM,
        data.name,
      ),
    ]);

    return new AppResponseDto(updatedProduct);
  }

  @Transactional()
  async updateProductPreview(
    productId: number,
    productPreviewId: number,
    data: UpdateProductPreviewAdminRequestDto,
    admin: AccountData,
  ) {
    const product = await this.getProductById(productId);
    const otherProductPreviewByCodeGet =
      await this.productPreviewRepo.findOneBy({
        code: product.code,
        id: Not(productPreviewId),
      });
    if (otherProductPreviewByCodeGet) {
      throw new BadRequestExc('Code duplicate');
    }

    const [updatedProductPreview] = await Promise.all([
      this.productPreviewRepo.save({
        ...data,
        id: productPreviewId,
        code: product.code,
        name: product.previewName,
      }),
      this.adminActionHistoryRepo.loggingUpdateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SAN_PHAM,
        product.name,
      ),
    ]);

    return new AppResponseDto(updatedProductPreview);
  }

  async getProductById(id: number) {
    if (!id) {
      throw new BadRequestExc('Id not found');
    }
    const product = await this.productRepo.findOne({
      where: {
        id,
      },
      relations: ['brandEntity'],
    });
    if (!product) {
      throw new NotFoundExc('Prduct not found');
    }

    return product;
  }

  async getBrands() {
    return new AppResponseDto(
      await this.brandRepo.find({
        order: {
          name: 'ASC',
        },
      }),
    );
  }

  async syncBrandInProducts() {
    await this.productRepo.query(`
      UPDATE product_mst p
      SET brand_id = b.id
      FROM brands b
      WHERE trim(p.brand) = trim(b.name)
        AND p.brand_id IS DISTINCT FROM b.id
    `);

    return 1;
  }
}
