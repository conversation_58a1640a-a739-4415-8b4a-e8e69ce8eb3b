import {
  Column,
  <PERSON><PERSON>ty,
  OneToMany,
  ManyToOne,
  PrimaryGeneratedColumn,
  JoinColumn,
} from 'typeorm';
import { EventAddCanProduct } from '../../event-add-can/entities/event-add-can-product.entity';
import { EventPointHistory } from '../../event/entities/event-point-history.entity';
import { EventProductChild } from '../../event/entities/event-product-child.entity';
import { EventProduct } from '../../event/entities/event-product.entity';
import { BrandEntity } from '../../brand/entities/brand.entity';

// tại sao lại có 2 table giống nhau làm logic event 2468 chưa được tối ưu do confuse
// có 2 loại table product
@Entity({ name: 'product_mst' })
export class Product {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 128 })
  name: string;

  @Column({ name: 'preview_name', length: 128, nullable: true })
  previewName: string;

  @Column({ type: 'text', nullable: true })
  image: string;

  @Column({ name: 'brand_code', nullable: true, length: 32 })
  brandCode: string;

  @Column({ name: 'brand_name', nullable: true, length: 128 })
  brandName: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'text', nullable: true })
  terms: string;

  @Column({ type: 'text', nullable: true })
  link: string;

  @Column({ name: 'is_active', nullable: true, default: true })
  isActive: boolean;

  @Column({ length: 255 })
  code: string;

  @Column({ type: 'int4', default: 0 })
  money: number;

  @Column({ length: 32, nullable: true })
  status: string;

  @Column({ name: 'sf_id', length: 64, nullable: true })
  sfId: string;

  @Column({ name: 'unit_retail', length: 32, nullable: true })
  unitRetail: string;

  @Column({ name: 'unit_wrapper', length: 32, nullable: true })
  unitWrapper: string;

  @Column({ nullable: true, type: 'int4' })
  total: number;

  @Column({ name: 'type_1', length: 32, nullable: true })
  type1: string;

  @Column({ name: 'type_name_1', length: 128, nullable: true })
  typeName1: string;

  @Column({ name: 'type_2', length: 32, nullable: true })
  type2: string;

  @Column({ name: 'type_name_2', type: 'varchar', nullable: true })
  typeName2: string;

  @Column({ name: 'category_id', nullable: true })
  categoryId: number;

  @Column({ name: 'priority', nullable: true })
  priority: number;

  @Column({ type: 'float8', nullable: true, default: 0 })
  point: number;

  @Column({ name: 'ignore_point', default: false })
  ignorePoint: boolean;

  @Column({ name: 'ignore_wheel_lucky', default: false })
  ignoreWheelLucky: boolean;

  @Column({ name: 'ignore_snt_wheel_lucky', default: false })
  ignoreSntWheelLucky: boolean;

  @Column({ name: 'prefix_brand_code', length: 5 })
  prefixBrandCode: string;

  @Column({ name: 'prefix_weight_code', length: 5 })
  prefixWeightCode: string;

  @Column({ length: 50, nullable: true })
  brand: string;

  @Column({ name: 'level_point', type: 'float4', nullable: true, default: 0 })
  levelPoint: number;

  @Column({ name: 'web_app_enable', type: 'boolean', default: false })
  webAppEnable: boolean;

  @Column({ type: 'float8', default: 0, name: 'brand_point' })
  brandPoint: number;

  // Join brand
  @Column({ name: 'brand_id', nullable: true })
  brandId: number;

  @Column({ name: 'is_allow_ra', default: true, type: 'boolean' })
  isAllowRa: boolean;

  @Column({ name: 'url_domain', type: 'varchar', length: 255, nullable: true })
  urlDomain: string;

  @ManyToOne(() => BrandEntity, (e) => e.products, {
    onDelete: 'RESTRICT',
  })
  @JoinColumn({ name: 'brand_id' })
  brandEntity: BrandEntity;

  // Join event_product
  @OneToMany(() => EventProduct, (ep) => ep.product)
  eventProducts: EventProduct[];
  // End join event_product

  // Join event_point_history
  @OneToMany(() => EventPointHistory, (eph) => eph.product)
  eventPointHistories: EventPointHistory[];
  // End join event_point_history

  // Join event_product_child
  @OneToMany(() => EventProductChild, (ep) => ep.product)
  eventProductChild: EventProductChild[];
  // End join event_product_child

  @OneToMany(() => EventAddCanProduct, (eacp) => eacp.product)
  eventAddCanProducts: EventAddCanProduct[];

  public checkIsProductBrand(): boolean {
    return this.brandPoint > 0;
  }
}
