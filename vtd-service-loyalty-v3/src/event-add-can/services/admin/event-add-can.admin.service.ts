import { HttpException, Injectable } from '@nestjs/common';
import { boolean } from 'boolean';
import { paginate } from 'nestjs-typeorm-paginate';
import { objectify } from 'radash';
import { UserRepository } from 'src/auth/repositories/user.repository';
import { LoggerService } from 'src/core';
import { EventNumberUserCanRepository } from 'src/event-add-can/repositories/event-number-user-can.repository';
import { In, IsNull, SelectQueryBuilder } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { EventAddCanStatus } from 'vtd-common-v3';
import { AdminActionHistoryRepository } from '../../../admin-action-history/repositories/admin-action-history.repository';
import { EnumNameAdminMenuModule } from '../../../admin_authorization/common/enums/admin-menu-module.enum';
import { StatusCode } from '../../../common/constants/status-code.constant';
import {
  addDayToDate,
  compareDateWithCurrent,
} from '../../../common/datetime.util';
import { QueryFilter } from '../../../common/decorators/query-filter.decorator';
import {
  AppPaginationDto,
  AppResponseDto,
} from '../../../common/dtos/app-response.dto';
import { DeleteMultipleByNumberIdsDto } from '../../../common/dtos/delete-multiple.dto';
import { AppBaseExc } from '../../../common/exceptions/custom-app.exception';
import {
  ConflictExc,
  NotFoundExc,
} from '../../../common/exceptions/custom-http.exception';
import {
  compareDateWithCurrentDateInTimezone,
  compareDateWithDateInTimezone,
  formatStringWithSuffix,
  getValEnumStr,
  mergeTwoArraysRemoveDuplicating,
  sortByAttribute,
  validateItemOverlapTimeRangeWithOtherItem,
  validateSetupTimeValidOnEachRange
} from '../../../common/utils';
import { EventCanMarkRepository } from '../../../event-can-mark/repositories/event-can-mark.repository';
import { EventCanMarkAdminService } from '../../../event-can-mark/services/event-can-mark.admin.service';
import { GiftStatusEnum } from '../../../external/java-loyalty';
import { VitaJavaService } from '../../../external/services/vita-java.service';
import { GiftCategory } from '../../../gift/entities/gift-category.entity';
import { GiftCategoryRepository } from '../../../gift/repositories/gift-category.repository';
import { AccountData } from '../../../proto/account.pb';
import { SystemFeatureRepository } from '../../../system-feature/repositorires/system-feature.repository';
import { CreateEventAddCanBrandPointConfigAdminReqDto } from '../../dtos/req/admin/create-event-add-can-brand-point-config.admin.req.dto';
import {
  CreateEventAddCanAdminReqDto,
  EventAddCanLimitTimeExchangeGiftReqDto,
  EventAddCanTimeExchangeGiftDetailReqDto,
  EventAddCanTimeExchangeGiftReqDto,
  GetListEventAddCanAdminReqDto,
  UpdateEventAddCanAdminReqDto,
} from '../../dtos/req/admin/event-add-can.admin.req.dto';
import { GetEventAddCanBrandPointConfigsAdminReqDto } from '../../dtos/req/admin/get-event-add-can-brand-point-configs.admin.req.dto';
import { GetEventBrandsAdminReqDto } from '../../dtos/req/admin/get-event-brands.admin.req.dto';
import { UpdateEventAddCanBrandPointConfigAdminReqDto } from '../../dtos/req/admin/update-event-add-can-brand-point-config.admin.req.dto';
import { EventAddCanProduct } from '../../entities/event-add-can-product.entity';
import { EventAddCanTimeExchangeGiftDetail } from '../../entities/event-add-can-time-exchange-gift-detail.entity';
import { EventAddCanTimeExchangeGift } from '../../entities/event-add-can-time-exchange-gift.entity';
import { EventAddCan } from '../../entities/event-add-can.entity';
import { EventAddCanLimitTimeCanExchangeGiftExpireTimeUnit } from '../../enums/event-add-can.enum';
import { EventAddCanPopupRepository } from '../../repositories/event-add-can-popup.repository';
import { EventAddCanProductRepository } from '../../repositories/event-add-can-product.repository';
import { EventAddCanTimeExchangeGiftDetailRepository } from '../../repositories/event-add-can-time-exchange-gift-detail.repository';
import { EventAddCanTimeExchangeGiftRepository } from '../../repositories/event-add-can-time-exchange-gift.repository';
import { EventAddCanWebviewRepository } from '../../repositories/event-add-can-webview.repository';
import { EventAddCanRepository } from '../../repositories/event-add-can.repository';
import { BrandPointConfigType } from '../../types/brand-point-config.type';

@Injectable()
export class EventAddCanAdminService {
  private _logger = new LoggerService(EventAddCanAdminService.name);
  constructor(
    private eventAddCanRepo: EventAddCanRepository,
    private giftCategoryRepo: GiftCategoryRepository,
    private eventAddCanProductRepo: EventAddCanProductRepository,
    private eventCanMarkRepo: EventCanMarkRepository,
    private eventAddCanWebviewRepo: EventAddCanWebviewRepository,
    private eventAddCanPopupRepo: EventAddCanPopupRepository,
    private eventCanMarkAdminService: EventCanMarkAdminService,
    private eventAddCanTimeExchangeGiftRepo: EventAddCanTimeExchangeGiftRepository,
    private eventAddCanTimeExchangeGiftDetailRepo: EventAddCanTimeExchangeGiftDetailRepository,
    private vitaJavaService: VitaJavaService,
    private eventNumberUserCanRepo: EventNumberUserCanRepository,
    private readonly userRepository: UserRepository,
    private readonly adminActionHistoryRepo: AdminActionHistoryRepository,
    private readonly systemFeatureRepo: SystemFeatureRepository,
  ) {}

  async findAll(getListEventAddCanAdminReqDto: GetListEventAddCanAdminReqDto) {
    const { limit, page, eventName, endDate, startDate } =
      getListEventAddCanAdminReqDto;
    let { skus } = getListEventAddCanAdminReqDto;

    if (typeof skus === 'string') {
      skus = [skus];
    }

    const skip = (page - 1) * limit;
    const qbEntities = this.eventAddCanRepo
      .createQueryBuilder('eventAddCan')
      .leftJoinAndSelect('eventAddCan.giftCategories', 'giftCategorie')
      .leftJoinAndSelect(
        'eventAddCan.eventAddCanProducts',
        'eventAddCanProduct',
      );
    if (eventName) {
      qbEntities.where('eventAddCan.eventName like :eventName', {
        eventName: `%${eventName}%`,
      });
    }
    if (skus) {
      qbEntities.andWhere('eventAddCanProduct.sku in (:...skus)', { skus });
    }
    if (startDate) {
      qbEntities.andWhere('eventAddCan.startDate >= :startDate', {
        startDate,
      });
    }
    if (endDate) {
      qbEntities.andWhere('eventAddCan.endDate <= :endDate', { endDate });
    }
    // sort by id
    qbEntities.orderBy('eventAddCan.id', 'DESC');

    const [totalRecords, result] = await Promise.all([
      qbEntities.getCount(),
      qbEntities.take(limit).skip(skip).getMany(),
    ]);

    return AppResponseDto.fromPagination(
      result,
      AppPaginationDto.fromTotalAndPageAndSizeV1(totalRecords, page, limit),
    );
  }

  @Transactional()
  async update(
    id: number,
    dto: UpdateEventAddCanAdminReqDto,
    admin: AccountData,
    token: string,
  ) {
    const {
      eventName,
      endDate,
      convertPoint,
      giftCategoryIds,
      skus,
      startDate,
      status,
      type,
      convertCan400g,
      convertCan800g,
      convertBarrel110ml,
      convertBarrel180ml,
      gsGiftCategoryCodes,
      activeOverCanPopup,
      enableTimeExchangeGift,
      timeExchangeGifts,
      enableLimitTimeCanExchangeGift,
      limitTimeExchangeGift,
      loyaltyProgramSfId,
      enableSetupListUsersCanJoin,
      isImportFile,
      userPhones,
    } = dto;

    const eventAddCan = await this.eventAddCanRepo.findOne({
      where: { id },
      relations: {
        giftCategories: true,
        eventAddCanProducts: true,
        timeExchangeGifts: { details: true },
      },
    });

    if (!eventAddCan) {
      throw new AppBaseExc(StatusCode.EVENT_ADD_CAN_NOT_FOUND);
    }

    // Validate setup time exchange gifts
    if (enableTimeExchangeGift) {
      this.validateTimeExchangeGiftsSetupData(timeExchangeGifts);

      // Validate gift in time exchange gift detail
      await this.validateGiftsSetupInEventAddCanTimeExchangeGift(
        token,
        timeExchangeGifts,
      );
    }

    // Validate setup limit time exchange gift
    if (enableLimitTimeCanExchangeGift) {
      this.validateLimitTimeExchangeGiftSetupData(limitTimeExchangeGift);
    }

    // Validate updating limit time exchange gift with running event
    const eventIsRunning =
      compareDateWithCurrentDateInTimezone(eventAddCan.startDate) != 1;
    if (eventIsRunning) {
      const {
        limitTimeCanExchangeGiftNumberOfCanRate,
        limitTimeCanExchangeGiftNumberOfCanValue,
        limitTimeCanExchangeGiftTotalTimes,
        limitTimeCanExchangeGiftEnableExpireTime,
        limitTimeCanExchangeGiftExpireTimeUnit,
        // limitTimeCanExchangeGiftDisplayDetailOfTimes is allowed to be updated when event is running
      } = limitTimeExchangeGift;
      if (
        eventAddCan.enableLimitTimeCanExchangeGift !=
        enableLimitTimeCanExchangeGift ||
        eventAddCan.limitTimeCanExchangeGiftTotalTimes !=
        limitTimeCanExchangeGiftTotalTimes ||
        eventAddCan.limitTimeCanExchangeGiftNumberOfCanRate !=
        limitTimeCanExchangeGiftNumberOfCanRate ||
        eventAddCan.limitTimeCanExchangeGiftNumberOfCanValue !=
        limitTimeCanExchangeGiftNumberOfCanValue ||
        eventAddCan.limitTimeCanExchangeGiftEnableExpireTime !=
        limitTimeCanExchangeGiftEnableExpireTime ||
        eventAddCan.limitTimeCanExchangeGiftExpireTimeUnit !=
        limitTimeCanExchangeGiftExpireTimeUnit ||
        (eventAddCan.enableSetupListUsersCanJoin &&
          !enableSetupListUsersCanJoin)
      ) {
        throw new AppBaseExc(
          StatusCode.SBPS_DEFAULT_ERROR,
          'Cài đặt giới hạn lượt đổi quà không hợp lệ. Event đã bắt đầu',
          true,
        );
      }
    }

    // check import list user can join event before or not
    if (enableSetupListUsersCanJoin) {
      // TO DO
      // Check in table todo queue_job
      // If exist -> throw error
      // Else -> continue
    }

    const updateEventAddCan = Object.assign(eventAddCan, {
      eventName,
      endDate,
      startDate,
      status,
      convertPoint,
      type,
      convertCan400g,
      convertCan800g,
      convertBarrel110ml,
      convertBarrel180ml,
      gsGiftCategoryCodes,
      activeOverCanPopup,
      enableTimeExchangeGift,
      enableSetupListUsersCanJoin,
      enableLimitTimeCanExchangeGift,
      loyaltyProgramSfId,
    });

    // Update limit time exchange gift setup data
    if (enableLimitTimeCanExchangeGift) {
      // Ensure expireTimeUnit is null if enableExpireTime is false
      const limitTimeExchangeGiftData = {
        ...limitTimeExchangeGift,
        limitTimeCanExchangeGiftExpireTimeUnit:
          limitTimeExchangeGift.limitTimeCanExchangeGiftEnableExpireTime
            ? limitTimeExchangeGift.limitTimeCanExchangeGiftExpireTimeUnit
            : null,
        limitTimeCanExchangeGiftDisplayDetailOfTimes:
          limitTimeExchangeGift.limitTimeCanExchangeGiftDisplayDetailOfTimes ??
          false,
      };
      Object.assign(updateEventAddCan, limitTimeExchangeGiftData);
    } else {
      Object.assign(updateEventAddCan, {
        limitTimeCanExchangeGiftTotalTimes: null,
        limitTimeCanExchangeGiftNumberOfCanRate: null,
        limitTimeCanExchangeGiftNumberOfCanValue: null,
        limitTimeCanExchangeGiftEnableExpireTime: false,
        limitTimeCanExchangeGiftExpireTimeUnit: null,
        limitTimeCanExchangeGiftDisplayDetailOfTimes: false,
      });
    }
    // Validate setup list user can join event
    if (enableSetupListUsersCanJoin) {
      const usersList = await this.validateSetupListUsersCanJoin(
        isImportFile,
        userPhones,
      );
      try {
        const existingUsers = await this.eventNumberUserCanRepo.find({
          where: { eventAddCanId: eventAddCan.id },
          select: ['userId'],
        });

        const existingUserIds = new Set(existingUsers.map((u) => u.userId));

        const newUsers = usersList
          .filter((user) => !existingUserIds.has(user.id))
          .map((user) => ({
            userId: user.id,
            eventAddCanId: eventAddCan.id,
            numberOfCan: 0,
            numberOfCanUsed: 0,
            phoneNumber: user.phoneNumber,
          }));

        if (newUsers.length > 0) {
          await this.eventNumberUserCanRepo.insert(newUsers);
        }
      } catch (error) {
        this._logger.error('Error when  list user can join event', error);
        throw new AppBaseExc(
          StatusCode.SBPS_DEFAULT_ERROR,
          'Cập nhập danh sách người tham gia không thành công',
          true,
        );
      }
    }
    try {
      await this.eventAddCanRepo.save(updateEventAddCan);
    } catch (error) {
      this._logger.error('Error when update event add can', error);
      if (
        error.code == 23505 &&
        error.constraint === 'event_add_can_type_key'
      ) {
        throw new AppBaseExc(StatusCode.DUPLICATE_EVENT_TYPE);
      } else {
        throw new AppBaseExc(StatusCode.API_FAILED_UNKNOWN, error.msg, true);
      }
    }

    // Update event add can product ( sku)
    await this.saveEventAddCanSkus(
      eventAddCan.eventAddCanProducts,
      skus,
      eventAddCan.id,
    );
    // update gift category
    const giftCategories = await this.updateGiftCategory(
      eventAddCan,
      giftCategoryIds,
    );

    // Update time exchange gift setup data
    if (enableTimeExchangeGift) {
      await this.updateSetupTimeExchangeGifts(
        eventAddCan,
        timeExchangeGifts,
        eventAddCan.timeExchangeGifts,
      );
    }

    await Promise.all([
      this.adminActionHistoryRepo.loggingUpdateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        eventAddCan.eventName,
      ),
      this.updateSystemFeatureActiveByEventAddCanId(id, status),
    ]);

    return await this.getDetail(id);
  }

  @Transactional()
  async delete(id: number, admin: AccountData) {
    const eventAddCan = await this.eventAddCanRepo.findOne({
      where: { id },
      relations: { giftCategories: true },
    });

    if (!eventAddCan) throw new AppBaseExc(StatusCode.EVENT_ADD_CAN_NOT_FOUND);

    await this.eventAddCanRepo.softDelete(id);

    // update gift category
    await this.updateGiftCategory(eventAddCan, []);

    await this.adminActionHistoryRepo.loggingDeleteAction(
      admin.email,
      EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
      eventAddCan.eventName,
    );

    return new AppResponseDto();
  }

  @Transactional()
  async deleteMany(dto: DeleteMultipleByNumberIdsDto, admin: AccountData) {
    const { ids } = dto;
    const eventAddCans = await this.eventAddCanRepo.find({
      where: { id: In(ids) },
      relations: { giftCategories: true },
    });

    if (eventAddCans.length !== ids.length)
      throw new AppBaseExc(StatusCode.EVENT_ADD_CAN_NOT_FOUND);

    await this.eventAddCanRepo.softDelete(ids);

    // update gift category
    await Promise.all(
      eventAddCans.map((eventAddCan) =>
        this.updateGiftCategory(eventAddCan, []),
      ),
    );

    // Prepare batch insertion for logging
    const logs = eventAddCans.map((eventAddCan) => {
      return {
        adminName: admin.email,
        adminMenu: EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        recordIdentity: eventAddCan.eventName,
      };
    });
    await this.adminActionHistoryRepo.loggingDeleteActions(logs);

    return new AppResponseDto();
  }

  @Transactional()
  async create(
    dto: CreateEventAddCanAdminReqDto,
    admin: AccountData,
    token: string,
  ) {
    const {
      eventName,
      endDate,
      convertPoint,
      giftCategoryIds,
      gsGiftCategoryCodes,
      skus,
      startDate,
      status,
      type,
      convertCan400g,
      convertCan800g,
      convertBarrel110ml,
      convertBarrel180ml,
      activeOverCanPopup,
      enableTimeExchangeGift,
      timeExchangeGifts,
      enableLimitTimeCanExchangeGift,
      limitTimeExchangeGift,
      loyaltyProgramSfId,
      enableSetupListUsersCanJoin,
      isImportFile,
      userPhones,
    } = dto;

    const exsistEventAddCan = await this.eventAddCanRepo.findOneBy({
      type: type,
    });

    if (exsistEventAddCan) {
      throw new AppBaseExc(StatusCode.EVENT_ADD_CAN_FAILED_UNIQUE_CONSTRAINT);
    }

    // Validate setup time exchange gifts
    if (enableTimeExchangeGift) {
      this.validateTimeExchangeGiftsSetupData(timeExchangeGifts);

      // Validate gift in time exchange gift detail
      await this.validateGiftsSetupInEventAddCanTimeExchangeGift(
        token,
        timeExchangeGifts,
      );
    }

    // Validate setup limit time exchange gift
    if (enableLimitTimeCanExchangeGift) {
      this.validateLimitTimeExchangeGiftSetupData(limitTimeExchangeGift);
    }

    let eventAddCan = this.eventAddCanRepo.create({
      eventName,
      endDate,
      startDate,
      status,
      convertPoint,
      type,
      convertCan400g,
      convertCan800g,
      convertBarrel110ml,
      convertBarrel180ml,
      gsGiftCategoryCodes,
      activeOverCanPopup,
      enableTimeExchangeGift,
      enableSetupListUsersCanJoin,
      enableLimitTimeCanExchangeGift,
      loyaltyProgramSfId,
    });

    // Update limit time exchange gift setup data
    if (enableLimitTimeCanExchangeGift) {
      const limitTimeExchangeGiftData = {
        ...limitTimeExchangeGift,
        limitTimeCanExchangeGiftExpireTimeUnit:
          limitTimeExchangeGift.limitTimeCanExchangeGiftEnableExpireTime
            ? limitTimeExchangeGift.limitTimeCanExchangeGiftExpireTimeUnit
            : null,
        limitTimeCanExchangeGiftDisplayDetailOfTimes:
          limitTimeExchangeGift.limitTimeCanExchangeGiftDisplayDetailOfTimes ??
          false,
      };
      Object.assign(eventAddCan, limitTimeExchangeGiftData);
    } else {
      Object.assign(eventAddCan, {
        limitTimeCanExchangeGiftTotalTimes: null,
        limitTimeCanExchangeGiftNumberOfCanRate: null,
        limitTimeCanExchangeGiftNumberOfCanValue: null,
        limitTimeCanExchangeGiftEnableExpireTime: false,
        limitTimeCanExchangeGiftExpireTimeUnit: null,
        limitTimeCanExchangeGiftDisplayDetailOfTimes: false,
      });
    }

    try {
      const newEventAddCan = await this.eventAddCanRepo.save(eventAddCan);

      // update event add can product ( sku)
      await this.saveEventAddCanSkus([], skus, eventAddCan.id);
      // update gift category
      await this.updateGiftCategory(newEventAddCan, giftCategoryIds);
      // Update time exchange gift setup data
      if (enableTimeExchangeGift) {
        await this.updateSetupTimeExchangeGifts(eventAddCan, timeExchangeGifts);
      }

      //
      // Validate setup list user can join event
      if (enableSetupListUsersCanJoin) {
        const usersList = await this.validateSetupListUsersCanJoin(
          isImportFile,
          userPhones,
        );
        try {
          const listUsersImport = this.eventNumberUserCanRepo.create(
            usersList.map((user) => ({
              userId: user.id,
              eventAddCanId: newEventAddCan.id,
              numberOfCan: 0,
              numberOfCanUsed: 0,
              phoneNumber: user.phoneNumber,
            })),
          );
          await this.eventNumberUserCanRepo.insert(listUsersImport);
        } catch (error) {
          this._logger.error('Error when  list user can join event', error);
          throw new AppBaseExc(
            StatusCode.SBPS_DEFAULT_ERROR,
            'Tạo danh sách người tham gia không thành công',
            true,
          );
        }
      }

      // throw new AppBaseExc(
      //   StatusCode.SBPS_DEFAULT_ERROR,
      //   'Rollback data sau khi đã hợp lệ',
      //   true,
      // );

      await this.adminActionHistoryRepo.loggingCreateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        newEventAddCan.eventName,
      );

      return await this.getDetail(newEventAddCan.id);
    } catch (error) {
      this._logger.error('Error when create event add can', error);
      if (
        error.code === '23505' &&
        error.constraint === 'event_add_can_type_key'
      ) {
        throw new AppBaseExc(
          StatusCode.EVENT_ADD_CAN_FAILED_UNIQUE_CONSTRAINT,
          'Loại sự kiện đã tồn tại',
        );
      }
      if (error instanceof HttpException) {
        throw error;
      }
      throw new AppBaseExc(
        StatusCode.SBPS_DEFAULT_ERROR,
        'Tạo mới không thành công',
      );
    }
  }

  async getDetail(id: number) {
    const eventAddCan = await this.eventAddCanRepo.findOne({
      where: { id },
      relations: {
        giftCategories: true,
        eventAddCanProducts: true,
        timeExchangeGifts: {
          details: true,
        },
        // eventNumberUserCans: true,
      },
    });

    if (!eventAddCan) throw new AppBaseExc(StatusCode.EVENT_ADD_CAN_NOT_FOUND);

    const { _setupState, ...rest } = eventAddCan;

    return new AppResponseDto({
      ...rest,
      setupState: _setupState,
    });
  }

  @Transactional()
  async duplicateById(token: string, id: number, admin: AccountData) {
    const eventAddCan = await this.eventAddCanRepo.findOne({
      where: { id },
      relations: { giftCategories: true, eventAddCanProducts: true },
    });

    if (!eventAddCan) throw new AppBaseExc(StatusCode.EVENT_ADD_CAN_NOT_FOUND);

    const tempSuffix = Math.floor(new Date().getTime() / 1000);
    const newEventAddCan = await this.create(
      {
        eventName: eventAddCan.eventName,
        endDate: eventAddCan.endDate,
        startDate: eventAddCan.startDate,
        convertPoint: eventAddCan.convertPoint,
        type: formatStringWithSuffix(
          eventAddCan.type,
          tempSuffix, // temporary suffix
        ),
        convertCan400g: eventAddCan.convertCan400g,
        convertCan800g: eventAddCan.convertCan800g,
        convertBarrel110ml: eventAddCan.convertBarrel110ml,
        convertBarrel180ml: eventAddCan.convertBarrel180ml,
        skus: eventAddCan.eventAddCanProducts?.map((product) => product.sku),
        status: EventAddCanStatus.INACTIVE,
        // enableTimeExchangeGift: eventAddCan.enableTimeExchangeGift,
        // enableLimitTimeCanExchangeGift:
        //   eventAddCan.enableLimitTimeCanExchangeGift,
        enableTimeExchangeGift: false,
        enableLimitTimeCanExchangeGift: false,
      },
      admin,
      token,
    );

    const newEventAddCanId = newEventAddCan.response.id;
    const suffix = `${newEventAddCanId}`;

    // Create new gsGiftCategoryCodes
    const gsGiftCategoryCodes = eventAddCan.gsGiftCategoryCodes;
    const newGsGiftCategories = await Promise.all(
      gsGiftCategoryCodes.map(async (code) => {
        const giftCategory = await this.vitaJavaService.getGiftCategoryByCode(
          token,
          code,
        );

        // Get gift category by new code. If not exist -> create
        const request = {
          ...giftCategory,
          code: formatStringWithSuffix(giftCategory.code, suffix),
          name: formatStringWithSuffix(giftCategory.name, suffix),
          isActive: false,
        };

        let newCategory = await this.vitaJavaService.getGiftCategoryByCode(
          token,
          request.code,
        );

        if (!newCategory) {
          const responseCreateGift =
            await this.vitaJavaService.createGiftCategory(token, request);

          if (!responseCreateGift.success || responseCreateGift.error) {
            throw new ConflictExc(
              `Lỗi nhân đôi Danh mục quà: ${giftCategory.code}. ${responseCreateGift.error}`,
            );
          }

          newCategory = responseCreateGift.data;
        }

        return newCategory;
      }),
    );

    const newGsGiftCategoryCodes = newGsGiftCategories.map((gc) => gc.code);

    await Promise.all([
      this.eventAddCanRepo.update(newEventAddCanId, {
        eventName: formatStringWithSuffix(eventAddCan.eventName, suffix),
        type: formatStringWithSuffix(eventAddCan.type, suffix),
        gsGiftCategoryCodes: newGsGiftCategoryCodes,
      }),
      this.duplicateEventCanMark(
        token,
        eventAddCan.id,
        newEventAddCanId,
        suffix,
        newGsGiftCategoryCodes,
      ),
      this.duplicateEventAddCanPopup(
        eventAddCan.id,
        newEventAddCanId,
        suffix,
        admin,
      ),
      this.duplicateEventAddCanWebview(
        eventAddCan.id,
        newEventAddCanId,
        suffix,
        admin,
      ),
    ]);

    return await this.getDetail(newEventAddCanId);
  }

  private async updateGiftCategory(
    eventAddCan: EventAddCan,
    giftCategoryIds: number[],
  ) {
    const giftCategoriesToUpdate: GiftCategory[] = [];
    const existedGiftCategories = eventAddCan.giftCategories;
    const giftCategoryIdsInReq = giftCategoryIds;

    if (!giftCategoryIds || !giftCategoryIds.length) {
      return [];
    }

    const giftCategories = await this.giftCategoryRepo
      .createQueryBuilder('giftCategory')
      .where('giftCategory.id IN (:...ids)', { ids: giftCategoryIdsInReq })
      .andWhere(
        '(giftCategory.eventAddCanId IS NULL OR giftCategory.eventAddCanId = :eventAddCanId)',
        { eventAddCanId: eventAddCan.id },
      )
      .getMany();

    if (giftCategories.length !== giftCategoryIds.length)
      throw new AppBaseExc(StatusCode.GIFT_CATEGORY_NOT_VALID);

    const giftCategoriesNeedToUpdateEventAddCanId = giftCategories.map(
      (giftCategory) => ({
        ...giftCategory,
        eventAddCanId: eventAddCan.id,
      }),
    );

    giftCategoriesToUpdate.push(...giftCategoriesNeedToUpdateEventAddCanId);

    if (existedGiftCategories && existedGiftCategories.length > 0) {
      const giftCategoryIdsToSetEventAddCanIdToNull =
        existedGiftCategories.filter(
          (giftCategory) => !giftCategoryIdsInReq.includes(giftCategory.id),
        );

      const giftCategoriesToSetEventAddCanIdToNull =
        giftCategoryIdsToSetEventAddCanIdToNull.map((giftCategory) => ({
          ...giftCategory,
          eventAddCanId: null,
        }));

      giftCategoriesToUpdate.push(...giftCategoriesToSetEventAddCanIdToNull);
    }
    await this.giftCategoryRepo.save(giftCategoriesToUpdate);

    return giftCategoriesNeedToUpdateEventAddCanId;
  }

  private async saveEventAddCanSkus(
    eventAddCanProducts: EventAddCanProduct[],
    skus: string[],
    eventAddCanId: number,
  ) {
    const eventAddCanProductIdsToDelete: number[] = [];
    const eventAddCanProductSkusToAdd: EventAddCanProduct[] = [];

    eventAddCanProducts.forEach((eventAddCanProduct) => {
      if (!skus.includes(eventAddCanProduct.sku))
        eventAddCanProductIdsToDelete.push(eventAddCanProduct.id);
    });

    skus.forEach((sku) => {
      if (
        !eventAddCanProducts.some(
          (eventAddCanProduct) => eventAddCanProduct.sku === sku,
        )
      ) {
        eventAddCanProductSkusToAdd.push(
          this.eventAddCanProductRepo.create({
            eventAddCanId,
            sku,
          }),
        );
      }
    });

    await Promise.all([
      eventAddCanProductIdsToDelete.length &&
      this.eventAddCanProductRepo.delete(eventAddCanProductIdsToDelete),
      this.eventAddCanProductRepo.save(eventAddCanProductSkusToAdd),
    ]);
  }

  private async duplicateEventCanMark(
    token: string,
    eventAddCanId: number,
    newEventAddCanId: number,
    suffix: string | number,
    newGiftCategoryCodes: string[],
  ) {
    // get list event can mark eventAddCanId
    const eventCanMarks = await this.eventCanMarkRepo.find({
      where: { eventAddCanId },
    });
    if (!eventCanMarks || !eventCanMarks.length) return;

    await Promise.all(
      eventCanMarks.map(async (eventCanMark) => {
        // get current gift
        const currentGift = await this.vitaJavaService.getGiftById(
          token,
          eventCanMark.gsGiftId,
        );

        // Get gift category
        const newCategoryCode = newGiftCategoryCodes.find((giftCategoryCode) =>
          giftCategoryCode.includes(currentGift.categoryCode),
        );

        // Calculate end date: if current end date < now -> add 1 day
        const endDate =
          compareDateWithCurrent(currentGift.endDate) < 1
            ? addDayToDate(new Date(), 1)
            : currentGift.endDate;

        // Remove setup limit exchange gift
        currentGift.enableTimeExchangeGift = false;
        currentGift.giftTimeExchangeGifts = [];

        // Create new gift
        const responseCreateGift = await this.vitaJavaService.createGift(
          token,
          {
            ...currentGift,
            name: formatStringWithSuffix(currentGift.name, suffix),
            categoryCode: newCategoryCode,
            status: GiftStatusEnum.DISABLED,
            endDate: endDate,
          },
        );
        if (!responseCreateGift.success || responseCreateGift.error) {
          throw new ConflictExc(
            `Lỗi nhân đôi Quà: ${currentGift.name}. ${responseCreateGift.error}`,
          );
        }

        // Create new eventCanMark
        const dto = {
          ...eventCanMark,
          eventAddCanId: newEventAddCanId,
          userType: eventCanMark.typeUser,
          gsGiftId: responseCreateGift.data.id,
        };
        return await this.eventCanMarkAdminService.createEventCanMark(
          token,
          dto,
          null,
        );
      }),
    );
  }

  @Transactional()
  private async duplicateEventAddCanPopup(
    eventAddCanId: number,
    newEventAddCanId: number,
    suffix: string | number,
    admin: AccountData,
  ) {
    // get list popup by eventAddCanId
    const eventAddCanPopups = await this.eventAddCanPopupRepo.find({
      where: { eventAddCanId },
    });

    if (!eventAddCanPopups || !eventAddCanPopups.length) return;

    // create new popup for each popup
    const newEventAddCanPopups = eventAddCanPopups.map((popup) => {
      const newCode = formatStringWithSuffix(popup.code, suffix);
      const newPopup = this.eventAddCanPopupRepo.create({
        ...popup,
        code: newCode,
        eventAddCanId: newEventAddCanId,
      });
      return newPopup;
    });

    // save new popups
    await Promise.all([
      this.eventAddCanPopupRepo.insert(newEventAddCanPopups),
      this.adminActionHistoryRepo.loggingCreateActions(
        newEventAddCanPopups.map((popup) => ({
          adminName: admin.email,
          adminMenu: EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
          recordIdentity: popup.code,
        })),
      ),
    ]);
  }

  @Transactional()
  private async duplicateEventAddCanWebview(
    eventAddCanId: number,
    newEventAddCanId: number,
    suffix: string | number,
    admin: AccountData,
  ) {
    // get webview by eventAddCanId
    const eventAddCanWebview = await this.eventAddCanWebviewRepo.findOne({
      where: { eventAddCanId },
    });

    if (!eventAddCanWebview) return;

    // create new webview
    const newEventAddCanWebview = this.eventAddCanWebviewRepo.create({
      ...eventAddCanWebview,
      linkWebview: formatStringWithSuffix(
        eventAddCanWebview.linkWebview,
        suffix,
      ),
      eventAddCanId: newEventAddCanId,
    });

    // save new webview
    await Promise.all([
      this.eventAddCanWebviewRepo.insert(newEventAddCanWebview),
      this.adminActionHistoryRepo.loggingCreateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        newEventAddCanWebview.linkWebview,
      ),
    ]);
  }

  private async updateSetupTimeExchangeGifts(
    eventAddCan: EventAddCan,
    newTimeExchangeGifts: EventAddCanTimeExchangeGiftReqDto[],
    oldTimeExchangeGifts: EventAddCanTimeExchangeGift[] = [],
  ) {
    if (!eventAddCan || !newTimeExchangeGifts || !newTimeExchangeGifts.length) {
      return;
    }

    // if (!eventAddCan.enableTimeExchangeGift) {
    //   return;
    // }

    // Validate updating old setup
    this.validateUpdatingTimeExchangeGiftsSetupData(
      newTimeExchangeGifts,
      oldTimeExchangeGifts,
    );

    const timeExchangeGiftIdsToRemove: number[] = [];
    const timeExchangeGiftDetailIdsToRemove: number[] = [];
    const timeExchangeGiftIdsToSave: EventAddCanTimeExchangeGift[] = [];
    const timeExchangeGiftDetailIdsToSave: EventAddCanTimeExchangeGiftDetail[] =
      [];
    const promisesUpdateData: any[] = [];

    if (oldTimeExchangeGifts && oldTimeExchangeGifts.length) {
      for (const oldTimeExchangeGift of oldTimeExchangeGifts) {
        if (
          !newTimeExchangeGifts.some(
            (item) => item.id === Number(oldTimeExchangeGift.id),
          )
        ) {
          timeExchangeGiftIdsToRemove.push(Number(oldTimeExchangeGift.id));
        }
      }
    }
    if (timeExchangeGiftIdsToRemove.length) {
      promisesUpdateData.push(
        this.eventAddCanTimeExchangeGiftDetailRepo.delete({
          eventAddCanTimeExchangeGiftId: In(timeExchangeGiftIdsToRemove),
        }),
        // this.eventAddCanTimeExchangeGiftRepo.delete({
        //   id: In(timeExchangeGiftIdsToRemove),
        // }),
      );
    }

    const mapOldTimeExchangeGiftsById = objectify(
      oldTimeExchangeGifts,
      (f) => f.id,
    );
    for (const newTimeExchangeGift of newTimeExchangeGifts) {
      if (mapOldTimeExchangeGiftsById.hasOwnProperty(newTimeExchangeGift.id)) {
        const oldTimeExchangeGift: EventAddCanTimeExchangeGift =
          mapOldTimeExchangeGiftsById[newTimeExchangeGift.id];
        if (oldTimeExchangeGift.details.length) {
          for (const oldTimeExchangeGiftDetail of oldTimeExchangeGift.details) {
            if (
              !newTimeExchangeGift.details.some(
                (item) => item.id === Number(oldTimeExchangeGiftDetail.id),
              )
            ) {
              timeExchangeGiftDetailIdsToRemove.push(
                Number(oldTimeExchangeGiftDetail.id),
              );
            }
          }
        }
      }
    }
    if (timeExchangeGiftDetailIdsToRemove.length) {
      promisesUpdateData.push(
        this.eventAddCanTimeExchangeGiftDetailRepo.delete({
          id: In(timeExchangeGiftDetailIdsToRemove),
        }),
      );
    }

    if (promisesUpdateData.length) {
      await Promise.all(promisesUpdateData);
    }

    if (timeExchangeGiftIdsToRemove.length) {
      await this.eventAddCanTimeExchangeGiftRepo.delete({
        id: In(timeExchangeGiftIdsToRemove),
      });
    }

    await this.eventAddCanTimeExchangeGiftRepo.save(
      newTimeExchangeGifts.map((item) => {
        return {
          ...item,
          eventAddCanId: eventAddCan.id,
        };
      }),
    );
  }

  private validateUpdatingTimeExchangeGiftsSetupData(
    newTimeExchangeGifts: EventAddCanTimeExchangeGiftReqDto[],
    oldTimeExchangeGifts: EventAddCanTimeExchangeGift[] = [],
  ) {
    if (!newTimeExchangeGifts || !newTimeExchangeGifts.length) {
      throw new AppBaseExc(
        StatusCode.SBPS_DEFAULT_ERROR,
        'Không cho phép cập nhật thời gian trúng giải. Dữ liệu rỗng',
        true,
      );
    }

    // Inserting new data
    const insertingNewData =
      !oldTimeExchangeGifts || !oldTimeExchangeGifts.length;
    const mapOldTimeExchangeGiftsById = objectify(
      oldTimeExchangeGifts,
      (f) => f.id,
    );
    for (const newTimeExchangeGift of newTimeExchangeGifts) {
      const oldTimeExchangeGift: EventAddCanTimeExchangeGift =
        mapOldTimeExchangeGiftsById.hasOwnProperty(newTimeExchangeGift.id)
          ? mapOldTimeExchangeGiftsById[newTimeExchangeGift.id]
          : null;
      let updatingTimeExchangeGiftIsHappening = false;
      // Inserting
      if (insertingNewData) {
        if (newTimeExchangeGift.id) {
          throw new AppBaseExc(
            StatusCode.SBPS_DEFAULT_ERROR,
            'Không cho phép tạo thời gian trúng giải. Tạo mới nhưng set id',
            true,
          );
        }
      } else {
        // Updating
        if (newTimeExchangeGift.id) {
          if (!oldTimeExchangeGift) {
            throw new AppBaseExc(
              StatusCode.SBPS_DEFAULT_ERROR,
              'Không cho phép cập nhật thời gian trúng giải. Cập nhật sai id',
              true,
            );
          }

          // Updating time exchange gift when it is happening
          updatingTimeExchangeGiftIsHappening =
            compareDateWithCurrentDateInTimezone(
              newTimeExchangeGift.startDate,
            ) != 1;
          if (updatingTimeExchangeGiftIsHappening) {
            const updatingStartDate =
              compareDateWithDateInTimezone(
                oldTimeExchangeGift.startDate,
                newTimeExchangeGift.startDate,
              ) != 0;
            const updatingEndDate =
              compareDateWithDateInTimezone(
                oldTimeExchangeGift.endDate,
                newTimeExchangeGift.endDate,
              ) != 0;
            if (updatingStartDate || updatingEndDate) {
              throw new AppBaseExc(
                StatusCode.SBPS_DEFAULT_ERROR,
                'Không cho phép cập nhật thời gian trúng giải. Cập nhật khung thời gian đang sử dụng',
                true,
              );
            }
            const updatingDetail =
              oldTimeExchangeGift.details.length !=
              newTimeExchangeGift.details.length;
            if (updatingDetail) {
              throw new AppBaseExc(
                StatusCode.SBPS_DEFAULT_ERROR,
                'Không cho phép cập nhật thời gian trúng giải. Cập nhật khung thời gian đang sử dụng',
                true,
              );
            }
          }
        }
      }
      this.validateUpdatingTimeExchangeGiftDetailsSetupData(
        updatingTimeExchangeGiftIsHappening,
        newTimeExchangeGift.details,
        oldTimeExchangeGift?.details,
      );
    }
  }

  private validateUpdatingTimeExchangeGiftDetailsSetupData(
    updatingTimeExchangeGiftIsHappening: boolean,
    newTimeExchangeGiftDetails: EventAddCanTimeExchangeGiftDetailReqDto[],
    oldTimeExchangeGiftDetails: EventAddCanTimeExchangeGiftDetail[] = [],
  ) {
    if (!newTimeExchangeGiftDetails || !newTimeExchangeGiftDetails.length) {
      throw new AppBaseExc(
        StatusCode.SBPS_DEFAULT_ERROR,
        'Không cho phép cập nhật chi tiết trúng giải. Dữ liệu rỗng',
        true,
      );
    }

    // Inserting new data
    const insertingNewData =
      !oldTimeExchangeGiftDetails || !oldTimeExchangeGiftDetails.length;
    const mapOldTimeExchangeGiftDetailsById = objectify(
      oldTimeExchangeGiftDetails,
      (f) => f.id,
    );
    for (const newTimeExchangeGiftDetail of newTimeExchangeGiftDetails) {
      // Inserting
      if (insertingNewData) {
        if (newTimeExchangeGiftDetail.id) {
          throw new AppBaseExc(
            StatusCode.SBPS_DEFAULT_ERROR,
            'Không cho phép tạo chi tiết trúng giải. Tạo mới nhưng set id',
            true,
          );
        }
      } else {
        // Updating
        if (newTimeExchangeGiftDetail.id) {
          if (
            !mapOldTimeExchangeGiftDetailsById.hasOwnProperty(
              newTimeExchangeGiftDetail.id,
            )
          ) {
            throw new AppBaseExc(
              StatusCode.SBPS_DEFAULT_ERROR,
              'Không cho phép cập nhật chi tiết trúng giải. Cập nhật sai id',
              true,
            );
          }

          const oldTimeExchangeGiftDetail: EventAddCanTimeExchangeGiftDetail =
            mapOldTimeExchangeGiftDetailsById[newTimeExchangeGiftDetail.id];
          // Updating time exchange gift when it is happening
          const updatingTotal =
            oldTimeExchangeGiftDetail.total != newTimeExchangeGiftDetail.total;
          let updatingGiftIds =
            oldTimeExchangeGiftDetail.giftIds.length !=
            newTimeExchangeGiftDetail.giftIds.length;
          if (!updatingGiftIds) {
            for (const giftId of newTimeExchangeGiftDetail.giftIds) {
              if (!oldTimeExchangeGiftDetail.giftIds.includes(giftId)) {
                updatingGiftIds = true;
                break;
              }
            }
          }
          if (updatingGiftIds || updatingTotal) {
            if (updatingTimeExchangeGiftIsHappening) {
              throw new AppBaseExc(
                StatusCode.SBPS_DEFAULT_ERROR,
                'Không cho phép cập nhật chi tiết trúng giải. Cập nhật khung thời gian đang sử dụng',
                true,
              );
            }
          }
        }
      }
    }
    // TODO: did not create unique index on table detail to make sure
    // TODO: time exchange gift and gift ids not duplicate
    // TODO: event_add_can_time_exchange_gift_id 1 and gift ids [1] (id 1)
    // TODO: can create event_add_can_time_exchange_gift_id 1 and gift ids [1] again
  }

  private validateTimeExchangeGiftsSetupData(
    timeExchangeGifts: EventAddCanTimeExchangeGiftReqDto[],
  ) {
    if (!timeExchangeGifts || !timeExchangeGifts.length) {
      throw new AppBaseExc(
        StatusCode.SBPS_DEFAULT_ERROR,
        'Cài đặt thời gian trúng giải không hợp lệ. Dữ liệu rỗng',
        true,
      );
    }

    // Validate time exchange gift
    let uniqueIds: number[] = [];
    let countOrgIds = 0;
    const orderedTimeExchangeGiftsSortByStartDate: EventAddCanTimeExchangeGiftReqDto[] =
      sortByAttribute(timeExchangeGifts, 'startDate');
    for (let i = 0; i < orderedTimeExchangeGiftsSortByStartDate.length; i++) {
      const timeExchangeGift = orderedTimeExchangeGiftsSortByStartDate[i];

      if (timeExchangeGift.id) {
        countOrgIds++;
        uniqueIds = mergeTwoArraysRemoveDuplicating(uniqueIds, [
          timeExchangeGift.id,
        ]);
      }
      // Validate startDate endDate > now
      // Only validate if create new
      if (
        !timeExchangeGift.id &&
        (compareDateWithCurrentDateInTimezone(timeExchangeGift.startDate) !=
          1 ||
          compareDateWithCurrentDateInTimezone(timeExchangeGift.endDate) != 1)
      ) {
        throw new AppBaseExc(
          StatusCode.SBPS_DEFAULT_ERROR,
          'Cài đặt thời gian trúng giải không hợp lệ. Dữ liệu start date, end date < now',
          true,
        );
      }

      // Validate startDate < endDate
      if (
        !validateSetupTimeValidOnEachRange(
          timeExchangeGift,
          'startDate',
          'endDate',
        )
      ) {
        throw new AppBaseExc(
          StatusCode.SBPS_DEFAULT_ERROR,
          'Cài đặt thời gian trúng giải không hợp lệ. Dữ liệu start date >= end date',
          true,
        );
      }

      // Validate startDate,endDate of each range is not overlap
      // if (
      //   validateItemOverlapTimeRangeWithOtherItemInListObject(
      //     timeExchangeGift,
      //     timeExchangeGifts,
      //     'startDate',
      //     'endDate',
      //   )
      // ) {
      //   throw new AppBaseExc(
      //     StatusCode.SBPS_DEFAULT_ERROR,
      //     'Cài đặt thời gian trúng giải không hợp lệ. Dữ liệu start date, end date bị trùng lặp',
      //     true,
      //   );
      // }

      // Validate detail
      let uniqueDetailIds: number[] = [];
      let countOrgDetailIds = 0;
      let uniqueGiftIdsInEachDetail: number[] = [];
      let countOrgGiftIdsInEachDetail = 0;
      for (const detail of timeExchangeGift.details) {
        // console.log('detail', detail);
        if (detail.id) {
          countOrgDetailIds++;
          uniqueDetailIds = mergeTwoArraysRemoveDuplicating(uniqueDetailIds, [
            detail.id,
          ]);
        }
        countOrgGiftIdsInEachDetail += detail.giftIds.length;
        uniqueGiftIdsInEachDetail = mergeTwoArraysRemoveDuplicating(
          uniqueGiftIdsInEachDetail,
          detail.giftIds,
        );
      }
      // Check duplicate detail id
      if (countOrgDetailIds != uniqueDetailIds.length) {
        throw new AppBaseExc(
          StatusCode.SBPS_DEFAULT_ERROR,
          'Cài đặt chi tiết trúng giải không hợp lệ. Dữ liệu detail id bị trùng lặp',
          true,
        );
      }
      // Check duplicate gift id
      if (countOrgGiftIdsInEachDetail != uniqueGiftIdsInEachDetail.length) {
        throw new AppBaseExc(
          StatusCode.SBPS_DEFAULT_ERROR,
          'Cài đặt chi tiết trúng giải không hợp lệ. Dữ liệu gift id bị trùng lặp',
          true,
        );
      }

      // console.log(timeExchangeGift);
      for (
        let j = i + 1;
        j < orderedTimeExchangeGiftsSortByStartDate.length;
        j++
      ) {
        const otherTimeExchangeGift =
          orderedTimeExchangeGiftsSortByStartDate[j];
        // console.log('otherTimeExchangeGift', otherTimeExchangeGift);
        // console.log(otherTimeExchangeGift);
        // console.log(
        //   compareDateWithDateInTimezone(
        //     timeExchangeGift.startDate,
        //     otherTimeExchangeGift.startDate,
        //   ),
        // );

        // Stop early if otherTimeExchangeGift starts after timeExchangeGift ends (no possible overlap)
        if (
          compareDateWithDateInTimezone(
            timeExchangeGift.startDate,
            otherTimeExchangeGift.startDate,
          ) == 1
        ) {
          break;
        }

        if (
          validateItemOverlapTimeRangeWithOtherItem(
            timeExchangeGift,
            otherTimeExchangeGift,
            'startDate',
            'endDate',
          )
        ) {
          // Reset counters for each overlap check
          let uniqueGiftIdsWhenOverlapTime: number[] = [];
          let countOrgGiftIdsWhenOverlapTime = 0;

          for (const detail1 of timeExchangeGift.details) {
            countOrgGiftIdsWhenOverlapTime += detail1.giftIds.length;
            uniqueGiftIdsWhenOverlapTime = mergeTwoArraysRemoveDuplicating(
              uniqueGiftIdsWhenOverlapTime,
              detail1.giftIds,
            );
          }
          for (const detail2 of otherTimeExchangeGift.details) {
            countOrgGiftIdsWhenOverlapTime += detail2.giftIds.length;
            uniqueGiftIdsWhenOverlapTime = mergeTwoArraysRemoveDuplicating(
              uniqueGiftIdsWhenOverlapTime,
              detail2.giftIds,
            );
          }

          // Check duplicate gift id
          // console.log(
          //   'countOrgGiftIdsWhenOverlapTime',
          //   countOrgGiftIdsWhenOverlapTime,
          // );
          // console.log(
          //   'uniqueGiftIdsWhenOverlapTime',
          //   uniqueGiftIdsWhenOverlapTime,
          // );
          if (
            countOrgGiftIdsWhenOverlapTime !=
            uniqueGiftIdsWhenOverlapTime.length
          ) {
            throw new AppBaseExc(
              StatusCode.SBPS_DEFAULT_ERROR,
              'Cài đặt chi tiết trúng giải không hợp lệ. Dữ liệu gift id bị trùng lặp',
              true,
            );
          }
        }
      }
    }

    // Check duplicate detail id
    if (countOrgIds != uniqueIds.length) {
      throw new AppBaseExc(
        StatusCode.SBPS_DEFAULT_ERROR,
        'Cài đặt thời gian trúng giải không hợp lệ. Dữ liệu id bị trùng lặp',
        true,
      );
    }
  }

  private validateLimitTimeExchangeGiftSetupData(
    limitTimeExchangeGifts: EventAddCanLimitTimeExchangeGiftReqDto,
  ) {
    if (!limitTimeExchangeGifts) {
      throw new AppBaseExc(
        StatusCode.SBPS_DEFAULT_ERROR,
        'Cài đặt giới hạn lượt đổi quà không hợp lệ. Dữ liệu rỗng',
        true,
      );
    }

    const {
      limitTimeCanExchangeGiftTotalTimes,
      limitTimeCanExchangeGiftNumberOfCanRate,
      limitTimeCanExchangeGiftNumberOfCanValue,
      limitTimeCanExchangeGiftEnableExpireTime,
      limitTimeCanExchangeGiftExpireTimeUnit,
    } = limitTimeExchangeGifts;
    if (
      limitTimeCanExchangeGiftTotalTimes < 1 ||
      limitTimeCanExchangeGiftNumberOfCanRate < 1 ||
      limitTimeCanExchangeGiftNumberOfCanValue < 1
    ) {
      throw new AppBaseExc(
        StatusCode.SBPS_DEFAULT_ERROR,
        'Cài đặt giới hạn lượt đổi quà không hợp lệ. Dữ liệu không hợp lệ',
        true,
      );
    }

    // Validate: if enableExpireTime is true, expireTimeUnit must be provided
    if (
      limitTimeCanExchangeGiftEnableExpireTime &&
      !limitTimeCanExchangeGiftExpireTimeUnit
    ) {
      throw new AppBaseExc(
        StatusCode.SBPS_DEFAULT_ERROR,
        'Cài đặt giới hạn lượt đổi quà không hợp lệ. Phải chọn đơn vị thời gian hết hạn',
        true,
      );
    }
  }

  private validateSetupListUsersCanJoin = async (
    isImportFile?: boolean,
    userPhones?: string[],
  ) => {
    if (isImportFile) {
      // TODO: queue
      throw new AppBaseExc(
        StatusCode.SBPS_DEFAULT_ERROR,
        'Tính năng này đang được phát triển. Vui lòng thử lại sau',
        true,
      );
    }
    //
    if (!userPhones || userPhones.length === 0) {
      return [];
    }
    const users = await this.userRepository.find({
      select: ['id', 'phoneNumber'],
      where: {
        phoneNumber: In(userPhones),
      },
    });
    // So sánh số lượng
    if (users.length !== userPhones.length) {
      const foundPhones = users.map((u) => u.phoneNumber);
      const notFound = userPhones.filter((p) => !foundPhones.includes(p));

      throw new AppBaseExc(
        StatusCode.SBPS_DEFAULT_ERROR,
        `Một số số điện thoại không tồn tại trong hệ thống: ${notFound.join(
          ', ',
        )}`,
        true,
        undefined,
        undefined,
        notFound,
      );
    }
    return users;
  };
  private async validateGiftsSetupInEventAddCanTimeExchangeGift(
    token: string,
    timeExchangeGifts: EventAddCanTimeExchangeGiftReqDto[],
  ) {
    const giftIds: number[] = [];
    for (const timeExchangeGift of timeExchangeGifts) {
      for (const detail of timeExchangeGift.details) {
        giftIds.push(...detail.giftIds);
      }
    }

    // Validate gifts in event add can time exchange gift detail is not setup limit exchange gift
    const gifts = await this.vitaJavaService.getAdminGiftByIds(token, giftIds);
    if (!gifts || !gifts.length) {
      throw new AppBaseExc(
        StatusCode.SBPS_DEFAULT_ERROR,
        'Cài đặt giới hạn đổi quà thuộc event không hợp lệ. Quà không tồn tại.',
        true,
      );
    }

    if (
      gifts.some(
        (gift) =>
          gift.enableTimeExchangeGift || gift.giftTimeExchangeGifts?.length,
      )
    ) {
      throw new AppBaseExc(
        StatusCode.SBPS_DEFAULT_ERROR,
        'Cài đặt giới hạn đổi quà thuộc event không hợp lệ. Quà đã được cài đặt giới hạn đổi quà.',
        true,
      );
    }
  }

  async getExpireTimeUnits() {
    const enumValues = getValEnumStr(
      EventAddCanLimitTimeCanExchangeGiftExpireTimeUnit,
    );
    return enumValues;
  }

  async getEventBrands(dto: GetEventBrandsAdminReqDto) {
    const { page, limit } = dto;

    const queryBuilder = this.generateEventBrandQueryBuilder();

    queryBuilder.distinct(true);
    queryBuilder.orderBy('eventAddCan.id', 'DESC');

    // Apply filters via reusable pipeline in BaseRepository
    await this.eventAddCanRepo.applyAllFilters(queryBuilder, dto, this);

    const { items, meta } = await paginate(queryBuilder, {
      page,
      limit,
    });

    return { items, meta };
  }

  async getEventAddCanWithBrandPointConfigs(
    dto: GetEventAddCanBrandPointConfigsAdminReqDto,
  ) {
    const queryBuilder = this.generateEventAddCanBrandPointConfigQueryBuilder();

    queryBuilder.distinct(true);
    queryBuilder.orderBy('eventAddCan.id', 'DESC');

    // Apply filters via reusable pipeline in BaseRepository
    await this.eventAddCanRepo.applyAllFilters(queryBuilder, dto, this);

    const { items, meta } = await paginate(queryBuilder, {
      page: dto.page,
      limit: dto.limit,
    });

    return {
      items,
      meta,
    };
  }

  async getEventAddCanWithBrandPointConfig(eventAddCanId: number) {
    const queryBuilder = this.generateEventAddCanBrandPointConfigQueryBuilder();

    queryBuilder.andWhere('eventAddCan.id = :eventAddCanId', { eventAddCanId });

    const eventAddCanWithBrandPointConfig = await queryBuilder.getOne();

    if (!eventAddCanWithBrandPointConfig) {
      throw new NotFoundExc(
        'Sự kiện đổi quà không tồn tại hoặc chưa có cấu hình bể xu riêng',
      );
    }

    return eventAddCanWithBrandPointConfig;
  }

  @Transactional()
  async createBrandPointConfigByEventAddCanId(
    eventAddCanId: number,
    dto: CreateEventAddCanBrandPointConfigAdminReqDto,
    admin: AccountData,
  ) {
    const eventAddCanWithNullBrandPointConfig =
      await this.eventAddCanRepo.findOne({
        where: { id: eventAddCanId, brandPointConfig: IsNull() },
      });
    if (!eventAddCanWithNullBrandPointConfig) {
      throw new NotFoundExc(
        'Sự kiện đổi quà không tồn tại hoặc đã có cấu hình bể xu riêng',
      );
    }

    await this.validateUniquePriorityBrandPointConfig(dto.priority);

    const brandPointConfig: BrandPointConfigType = {
      name: dto.name,
      is_active: dto.isActive,
      priority: dto.priority,
      shortcut_name: dto.shortcutName,
      shortcut_icon_url: dto.shortcutIconUrl,
      shortcut_deeplink: dto.shortcutDeeplink,
      point_icon_url: dto.pointIconUrl,
    };

    await Promise.all([
      this.eventAddCanRepo.update(eventAddCanWithNullBrandPointConfig.id, {
        brandPointConfig,
      }),
      this.adminActionHistoryRepo.loggingUpdateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        eventAddCanWithNullBrandPointConfig.eventName,
      ),
    ]);

    return await this.getEventAddCanWithBrandPointConfig(eventAddCanId);
  }

  @Transactional()
  async updateBrandPointConfigByEventAddCanId(
    eventAddCanId: number,
    dto: UpdateEventAddCanBrandPointConfigAdminReqDto,
    admin: AccountData,
  ) {
    const eventAddCanWithBrandPointConfig =
      await this.getEventAddCanWithBrandPointConfig(eventAddCanId);

    if (dto.priority !== undefined) {
      await this.validateUniquePriorityBrandPointConfig(
        dto.priority,
        eventAddCanId,
      );
    }

    const brandPointConfig: BrandPointConfigType = {
      ...eventAddCanWithBrandPointConfig.brandPointConfig,
      name: dto.name,
      is_active: dto.isActive,
      priority: dto.priority,
      shortcut_name: dto.shortcutName,
      shortcut_icon_url: dto.shortcutIconUrl,
      shortcut_deeplink: dto.shortcutDeeplink,
      point_icon_url: dto.pointIconUrl,
    };

    await Promise.all([
      this.eventAddCanRepo.update(eventAddCanWithBrandPointConfig.id, {
        brandPointConfig,
      }),
      this.adminActionHistoryRepo.loggingUpdateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        eventAddCanWithBrandPointConfig.eventName,
      ),
    ]);

    return await this.getEventAddCanWithBrandPointConfig(eventAddCanId);
  }

  @Transactional()
  async deleteBrandPointConfigByEventAddCanId(
    eventAddCanId: number,
    admin: AccountData,
  ) {
    const eventAddCanWithBrandPointConfig =
      await this.getEventAddCanWithBrandPointConfig(eventAddCanId);

    await Promise.all([
      this.eventAddCanRepo.update(eventAddCanWithBrandPointConfig.id, {
        brandPointConfig: null,
      }),
      this.adminActionHistoryRepo.loggingUpdateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        eventAddCanWithBrandPointConfig.eventName,
      ),
    ]);

    return 'ok';
  }

  private async validateUniquePriorityBrandPointConfig(
    priority: number,
    excludeEventAddCanId?: number,
  ) {
    const queryBuilder = this.eventAddCanRepo
      .createQueryBuilder('eventAddCan')
      .where('eventAddCan.brandPointConfig IS NOT NULL')
      .andWhere(
        `("eventAddCan"."brand_point_config"->>'priority')::int = :priority`,
        {
          priority: priority,
        },
      );

    if (excludeEventAddCanId) {
      queryBuilder.andWhere('eventAddCan.id != :id', {
        id: excludeEventAddCanId,
      });
    }

    const count = await queryBuilder.getCount();
    if (count > 0) {
      throw new ConflictExc(
        `Thứ tự hiển thị (priority) ${priority} đã tồn tại ở sự kiện khác`,
      );
    }
  }

  private generateEventBrandQueryBuilder() {
    return this.eventAddCanRepo
      .createQueryBuilder('eventAddCan')
      .innerJoin('eventAddCan.eventAddCanProducts', 'eventAddCanProducts')
      .innerJoin('eventAddCanProducts.product', 'eventAddCanProductsProduct')
      .andWhere('eventAddCanProductsProduct.brandPoint > 0')
      .andWhere('eventAddCanProductsProduct.brandId IS NOT NULL');
  }

  private generateEventAddCanBrandPointConfigQueryBuilder() {
    return this.eventAddCanRepo
      .createQueryBuilder('eventAddCan')
      .innerJoin('eventAddCan.eventAddCanProducts', 'eventAddCanProducts')
      .innerJoin('eventAddCanProducts.product', 'eventAddCanProductsProduct')
      .andWhere('eventAddCanProductsProduct.brandPoint > 0')
      .andWhere('eventAddCanProductsProduct.brandId IS NOT NULL')
      .andWhere('eventAddCan.brandPointConfig IS NOT NULL');
  }

  @QueryFilter({ order: 10 })
  private applySearchTextFilterEventBrands(
    queryBuilder: SelectQueryBuilder<EventAddCan>,
    dto: GetEventBrandsAdminReqDto,
  ): void {
    if (dto.searchText) {
      queryBuilder.andWhere('eventAddCan.name ILIKE :searchText', {
        searchText: `%${dto.searchText}%`,
      });
    }
  }

  @QueryFilter({ order: 20 })
  private applyHasBrandPointConfigFilterEventBrands(
    queryBuilder: SelectQueryBuilder<EventAddCan>,
    dto: GetEventBrandsAdminReqDto,
  ): void {
    if (dto.hasBrandPointConfig === undefined) {
      return;
    }

    if (boolean(dto.hasBrandPointConfig) === true) {
      queryBuilder.andWhere('eventAddCan.brandPointConfig IS NOT NULL');
    } else {
      queryBuilder.andWhere('eventAddCan.brandPointConfig IS NULL');
    }
  }

  @QueryFilter({ order: 10 })
  private applyEventAddCanIdFilter(
    queryBuilder: SelectQueryBuilder<EventAddCan>,
    dto: GetEventAddCanBrandPointConfigsAdminReqDto,
  ): void {
    if (dto.eventAddCanId) {
      queryBuilder.andWhere('eventAddCan.id = :eventAddCanId', {
        eventAddCanId: dto.eventAddCanId,
      });
    }
  }

  @QueryFilter({ order: 20 })
  private applyNameFilter(
    queryBuilder: SelectQueryBuilder<EventAddCan>,
    dto: GetEventAddCanBrandPointConfigsAdminReqDto,
  ): void {
    if (dto.name) {
      queryBuilder.andWhere(
        `"eventAddCan"."brand_point_config"->>'name' ILIKE :name`,
        {
          name: `%${dto.name}%`,
        },
      );
    }
  }

  @QueryFilter({ order: 30 })
  private applyDateRangeFilter(
    queryBuilder: SelectQueryBuilder<EventAddCan>,
    dto: GetEventAddCanBrandPointConfigsAdminReqDto,
  ): void {
    if (dto.startDate) {
      queryBuilder.andWhere('eventAddCan.startDate >= :startDate', {
        startDate: dto.startDate,
      });
    }

    if (dto.endDate) {
      queryBuilder.andWhere('eventAddCan.endDate <= :endDate', {
        endDate: dto.endDate,
      });
    }
  }

  private async updateSystemFeatureActiveByEventAddCanId(
    eventAddCanId: number,
    status: EventAddCanStatus,
  ) {
    const eventAddCanWebview = await this.systemFeatureRepo
      .createQueryBuilder('systemFeature')
      .leftJoinAndSelect(
        'systemFeature.eventAddCanWebview',
        'eventAddCanWebview',
      )
      .where('eventAddCanWebview.eventAddCanId = :eventAddCanId', {
        eventAddCanId,
      })
      .getOne();

    if (!eventAddCanWebview) {
      return;
    }

    const isActive = status === EventAddCanStatus.ACTIVE;
    await this.systemFeatureRepo.update(
      { code: eventAddCanWebview.code },
      { isActive },
    );
  }
}
