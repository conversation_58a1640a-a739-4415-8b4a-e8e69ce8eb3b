import { Injectable } from '@nestjs/common';
import { EventAddCanRepository } from '../../repositories/event-add-can.repository';
import { Transactional } from 'typeorm-transactional';
import { objectify } from 'radash';
import { EnumNameAdminMenuModule } from '../../../admin_authorization/common/enums/admin-menu-module.enum';
import {
  CreateEventAddCanAdminReqDto,
  GetListEventAddCanAdminReqDto,
  UpdateEventAddCanAdminReqDto,
  EventAddCanTimeExchangeGiftReqDto,
  EventAddCanTimeExchangeGiftDetailReqDto,
} from '../../dtos/req/admin/event-add-can.admin.req.dto';
import {
  validateSetupTimeValidOnEachRange,
  validateTimeRangeOverlapInListObject,
  validateItemOverlapTimeRangeWithOtherItemInListObject,
  validateItemOverlapTimeRangeWithOtherItem,
  compareDateWithCurrentDateInTimezone,
  compareDateWithDateInTimezone,
  mergeTwoArraysRemoveDuplicating,
  sortByAttribute,
} from '../../../common/utils';
import { GiftCategoryRepository } from '../../../gift/repositories/gift-category.repository';
import { AppBaseExc } from '../../../common/exceptions/custom-app.exception';
import { StatusCode } from '../../../common/constants/status-code.constant';
import { EventAddCan } from '../../entities/event-add-can.entity';
import { In } from 'typeorm';
import {
  AppPaginationDto,
  AppResponseDto,
} from '../../../common/dtos/app-response.dto';
import { DeleteMultipleByNumberIdsDto } from '../../../common/dtos/delete-multiple.dto';
import { GiftCategory } from '../../../gift/entities/gift-category.entity';
import { EventAddCanProductRepository } from '../../repositories/event-add-can-product.repository';
import { EventAddCanProduct } from '../../entities/event-add-can-product.entity';
import { EventAddCanWebviewRepository } from '../../repositories/event-add-can-webview.repository';
import { EventAddCanPopupRepository } from '../../repositories/event-add-can-popup.repository';
import { EventCanMarkRepository } from '../../../event-can-mark/repositories/event-can-mark.repository';
import { EventAddCanTimeExchangeGiftRepository } from '../../repositories/event-add-can-time-exchange-gift.repository';
import { EventAddCanTimeExchangeGiftDetailRepository } from '../../repositories/event-add-can-time-exchange-gift-detail.repository';
import { EventAddCanTimeExchangeGift } from '../../entities/event-add-can-time-exchange-gift.entity';
import { EventAddCanTimeExchangeGiftDetail } from '../../entities/event-add-can-time-exchange-gift-detail.entity';
import { EventCanMarkAdminService } from '../../../event-can-mark/services/event-can-mark.admin.service';
import { VitaJavaService } from '../../../external/services/vita-java.service';
import { EventAddCanStatus } from '../../enums/event-add-can.enum';
import { GiftStatusEnum } from '../../../external/java-loyalty';
import { formatStringWithSuffix } from '../../../common/utils';
import { AccountData } from '../../../proto/account.pb';
import { AdminActionHistoryRepository } from '../../../admin-action-history/repositories/admin-action-history.repository';

@Injectable()
export class EventAddCanAdminService {
  constructor(
    private eventAddCanRepo: EventAddCanRepository,
    private giftCategoryRepo: GiftCategoryRepository,
    private eventAddCanProductRepo: EventAddCanProductRepository,
    private eventCanMarkRepo: EventCanMarkRepository,
    private eventAddCanWebviewRepo: EventAddCanWebviewRepository,
    private eventAddCanPopupRepo: EventAddCanPopupRepository,
    private eventCanMarkAdminService: EventCanMarkAdminService,
    private eventAddCanTimeExchangeGiftRepo: EventAddCanTimeExchangeGiftRepository,
    private eventAddCanTimeExchangeGiftDetailRepo: EventAddCanTimeExchangeGiftDetailRepository,
    private vitaJavaService: VitaJavaService,
    private readonly adminActionHistoryRepo: AdminActionHistoryRepository,
  ) {}

  async findAll(getListEventAddCanAdminReqDto: GetListEventAddCanAdminReqDto) {
    const { limit, page, eventName, endDate, startDate } =
      getListEventAddCanAdminReqDto;
    let { skus } = getListEventAddCanAdminReqDto;

    if (typeof skus === 'string') {
      skus = [skus];
    }

    const skip = (page - 1) * limit;
    const qbEntities = this.eventAddCanRepo
      .createQueryBuilder('eventAddCan')
      .leftJoinAndSelect('eventAddCan.giftCategories', 'giftCategorie')
      .leftJoinAndSelect(
        'eventAddCan.eventAddCanProducts',
        'eventAddCanProduct',
      );
    if (eventName) {
      qbEntities.where('eventAddCan.eventName like :eventName', {
        eventName: `%${eventName}%`,
      });
    }
    if (skus) {
      qbEntities.andWhere('eventAddCanProduct.sku in (:...skus)', { skus });
    }
    if (startDate) {
      qbEntities.andWhere('eventAddCan.startDate >= :startDate', {
        startDate,
      });
    }
    if (endDate) {
      qbEntities.andWhere('eventAddCan.endDate <= :endDate', { endDate });
    }
    // sort by id
    qbEntities.orderBy('eventAddCan.id', 'DESC');

    const [totalRecords, result] = await Promise.all([
      qbEntities.getCount(),
      qbEntities.take(limit).skip(skip).getMany(),
    ]);

    return AppResponseDto.fromPagination(
      result,
      AppPaginationDto.fromTotalAndPageAndSizeV1(totalRecords, page, limit),
    );
  }

  @Transactional()
  async update(
    id: number,
    dto: UpdateEventAddCanAdminReqDto,
    admin: AccountData,
  ) {
    const {
      eventName,
      endDate,
      convertPoint,
      giftCategoryIds,
      skus,
      startDate,
      status,
      type,
      convertCan400g,
      convertCan800g,
      convertBarrel110ml,
      convertBarrel180ml,
      gsGiftCategoryCodes,
      activeOverCanPopup,
      enableTimeExchangeGift,
      timeExchangeGifts,
    } = dto;

    const eventAddCan = await this.eventAddCanRepo.findOne({
      where: { id },
      relations: {
        giftCategories: true,
        eventAddCanProducts: true,
        timeExchangeGifts: { details: true },
      },
    });

    if (!eventAddCan) throw new AppBaseExc(StatusCode.EVENT_ADD_CAN_NOT_FOUND);

    // Validate setup time exchange gifts
    if (enableTimeExchangeGift) {
      this.validateTimeExchangeGiftsSetupData(timeExchangeGifts);
    }

    const updateEventAddCan: EventAddCan = {
      ...eventAddCan,
      eventName,
      endDate,
      startDate,
      status,
      convertPoint,
      type,
      convertCan400g,
      convertCan800g,
      convertBarrel110ml,
      convertBarrel180ml,
      gsGiftCategoryCodes,
      activeOverCanPopup,
      enableTimeExchangeGift,
    };

    await this.eventAddCanRepo.save(updateEventAddCan);

    // Update event add can product ( sku)
    await this.saveEventAddCanSkus(
      eventAddCan.eventAddCanProducts,
      skus,
      eventAddCan.id,
    );
    // update gift category
    const giftCategories = await this.updateGiftCategory(
      eventAddCan,
      giftCategoryIds,
    );

    // Update time exchange gift setup data
    if (enableTimeExchangeGift) {
      await this.updateSetupTimeExchangeGifts(
        eventAddCan,
        timeExchangeGifts,
        eventAddCan.timeExchangeGifts,
      );
    }

    await this.adminActionHistoryRepo.loggingUpdateAction(
      admin.email,
      EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
      eventAddCan.eventName,
    );

    return await this.getDetail(id);
  }

  @Transactional()
  async delete(id: number, admin: AccountData) {
    const eventAddCan = await this.eventAddCanRepo.findOne({
      where: { id },
      relations: { giftCategories: true },
    });

    if (!eventAddCan) throw new AppBaseExc(StatusCode.EVENT_ADD_CAN_NOT_FOUND);

    await this.eventAddCanRepo.softDelete(id);

    // update gift category
    await this.updateGiftCategory(eventAddCan, []);

    await this.adminActionHistoryRepo.loggingDeleteAction(
      admin.email,
      EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
      eventAddCan.eventName,
    );

    return new AppResponseDto();
  }

  @Transactional()
  async deleteMany(dto: DeleteMultipleByNumberIdsDto, admin: AccountData) {
    const { ids } = dto;
    const eventAddCans = await this.eventAddCanRepo.find({
      where: { id: In(ids) },
      relations: { giftCategories: true },
    });

    if (eventAddCans.length !== ids.length)
      throw new AppBaseExc(StatusCode.EVENT_ADD_CAN_NOT_FOUND);

    await this.eventAddCanRepo.softDelete(ids);

    // update gift category
    await Promise.all(
      eventAddCans.map((eventAddCan) =>
        this.updateGiftCategory(eventAddCan, []),
      ),
    );

    // Prepare batch insertion for logging
    const logs = eventAddCans.map((eventAddCan) => {
      return {
        adminName: admin.email,
        adminMenu: EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        recordIdentity: eventAddCan.eventName,
      };
    });
    await this.adminActionHistoryRepo.loggingDeleteActions(logs);

    return new AppResponseDto();
  }

  @Transactional()
  async create(dto: CreateEventAddCanAdminReqDto, admin: AccountData) {
    const {
      eventName,
      endDate,
      convertPoint,
      giftCategoryIds,
      gsGiftCategoryCodes,
      skus,
      startDate,
      status,
      type,
      convertCan400g,
      convertCan800g,
      convertBarrel110ml,
      convertBarrel180ml,
      activeOverCanPopup,
      enableTimeExchangeGift,
      timeExchangeGifts,
    } = dto;

    const exsistEventAddCan = await this.eventAddCanRepo.findOneBy({
      type: type,
    });

    if (exsistEventAddCan) {
      throw new AppBaseExc(StatusCode.EVENT_ADD_CAN_FAILED_UNIQUE_CONSTRAINT);
    }

    // Validate setup time exchange gifts
    if (enableTimeExchangeGift) {
      this.validateTimeExchangeGiftsSetupData(timeExchangeGifts);
    }

    const eventAddCan = this.eventAddCanRepo.create({
      eventName,
      endDate,
      startDate,
      status,
      convertPoint,
      type,
      convertCan400g,
      convertCan800g,
      convertBarrel110ml,
      convertBarrel180ml,
      gsGiftCategoryCodes,
      activeOverCanPopup,
      enableTimeExchangeGift,
    });

    const newEventAddCan = await this.eventAddCanRepo.save(eventAddCan);

    // update event add can product ( sku)
    await this.saveEventAddCanSkus([], skus, eventAddCan.id);
    // update gift category
    await this.updateGiftCategory(newEventAddCan, giftCategoryIds);
    // Update time exchange gift setup data
    if (enableTimeExchangeGift) {
      await this.updateSetupTimeExchangeGifts(eventAddCan, timeExchangeGifts);
    }

    /*throw new AppBaseExc(
      StatusCode.SBPS_DEFAULT_ERROR,
      'Rollback data sau khi đã hợp lệ',
      true,
    );*/

    await this.adminActionHistoryRepo.loggingCreateAction(
      admin.email,
      EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
      newEventAddCan.eventName,
    );

    return await this.getDetail(newEventAddCan.id);
  }

  async getDetail(id: number) {
    const eventAddCan = await this.eventAddCanRepo.findOne({
      where: { id },
      relations: {
        giftCategories: true,
        eventAddCanProducts: true,
        timeExchangeGifts: {
          details: true,
        },
      },
    });

    if (!eventAddCan) throw new AppBaseExc(StatusCode.EVENT_ADD_CAN_NOT_FOUND);

    return new AppResponseDto(eventAddCan);
  }

  @Transactional()
  async duplicateById(token: string, id: number, admin: AccountData) {
    const eventAddCan = await this.eventAddCanRepo.findOne({
      where: { id },
      relations: { giftCategories: true, eventAddCanProducts: true },
    });

    if (!eventAddCan) throw new AppBaseExc(StatusCode.EVENT_ADD_CAN_NOT_FOUND);

    const tempSuffix = Math.floor(new Date().getTime() / 1000);
    const newEventAddCan = await this.create(
      {
        eventName: eventAddCan.eventName,
        endDate: eventAddCan.endDate,
        startDate: eventAddCan.startDate,
        convertPoint: eventAddCan.convertPoint,
        type: formatStringWithSuffix(
          eventAddCan.type,
          tempSuffix, // temporary suffix
        ),
        convertCan400g: eventAddCan.convertCan400g,
        convertCan800g: eventAddCan.convertCan800g,
        convertBarrel110ml: eventAddCan.convertBarrel110ml,
        convertBarrel180ml: eventAddCan.convertBarrel180ml,
        skus: eventAddCan.eventAddCanProducts?.map((product) => product.sku),
        status: EventAddCanStatus.INACTIVE,
        enableTimeExchangeGift: eventAddCan.enableTimeExchangeGift,
      },
      admin,
    );

    const newEventAddCanId = newEventAddCan.response.id;
    const suffix = `${newEventAddCanId}`;

    // Create new gsGiftCategoryCodes
    const gsGiftCategoryCodes = eventAddCan.gsGiftCategoryCodes;
    const newGsGiftCategories = await Promise.all(
      gsGiftCategoryCodes.map(async (code) => {
        const giftCategory = await this.vitaJavaService.getGiftCategoryByCode(
          token,
          code,
        );

        return await this.vitaJavaService.createGiftCategory(token, {
          ...giftCategory,
          code: formatStringWithSuffix(giftCategory.code, suffix),
          name: formatStringWithSuffix(giftCategory.name, suffix),
          isActive: false,
        });
      }),
    );
    // Update with new gift category
    const eventAddCanUpdated = await this.eventAddCanRepo.update(
      newEventAddCanId,
      {
        eventName: formatStringWithSuffix(eventAddCan.eventName, suffix),
        type: formatStringWithSuffix(eventAddCan.type, suffix),
        gsGiftCategoryCodes: newGsGiftCategories.map((gc) => gc.code),
      },
    );

    await Promise.all([
      this.duplicateEventCanMark(
        token,
        eventAddCan.id,
        newEventAddCanId,
        suffix,
      ),
      this.duplicateEventAddCanPopup(eventAddCan.id, newEventAddCanId, suffix),
      this.duplicateEventAddCanWebview(
        eventAddCan.id,
        newEventAddCanId,
        suffix,
      ),
    ]);

    return eventAddCanUpdated;
  }

  private async updateGiftCategory(
    eventAddCan: EventAddCan,
    giftCategoryIds: number[],
  ) {
    const giftCategoriesToUpdate: GiftCategory[] = [];
    const existedGiftCategories = eventAddCan.giftCategories;
    const giftCategoryIdsInReq = giftCategoryIds;

    if (!giftCategoryIds || !giftCategoryIds.length) {
      return [];
    }

    const giftCategories = await this.giftCategoryRepo
      .createQueryBuilder('giftCategory')
      .where('giftCategory.id IN (:...ids)', { ids: giftCategoryIdsInReq })
      .andWhere(
        '(giftCategory.eventAddCanId IS NULL OR giftCategory.eventAddCanId = :eventAddCanId)',
        { eventAddCanId: eventAddCan.id },
      )
      .getMany();

    if (giftCategories.length !== giftCategoryIds.length)
      throw new AppBaseExc(StatusCode.GIFT_CATEGORY_NOT_VALID);

    const giftCategoriesNeedToUpdateEventAddCanId = giftCategories.map(
      (giftCategory) => ({
        ...giftCategory,
        eventAddCanId: eventAddCan.id,
      }),
    );

    giftCategoriesToUpdate.push(...giftCategoriesNeedToUpdateEventAddCanId);

    if (existedGiftCategories && existedGiftCategories.length > 0) {
      const giftCategoryIdsToSetEventAddCanIdToNull =
        existedGiftCategories.filter(
          (giftCategory) => !giftCategoryIdsInReq.includes(giftCategory.id),
        );

      const giftCategoriesToSetEventAddCanIdToNull =
        giftCategoryIdsToSetEventAddCanIdToNull.map((giftCategory) => ({
          ...giftCategory,
          eventAddCanId: null,
        }));

      giftCategoriesToUpdate.push(...giftCategoriesToSetEventAddCanIdToNull);
    }
    await this.giftCategoryRepo.save(giftCategoriesToUpdate);

    return giftCategoriesNeedToUpdateEventAddCanId;
  }

  private async saveEventAddCanSkus(
    eventAddCanProducts: EventAddCanProduct[],
    skus: string[],
    eventAddCanId: number,
  ) {
    const eventAddCanProductIdsToDelete: number[] = [];
    const eventAddCanProductSkusToAdd: EventAddCanProduct[] = [];

    eventAddCanProducts.forEach((eventAddCanProduct) => {
      if (!skus.includes(eventAddCanProduct.sku))
        eventAddCanProductIdsToDelete.push(eventAddCanProduct.id);
    });

    skus.forEach((sku) => {
      if (
        !eventAddCanProducts.some(
          (eventAddCanProduct) => eventAddCanProduct.sku === sku,
        )
      ) {
        eventAddCanProductSkusToAdd.push(
          this.eventAddCanProductRepo.create({
            eventAddCanId,
            sku,
          }),
        );
      }
    });

    await Promise.all([
      eventAddCanProductIdsToDelete.length &&
        this.eventAddCanProductRepo.delete(eventAddCanProductIdsToDelete),
      this.eventAddCanProductRepo.save(eventAddCanProductSkusToAdd),
    ]);
  }

  private async duplicateEventCanMark(
    token: string,
    eventAddCanId: number,
    newEventAddCanId: number,
    suffix: string | number,
  ) {
    // get list event can mark eventAddCanId
    const eventCanMarks = await this.eventCanMarkRepo.find({
      where: { eventAddCanId },
    });
    if (!eventCanMarks || !eventCanMarks.length) return;

    await Promise.all(
      eventCanMarks.map(async (eventCanMark) => {
        // get current gift
        const currentGift = await this.vitaJavaService.getGiftById(
          token,
          eventCanMark.gsGiftId,
        );
        // create new gift
        const newGift = await this.vitaJavaService.createGift(token, {
          ...currentGift,
          name: formatStringWithSuffix(currentGift.name, suffix),
          categoryCode: formatStringWithSuffix(
            currentGift.categoryCode,
            suffix,
          ),
          status: GiftStatusEnum.DISABLED,
        });
        // create new eventCanMark
        const dto = {
          ...eventCanMark,
          eventAddCanId: newEventAddCanId,
          userType: eventCanMark.typeUser,
          gsGiftId: newGift.id,
        };
        return await this.eventCanMarkAdminService.createEventCanMark(
          token,
          dto,
          null,
        );
      }),
    );
  }

  private async duplicateEventAddCanPopup(
    eventAddCanId: number,
    newEventAddCanId: number,
    suffix: string | number,
  ) {
    // get list popup by eventAddCanId
    const eventAddCanPopups = await this.eventAddCanPopupRepo.find({
      where: { eventAddCanId },
    });

    if (!eventAddCanPopups || !eventAddCanPopups.length) return;

    // create new popup for each popup
    const newEventAddCanPopups = eventAddCanPopups.map((popup) => {
      const newCode = formatStringWithSuffix(popup.code, suffix);
      const newPopup = this.eventAddCanPopupRepo.create({
        ...popup,
        code: newCode,
        eventAddCanId: newEventAddCanId,
      });
      return newPopup;
    });

    // save new popups
    await this.eventAddCanPopupRepo.insert(newEventAddCanPopups);
  }

  private async duplicateEventAddCanWebview(
    eventAddCanId: number,
    newEventAddCanId: number,
    suffix: string | number,
  ) {
    // get webview by eventAddCanId
    const eventAddCanWebview = await this.eventAddCanWebviewRepo.findOne({
      where: { eventAddCanId },
    });

    if (!eventAddCanWebview) return;

    // create new webview
    const newEventAddCanWebview = this.eventAddCanWebviewRepo.create({
      ...eventAddCanWebview,
      linkWebview: formatStringWithSuffix(
        eventAddCanWebview.linkWebview,
        suffix,
      ),
      eventAddCanId: newEventAddCanId,
    });

    // save new webview
    await this.eventAddCanWebviewRepo.insert(newEventAddCanWebview);
  }

  private async updateSetupTimeExchangeGifts(
    eventAddCan: EventAddCan,
    newTimeExchangeGifts: EventAddCanTimeExchangeGiftReqDto[],
    oldTimeExchangeGifts: EventAddCanTimeExchangeGift[] = [],
  ) {
    if (!eventAddCan || !newTimeExchangeGifts || !newTimeExchangeGifts.length) {
      return;
    }

    // if (!eventAddCan.enableTimeExchangeGift) {
    //   return;
    // }

    // Validate updating old setup
    this.validateUpdatingTimeExchangeGiftsSetupData(
      newTimeExchangeGifts,
      oldTimeExchangeGifts,
    );

    const timeExchangeGiftIdsToRemove: number[] = [];
    const timeExchangeGiftDetailIdsToRemove: number[] = [];
    const timeExchangeGiftIdsToSave: EventAddCanTimeExchangeGift[] = [];
    const timeExchangeGiftDetailIdsToSave: EventAddCanTimeExchangeGiftDetail[] =
      [];
    const promisesUpdateData: any[] = [];

    if (oldTimeExchangeGifts && oldTimeExchangeGifts.length) {
      for (const oldTimeExchangeGift of oldTimeExchangeGifts) {
        if (
          !newTimeExchangeGifts.some(
            (item) => item.id === Number(oldTimeExchangeGift.id),
          )
        ) {
          timeExchangeGiftIdsToRemove.push(Number(oldTimeExchangeGift.id));
        }
      }
    }
    if (timeExchangeGiftIdsToRemove.length) {
      promisesUpdateData.push(
        this.eventAddCanTimeExchangeGiftDetailRepo.delete({
          eventAddCanTimeExchangeGiftId: In(timeExchangeGiftIdsToRemove),
        }),
        // this.eventAddCanTimeExchangeGiftRepo.delete({
        //   id: In(timeExchangeGiftIdsToRemove),
        // }),
      );
    }

    const mapOldTimeExchangeGiftsById = objectify(
      oldTimeExchangeGifts,
      (f) => f.id,
    );
    for (const newTimeExchangeGift of newTimeExchangeGifts) {
      if (mapOldTimeExchangeGiftsById.hasOwnProperty(newTimeExchangeGift.id)) {
        const oldTimeExchangeGift: EventAddCanTimeExchangeGift =
          mapOldTimeExchangeGiftsById[newTimeExchangeGift.id];
        if (oldTimeExchangeGift.details.length) {
          for (const oldTimeExchangeGiftDetail of oldTimeExchangeGift.details) {
            if (
              !newTimeExchangeGift.details.some(
                (item) => item.id === Number(oldTimeExchangeGiftDetail.id),
              )
            ) {
              timeExchangeGiftDetailIdsToRemove.push(
                Number(oldTimeExchangeGiftDetail.id),
              );
            }
          }
        }
      }
    }
    if (timeExchangeGiftDetailIdsToRemove.length) {
      promisesUpdateData.push(
        this.eventAddCanTimeExchangeGiftDetailRepo.delete({
          id: In(timeExchangeGiftDetailIdsToRemove),
        }),
      );
    }

    if (promisesUpdateData.length) {
      await Promise.all(promisesUpdateData);
    }

    if (timeExchangeGiftIdsToRemove.length) {
      await this.eventAddCanTimeExchangeGiftRepo.delete({
        id: In(timeExchangeGiftIdsToRemove),
      });
    }

    await this.eventAddCanTimeExchangeGiftRepo.save(
      newTimeExchangeGifts.map((item) => {
        return {
          ...item,
          eventAddCanId: eventAddCan.id,
        };
      }),
    );
  }

  private validateUpdatingTimeExchangeGiftsSetupData(
    newTimeExchangeGifts: EventAddCanTimeExchangeGiftReqDto[],
    oldTimeExchangeGifts: EventAddCanTimeExchangeGift[] = [],
  ) {
    if (!newTimeExchangeGifts || !newTimeExchangeGifts.length) {
      throw new AppBaseExc(
        StatusCode.SBPS_DEFAULT_ERROR,
        'Không cho phép cập nhật thời gian trúng giải. Dữ liệu rỗng',
        true,
      );
    }

    // Inserting new data
    const insertingNewData =
      !oldTimeExchangeGifts || !oldTimeExchangeGifts.length;
    const mapOldTimeExchangeGiftsById = objectify(
      oldTimeExchangeGifts,
      (f) => f.id,
    );
    for (const newTimeExchangeGift of newTimeExchangeGifts) {
      const oldTimeExchangeGift: EventAddCanTimeExchangeGift =
        mapOldTimeExchangeGiftsById.hasOwnProperty(newTimeExchangeGift.id)
          ? mapOldTimeExchangeGiftsById[newTimeExchangeGift.id]
          : null;
      let updatingTimeExchangeGiftIsHappening = false;
      // Inserting
      if (insertingNewData) {
        if (newTimeExchangeGift.id) {
          throw new AppBaseExc(
            StatusCode.SBPS_DEFAULT_ERROR,
            'Không cho phép tạo thời gian trúng giải. Tạo mới nhưng set id',
            true,
          );
        }
      } else {
        // Updating
        if (newTimeExchangeGift.id) {
          if (!oldTimeExchangeGift) {
            throw new AppBaseExc(
              StatusCode.SBPS_DEFAULT_ERROR,
              'Không cho phép cập nhật thời gian trúng giải. Cập nhật sai id',
              true,
            );
          }

          // Updating time exchange gift when it is happening
          updatingTimeExchangeGiftIsHappening =
            compareDateWithCurrentDateInTimezone(
              newTimeExchangeGift.startDate,
            ) != 1;
          if (updatingTimeExchangeGiftIsHappening) {
            const updatingStartDate =
              compareDateWithDateInTimezone(
                oldTimeExchangeGift.startDate,
                newTimeExchangeGift.startDate,
              ) != 0;
            const updatingEndDate =
              compareDateWithDateInTimezone(
                oldTimeExchangeGift.endDate,
                newTimeExchangeGift.endDate,
              ) != 0;
            if (updatingStartDate || updatingEndDate) {
              throw new AppBaseExc(
                StatusCode.SBPS_DEFAULT_ERROR,
                'Không cho phép cập nhật thời gian trúng giải. Cập nhật khung thời gian đang sử dụng',
                true,
              );
            }
            const updatingDetail =
              oldTimeExchangeGift.details.length !=
              newTimeExchangeGift.details.length;
            if (updatingDetail) {
              throw new AppBaseExc(
                StatusCode.SBPS_DEFAULT_ERROR,
                'Không cho phép cập nhật thời gian trúng giải. Cập nhật khung thời gian đang sử dụng',
                true,
              );
            }
          }
        }
      }
      this.validateUpdatingTimeExchangeGiftDetailsSetupData(
        updatingTimeExchangeGiftIsHappening,
        newTimeExchangeGift.details,
        oldTimeExchangeGift?.details,
      );
    }
  }

  private validateUpdatingTimeExchangeGiftDetailsSetupData(
    updatingTimeExchangeGiftIsHappening: boolean,
    newTimeExchangeGiftDetails: EventAddCanTimeExchangeGiftDetailReqDto[],
    oldTimeExchangeGiftDetails: EventAddCanTimeExchangeGiftDetail[] = [],
  ) {
    if (!newTimeExchangeGiftDetails || !newTimeExchangeGiftDetails.length) {
      throw new AppBaseExc(
        StatusCode.SBPS_DEFAULT_ERROR,
        'Không cho phép cập nhật chi tiết trúng giải. Dữ liệu rỗng',
        true,
      );
    }

    // Inserting new data
    const insertingNewData =
      !oldTimeExchangeGiftDetails || !oldTimeExchangeGiftDetails.length;
    const mapOldTimeExchangeGiftDetailsById = objectify(
      oldTimeExchangeGiftDetails,
      (f) => f.id,
    );
    for (const newTimeExchangeGiftDetail of newTimeExchangeGiftDetails) {
      // Inserting
      if (insertingNewData) {
        if (newTimeExchangeGiftDetail.id) {
          throw new AppBaseExc(
            StatusCode.SBPS_DEFAULT_ERROR,
            'Không cho phép tạo chi tiết trúng giải. Tạo mới nhưng set id',
            true,
          );
        }
      } else {
        // Updating
        if (newTimeExchangeGiftDetail.id) {
          if (
            !mapOldTimeExchangeGiftDetailsById.hasOwnProperty(
              newTimeExchangeGiftDetail.id,
            )
          ) {
            throw new AppBaseExc(
              StatusCode.SBPS_DEFAULT_ERROR,
              'Không cho phép cập nhật chi tiết trúng giải. Cập nhật sai id',
              true,
            );
          }

          const oldTimeExchangeGiftDetail: EventAddCanTimeExchangeGiftDetail =
            mapOldTimeExchangeGiftDetailsById[newTimeExchangeGiftDetail.id];
          // Updating time exchange gift when it is happening
          const updatingTotal =
            oldTimeExchangeGiftDetail.total != newTimeExchangeGiftDetail.total;
          let updatingGiftIds =
            oldTimeExchangeGiftDetail.giftIds.length !=
            newTimeExchangeGiftDetail.giftIds.length;
          if (!updatingGiftIds) {
            for (const giftId of newTimeExchangeGiftDetail.giftIds) {
              if (!oldTimeExchangeGiftDetail.giftIds.includes(giftId)) {
                updatingGiftIds = true;
                break;
              }
            }
          }
          if (updatingGiftIds || updatingTotal) {
            if (updatingTimeExchangeGiftIsHappening) {
              throw new AppBaseExc(
                StatusCode.SBPS_DEFAULT_ERROR,
                'Không cho phép cập nhật chi tiết trúng giải. Cập nhật khung thời gian đang sử dụng',
                true,
              );
            }
          }
        }
      }
    }
    // TODO: did not create unique index on table detail to make sure
    // TODO: time exchange gift and gift ids not duplicate
    // TODO: event_add_can_time_exchange_gift_id 1 and gift ids [1] (id 1)
    // TODO: can create event_add_can_time_exchange_gift_id 1 and gift ids [1] again
  }

  private validateTimeExchangeGiftsSetupData(
    timeExchangeGifts: EventAddCanTimeExchangeGiftReqDto[],
  ) {
    if (!timeExchangeGifts || !timeExchangeGifts.length) {
      throw new AppBaseExc(
        StatusCode.SBPS_DEFAULT_ERROR,
        'Cài đặt thời gian trúng giải không hợp lệ. Dữ liệu rỗng',
        true,
      );
    }

    // Validate time exchange gift
    let uniqueIds: number[] = [];
    let countOrgIds = 0;
    const orderedTimeExchangeGiftsSortByStartDate: EventAddCanTimeExchangeGiftReqDto[] =
      sortByAttribute(timeExchangeGifts, 'startDate');
    for (let i = 0; i < orderedTimeExchangeGiftsSortByStartDate.length; i++) {
      const timeExchangeGift = orderedTimeExchangeGiftsSortByStartDate[i];

      if (timeExchangeGift.id) {
        countOrgIds++;
        uniqueIds = mergeTwoArraysRemoveDuplicating(uniqueIds, [
          timeExchangeGift.id,
        ]);
      }
      // Validate startDate endDate > now
      // Only validate if create new
      if (
        !timeExchangeGift.id &&
        (compareDateWithCurrentDateInTimezone(timeExchangeGift.startDate) !=
          1 ||
          compareDateWithCurrentDateInTimezone(timeExchangeGift.endDate) != 1)
      ) {
        throw new AppBaseExc(
          StatusCode.SBPS_DEFAULT_ERROR,
          'Cài đặt thời gian trúng giải không hợp lệ. Dữ liệu start date, end date < now',
          true,
        );
      }

      // Validate startDate < endDate
      if (
        !validateSetupTimeValidOnEachRange(
          timeExchangeGift,
          'startDate',
          'endDate',
        )
      ) {
        throw new AppBaseExc(
          StatusCode.SBPS_DEFAULT_ERROR,
          'Cài đặt thời gian trúng giải không hợp lệ. Dữ liệu start date >= end date',
          true,
        );
      }

      // Validate startDate,endDate of each range is not overlap
      // if (
      //   validateItemOverlapTimeRangeWithOtherItemInListObject(
      //     timeExchangeGift,
      //     timeExchangeGifts,
      //     'startDate',
      //     'endDate',
      //   )
      // ) {
      //   throw new AppBaseExc(
      //     StatusCode.SBPS_DEFAULT_ERROR,
      //     'Cài đặt thời gian trúng giải không hợp lệ. Dữ liệu start date, end date bị trùng lặp',
      //     true,
      //   );
      // }

      // Validate detail
      let uniqueDetailIds: number[] = [];
      let countOrgDetailIds = 0;
      let uniqueGiftIdsInEachDetail: number[] = [];
      let countOrgGiftIdsInEachDetail = 0;
      for (const detail of timeExchangeGift.details) {
        if (detail.id) {
          countOrgDetailIds++;
          uniqueDetailIds = mergeTwoArraysRemoveDuplicating(uniqueDetailIds, [
            detail.id,
          ]);
        }
        countOrgGiftIdsInEachDetail += detail.giftIds.length;
        uniqueGiftIdsInEachDetail = mergeTwoArraysRemoveDuplicating(
          uniqueGiftIdsInEachDetail,
          detail.giftIds,
        );
      }
      // Check duplicate detail id
      if (countOrgDetailIds != uniqueDetailIds.length) {
        throw new AppBaseExc(
          StatusCode.SBPS_DEFAULT_ERROR,
          'Cài đặt chi tiết trúng giải không hợp lệ. Dữ liệu detail id bị trùng lặp',
          true,
        );
      }
      // Check duplicate gift id
      if (countOrgGiftIdsInEachDetail != uniqueGiftIdsInEachDetail.length) {
        throw new AppBaseExc(
          StatusCode.SBPS_DEFAULT_ERROR,
          'Cài đặt chi tiết trúng giải không hợp lệ. Dữ liệu gift id bị trùng lặp',
          true,
        );
      }

      // console.log(timeExchangeGift);
      let uniqueGiftIdsWhenOverlapTime: number[] = [];
      let countOrgGiftIdsWhenOverlapTime = 0;
      for (
        let j = i + 1;
        j < orderedTimeExchangeGiftsSortByStartDate.length;
        j++
      ) {
        const otherTimeExchangeGift =
          orderedTimeExchangeGiftsSortByStartDate[j];
        // console.log(otherTimeExchangeGift);
        // console.log(
        //   compareDateWithDateInTimezone(
        //     timeExchangeGift.startDate,
        //     otherTimeExchangeGift.startDate,
        //   ),
        // );

        // Stop early if otherTimeExchangeGift starts after timeExchangeGift ends (no possible overlap)
        if (
          compareDateWithDateInTimezone(
            timeExchangeGift.startDate,
            otherTimeExchangeGift.startDate,
          ) == 1
        ) {
          break;
        }

        if (
          validateItemOverlapTimeRangeWithOtherItem(
            timeExchangeGift,
            otherTimeExchangeGift,
            'startDate',
            'endDate',
          )
        ) {
          for (const detail1 of timeExchangeGift.details) {
            countOrgGiftIdsWhenOverlapTime += detail1.giftIds.length;
            uniqueGiftIdsWhenOverlapTime = mergeTwoArraysRemoveDuplicating(
              uniqueGiftIdsWhenOverlapTime,
              detail1.giftIds,
            );
          }
          for (const detail2 of otherTimeExchangeGift.details) {
            countOrgGiftIdsWhenOverlapTime += detail2.giftIds.length;
            uniqueGiftIdsWhenOverlapTime = mergeTwoArraysRemoveDuplicating(
              uniqueGiftIdsWhenOverlapTime,
              detail2.giftIds,
            );
          }

          // Check duplicate gift id
          if (
            countOrgGiftIdsWhenOverlapTime !=
            uniqueGiftIdsWhenOverlapTime.length
          ) {
            throw new AppBaseExc(
              StatusCode.SBPS_DEFAULT_ERROR,
              'Cài đặt chi tiết trúng giải không hợp lệ. Dữ liệu gift id bị trùng lặp',
              true,
            );
          }
        }
      }
    }

    // Check duplicate detail id
    if (countOrgIds != uniqueIds.length) {
      throw new AppBaseExc(
        StatusCode.SBPS_DEFAULT_ERROR,
        'Cài đặt thời gian trúng giải không hợp lệ. Dữ liệu id bị trùng lặp',
        true,
      );
    }
  }
}
