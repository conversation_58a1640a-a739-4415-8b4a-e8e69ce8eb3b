import { Injectable } from '@nestjs/common';
import { paginate } from 'nestjs-typeorm-paginate';
import { Transactional } from 'typeorm-transactional';
import { AdminActionHistoryRepository } from '../../../admin-action-history/repositories/admin-action-history.repository';
import { EnumNameAdminMenuModule } from '../../../admin_authorization/common/enums/admin-menu-module.enum';
import { AppResponseDto } from '../../../common/dtos/app-response.dto';
import {
  ConflictExc,
  NotFoundExc,
} from '../../../common/exceptions/custom-http.exception';
import { AccountData } from '../../../proto/account.pb';
import { CreateSystemFeatureAdminReqDto } from '../../../system-feature/dtos/req/create-system-feature.admin.req.dto';
import { UpdateSystemFeatureAdminReqDto } from '../../../system-feature/dtos/req/update-system-feature.admin.req.dto';
import {
  SystemFeatureAttrCode,
  SystemFeatureGroup,
  SystemFeatureSource,
} from '../../../system-feature/enums/system-feature.enum';
import { SystemFeatureAdminService } from '../../../system-feature/services/system-feature.admin.service';
import {
  CreateEventAddCanWebviewAdminReqDto,
  GetListEventAddCanWebviewAdminReqDto,
  UpdateEventAddCanWebviewAdminReqDto,
} from '../../dtos/req/admin/event-add-can-webview.admin.req.dto';
import { EventAddCanWebviewRepository } from '../../repositories/event-add-can-webview.repository';
import { EventAddCanRepository } from '../../repositories/event-add-can.repository';
import { EventAddCanTabStatus, EventAddCanTabStatusKeys } from '../../enums/event-add-can.enum';

@Injectable()
export class EventAddCanWebviewAdminService {
  constructor(
    private readonly eventAddCanWebviewRepo: EventAddCanWebviewRepository,
    private readonly adminActionHistoryRepo: AdminActionHistoryRepository,
    private readonly eventAddCanRepo: EventAddCanRepository,
    private readonly systemFeatureService: SystemFeatureAdminService,
  ) {}
  async getListEventAddCanWebview(dto: GetListEventAddCanWebviewAdminReqDto) {
    const { page, limit, eventAddCanId, eventType } = dto;
    const queryBuilder =
      this.eventAddCanWebviewRepo.createQueryBuilder('eventAddCanWebview');
    queryBuilder
      .leftJoin('eventAddCanWebview.eventAddCan', 'eventAddCan')
      .addSelect([
        'eventAddCan.id',
        'eventAddCan.eventName',
        'eventAddCan.type',
      ]);
    queryBuilder.leftJoinAndSelect('eventAddCanWebview.file', 'file');
    queryBuilder.leftJoinAndSelect('eventAddCanWebview.systemFeature', 'systemFeature');

    if (eventAddCanId) {
      queryBuilder.andWhere('eventAddCan.id = :eventAddCanId', {
        eventAddCanId,
      });
    }

    if (eventType) {
      queryBuilder.andWhere('eventAddCan.type = :eventType', {
        eventType,
      });
    }

    queryBuilder.orderBy('eventAddCanWebview.createdAt', 'DESC');
    const paginateResult = await paginate(queryBuilder, {
      limit,
      page,
    });

    return paginateResult;
  }

  async getEventAddCanWebviewById(id: number) {
    const eventAddCanWebview = await this.eventAddCanWebviewRepo.findOne({
      where: { id },
      relations: ['eventAddCan', 'systemFeature'],
    });
    if (!eventAddCanWebview) {
      throw new NotFoundExc('Event Add Can Webview not found');
    }
    return new AppResponseDto(eventAddCanWebview);
  }

  @Transactional()
  async createEventAddCanWebview(
    dto: CreateEventAddCanWebviewAdminReqDto,
    admin: AccountData,
  ) {
    // check eventAddCanId is available
    const eventAddCanWebView = await this.eventAddCanWebviewRepo.findOne({
      where: { eventAddCanId: dto.eventAddCanId },
    });
    if (eventAddCanWebView) {
      throw new ConflictExc('Event Add Can Web View đã tồn tại');
    }

    // Create and save EventAddCanWebview first to get the ID
    const newEventAddCanWebview = this.eventAddCanWebviewRepo.create(dto);
    await this.eventAddCanWebviewRepo.save(newEventAddCanWebview);

    // Get EventAddCan info
    const eventAddCan = await this.eventAddCanRepo.findOneBy({
      id: dto.eventAddCanId,
    });

    const systemFeatureAttributes: CreateSystemFeatureAdminReqDto['attributes'] =
      [
        { code: SystemFeatureAttrCode.WHEEL_ICON, value: dto.fileUrl },
        { code: SystemFeatureAttrCode.WHEEL_URL, value: dto.linkWebview },
      ];

    // Create SystemFeature with the relationship
    const newSystemFeatureDTO: CreateSystemFeatureAdminReqDto = {
      isActive: true,
      code: dto.code,
      description: dto.description,
      priority: 1,
      group: SystemFeatureGroup.WHEEL,
      source: SystemFeatureSource.RA,
      eventAddCanWebviewId: newEventAddCanWebview.id,
      attributes: systemFeatureAttributes,
    };

    await Promise.all([
      this.adminActionHistoryRepo.loggingCreateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        eventAddCan.eventName,
      ),
      this.systemFeatureService.createSystemFeature(newSystemFeatureDTO, admin),
    ]);

    return new AppResponseDto(newEventAddCanWebview);
  }

  @Transactional()
  async updateEventAddCanWebview(
    id: number,
    dto: UpdateEventAddCanWebviewAdminReqDto,
    admin: AccountData,
  ) {
    // Check eventAddCanWebview is available
    const [eventAddCanWebview, eventAddCan] = await Promise.all([
      this.eventAddCanWebviewRepo.findOne({
        where: { id },
        relations: ['systemFeature'],
      }),
      this.eventAddCanRepo.findOneByOrFail({ id: dto.eventAddCanId }),
    ]);
    if (!eventAddCanWebview) {
      throw new NotFoundExc('Event Add Can Webview not found');
    }
    // Update eventAddCanWebview
    this.eventAddCanWebviewRepo.merge(eventAddCanWebview, dto);

    await Promise.all([
      this.eventAddCanWebviewRepo.save(eventAddCanWebview),
      this.adminActionHistoryRepo.loggingUpdateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        eventAddCan.eventName,
      ),
    ]);

    if (eventAddCanWebview.systemFeature) {
      const { code } = eventAddCanWebview.systemFeature;
      const systemFeatureAttributes: CreateSystemFeatureAdminReqDto['attributes'] =
        [
          { code: SystemFeatureAttrCode.WHEEL_ICON, value: dto.fileUrl },
          { code: SystemFeatureAttrCode.WHEEL_URL, value: dto.linkWebview },
        ];

      const updateSystemFeatureDTO: UpdateSystemFeatureAdminReqDto = {
        description: dto.description,
        attributes: systemFeatureAttributes,
      };

      await this.systemFeatureService.updateSystemFeature(
        code,
        updateSystemFeatureDTO,
        admin,
        true, // skipEventStatusCheck: true
      );
    }

    return new AppResponseDto(eventAddCanWebview);
  }

  @Transactional()
  async deleteEventAddCanWebview(id: number, admin: AccountData) {
    const eventAddCanWebview = await this.eventAddCanWebviewRepo.findOne({
      where: { id },
      relations: ['eventAddCan'],
    });
    if (!eventAddCanWebview) {
      throw new NotFoundExc('Event Add Can Webview not found');
    }

    const eventAddCanId = eventAddCanWebview.eventAddCanId;

    await Promise.all([
      this.adminActionHistoryRepo.loggingDeleteAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        eventAddCanWebview.eventAddCan.eventName,
      ),
      this.eventAddCanWebviewRepo.remove(eventAddCanWebview),
    ]);

    const eventAddCan = await this.eventAddCanRepo.findOneByOrFail({
      id: eventAddCanId,
    });

    eventAddCan.setupState = {
      key: EventAddCanTabStatusKeys.WEBVIEW,
      value: EventAddCanTabStatus.PENDING,
    };
    await this.eventAddCanRepo.save(eventAddCan);
  }
}
