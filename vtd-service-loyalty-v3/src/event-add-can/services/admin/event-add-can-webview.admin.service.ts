import { Injectable } from '@nestjs/common';
import { paginate } from 'nestjs-typeorm-paginate';
import { AppResponseDto } from '../../../common/dtos/app-response.dto';
import {
  ConflictExc,
  NotFoundExc,
} from '../../../common/exceptions/custom-http.exception';
import {
  CreateEventAddCanWebviewAdminReqDto,
  GetListEventAddCanWebviewAdminReqDto,
  UpdateEventAddCanWebviewAdminReqDto,
} from '../../dtos/req/admin/event-add-can-webview.admin.req.dto';
import { EventAddCanWebviewRepository } from '../../repositories/event-add-can-webview.repository';
import { EventAddCanRepository } from '../../repositories/event-add-can.repository';
import { AdminActionHistoryRepository } from '../../../admin-action-history/repositories/admin-action-history.repository';
import { AccountData } from '../../../proto/account.pb';
import { EnumNameAdminMenuModule } from '../../../admin_authorization/common/enums/admin-menu-module.enum';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class EventAddCanWebviewAdminService {
  constructor(
    private readonly eventAddCanWebviewRepo: EventAddCanWebviewRepository,
    private readonly adminActionHistoryRepo: AdminActionHistoryRepository,
    private readonly eventAddCanRepo: EventAddCanRepository,
  ) {}
  async getListEventAddCanWebview(dto: GetListEventAddCanWebviewAdminReqDto) {
    const { page, limit, eventAddCanId, eventType } = dto;
    const queryBuilder =
      this.eventAddCanWebviewRepo.createQueryBuilder('eventAddCanWebview');
    queryBuilder
      .leftJoin('eventAddCanWebview.eventAddCan', 'eventAddCan')
      .addSelect([
        'eventAddCan.id',
        'eventAddCan.eventName',
        'eventAddCan.type',
      ]);
    queryBuilder.leftJoinAndSelect('eventAddCanWebview.file', 'file');

    if (eventAddCanId) {
      queryBuilder.andWhere('eventAddCan.id = :eventAddCanId', {
        eventAddCanId,
      });
    }

    if (eventType) {
      queryBuilder.andWhere('eventAddCan.type = :eventType', {
        eventType,
      });
    }

    queryBuilder.orderBy('eventAddCanWebview.createdAt', 'DESC');
    const { items, meta } = await paginate(queryBuilder, {
      limit,
      page,
    });
    return AppResponseDto.fromNestJsPagination(items, meta);
  }

  async getEventAddCanWebviewById(id: number) {
    const eventAddCanWebview = await this.eventAddCanWebviewRepo.findOne({
      where: { id },
      relations: ['eventAddCan'],
    });
    if (!eventAddCanWebview) {
      throw new NotFoundExc('Event Add Can Webview not found');
    }
    return new AppResponseDto(eventAddCanWebview);
  }

  @Transactional()
  async createEventAddCanWebview(
    dto: CreateEventAddCanWebviewAdminReqDto,
    admin: AccountData,
  ) {
    // check eventAddCanId is available
    const eventAddCanWebView = await this.eventAddCanWebviewRepo.findOne({
      where: { eventAddCanId: dto.eventAddCanId },
    });
    if (eventAddCanWebView) {
      throw new ConflictExc('Event Add Can Web View is already exist');
    }
    const newEventAddCanWebview = this.eventAddCanWebviewRepo.create(dto);

    const [eventAddCan] = await Promise.all([
      this.eventAddCanRepo.findOneBy({
        id: dto.eventAddCanId,
      }),
      this.eventAddCanWebviewRepo.save(newEventAddCanWebview),
    ]);

    await this.adminActionHistoryRepo.loggingCreateAction(
      admin.email,
      EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
      eventAddCan.eventName,
    );

    return new AppResponseDto(newEventAddCanWebview);
  }

  @Transactional()
  async updateEventAddCanWebview(
    id: number,
    dto: UpdateEventAddCanWebviewAdminReqDto,
    admin: AccountData,
  ) {
    // Check eventAddCanWebview is available
    const [eventAddCanWebview, eventAddCan] = await Promise.all([
      this.eventAddCanWebviewRepo.findOne({
        where: { id },
      }),
      this.eventAddCanRepo.findOneByOrFail({ id: dto.eventAddCanId }),
    ]);
    if (!eventAddCanWebview) {
      throw new NotFoundExc('Event Add Can Webview not found');
    }
    // Update eventAddCanWebview
    this.eventAddCanWebviewRepo.merge(eventAddCanWebview, dto);
    await Promise.all([
      this.eventAddCanWebviewRepo.save(eventAddCanWebview),
      this.adminActionHistoryRepo.loggingUpdateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        eventAddCan.eventName,
      ),
    ]);
    return new AppResponseDto(eventAddCanWebview);
  }

  @Transactional()
  async deleteEventAddCanWebview(id: number, admin: AccountData) {
    const eventAddCanWebview = await this.eventAddCanWebviewRepo.findOne({
      where: { id },
      relations: ['eventAddCan'],
    });
    if (!eventAddCanWebview) {
      throw new NotFoundExc('Event Add Can Webview not found');
    }
    await Promise.all([
      this.adminActionHistoryRepo.loggingUpdateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        eventAddCanWebview.eventAddCan.eventName,
      ),
      this.eventAddCanWebviewRepo.remove(eventAddCanWebview),
    ]);
  }
}
