import { Injectable } from '@nestjs/common';
import { paginate } from 'nestjs-typeorm-paginate';
import { AppResponseDto } from '../../../common/dtos/app-response.dto';
import {
  ConflictExc,
  NotFoundExc,
} from '../../../common/exceptions/custom-http.exception';
import {
  CreateEventAddCanPopupAdminReqDto,
  GetListEventAddCanPopupAdminReqDto,
  UpdateEventAddCanPopupAdminReqDto,
} from '../../dtos/req/admin/event-add-can-popup.admin.req.dto';
import { EventAddCanPopupRepository } from '../../repositories/event-add-can-popup.repository';
import { EventCommonPopupAdminService } from '../../../event-common/services/admin/event-common-popup.admin.service';
import { EventAddCanRepository } from '../../repositories/event-add-can.repository';
import { AdminActionHistoryRepository } from '../../../admin-action-history/repositories/admin-action-history.repository';
import { AccountData } from '../../../proto/account.pb';
import { EnumNameAdminMenuModule } from '../../../admin_authorization/common/enums/admin-menu-module.enum';
import { Transactional } from 'typeorm-transactional';
import { EventAddCanTabStatus, EventAddCanTabStatusKeys } from '../../enums/event-add-can.enum';

@Injectable()
export class EventAddCanPopupAdminService {
  constructor(
    private readonly eventAddCanPopupRepo: EventAddCanPopupRepository,
    private readonly eventCommonPopupAdminService: EventCommonPopupAdminService,
    private readonly adminActionHistoryRepo: AdminActionHistoryRepository,
    private readonly eventAddCanRepo: EventAddCanRepository,
  ) { }
  async getListEventAddCanPopup(dto: GetListEventAddCanPopupAdminReqDto) {
    const { page, limit, eventAddCanId, eventType } = dto;
    const queryBuilder =
      this.eventAddCanPopupRepo.createQueryBuilder('eventAddCanPopup');
    queryBuilder
      .leftJoin('eventAddCanPopup.eventAddCan', 'eventAddCan')
      .addSelect([
        'eventAddCan.id',
        'eventAddCan.eventName',
        'eventAddCan.type',
      ]);
    queryBuilder.leftJoinAndSelect('eventAddCanPopup.file', 'file');

    if (eventAddCanId) {
      queryBuilder.andWhere('eventAddCan.id = :eventAddCanId', {
        eventAddCanId,
      });
    }

    if (eventType) {
      queryBuilder.andWhere('eventAddCan.type = :eventType', {
        eventType,
      });
    }

    queryBuilder.orderBy('eventAddCanPopup.createdAt', 'DESC');
    const { items, meta } = await paginate(queryBuilder, {
      limit,
      page,
    });
    return AppResponseDto.fromNestJsPagination(items, meta);
  }

  async getEventAddCanPopupById(id: number) {
    const eventAddCanPopup = await this.eventAddCanPopupRepo.findOne({
      where: { id },
      relations: ['eventAddCan', 'file'],
    });
    if (!eventAddCanPopup) {
      throw new NotFoundExc('Event Add Can Popup không tìm thấy');
    }
    return new AppResponseDto(eventAddCanPopup);
  }

  @Transactional()
  async createEventAddCanPopup(
    dto: CreateEventAddCanPopupAdminReqDto,
    admin: AccountData,
  ) {
    // check code is available
    const isCodeExisted =
      await this.eventCommonPopupAdminService.checkCodeExist(dto.code);
    if (isCodeExisted) {
      throw new ConflictExc(`[ ${dto.code} ] đã tồn tại`);
    }
    const newEventAddCanPopup = this.eventAddCanPopupRepo.create(dto);

    const [eventAddCan] = await Promise.all([
      this.eventAddCanRepo.findOneBy({
        id: dto.eventAddCanId,
      }),
      this.eventAddCanPopupRepo.save(newEventAddCanPopup),
    ]);

    eventAddCan.setupState = {
      key: EventAddCanTabStatusKeys.POPUPS,
      value: EventAddCanTabStatus.COMPLETED,
    };
    await this.eventAddCanRepo.save(eventAddCan);

    await this.adminActionHistoryRepo.loggingUpdateAction(
      admin.email,
      EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
      eventAddCan.eventName,
    );

    return new AppResponseDto(newEventAddCanPopup);
  }

  @Transactional()
  async updateEventAddCanPopup(
    id: number,
    dto: UpdateEventAddCanPopupAdminReqDto,
    admin: AccountData,
  ) {
    // Check eventAddCanPopup is available
    const [eventAddCanPopup, eventAddCan] = await Promise.all([
      this.eventAddCanPopupRepo.findOne({
        where: { id },
      }),
      this.eventAddCanRepo.findOneByOrFail({ id: dto.eventAddCanId }),
    ]);
    if (!eventAddCanPopup) {
      throw new NotFoundExc('Event Add Can Popup không tìm thấy');
    }
    // Check code is available
    const isCodeExisted =
      await this.eventCommonPopupAdminService.checkCodeExist(dto.code);
    if (isCodeExisted && dto.code !== eventAddCanPopup.code) {
      throw new ConflictExc(`[ ${dto.code} ] đã tồn tại`);
    }
    // Update eventAddCanPopup
    const [eventAddCanPopupUpdated] = await Promise.all([
      this.eventAddCanPopupRepo.save({
        ...eventAddCanPopup,
        ...dto,
      }),
      this.adminActionHistoryRepo.loggingUpdateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        eventAddCan.eventName,
      ),
    ]);
    return new AppResponseDto(eventAddCanPopupUpdated);
  }

  @Transactional()
  async deleteEventAddCanPopup(id: number, admin: AccountData) {
    const eventAddCanPopup = await this.eventAddCanPopupRepo.findOne({
      where: { id },
      relations: ['eventAddCan'],
    });
    if (!eventAddCanPopup) {
      throw new NotFoundExc('Event Add Can Popup không tìm thấy');
    }
    await Promise.all([
      this.adminActionHistoryRepo.loggingUpdateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        eventAddCanPopup.eventAddCan.eventName,
      ),
      this.eventAddCanPopupRepo.remove(eventAddCanPopup),
    ]);

    const eventAddCanId = eventAddCanPopup.eventAddCanId;

    const exists = await this.eventAddCanPopupRepo.findOneBy({
      eventAddCanId,
    });

    if (!exists) {
      const eventAddCan = await this.eventAddCanRepo.findOneByOrFail({
        id: eventAddCanId,
      });

      eventAddCan.setupState = {
        key: EventAddCanTabStatusKeys.POPUPS,
        value: EventAddCanTabStatus.PENDING,
      };
      await this.eventAddCanRepo.save(eventAddCan);
    }
  }
}
