import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { OnEvent } from '@nestjs/event-emitter';
import { In } from 'typeorm';
import { User } from '../../auth/entities/user.entity';
import { UserTypeV2 } from '../../auth/enums/user.enum';
import { GlobalConfig } from '../../common/config/global.config';
import { EVENT_EMITTER_NAME } from '../../common/constants/index.constant';
import { ProductDataGotByQrDto } from '../../point/dto/misc/product-data-got-by-qr.dto';
import { RequestOutboxMessage } from '../../point/interfaces/outbox-message.interface';
import { Product } from '../../product/entities/product.entity';
import { ProductRepository } from '../../product/repositories/product.repository';
import { ProductMst } from '../../qr-code-sbps/entities/product-mst.entity';
import {
  MAX_NUMBER_CAN_2024_Q2_CLG,
  PopupEventAddCan2024Q2CLG,
  PopupEventAddCanCLBB3BrandQ42024,
  PopupEventAddCanCLBBQ22024,
  PopupEventAddCanCLBBQ32024,
  PopupEventAddCanCLS,
  PopupEventAddCanD3K2,
  PopupEventAddCanDHA,
  PopupEventAddCanETE,
  PopupEventAddCanOPTI,
  PopupEventAddCanQ12024CurrentUser,
  PopupEventAddCanQ12024NewUser,
} from '../constants/index.constant';
import { EventAddCan } from '../entities/event-add-can.entity';
import { EventNumberUserCan } from '../entities/event-number-user-can.entity';
import { EventAddCanType } from '../enums/event-add-can.enum';
import { getUserType } from '../helpers/event-add-can.helper';
import { EventAddCanProductRepository } from '../repositories/event-add-can-product.repository';
import { EventAddCanRepository } from '../repositories/event-add-can.repository';
import { EventNumberUserCanRepository } from '../repositories/event-number-user-can.repository';
import { EventAddCanPopupRepository } from '../repositories/event-add-can-popup.repository';
import { UserTrackingNumberTimeExchangeGiftRepository } from '../repositories/user-tracking-number-time-exchange-gift.repository';
import { UserTrackingNumberTimeExchangeGiftEntity } from '../entities/user-tracking-number-time-exchange-gift.entity';
import { EventAddCanConfigInterface } from '../interfaces/user-tracking-number-time-exchange-gift.interface';
import { countNumberOfTimesEarn } from '../helpers/user-tracking-number-time-exchange-gift.helper';

@Injectable()
export class EventAddCanUserListenerService {
  constructor(
    private eventAddCanRepo: EventAddCanRepository,
    private eventAddCanProductRepo: EventAddCanProductRepository,
    private configService: ConfigService<GlobalConfig>,
    private eventNumberUserCanRepo: EventNumberUserCanRepository,
    private productRepo: ProductRepository,
    private eventAddCanPopupRepo: EventAddCanPopupRepository,
    private readonly userTrackingNumberTimeExchangeGiftRepo: UserTrackingNumberTimeExchangeGiftRepository,
  ) {}

  @OnEvent(EVENT_EMITTER_NAME.EVENT_2468)
  async handleEvents(
    user: User,
    product: Product | ProductMst,
    code: string,
    productData: ProductDataGotByQrDto,
    transactionRefId: string,
    arrNoti: string[],
    appversionname: string,
    requestOutboxMessage: RequestOutboxMessage,
    eventAddCanTypeTrigger: EventAddCanType[],
    type?: string,
  ) {
    return await this.handleEventAddCanUserListener(
      user,
      product,
      code,
      productData,
      transactionRefId,
      arrNoti,
      appversionname,
      requestOutboxMessage,
      eventAddCanTypeTrigger,
      type,
    );
  }

  private async handleEventAddCanUserListener(
    user: User,
    product: Product | ProductMst,
    code: string,
    productData: ProductDataGotByQrDto,
    transactionRefId: string,
    arrNoti: string[],
    appversionname: string,
    requestOutboxMessage: RequestOutboxMessage,
    eventAddCanTypeTrigger: EventAddCanType[] | string[],
    type?: string,
  ) {
    console.log(`Handle event 2468 add can for user`, user.id);

    const eventAddCansTriggered: EventAddCan[] = [];
    try {
      const activeEventsAddCan = await this.eventAddCanRepo.findActiveEvents();

      if (activeEventsAddCan.length === 0) {
        return eventAddCansTriggered;
      }

      const eventAddCanIdList = activeEventsAddCan.map((item) => item.id);

      const eventAddCanProductList = await this.eventAddCanProductRepo.find({
        where: {
          eventAddCanId: In(eventAddCanIdList),
          sku: product.code,
        },
        relations: {
          eventAddCan: true,
        },
      });

      console.log(
        'Check event product of active events and sku product for user',
        user.id,
      );

      if (eventAddCanProductList.length === 0) {
        return eventAddCansTriggered;
      }

      const eventAddCanProductArrToUpdate: EventNumberUserCan[] = [];

      let userType: UserTypeV2 = UserTypeV2.CUSTOMER;
      activeEventsAddCan.forEach((eventAddCan) => {
        userType = getUserType(user, eventAddCan);
      });

      let isFirstTime = false;
      const popupCodeEventCLBB = [];
      const popupCodeEventD3K2 = [];
      const userTrackingNumberTimeExchangeGiftUpdate: UserTrackingNumberTimeExchangeGiftEntity[] =
        [];

      for (const eventProduct of eventAddCanProductList) {
        let [eventUserNumberCan, userTrackingNumberTimeExchangeGift] =
          await Promise.all([
            this.eventNumberUserCanRepo.findOne({
              where: {
                userId: user.id,
                eventAddCanId: eventProduct.eventAddCanId,
              },
            }),
            this.userTrackingNumberTimeExchangeGiftRepo.findOneBy({
              eventAddCanId: eventProduct.eventAddCanId,
              userId: user.id,
            }),
          ]);

        if (!eventUserNumberCan) {
          isFirstTime = true;
          const newEventUserNumberCan = this.eventNumberUserCanRepo.create({
            userId: user.id,
            eventAddCanId: eventProduct.eventAddCanId,
            numberOfCan: 0,
            numberOfCanUsed: 0,
            phoneNumber: user.phoneNumber,
          });

          eventUserNumberCan = await this.eventNumberUserCanRepo.save(
            newEventUserNumberCan,
          );
        }

        eventAddCansTriggered.push(eventProduct.eventAddCan);

        // Implement improve event phase 1
        if (
          eventProduct.eventAddCan?.id >
          Number(this.configService.get('event.eventAddCanLatestId'))
        ) {
          const canToAdd = await this.getCanToAdd(
            type,
            eventProduct.sku,
            eventProduct.eventAddCan,
          );
          eventUserNumberCan.numberOfCan += canToAdd;
          eventAddCanProductArrToUpdate.push(eventUserNumberCan);

          const numberOfCanRemain =
            eventUserNumberCan.numberOfCan - eventUserNumberCan.numberOfCanUsed;
          // Get current eventAddCan
          const eventAddCan = await this.eventAddCanRepo.findOne({
            where: {
              id: eventProduct.eventAddCanId,
            },
          });
          // Get popup has numberCan is equal numberCan
          const popupWithNumberCanHighest =
            await this.eventAddCanPopupRepo.findOne({
              where: {
                eventAddCanId: eventProduct.eventAddCanId,
              },
              order: { numberCan: 'DESC' },
            });
          // Push popup code to arrNoti
          if (
            eventAddCan.activeOverCanPopup &&
            popupWithNumberCanHighest &&
            numberOfCanRemain >= popupWithNumberCanHighest.numberCan
          ) {
            arrNoti.push(popupWithNumberCanHighest.code);
          } else {
            const popup = await this.eventAddCanPopupRepo.findOne({
              where: {
                eventAddCanId: eventProduct.eventAddCanId,
                numberCan: numberOfCanRemain,
              },
            });
            if (popup) {
              arrNoti.push(popup.code);
            }
          }

          if (
            eventAddCan.enableLimitTimeCanExchangeGift ||
            userTrackingNumberTimeExchangeGift
          ) {
            let eventAddCanConfig: EventAddCanConfigInterface = {
              limitTimeCanExchangeGiftTotalTimes:
                eventAddCan.limitTimeCanExchangeGiftTotalTimes,
              limitTimeCanExchangeGiftNumberOfCanRate:
                eventAddCan.limitTimeCanExchangeGiftNumberOfCanRate,
              limitTimeCanExchangeGiftNumberOfCanValue:
                eventAddCan.limitTimeCanExchangeGiftNumberOfCanValue,
            };
            if (userTrackingNumberTimeExchangeGift) {
              eventAddCanConfig =
                userTrackingNumberTimeExchangeGift.eventAddCanConfig;
              userTrackingNumberTimeExchangeGift.numberOfTimesEarn =
                countNumberOfTimesEarn(
                  eventAddCanConfig,
                  eventUserNumberCan.numberOfCan,
                );
            } else {
              userTrackingNumberTimeExchangeGift =
                this.userTrackingNumberTimeExchangeGiftRepo.create({
                  eventAddCanId: eventAddCan.id,
                  userId: user.id,
                  numberOfTimesEarn: countNumberOfTimesEarn(
                    eventAddCanConfig,
                    eventUserNumberCan.numberOfCan,
                  ),
                  eventAddCanConfig,
                });
            }

            userTrackingNumberTimeExchangeGiftUpdate.push(
              userTrackingNumberTimeExchangeGift,
            );
          }

          continue;
        }

        // có thể sửa logic của SB sửa giống logic của SBPS được
        // vì thấy logic của SBPS có vẻ đúng hơn
        //  ? cần confirm BA xem thông tin product của SB
        // có lưu cùng bảng product_mst với SBPS không
        if (
          type === 'SB' &&
          eventProduct.eventAddCan.type !== EventAddCanType.EV1_24_Q1 &&
          eventProduct.eventAddCan.type !== EventAddCanType.EV1_24_D3K2
        ) {
          const skuLength = eventProduct.sku.length;
          const skuDigit =
            skuLength === 8 ? eventProduct.sku[4] : eventProduct.sku[5];

          if (skuDigit === '4' || skuDigit === '8' || skuDigit === '9') {
            const canToAdd = skuDigit === '4' ? 0.5 : 1;
            eventUserNumberCan.numberOfCan += canToAdd;
            eventAddCanProductArrToUpdate.push(eventUserNumberCan);
          }
        } else if (
          type === 'SBPS' &&
          (eventProduct.eventAddCan.type === EventAddCanType.EVQ2_CLBB_24 ||
            eventProduct.eventAddCan.type === EventAddCanType.EVQ3_CLBB_24 ||
            eventProduct.eventAddCan.type === EventAddCanType.EV_24_Q4_3BRAND)
        ) {
          const skuLength = eventProduct.sku.length;
          const lastCharInSku = Number(eventProduct.sku[skuLength - 1]);
          let canToAdd = 0;
          if (lastCharInSku % 2 == 0) {
            canToAdd = 0.5;
          } else {
            canToAdd = 1;
          }
          eventUserNumberCan.numberOfCan += canToAdd;
          eventAddCanProductArrToUpdate.push(eventUserNumberCan);
        } else if (
          (type === 'SBPS' &&
            eventProduct.eventAddCan.type === EventAddCanType.EV1_24_Q1) ||
          (type === 'SB' &&
            eventProduct.eventAddCan.type === EventAddCanType.EV1_24_Q1)
        ) {
          eventUserNumberCan.numberOfCan += 1;
          eventAddCanProductArrToUpdate.push(eventUserNumberCan);
        } else if (
          eventProduct.eventAddCan.type === EventAddCanType.EV1_24_Q1 ||
          eventProduct.eventAddCan.type === EventAddCanType.EV1_24_D3K2
        ) {
          eventUserNumberCan.numberOfCan += 1;
          eventAddCanProductArrToUpdate.push(eventUserNumberCan);
        }

        let numberOfCanAfterAdded =
          eventUserNumberCan.numberOfCan - eventUserNumberCan.numberOfCanUsed;
        if (
          eventProduct.eventAddCan.type === EventAddCanType.EVQ2_CLBB_24 ||
          eventProduct.eventAddCan.type === EventAddCanType.EVQ3_CLBB_24
        ) {
          if (0.5 == numberOfCanAfterAdded && isFirstTime) {
            numberOfCanAfterAdded = 0;
          }
        }

        const numberOfCanRemain =
          numberOfCanAfterAdded == 0.5
            ? 1
            : numberOfCanAfterAdded == 8.5 &&
              (eventProduct.eventAddCan.type === EventAddCanType.EVQ3_CLBB_24 ||
                eventProduct.eventAddCan.type ===
                  EventAddCanType.EV_24_Q4_3BRAND)
            ? 9
            : Math.floor(numberOfCanAfterAdded);

        eventAddCanTypeTrigger.push(
          eventProduct.eventAddCan.type as EventAddCanType,
        );
        if (eventProduct.eventAddCan.type === EventAddCanType.EV4_CLS) {
          numberOfCanRemain > 12
            ? arrNoti.push(PopupEventAddCanCLS[13])
            : arrNoti.push(PopupEventAddCanCLS[numberOfCanRemain]);
        } else if (eventProduct.eventAddCan.type === EventAddCanType.EV4_ETE) {
          numberOfCanRemain > 12
            ? arrNoti.push(PopupEventAddCanETE[13])
            : arrNoti.push(PopupEventAddCanETE[numberOfCanRemain]);
        } else if (eventProduct.eventAddCan.type === EventAddCanType.EV4_DHA) {
          numberOfCanRemain > 12
            ? arrNoti.push(PopupEventAddCanDHA[13])
            : arrNoti.push(PopupEventAddCanDHA[numberOfCanRemain]);
        } else if (eventProduct.eventAddCan.type === EventAddCanType.EV4_OPTI) {
          numberOfCanRemain > 12
            ? arrNoti.push(PopupEventAddCanOPTI[13])
            : arrNoti.push(PopupEventAddCanOPTI[numberOfCanRemain]);
        } else if (
          eventProduct.eventAddCan.type === EventAddCanType.EV1_24_D3K2
        ) {
          const index = numberOfCanRemain >= 1 ? 1 : numberOfCanRemain;
          //if (numberOfCanRemain !== 1 && numberOfCanRemain !== 3) {
          //arrNoti.push(PopupEventAddCanD3K2[index]);
          popupCodeEventD3K2.push(PopupEventAddCanD3K2[index]);
          //}
        } else if (
          eventProduct.eventAddCan.type === EventAddCanType.EVQ2_CLBB_24
        ) {
          const index = numberOfCanRemain > 1 ? 2 : numberOfCanRemain;
          //arrNoti.push(PopupEventAddCanCLBBQ22024[index]);
          popupCodeEventCLBB.push(PopupEventAddCanCLBBQ22024[index]);
        } else if (
          eventProduct.eventAddCan.type === EventAddCanType.EVQ3_CLBB_24
        ) {
          const index = numberOfCanRemain > 8 ? 9 : numberOfCanRemain;
          //arrNoti.push(PopupEventAddCanCLBBQ22024[index]);
          if (index == 2 || index == 4 || index >= 8) {
            popupCodeEventCLBB.push(PopupEventAddCanCLBBQ32024[index]);
          }
        } else if (
          eventProduct.eventAddCan.type === EventAddCanType.EV_24_Q4_3BRAND
        ) {
          const index = numberOfCanRemain > 8 ? 9 : numberOfCanRemain;
          //arrNoti.push(PopupEventAddCanCLBBQ22024[index]);
          if (PopupEventAddCanCLBB3BrandQ42024.hasOwnProperty(index)) {
            popupCodeEventCLBB.push(PopupEventAddCanCLBB3BrandQ42024[index]);
          }
        } else if (
          eventProduct.eventAddCan.type === EventAddCanType.EV1_24_Q1
        ) {
          if (numberOfCanRemain > 0) {
            if (UserTypeV2.CUSTOMER == userType) {
              if (numberOfCanRemain == 2) {
                arrNoti.push(PopupEventAddCanQ12024CurrentUser[2]);
              }
            } else if (UserTypeV2.NEW_USER == userType) {
              if (1 == numberOfCanRemain) {
                arrNoti.push(PopupEventAddCanQ12024NewUser[1]);
              } else if (2 == numberOfCanRemain) {
                arrNoti.push(PopupEventAddCanQ12024NewUser[2]);
              }
            }
          }
        } else if (
          eventProduct.eventAddCan.type === EventAddCanType.EV_24_Q2_CLG
        ) {
          if (eventUserNumberCan.numberOfCan > MAX_NUMBER_CAN_2024_Q2_CLG)
            arrNoti.push(
              PopupEventAddCan2024Q2CLG[MAX_NUMBER_CAN_2024_Q2_CLG + 1],
            );
          else
            arrNoti.push(
              PopupEventAddCan2024Q2CLG[eventUserNumberCan.numberOfCan],
            );
        }
      }

      if (popupCodeEventCLBB.length) {
        popupCodeEventCLBB.forEach((code) => {
          arrNoti.push(code);
        });
      } else if (popupCodeEventD3K2.length) {
        popupCodeEventD3K2.forEach((code) => {
          arrNoti.push(code);
        });
      }

      const promisesUpdate: any[] = [
        this.eventNumberUserCanRepo.save(eventAddCanProductArrToUpdate),
      ];
      if (userTrackingNumberTimeExchangeGiftUpdate.length) {
        promisesUpdate.push(
          this.userTrackingNumberTimeExchangeGiftRepo.save(
            userTrackingNumberTimeExchangeGiftUpdate,
          ),
        );
      }
      await Promise.all(promisesUpdate);
      console.log('Finished event');
    } catch (error) {
      console.error('Error handling event add can for user:', user.id, error);
      // Handle the error or throw it as needed
      throw error;
    }

    return eventAddCansTriggered;
  }

  private async getCanToAdd(
    type: string,
    sku: string,
    eventAddCan: EventAddCan,
  ): Promise<number> {
    if (type === 'SB') {
      const product = await this.productRepo.findOne({
        where: { code: sku },
      });
      const prefixWeightCode = product.prefixWeightCode;
      switch (prefixWeightCode) {
        case '4':
          return eventAddCan.convertCan400g;
        case '8':
          return eventAddCan.convertCan800g;
        default:
          return 0;
      }
    }

    if (type === 'SBPS') {
      const skuDigit = Number(sku[sku.length - 1]);
      return skuDigit % 2 === 0
        ? eventAddCan.convertBarrel110ml
        : eventAddCan.convertBarrel180ml;
    }

    return 0;
  }
}
