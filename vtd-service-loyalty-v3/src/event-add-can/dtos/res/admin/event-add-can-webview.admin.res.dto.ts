import { AutoMapDecorator } from '../../../../common/decorators/automap.decorator';
import { BaseMapperDto } from '../../../../common/dtos/base-mapper.dto';
import { File } from '../../../../file/entities/file.entity';
import { EventAddCan } from '../../../entities/event-add-can.entity';

export class EventAddCanWebviewAdminResDto extends BaseMapperDto {
  @AutoMapDecorator()
  id: number;

  @AutoMapDecorator()
  titleWebview: string;

  @AutoMapDecorator()
  linkWebview: string;

  @AutoMapDecorator()
  linkRule: string;

  @AutoMapDecorator()
  active: boolean;

  @AutoMapDecorator()
  eventAddCanId: number;

  @AutoMapDecorator()
  fileId: number;

  @AutoMapDecorator()
  eventAddCan: EventAddCan;

  @AutoMapDecorator()
  file: File;

  @AutoMapDecorator({
    transformSource: (src) => src?.systemFeature?.code || null,
  })
  code?: string;

  @AutoMapDecorator({
    transformSource: (src) => src?.systemFeature?.description || null,
  })
  description?: string;
}
