import { AutoMapDecorator } from '../../../../common/decorators/automap.decorator';
import { BaseMapperDto } from '../../../../common/dtos/base-mapper.dto';
import { EventAddCan } from '../../../entities/event-add-can.entity';

export class EventAddCanBrandPointAdminResDto extends BaseMapperDto {
  @AutoMapDecorator({
    transformSource: (src: EventAddCan) => {
      return src.id;
    },
  })
  eventAddCanId: number;

  @AutoMapDecorator({
    transformSource: (src: EventAddCan) => {
      return src.eventName;
    },
  })
  eventAddCanName: string;

  @AutoMapDecorator({
    transformSource: (src: EventAddCan) => {
      return src.startDate;
    },
  })
  startDate: string;

  @AutoMapDecorator({
    transformSource: (src: EventAddCan) => {
      return src.endDate;
    },
  })
  endDate: string;

  @AutoMapDecorator({
    transformSource: (src: EventAddCan) => {
      return src.brandPointConfig?.['name'];
    },
  })
  name: string;

  @AutoMapDecorator({
    transformSource: (src: EventAddCan) => {
      return src.brandPointConfig?.['is_active'];
    },
  })
  isActive: boolean;

  @AutoMapDecorator({
    transformSource: (src: EventAddCan) => {
      return src.brandPointConfig?.['priority'];
    },
  })
  priority: number;

  @AutoMapDecorator({
    transformSource: (src: EventAddCan) => {
      return src.brandPointConfig?.['shortcut_name'];
    },
  })
  shortcutName: string;

  @AutoMapDecorator({
    transformSource: (src: EventAddCan) => {
      return src.brandPointConfig?.['shortcut_icon_url'];
    },
  })
  shortcutIconUrl: string;

  @AutoMapDecorator({
    transformSource: (src: EventAddCan) => {
      return src.brandPointConfig?.['shortcut_deeplink'];
    },
  })
  shortcutDeeplink: string;

  @AutoMapDecorator({
    transformSource: (src: EventAddCan) => {
      return src.brandPointConfig?.['point_icon_url'];
    },
  })
  pointIconUrl: string;
}
