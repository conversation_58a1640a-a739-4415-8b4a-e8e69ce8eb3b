import { PartialType } from '@nestjs/swagger';
import {
  IsValidBoolean,
  IsValidNumber,
  IsValidText,
} from 'src/common/decorators/custom-validator.decorator';
import { PaginationReqDto } from 'src/common/dtos/pagination.dto';

export class CreateEventAddCanWebviewAdminReqDto {
  @IsValidText()
  titleWebview: string;

  @IsValidText()
  linkWebview: string;

  @IsValidText({ required: false })
  linkRule: string;

  @IsValidBoolean()
  active: boolean;

  @IsValidNumber()
  eventAddCanId: number;

  @IsValidNumber()
  fileId: number;

  @IsValidText()
  code: string;

  @IsValidText()
  description: string;

  @IsValidText()
  fileUrl: string;
}

export class UpdateEventAddCanWebviewAdminReqDto extends PartialType(
  CreateEventAddCanWebviewAdminReqDto,
) {}

export class GetListEventAddCanWebviewAdminReqDto extends PaginationReqDto {
  @IsValidNumber({ required: false })
  eventAddCanId?: number;

  @IsValidText({ required: false })
  eventType?: string;
}
