import {
  IsValidDate,
  IsValidNumber,
  IsValidText,
} from '../../../../common/decorators/custom-validator.decorator';
import { PaginationReqDto } from '../../../../common/dtos/pagination.dto';

export class GetEventAddCanBrandPointConfigsAdminReqDto extends PaginationReqDto {
  @IsValidNumber({ required: false })
  eventAddCanId?: number;

  @IsValidText({ required: false })
  name?: string;

  @IsValidDate({ required: false })
  startDate?: Date;

  @IsValidDate({ required: false })
  endDate?: Date;
}
