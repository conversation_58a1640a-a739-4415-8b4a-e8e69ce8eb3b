import {
  IsValidBoolean,
  IsValidNumber,
  IsValidText,
} from '../../../../common/decorators/custom-validator.decorator';

export class CreateEventAddCanBrandPointConfigAdminReqDto {
  @IsValidText({ required: true })
  name: string;

  @IsValidBoolean({ required: true })
  isActive: boolean;

  @IsValidNumber({ required: true })
  priority: number;

  @IsValidText({ required: true })
  shortcutName: string;

  @IsValidText({ required: true })
  shortcutIconUrl: string;

  @IsValidText({ required: true })
  shortcutDeeplink: string;

  @IsValidText({ required: false })
  pointIconUrl?: string;
}
