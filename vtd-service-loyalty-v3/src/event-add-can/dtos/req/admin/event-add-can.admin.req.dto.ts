import {
  IsValidArrayNumber,
  IsValidArrayString,
  IsValidBoolean,
  IsValidDate,
  IsValidEnum,
  IsValidNumber,
  IsValidText,
  IsValidArrayObject,
  IsValidObject,
} from '../../../../common/decorators/custom-validator.decorator';
import { PaginationReqDto } from '../../../../common/dtos/pagination.dto';
import {
  EventAddCanStatus,
  EventAddCanType,
} from '../../../enums/event-add-can.enum';

export class EventAddCanTimeExchangeGiftDetailReqDto {
  @IsValidNumber({ required: false })
  id?: number;

  @IsValidArrayNumber({ minSize: 1 })
  giftIds: number[];

  @IsValidNumber({ min: 1 })
  total: number;
}

export class EventAddCanTimeExchangeGiftReqDto {
  @IsValidNumber({ required: false })
  id?: number;

  @IsValidDate()
  startDate: Date;

  @IsValidDate()
  endDate: Date;

  @IsValidArrayObject({ minSize: 1 }, EventAddCanTimeExchangeGiftDetailReqDto)
  details: EventAddCanTimeExchangeGiftDetailReqDto[];
}

export class EventAddCanLimitTimeExchangeGiftReqDto {
  @IsValidNumber({ min: 1 })
  limitTimeCanExchangeGiftTotalTimes: number;

  @IsValidNumber({ min: 1 })
  limitTimeCanExchangeGiftNumberOfCanRate: number;

  @IsValidNumber({ min: 1 })
  limitTimeCanExchangeGiftNumberOfCanValue: number;
}

export class SaveEventAddCanReqDto {
  @IsValidText({ maxLength: 255 })
  eventName: string;

  @IsValidArrayString()
  skus: string[];

  @IsValidDate()
  startDate: Date;

  @IsValidDate()
  endDate: Date;

  @IsValidEnum({ enum: EventAddCanStatus })
  status: EventAddCanStatus;

  @IsValidNumber()
  convertPoint: number;

  @IsValidText()
  type: string;

  @IsValidArrayNumber({ required: false })
  giftCategoryIds?: number[];

  @IsValidArrayString({ required: false })
  gsGiftCategoryCodes?: string[];

  @IsValidNumber({ required: false })
  convertCan400g?: number;

  @IsValidNumber({ required: false })
  convertCan800g?: number;

  @IsValidNumber({ required: false })
  convertBarrel110ml?: number;

  @IsValidNumber({ required: false })
  convertBarrel180ml?: number;

  @IsValidBoolean({ required: false })
  activeOverCanPopup?: boolean;

  @IsValidBoolean({
    required: true,
  })
  enableTimeExchangeGift: boolean;

  @IsValidArrayObject({ required: false }, EventAddCanTimeExchangeGiftReqDto)
  timeExchangeGifts?: EventAddCanTimeExchangeGiftReqDto[];

  @IsValidBoolean({
    default: false,
  })
  enableLimitTimeCanExchangeGift: boolean;

  @IsValidObject({
    required: false,
    object: EventAddCanLimitTimeExchangeGiftReqDto,
  })
  limitTimeExchangeGift?: EventAddCanLimitTimeExchangeGiftReqDto;

  @IsValidText({ maxLength: 255, required: false })
  loyaltyProgramSfId?: string;
}

export class CreateEventAddCanAdminReqDto extends SaveEventAddCanReqDto {}
export class UpdateEventAddCanAdminReqDto extends SaveEventAddCanReqDto {}
export class GetListEventAddCanAdminReqDto extends PaginationReqDto {
  @IsValidText({ maxLength: 255, required: false })
  eventName?: string;

  @IsValidArrayString({ required: false })
  skus?: string[];

  @IsValidDate({ required: false })
  startDate?: Date;

  @IsValidDate({ required: false })
  endDate?: Date;
}
