import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { join } from 'path';
import { AuthModule } from '../auth/auth.module';
import { AuthService } from '../auth/auth.service';
import { GlobalConfig } from '../common/config/global.config';
import { EventCanMarkRepository } from '../event-can-mark/repositories/event-can-mark.repository';
import { EventCommonModule } from '../event-common/event-common.module';
import { GiftCategoryRepository } from '../gift/repositories/gift-category.repository';
import { ProductRepository } from '../product/repositories/product.repository';
import {
  ACCOUNT_PACKAGE_NAME,
  ACCOUNT_SERVICE_NAME,
} from '../proto/account.pb';
import { USER_PACKAGE_NAME, USER_SERVICE_NAME } from '../proto/user.pb';
import { EventAddCanPopupAdminController } from './controllers/admin/event-add-can-popup.admin.controller';
import { EventAddCanWebviewAdminController } from './controllers/admin/event-add-can-webview.admin.controller';
import { EventAddCanAdminController } from './controllers/admin/event-add-can.admin.controller';
import { EventAddCanWebviewUserController } from './controllers/user/event-add-can-webview.user.controller';
import { EventAddCanUserController } from './controllers/user/event-add-can.user.controller';
import { EventAddCanUserListenerService } from './listeners/event-add-can.user.listener';
import { EventAddCanPopupRepository } from './repositories/event-add-can-popup.repository';
import { EventAddCanProductRepository } from './repositories/event-add-can-product.repository';
import { EventAddCanWebviewRepository } from './repositories/event-add-can-webview.repository';
import { EventAddCanRepository } from './repositories/event-add-can.repository';
import { EventAddCanTimeExchangeGiftRepository } from './repositories/event-add-can-time-exchange-gift.repository';
import { EventAddCanTimeExchangeGiftDetailRepository } from './repositories/event-add-can-time-exchange-gift-detail.repository';
import { EventNumberUserCanRepository } from './repositories/event-number-user-can.repository';
import { UserTrackingNumberTimeExchangeGiftRepository } from './repositories/user-tracking-number-time-exchange-gift.repository';
import { UserTrackingNumberTimeExchangeGiftDetailRepository } from './repositories/user-tracking-number-time-exchange-gift-detail.repository';
import { EventAddCanPopupAdminService } from './services/admin/event-add-can-popup.admin.service';
import { EventAddCanWebviewAdminService } from './services/admin/event-add-can-webview.admin.service';
import { EventAddCanAdminService } from './services/admin/event-add-can.admin.service';
import { EventAddCanWebviewUserService } from './services/user/event-add-can-webview.user.service';
import { EventNumberUserCanService } from './services/user/event-number-user-can.service';
import { EventAddCanUserService } from './services/user/event-add-can.user.service';
import { EventCanMarkAdminService } from '../event-can-mark/services/event-can-mark.admin.service';
import { EventCanMarkModule } from '../event-can-mark/event-can-mark.module';
import { ExternalModule } from '../external/external.module';
import { AdminAuthorizationModule } from '../admin_authorization/admin_authorization.module';
import { AdminActionHistoryModule } from '../admin-action-history/admin-action-history.module';
import { EventNumberUserCanAdminService } from './services/admin/event-number-user-can.admin.service';
import { HttpModule } from '@nestjs/axios';
import { SystemFeatureModule } from '../system-feature/system-feature.module';

@Module({
  imports: [
    HttpModule,
    ClientsModule.registerAsync([
      {
        inject: [ConfigService],
        name: USER_SERVICE_NAME,
        useFactory: (configSer: ConfigService<GlobalConfig>) => {
          return {
            transport: Transport.GRPC,
            options: {
              url: configSer.getOrThrow('grpc.userSerivce.url'),
              package: USER_PACKAGE_NAME,
              protoPath: join(
                __dirname +
                  '/../../../node_modules/vtd-common-v3/proto/user.proto',
              ),
            },
          };
        },
      },
      {
        inject: [ConfigService],
        name: ACCOUNT_SERVICE_NAME,
        useFactory: (configSer: ConfigService<GlobalConfig>) => {
          return {
            name: ACCOUNT_SERVICE_NAME,
            transport: Transport.GRPC,
            options: {
              url: configSer.getOrThrow('grpc.accountService.url'),
              package: ACCOUNT_PACKAGE_NAME,
              protoPath: join(
                __dirname +
                  '/../../../node_modules/vtd-common-v3/proto/account.proto',
              ),
            },
          };
        },
      },
    ]),
    AuthModule,
    EventCommonModule,
    EventCanMarkModule,
    ExternalModule,
    AdminAuthorizationModule,
    AdminActionHistoryModule,
    SystemFeatureModule,
  ],
  providers: [
    EventAddCanAdminService,
    EventNumberUserCanAdminService,
    EventAddCanRepository,
    EventAddCanTimeExchangeGiftRepository,
    EventAddCanTimeExchangeGiftDetailRepository,
    GiftCategoryRepository,
    AuthService,
    EventAddCanProductRepository,
    EventAddCanUserListenerService,
    EventNumberUserCanRepository,
    EventNumberUserCanService,
    EventAddCanWebviewAdminService,
    EventAddCanWebviewRepository,
    EventAddCanPopupAdminService,
    EventAddCanPopupRepository,
    EventCanMarkRepository,
    UserTrackingNumberTimeExchangeGiftRepository,
    UserTrackingNumberTimeExchangeGiftDetailRepository,
    ProductRepository,
    EventAddCanWebviewUserService,
    EventAddCanUserService,
  ],
  controllers: [
    EventAddCanAdminController,
    EventAddCanUserController,
    EventAddCanWebviewAdminController,
    EventAddCanPopupAdminController,
    EventAddCanWebviewUserController,
  ],
})
export class EventAddCanModule {}
