import {
  Column,
  DeleteDateColumn,
  Entity,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { EventCanMark } from '../../event-can-mark/entities/event-can-mark.entity';
import { GiftCategory } from '../../gift/entities/gift-category.entity';
import {
  EventAddCanStatus,
  EventAddCanType,
} from '../enums/event-add-can.enum';
import { EventAddCanProduct } from './event-add-can-product.entity';
import { EventNumberUserCan } from './event-number-user-can.entity';
import { EventAddCanWebview } from './event-add-can-webview.entity';
import { EventAddCanPopup } from './event-add-can-popup.entity';
import { EventAddCanTimeExchangeGift } from './event-add-can-time-exchange-gift.entity';
import { MapUserToBrandPointEntity } from '../../map-user-to-brand-point/entities/map-user-to-brand-point.entity';

@Entity({ name: 'event_add_can' })
export class EventAddCan {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({ name: 'event_name', nullable: false })
  eventName: string;

  @Column({ name: 'start_date', nullable: false })
  startDate: Date;

  @Column({ name: 'end_date', nullable: false })
  endDate: Date;

  @Column({ enum: EventAddCanStatus, default: EventAddCanStatus.ACTIVE })
  status: EventAddCanStatus;

  @Column({ name: 'convert_point', nullable: false })
  convertPoint: number;

  @Column({ name: 'type', nullable: false })
  type: EventAddCanType | string; // EventAddCanType

  @Column({ name: 'convert_can_400g', nullable: true })
  convertCan400g: number;

  @Column({ name: 'convert_can_800g', nullable: true })
  convertCan800g: number;

  @Column({ name: 'convert_barrel_110ml', nullable: true })
  convertBarrel110ml: number;

  @Column({ name: 'convert_barrel_180ml', nullable: true })
  convertBarrel180ml: number;

  @Column({ name: 'event_popup_priority', type: 'int4', nullable: true })
  eventPopupPriority: number;

  @Column({
    name: 'gs_gift_category_codes',
    nullable: true,
    type: 'text',
    array: true,
  })
  gsGiftCategoryCodes: string[];

  @Column({
    default: false,
    name: 'enable_time_exchange_gift',
    type: 'boolean',
  })
  enableTimeExchangeGift: boolean;

  @Column({
    default: false,
    name: 'enable_limit_time_can_exchange_gift',
    type: 'boolean',
  })
  enableLimitTimeCanExchangeGift: boolean;

  @Column({
    name: 'limit_time_can_exchange_gift_total_times',
    nullable: true,
    type: 'int4',
  })
  limitTimeCanExchangeGiftTotalTimes: number;

  @Column({
    name: 'limit_time_can_exchange_gift_number_of_can_rate',
    nullable: true,
    type: 'int4',
  })
  limitTimeCanExchangeGiftNumberOfCanRate: number;

  @Column({
    name: 'limit_time_can_exchange_gift_number_of_can_value',
    nullable: true,
    type: 'int4',
  })
  limitTimeCanExchangeGiftNumberOfCanValue: number;

  @Column({
    type: 'varchar',
    length: 255,
    name: 'loyalty_program_sf_id',
    nullable: true,
  })
  loyaltyProgramSfId: string;

  @DeleteDateColumn({ name: 'deleted_at', type: 'timestamptz' })
  deletedAt: Date;

  @Column({ name: 'active_over_can_popup', type: 'boolean', default: false })
  activeOverCanPopup: boolean;

  @OneToMany(() => GiftCategory, (giftCategory) => giftCategory.eventAddCan)
  giftCategories: GiftCategory[];

  @OneToMany(
    () => EventAddCanProduct,
    (eventAddCanProduct) => eventAddCanProduct.eventAddCan,
  )
  eventAddCanProducts: EventAddCanProduct[];

  @OneToMany(
    () => EventNumberUserCan,
    (eventNumberUserCan) => eventNumberUserCan.eventAddCan,
  )
  eventNumberUserCans: EventNumberUserCan[];

  @OneToMany(() => EventCanMark, (eventCanMark) => eventCanMark.eventAddCan)
  eventCanMarks: EventCanMark[];

  @OneToOne(
    () => EventAddCanWebview,
    (eventAddCanWebview) => eventAddCanWebview.eventAddCanId,
    {
      onDelete: 'CASCADE',
    },
  )
  eventAddCanWebview: EventAddCanWebview;

  @OneToMany(
    () => EventAddCanPopup,
    (eventAddCanPopup) => eventAddCanPopup.eventAddCanId,
    {
      onDelete: 'CASCADE',
    },
  )
  eventAddCanPopups: EventAddCanPopup[];

  @OneToMany(
    () => EventAddCanTimeExchangeGift,
    (eventAddCanTimeExchangeGift) => eventAddCanTimeExchangeGift.eventAddCan,
  )
  timeExchangeGifts: EventAddCanTimeExchangeGift[];

  @OneToMany(() => MapUserToBrandPointEntity, (p) => p.eventAddCan)
  userBrandPoints: MapUserToBrandPointEntity[];
}
