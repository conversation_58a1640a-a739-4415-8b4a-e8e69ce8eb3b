import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { BaseEntityWithoutDeletedAtWithoutVersion } from '../../common/entities/base.entity';
import { EventAddCan } from '../../event-add-can/entities/event-add-can.entity';
import { File } from '../../file/entities/file.entity';
import { SystemFeature } from '../../system-feature/entities/system-feature.entity';

@Entity({ name: 'event_add_can_webview' })
export class EventAddCanWebview extends BaseEntityWithoutDeletedAtWithoutVersion {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({ name: 'title_webview' })
  titleWebview: string;

  @Column({ name: 'link_webview' })
  linkWebview: string;

  @Column({ name: 'link_rule', nullable: true })
  linkRule: string;

  @Column({ name: 'active', type: 'boolean', default: false })
  active: boolean;

  @Column({ name: 'event_add_can_id', type: 'integer' })
  eventAddCanId: number;

  @OneToOne(() => EventAddCan, (eventAddCan) => eventAddCan.eventAddCanWebview)
  @JoinColumn({ name: 'event_add_can_id' })
  eventAddCan: EventAddCan;

  @Column({ name: 'file_id', type: 'integer' })
  fileId: number;

  @OneToOne(() => File, (file) => file.eventAddCanWebview)
  @JoinColumn({ name: 'file_id' })
  file: File;

  @OneToOne(() => SystemFeature, (systemFeature) => systemFeature.eventAddCanWebview)
  systemFeature?: SystemFeature;
}
