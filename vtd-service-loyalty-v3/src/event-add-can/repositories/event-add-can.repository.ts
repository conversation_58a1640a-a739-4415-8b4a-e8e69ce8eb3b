import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { EventAddCan } from '../entities/event-add-can.entity';
import { EventAddCanStatus } from '../enums/event-add-can.enum';
import { getNowAtTimezone } from '../../common/datetime.util';

@Injectable()
export class EventAddCanRepository extends BaseRepository<EventAddCan> {
  constructor(dataSource: DataSource) {
    super(EventAddCan, dataSource);
  }

  findActiveEvents() {
    return this.createQueryBuilder('eventAddCan')
      .where('eventAddCan.startDate < :startDate', { startDate: new Date() })
      .andWhere('eventAddCan.endDate > :endDate', { endDate: new Date() })
      .andWhere('eventAddCan.status = :status', {
        status: EventAddCanStatus.ACTIVE,
      })
      .getMany();
  }

  findActiveEvent(id: number) {
    return this.createQueryBuilder('eventAddCan')
      .where('eventAddCan.id = :id', { id })
      .andWhere('eventAddCan.startDate < :startDate', { startDate: new Date() })
      .andWhere('eventAddCan.endDate > :endDate', { endDate: new Date() })
      .andWhere('eventAddCan.status = :status', {
        status: EventAddCanStatus.ACTIVE,
      })
      .getOne();
  }

  public async getAnyActiveEventContainSku(
    sku: string,
  ): Promise<EventAddCan[]> {
    if (!sku) {
      return null;
    }

    const current = getNowAtTimezone();

    return this.createQueryBuilder('eventAddCan')
      .innerJoin(
        'eventAddCan.eventAddCanProducts',
        'eventAddCanProducts',
        'eventAddCanProducts.sku = :sku',
        {
          sku,
        },
      )
      .where('eventAddCan.startDate < :startDate', { startDate: current })
      .andWhere('eventAddCan.endDate > :endDate', { endDate: current })
      .andWhere('eventAddCan.status = :status', {
        status: EventAddCanStatus.ACTIVE,
      })
      .getMany();
  }
}
