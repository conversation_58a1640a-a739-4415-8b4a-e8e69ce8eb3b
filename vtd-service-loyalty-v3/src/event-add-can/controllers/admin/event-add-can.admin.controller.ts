import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Patch,
  Post,
  Put,
  Query,
} from '@nestjs/common';
import {
  EnumCodeAdminMenuModule,
  EnumCodeActionOnAdminMenuModule,
} from 'vtd-common-v3';
import { AdminMenuModuleDecorator } from '../../../admin_authorization/common/decorators/admin-menu-module.decorator';
import { ActionsOnAdminMenuModuleDecorator } from '../../../admin_authorization/common/decorators/action-on-admin-menu-module.decorator';
import { Prefix } from '../../../common/constants/index.constant';
import { EventAddCanAdminService } from '../../services/admin/event-add-can.admin.service';
import { ApiTags } from '@nestjs/swagger';
import {
  AuthAdmin,
  CurrentToken,
  UseAdminWithAuthorizeBySetupAdminMenuModuleAcl,
} from '../../../common/decorators/user.decorator';
import {
  CreateEventAddCanAdminReqDto,
  DeleteManyUserJoinEventAddCanAdminReqDto,
  GetListEventAddCanAdminReqDto,
  GetListUserJoinEventAddCanAdminReqDto,
  ImportListUserJoinEventAddCanAdminReqDto,
  UpdateEventAddCanAdminReqDto,
} from '../../dtos/req/admin/event-add-can.admin.req.dto';
import { DeleteMultipleByNumberIdsDto } from '../../../common/dtos/delete-multiple.dto';
import { AccountData } from '../../../proto/account.pb';
import { EventNumberUserCanAdminService } from '../../services/admin/event-number-user-can.admin.service';
import { AppResponseDto } from '../../../common/dtos/app-response.dto';
import { EventAddCanBrandPointAdminResDto } from '../../dtos/res/admin/event-add-can-brand-point.admin.res.dto';
import { GetEventAddCanBrandPointConfigsAdminReqDto } from '../../dtos/req/admin/get-event-add-can-brand-point-configs.admin.req.dto';
import { CreateEventAddCanBrandPointConfigAdminReqDto } from '../../dtos/req/admin/create-event-add-can-brand-point-config.admin.req.dto';
import { UpdateEventAddCanBrandPointConfigAdminReqDto } from '../../dtos/req/admin/update-event-add-can-brand-point-config.admin.req.dto';
import { GetEventBrandsAdminReqDto } from '../../dtos/req/admin/get-event-brands.admin.req.dto';

@Controller({ version: '1', path: `${Prefix.ADMIN}/event-add-can` })
@UseAdminWithAuthorizeBySetupAdminMenuModuleAcl()
@AdminMenuModuleDecorator(EnumCodeAdminMenuModule.QUAN_LY_SU_KIEN)
@ApiTags('Event Add Can Admin Controller')
export class EventAddCanAdminController {
  constructor(
    private eventAddCanAdminService: EventAddCanAdminService,
    private eventNumberUserCanAdminService: EventNumberUserCanAdminService,
  ) {}

  @Get()
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.READ,
  )
  async findAll(
    @Query() getListEventAddCanAdminReqDto: GetListEventAddCanAdminReqDto,
  ) {
    return this.eventAddCanAdminService.findAll(getListEventAddCanAdminReqDto);
  }

  @Post()
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.CREATE,
  )
  async create(
    @Body() dto: CreateEventAddCanAdminReqDto,
    @AuthAdmin() admin: AccountData,
    @CurrentToken() token: string,
  ) {
    return await this.eventAddCanAdminService.create(dto, admin, token);
  }

  @Put(':id')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.UPDATE,
  )
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() dto: UpdateEventAddCanAdminReqDto,
    @AuthAdmin() admin: AccountData,
    @CurrentToken() token: string,
  ) {
    return await this.eventAddCanAdminService.update(id, dto, admin, token);
  }

  @Get(':id')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.READ,
  )
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return await this.eventAddCanAdminService.getDetail(id);
  }

  @Delete(':id')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.DELETE,
  )
  async delete(
    @Param('id', ParseIntPipe) id: number,
    @AuthAdmin() admin: AccountData,
  ) {
    return await this.eventAddCanAdminService.delete(id, admin);
  }

  @Delete()
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.DELETE,
  )
  async deleteMany(
    @Body('body') body: DeleteMultipleByNumberIdsDto,
    @AuthAdmin() admin: AccountData,
  ) {
    return await this.eventAddCanAdminService.deleteMany(body, admin);
  }

  @Post('duplicate/:id')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.CREATE,
  )
  async duplicateById(
    @CurrentToken() token: string,
    @Param('id', ParseIntPipe) id: number,
    @AuthAdmin() admin: AccountData,
  ) {
    return await this.eventAddCanAdminService.duplicateById(token, id, admin);
  }

  @Delete('list-user-join-event/:id')
  async deleteEventNumberUserAddCan(
    @Param('id', ParseIntPipe) id: number,
    @AuthAdmin() admin: AccountData,
  ) {
    return await this.eventNumberUserCanAdminService.delete(id, admin);
  }
  @Delete('list-user-join-event')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.DELETE,
  )
  async deleteManyUserJoinEvent(
    @Body() body: DeleteManyUserJoinEventAddCanAdminReqDto,
    @AuthAdmin() admin: AccountData,
  ) {
    return await this.eventNumberUserCanAdminService.deleteMany(body, admin);
  }

  // get list user join event
  @Get(':eventAddCanId/list-user-join-event')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.READ,
  )
  async findAllUserJoinEvent(
    @Param('eventAddCanId', ParseIntPipe) eventAddCanId: number,
    @Query()
    getListUserJoinEventAddCanAdminReqDto: GetListUserJoinEventAddCanAdminReqDto,
  ) {
    return this.eventNumberUserCanAdminService.getListUserJoinEvent(
      eventAddCanId,
      getListUserJoinEventAddCanAdminReqDto,
    );
  }

  @Get('expire-time-units')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.READ,
  )
  async getExpireTimeUnits() {
    const enumValues = await this.eventAddCanAdminService.getExpireTimeUnits();
    return new AppResponseDto(enumValues);
  }

  @Get('event-brands')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.READ,
  )
  async getEventBrands(
    @Query()
    dto: GetEventBrandsAdminReqDto,
  ) {
    const { items, meta } = await this.eventAddCanAdminService.getEventBrands(
      dto,
    );
    return AppResponseDto.fromNestJsPagination(items, meta);
  }

  @Get('brand-point-configs')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.READ,
  )
  async getBrandPointConfigs(
    @Query()
    dto: GetEventAddCanBrandPointConfigsAdminReqDto,
  ) {
    const { items, meta } =
      await this.eventAddCanAdminService.getEventAddCanWithBrandPointConfigs(
        dto,
      );

    return AppResponseDto.fromNestJsPagination(
      items?.map((item) => new EventAddCanBrandPointAdminResDto(item)),
      meta,
    );
  }

  @Get(':eventAddCanId/brand-point-config')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.READ,
  )
  async getBrandPointConfig(@Param('eventAddCanId') eventAddCanId: number) {
    const result =
      await this.eventAddCanAdminService.getEventAddCanWithBrandPointConfig(
        eventAddCanId,
      );

    return new AppResponseDto(new EventAddCanBrandPointAdminResDto(result));
  }

  @Post(':eventAddCanId/brand-point-config')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.CREATE,
  )
  async createBrandPointConfig(
    @Param('eventAddCanId') eventAddCanId: number,
    @Body() dto: CreateEventAddCanBrandPointConfigAdminReqDto,
    @AuthAdmin() admin: AccountData,
  ) {
    const result =
      await this.eventAddCanAdminService.createBrandPointConfigByEventAddCanId(
        eventAddCanId,
        dto,
        admin,
      );

    return new AppResponseDto(new EventAddCanBrandPointAdminResDto(result));
  }

  @Patch(':eventAddCanId/brand-point-config')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.UPDATE,
  )
  async updateBrandPointConfig(
    @Param('eventAddCanId') eventAddCanId: number,
    @Body() dto: UpdateEventAddCanBrandPointConfigAdminReqDto,
    @AuthAdmin() admin: AccountData,
  ) {
    const result =
      await this.eventAddCanAdminService.updateBrandPointConfigByEventAddCanId(
        eventAddCanId,
        dto,
        admin,
      );

    return new AppResponseDto(new EventAddCanBrandPointAdminResDto(result));
  }

  @Delete(':eventAddCanId/brand-point-config')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.DELETE,
  )
  async deleteBrandPointConfig(
    @Param('eventAddCanId') eventAddCanId: number,
    @AuthAdmin() admin: AccountData,
  ) {
    const result =
      await this.eventAddCanAdminService.deleteBrandPointConfigByEventAddCanId(
        eventAddCanId,
        admin,
      );

    return new AppResponseDto(result);
  }
}
