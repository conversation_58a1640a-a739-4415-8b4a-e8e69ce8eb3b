import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import {
  EnumCodeAdminMenuModule,
  EnumCodeActionOnAdminMenuModule,
} from 'vtd-common-v3';
import { Prefix } from '../../../common/constants/index.constant';
import {
  AuthAdmin,
  UseAdminWithAuthorizeBySetupAdminMenuModuleAcl,
} from '../../../common/decorators/user.decorator';
import { AdminMenuModuleDecorator } from '../../../admin_authorization/common/decorators/admin-menu-module.decorator';
import { ActionsOnAdminMenuModuleDecorator } from '../../../admin_authorization/common/decorators/action-on-admin-menu-module.decorator';
import {
  CreateEventAddCanWebviewAdminReqDto,
  GetListEventAddCanWebviewAdminReqDto,
  UpdateEventAddCanWebviewAdminReqDto,
} from '../../dtos/req/admin/event-add-can-webview.admin.req.dto';
import { EventAddCanWebviewAdminService } from '../../services/admin/event-add-can-webview.admin.service';
import { AccountData } from '../../../proto/account.pb';
import { EventAddCanWebviewAdminResDto } from '../../dtos/res/admin/event-add-can-webview.admin.res.dto';
import { AppResponseDto } from '../../../common/dtos/app-response.dto';

@Controller({ version: '1', path: `${Prefix.ADMIN}/event-add-can-webview` })
@UseAdminWithAuthorizeBySetupAdminMenuModuleAcl()
@AdminMenuModuleDecorator(EnumCodeAdminMenuModule.QUAN_LY_SU_KIEN)
@ApiTags('Event Add Can Webview Admin Controller')
export class EventAddCanWebviewAdminController {
  constructor(
    private readonly eventWebviewAdminService: EventAddCanWebviewAdminService,
  ) {}

  @Get()
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.READ,
  )
  async getListEventAddCanWebview(
    @Query() dto: GetListEventAddCanWebviewAdminReqDto,
  ) {
    const { items, meta } =
      await this.eventWebviewAdminService.getListEventAddCanWebview(dto);

    const dataResponse = items.map(
      (item) => new EventAddCanWebviewAdminResDto(item),
    );

    return AppResponseDto.fromNestJsPagination(dataResponse, meta);
  }

  @Get(':id')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.READ,
  )
  async getEventAddCanWebviewById(@Param('id') id: number) {
    return this.eventWebviewAdminService.getEventAddCanWebviewById(id);
  }

  @Post()
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.CREATE,
  )
  async createEventAddCanWebview(
    @Body() dto: CreateEventAddCanWebviewAdminReqDto,
    @AuthAdmin() admin: AccountData,
  ) {
    return this.eventWebviewAdminService.createEventAddCanWebview(dto, admin);
  }

  @Patch(':id')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.UPDATE,
  )
  async updateEventAddCanWebview(
    @Param('id') id: number,
    @Body() dto: UpdateEventAddCanWebviewAdminReqDto,
    @AuthAdmin() admin: AccountData,
  ) {
    return this.eventWebviewAdminService.updateEventAddCanWebview(
      id,
      dto,
      admin,
    );
  }

  @Delete(':id')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.DELETE,
  )
  async deleteEventAddCanWebview(
    @Param('id') id: number,
    @AuthAdmin() admin: AccountData,
  ) {
    return this.eventWebviewAdminService.deleteEventAddCanWebview(id, admin);
  }
}
