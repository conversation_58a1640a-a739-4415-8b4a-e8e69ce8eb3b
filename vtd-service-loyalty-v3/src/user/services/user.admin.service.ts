import { Injectable } from '@nestjs/common';
import { objectify } from 'radash';
import { In, SelectQueryBuilder, Brackets } from 'typeorm';
import { paginate } from 'nestjs-typeorm-paginate';
import { BlockedAccountType } from '../../auth/enums/user.enum';
import { formatToString, getNowAtTimezone } from '../../common/datetime.util';
import { AppResponseDto } from '../../common/dtos/app-response.dto';
import {
  BadRequestExc,
  ForbiddenExc,
} from '../../common/exceptions/custom-http.exception';
import { GetUsersRequestAdminDto } from '../dtos/admin/requests/get-users.request.admin.dto';
import { UpdateUserStatusLoginAccountRequestAdminDto } from '../dtos/admin/requests/update-user-status-login-account.request.admin.dto';
import { UpdateUserStatusUpdateDefaultAddressRequestAdminDto } from '../dtos/admin/requests/update-user-status-update-default-address.request.admin.dto';
import { UserResponseAdminDto } from '../dtos/admin/responses/user.response.admin.dto';
import { UserRepository } from '../../auth/repositories/user.repository';
import { ProvinceRepository } from '../../auth/repositories/province.repository';
import { User } from '../../auth/entities/user.entity';
import { AdminRoleRepository } from '../../admin_authorization/repositories/admin-role.repository';
import { AccountData } from '../../proto/account.pb';
import { EnumCodeAdminRole } from 'vtd-common-v3';
import { ConfigService } from '@nestjs/config';
import { AdminActionHistoryRepository } from '../../admin-action-history/repositories/admin-action-history.repository';
import { EnumNameAdminMenuModule } from '../../admin_authorization/common/enums/admin-menu-module.enum';
import { BlockedScanRepository } from '../../qr-code-sbps/repositories/blocked-scan.repository';
import { Transactional } from 'typeorm-transactional';
import { QueryFilter } from '../../common/decorators/query-filter.decorator';
import { isArray, isString, trim } from 'lodash';

@Injectable()
export class UserAdminService {
  private limitNumberOfBlockedAccount: number;

  constructor(
    private readonly userRepo: UserRepository,
    private readonly provinceRepo: ProvinceRepository,
    private readonly adminRoleRepo: AdminRoleRepository,
    private readonly adminActionHistoryRepo: AdminActionHistoryRepository,
    private readonly configService: ConfigService,
    private readonly blockedScanRepo: BlockedScanRepository,
  ) {
    this.limitNumberOfBlockedAccount = this.configService.get<number>(
      'systemConfig.limitNumberOfBlockedAccount',
    );
  }

  async getUsers(dto: GetUsersRequestAdminDto) {
    const queryBuilder = await this.buildUsersQuery(dto);
    const { items, meta } = await paginate(queryBuilder, {
      limit: dto.limit,
      page: dto.page,
    });

    const provinceIds = items.map((item) => item.provinceId);
    const districtIds = items.map((item) => item.districtId);
    const wardIds = items.map((item) => item.wardId);
    const finalProvinceIds = [
      ...provinceIds,
      ...districtIds,
      ...wardIds,
    ].filter((item) => item);
    const provincesGet = finalProvinceIds.length
      ? await this.provinceRepo.findBy({
          id: In(finalProvinceIds),
        })
      : [];
    const mapProvincesGetById = objectify(provincesGet, (f) => f.id);
    const data = items.map((item) => {
      item.province = mapProvincesGetById[item.provinceId];
      item.district = mapProvincesGetById[item.districtId];
      item.ward = mapProvincesGetById[item.wardId];

      return new UserResponseAdminDto(item);
    });

    return AppResponseDto.fromNestJsPagination(data, meta);
  }

  @Transactional()
  async updateStatusLogin(
    admin: AccountData,
    dto: UpdateUserStatusLoginAccountRequestAdminDto,
  ) {
    const { userId, blockLoginAccount } = dto;

    const user = await this.userRepo.findOneBy({
      id: userId,
    });
    if (!user) {
      throw new BadRequestExc('Dữ liệu không hợp lệ');
    }

    const now = formatToString(getNowAtTimezone());
    let dataUpdate: any = {};
    if (blockLoginAccount) {
      dataUpdate = {
        blockedAccount: true,
        blockedAccountType: BlockedAccountType.BLOCKED_ACCOUNT_BY_ADMIN,
        blockedAccountDate: now,
      };
    } else {
      dataUpdate = {
        blockedAccount: false,
        blockedAccountType: null,
        blockedAccountDate: null,
        numberOfBlockedScan: 0,
        numberOfBlockedScanSbps: 0,
        blockedScan: null,
        blockedScanType: null,
        blockedScanDate: null,
        blockedScanExpiryDate: null,
      };

      // Kiểm tra nếu vượt quá số lần khóa đăng nhập, thì chỉ có Super Admin mới có quyền mở khóa
      const isSuperAdmin = await this.isSuperAdmin(admin.accountId);
      if (user.numberOfBlockedAccount >= this.limitNumberOfBlockedAccount) {
        if (!isSuperAdmin) {
          throw new ForbiddenExc('Không có quyền thực hiện thao tác này');
        }

        dataUpdate = {
          ...dataUpdate,
          numberOfBlockedAccount: 0,
        };
      }
    }

    await Promise.all([
      this.userRepo.update(
        {
          id: userId,
        },
        dataUpdate,
      ),
      this.blockedScanRepo.delete({
        userId,
      }),
      this.adminActionHistoryRepo.loggingUpdateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_TAI_KHOAN,
        user.phoneNumber,
      ),
    ]);

    return new AppResponseDto('ok');
  }

  async updateStatusUpdateDefaultAddress(
    admin: AccountData,
    dto: UpdateUserStatusUpdateDefaultAddressRequestAdminDto,
  ) {
    const { userId, blockUpdateDefaultAddress } = dto;

    const user = await this.userRepo.findOneBy({
      id: userId,
    });
    if (!user) {
      throw new BadRequestExc('Dữ liệu không hợp lệ');
    }

    let dataUpdate: any = {};
    if (blockUpdateDefaultAddress) {
      dataUpdate = {
        blockUpdateAddress: true,
      };
    } else {
      dataUpdate = {
        blockUpdateAddress: false,
      };
    }

    await Promise.all([
      this.userRepo.update(
        {
          id: userId,
        },
        dataUpdate,
      ),
      this.adminActionHistoryRepo.loggingUpdateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_TAI_KHOAN,
        user.phoneNumber,
      ),
    ]);

    return new AppResponseDto('ok');
  }

  private async buildUsersQuery(dto: GetUsersRequestAdminDto) {
    const queryBuilder = this.userRepo
      .createQueryBuilder('user')
      .orderBy('user.createdDate', 'DESC');

    // Apply filters via reusable pipeline in BaseRepository
    await this.userRepo.applyAllFilters(queryBuilder, dto, this);

    return queryBuilder;
  }

  /**
   * Applies account status filter
   */
  @QueryFilter({ order: 10 })
  private applyAccountStatusFilter(
    queryBuilder: SelectQueryBuilder<User>,
    dto: GetUsersRequestAdminDto,
  ): void {
    if (dto.accountStatus) {
      queryBuilder.andWhere('user.accountStatus = :accountStatus', {
        accountStatus: dto.accountStatus,
      });
    }
  }

  /**
   * Applies tier code filter
   */
  @QueryFilter({ order: 20 })
  private applyTierCodeFilter(
    queryBuilder: SelectQueryBuilder<User>,
    dto: GetUsersRequestAdminDto,
  ): void {
    if (isString(dto.tierCodes)) {
      queryBuilder.andWhere('user.tierCode = :tierCode', {
        tierCode: dto.tierCodes,
      });
    }

    if (isArray(dto.tierCodes)) {
      queryBuilder.andWhere('user.tierCode IN (:...tierCodes)', {
        tierCodes: dto.tierCodes,
      });
    }
  }

  /**
   * Applies search filter
   */
  @QueryFilter({ order: 30 })
  private applySearchFilter(
    queryBuilder: SelectQueryBuilder<User>,
    dto: GetUsersRequestAdminDto,
  ): void {
    const searchText = trim(dto.search);
    if (!searchText) {
      return;
    }

    queryBuilder.andWhere(
      new Brackets((qb) => {
        qb.where('user.id = :searchText', {
          searchText,
        });
        qb.orWhere('user.phoneNumber = :searchText', {
          searchText,
        });
      }),
    );
  }

  /**
   * Applies date range filter with default 30-day window if no dates provided
   */
  @QueryFilter({ order: 50 })
  private applyDateRangeFilter(
    queryBuilder: SelectQueryBuilder<User>,
    dto: GetUsersRequestAdminDto,
  ): void {
    if (!dto.startDate && !dto.endDate) {
      // Default filter: last 30 days
      queryBuilder.andWhere(`user.createdDate >= NOW() - INTERVAL '30 days'`);
      return;
    }

    if (dto.startDate) {
      queryBuilder.andWhere('user.createdDate >= :startDate', {
        startDate: dto.startDate,
      });
    }

    if (dto.endDate) {
      queryBuilder.andWhere('user.createdDate <= :endDate', {
        endDate: dto.endDate,
      });
    }
  }

  private async isSuperAdmin(accountId: number): Promise<boolean> {
    const count = await this.adminRoleRepo.countByAccountIdAndCodeRoleIn(
      accountId,
      [EnumCodeAdminRole.SUPER_ADMIN],
    );

    return count > 0;
  }
}
