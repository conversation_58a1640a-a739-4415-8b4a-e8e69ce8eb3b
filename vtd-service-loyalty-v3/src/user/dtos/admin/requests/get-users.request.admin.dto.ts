import {
  IsValidArrayString,
  IsValidDate,
  IsValidText,
} from '../../../../common/decorators/custom-validator.decorator';
import { PaginationReqDto } from '../../../../common/dtos/pagination.dto';

export class GetUsersRequestAdminDto extends PaginationReqDto {
  @IsValidText({
    required: false,
  })
  search?: string;

  @IsValidArrayString({
    required: false,
  })
  tierCodes?: string[];

  @IsValidText({
    required: false,
  })
  accountStatus?: string;

  @IsValidDate({ required: false })
  startDate?: Date;

  @IsValidDate({ required: false })
  endDate?: Date;
}
