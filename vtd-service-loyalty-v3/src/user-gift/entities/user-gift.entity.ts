import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';
import { BaseEntityWithoutDeletedAtWithoutVersion } from '../../common/entities/base.entity';
import { UserGiftStatusEnum } from '../enums/user-gift-status.enum';

@Entity({ name: 'gs_user_gifts' })
export class UserGift extends BaseEntityWithoutDeletedAtWithoutVersion {
  @PrimaryGeneratedColumn()
  id: number;

  @Index()
  @Column({ name: 'user_id', type: 'int4' })
  userId: number;

  @Index()
  @Column({ name: 'gift_id', type: 'int4' })
  giftId: number;

  @Column({ name: 'status', length: 255, nullable: true })
  status: UserGiftStatusEnum;

  @Column({
    name: 'holding_date',
    type: 'timestamp',
    nullable: true,
  })
  holdingDate: Date;

  @Column({ name: 'quantity', type: 'int4', default: 0 })
  quantity: number;

  @Column({ name: 'used_quantity', type: 'int4', default: 0 })
  usedQuantity: number;

  @Column({
    name: 'reservation_point',
    type: 'float4',
    default: 0,
  })
  reservationPoint: number;

  @Column({ name: 'point', type: 'float4', default: 0 })
  point: number;

  @Column({
    name: 'dynamic_data',
    type: 'jsonb',
    nullable: true,
  })
  dynamicData: Record<string, any>;

  @Column({
    name: 'gift_snapshot',
    type: 'jsonb',
    nullable: true,
  })
  giftSnapshot: Record<string, any>;

  @Column({
    name: 'recipient_snapshot',
    type: 'jsonb',
    nullable: true,
  })
  recipientSnapshot: Record<string, any>;

  @Column({
    name: 'gift_reservation_snapshot',
    type: 'jsonb',
    nullable: true,
  })
  giftReservationSnapshot: Record<string, any>;

  @Column({
    name: 'pre_order_status',
    length: 255,
    nullable: true,
  })
  preOrderStatus: string;

  @Index()
  @Column({
    name: 'transaction_code',
    length: 255,
    nullable: true,
  })
  transactionCode: string;

  @Column({
    name: 'extended_search',
    type: 'tsvector',
    nullable: true,
  })
  extendedSearch: string;

  @Column({
    name: 'notified_at',
    type: 'timestamp',
    nullable: true,
  })
  notifiedAt: Date;

  @Column({
    name: 'stock_point',
    type: 'float4',
    default: 0,
  })
  stockPoint: number;

  @Column({
    name: 'last_notified_stage',
    length: 255,
    nullable: true,
  })
  lastNotifiedStage: string;
}
