import { Module } from '@nestjs/common';
import { AuthModule } from '../auth/auth.module';
import { UserGiftAdminController } from './controllers/admin/user-gift.admin.controller';
import { UserGiftAdminService } from './services/admin/user-gift.admin.service';
import { UserGiftRepository } from './repositories/user-gift.repository';

@Module({
  imports: [AuthModule],
  controllers: [UserGiftAdminController],
  providers: [UserGiftAdminService, UserGiftRepository],
})
export class UserGiftModule {}
