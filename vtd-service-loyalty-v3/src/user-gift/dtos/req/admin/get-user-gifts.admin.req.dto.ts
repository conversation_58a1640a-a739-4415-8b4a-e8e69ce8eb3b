import {
  IsValidArrayEnum,
  IsValidDate,
  IsValidNumber,
  IsValidText,
} from '../../../../common/decorators/custom-validator.decorator';
import { PaginationReqDto } from '../../../../common/dtos/pagination.dto';
import { GiftTypeEnum } from '../../../enums/gift-type.enum';
import { UserGiftStatusEnum } from '../../../enums/user-gift-status.enum';

export class GetUserGiftsAdminReqDto extends PaginationReqDto {
  @IsValidArrayEnum({ required: false }, UserGiftStatusEnum)
  statuses?: UserGiftStatusEnum[];

  @IsValidArrayEnum({ required: false }, GiftTypeEnum)
  giftTypes?: GiftTypeEnum[];

  @IsValidText({ required: false })
  customerName?: string;

  @IsValidNumber({ required: false })
  giftId?: number;

  @IsValidText({ required: false })
  transactionSearchText?: string;

  @IsValidDate({ required: false })
  startDate?: Date;

  @IsValidDate({ required: false })
  endDate?: Date;
}
