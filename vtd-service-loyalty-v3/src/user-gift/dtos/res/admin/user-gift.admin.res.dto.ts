import { AutoMapDecorator } from '../../../../common/decorators/automap.decorator';
import { BaseMapperDto } from '../../../../common/dtos/base-mapper.dto';

export class UserGiftAdminResDto extends BaseMapperDto {
  @AutoMapDecorator()
  id: number;

  @AutoMapDecorator()
  userId: number;

  @AutoMapDecorator()
  giftId: number;

  @AutoMapDecorator()
  status: string;

  @AutoMapDecorator()
  transactionCode: string;

  @AutoMapDecorator()
  createdAt: Date;

  @AutoMapDecorator()
  updatedAt: Date;

  // Customer Information
  @AutoMapDecorator({
    transformSource: (src) => src?.user?.lastName,
  })
  customerName: string;

  @AutoMapDecorator({
    transformSource: (src) => src?.user?.phoneNumber,
  })
  customerPhone: string;

  // Gift snapshot
  @AutoMapDecorator({
    transformSource: (src) => src?.giftSnapshot?.['name'],
  })
  giftName: string;

  @AutoMapDecorator({
    transformSource: (src) => src?.giftSnapshot?.['point'],
  })
  giftPoint: number;

  @AutoMapDecorator({
    transformSource: (src) => src?.giftSnapshot?.['price'],
  })
  giftPrice: number;

  @AutoMapDecorator({
    transformSource: (src) => src?.giftSnapshot?.['type'],
  })
  giftType: string;

  //  Dynamic data
  @AutoMapDecorator({
    transformSource: (src) => src?.dynamicData?.['used_date'],
  })
  usedDate?: Date;

  @AutoMapDecorator({
    transformSource: (src) => src?.dynamicData?.['expiry_date'],
  })
  expiryDate?: Date;

  @AutoMapDecorator({
    transformSource: (src) => src?.dynamicData?.['voucher_ref_id'],
  })
  voucherRefId?: string;
}
