import { Injectable } from '@nestjs/common';
import { paginate } from 'nestjs-typeorm-paginate';
import { Brackets, In, Like, SelectQueryBuilder } from 'typeorm';
import { User } from '../../../auth/entities/user.entity';
import { UserRepository } from '../../../auth/repositories/user.repository';
import { QueryFilter } from '../../../common/decorators/query-filter.decorator';
import { GetUserGiftsAdminReqDto } from '../../dtos/req/admin/get-user-gifts.admin.req.dto';
import { UserGift } from '../../entities/user-gift.entity';
import { UserGiftRepository } from '../../repositories/user-gift.repository';

@Injectable()
export class UserGiftAdminService {
  constructor(
    private readonly userGiftRepo: UserGiftRepository,
    private readonly userRepo: UserRepository,
  ) {}

  async getUserGifts(dto: GetUserGiftsAdminReqDto) {
    // Build and execute the query
    const queryBuilder = await this.buildUserGiftsQuery(dto);
    const { items, meta } = await paginate(queryBuilder, {
      limit: dto.limit,
      page: dto.page,
    });

    // Fetch users for the returned items
    let users: User[] = [];
    if (items.length > 0) {
      const userIds = [...new Set(items.map((item) => item.userId))];
      users = await this.getUserByIds(userIds);
    }

    const userMap = new Map(users.map((user) => [user.id, user]));
    const formattedItems = items.map((item) => ({
      ...item,
      user: userMap.get(item.userId),
    }));

    return {
      data: formattedItems,
      meta,
    };
  }

  /**
   * Builds the query for fetching user gifts with all filters applied
   */
  private async buildUserGiftsQuery(
    dto: GetUserGiftsAdminReqDto,
  ): Promise<SelectQueryBuilder<UserGift>> {
    const queryBuilder = this.userGiftRepo
      .createQueryBuilder('gs_user_gifts')
      .select([
        'gs_user_gifts.id',
        'gs_user_gifts.userId',
        'gs_user_gifts.status',
        'gs_user_gifts.transactionCode',
        'gs_user_gifts.createdAt',
        'gs_user_gifts.updatedAt',
        'gs_user_gifts.giftId',
        'gs_user_gifts.giftSnapshot',
        'gs_user_gifts.dynamicData',
      ])
      .orderBy('gs_user_gifts.created_at', 'DESC');

    // Apply filters via reusable pipeline in BaseRepository
    await this.userGiftRepo.applyAllFilters(queryBuilder, dto, this);

    return queryBuilder;
  }

  /**
   * Applies customer name filter by fetching matching users
   */
  @QueryFilter({ order: 10 })
  private async applyCustomerNameFilter(
    queryBuilder: SelectQueryBuilder<UserGift>,
    dto: GetUserGiftsAdminReqDto,
  ): Promise<void> {
    if (!dto.customerName) return;

    const users = await this.getUsersByCustomerName(dto.customerName);
    if (users?.length) {
      queryBuilder.andWhere('gs_user_gifts.user_id IN (:...userIds)', {
        userIds: users.map((user) => user.id),
      });
    }
  }

  /**
   * Applies transaction search filter (searches in transaction code and voucher ref ID)
   */
  @QueryFilter({ order: 20 })
  private applyTransactionSearchFilter(
    queryBuilder: SelectQueryBuilder<UserGift>,
    dto: GetUserGiftsAdminReqDto,
  ): void {
    if (!dto.transactionSearchText) return;

    queryBuilder.andWhere(
      new Brackets((qb) => {
        qb.where(
          `gs_user_gifts.transaction_code ILIKE :transactionSearchText`,
          { transactionSearchText: `${dto.transactionSearchText}%` },
        ).orWhere(
          `gs_user_gifts.dynamic_data->>'voucher_ref_id' ILIKE :transactionSearchText`,
          { transactionSearchText: `${dto.transactionSearchText}%` },
        );
      }),
    );
  }

  /**
   * Applies status filter
   */
  @QueryFilter({ order: 30 })
  private applyStatusFilter(
    queryBuilder: SelectQueryBuilder<UserGift>,
    dto: GetUserGiftsAdminReqDto,
  ): void {
    if (dto.statuses?.length) {
      queryBuilder.andWhere('gs_user_gifts.status IN (:...statuses)', {
        statuses: dto.statuses,
      });
    }
  }

  /**
   * Applies gift type filter (searches in gift snapshot JSONB)
   */
  @QueryFilter({ order: 40 })
  private applyGiftTypeFilter(
    queryBuilder: SelectQueryBuilder<UserGift>,
    dto: GetUserGiftsAdminReqDto,
  ): void {
    if (dto.giftTypes?.length) {
      queryBuilder.andWhere(
        `gs_user_gifts.gift_snapshot->>'type' IN (:...giftTypes)`,
        { giftTypes: dto.giftTypes },
      );
    }
  }

  /**
   * Applies gift ID filter
   */
  @QueryFilter({ order: 50 })
  private applyGiftIdFilter(
    queryBuilder: SelectQueryBuilder<UserGift>,
    dto: GetUserGiftsAdminReqDto,
  ): void {
    if (dto.giftId) {
      queryBuilder.andWhere('gs_user_gifts.gift_id = :giftId', {
        giftId: dto.giftId,
      });
    }
  }

  /**
   * Applies date range filter with default 30-day window if no dates provided
   */
  @QueryFilter({ order: 60 })
  private applyDateRangeFilter(
    queryBuilder: SelectQueryBuilder<UserGift>,
    dto: GetUserGiftsAdminReqDto,
  ): void {
    if (!dto.startDate && !dto.endDate) {
      // Default filter: last 30 days
      queryBuilder.andWhere(
        `gs_user_gifts.created_at >= NOW() - INTERVAL '30 days'`,
      );
      return;
    }

    if (dto.startDate) {
      queryBuilder.andWhere('gs_user_gifts.created_at >= :startDate', {
        startDate: dto.startDate,
      });
    }

    if (dto.endDate) {
      queryBuilder.andWhere('gs_user_gifts.created_at <= :endDate', {
        endDate: dto.endDate,
      });
    }
  }

  /**
   * Fetches users by customer name (last name search)
   */
  private async getUsersByCustomerName(customerName?: string): Promise<User[]> {
    if (!customerName) {
      return [];
    }

    return this.userRepo.find({
      where: {
        lastName: Like(`${customerName}%`),
      },
      select: ['id', 'lastName', 'phoneNumber'],
    });
  }

  /**
   * Fetches users by their IDs
   */
  private async getUserByIds(userIds: number[]): Promise<User[]> {
    if (!userIds?.length) {
      return [];
    }

    return this.userRepo.find({
      where: {
        id: In(userIds),
      },
      select: ['id', 'lastName', 'phoneNumber'],
    });
  }
}
