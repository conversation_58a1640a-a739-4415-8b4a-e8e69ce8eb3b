import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Prefix } from '../../../common/constants/index.constant';
import { UseAdmin } from '../../../common/decorators/user.decorator';
import { AppResponseDto } from '../../../common/dtos/app-response.dto';
import { GetUserGiftsAdminReqDto } from '../../dtos/req/admin/get-user-gifts.admin.req.dto';
import { UserGiftAdminResDto } from '../../dtos/res/admin/user-gift.admin.res.dto';
import { UserGiftAdminService } from '../../services/admin/user-gift.admin.service';

@Controller({ version: '1', path: `${Prefix.ADMIN}/user-gifts` })
@ApiTags('User Gift Admin Controller')
@UseAdmin()
export class UserGiftAdminController {
  constructor(private readonly userGiftAdminService: UserGiftAdminService) {}

  @Get()
  async getUserGifts(@Query() dto: GetUserGiftsAdminReqDto) {
    const { data, meta } = await this.userGiftAdminService.getUserGifts(dto);
    return AppResponseDto.fromNestJsPagination(
      data.map((item) => new UserGiftAdminResDto(item)),
      meta,
    );
  }
}
