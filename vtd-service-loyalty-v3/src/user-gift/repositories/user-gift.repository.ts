import { Injectable } from '@nestjs/common';
import { InjectDataSource } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { DATASOURCE_NAME } from '../../external/constants/index.constant';
import { UserGift } from '../entities/user-gift.entity';

@Injectable()
export class UserGiftRepository extends BaseRepository<UserGift> {
  constructor(
    @InjectDataSource(DATASOURCE_NAME.DATASOUCE_GIFT)
    dataSource: DataSource,
  ) {
    super(UserGift, dataSource);
  }
}
