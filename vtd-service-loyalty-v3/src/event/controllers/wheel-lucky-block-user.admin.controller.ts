import { Controller } from '@nestjs/common';
import { Prefix } from '../../common/constants/index.constant';
import { ApiTags } from '@nestjs/swagger';
import { EnumCodeAdminMenuModule } from 'vtd-common-v3';
import { AdminMenuModuleDecorator } from '../../admin_authorization/common/decorators/admin-menu-module.decorator';
import { UseAdminWithAuthorizeBySetupAdminMenuModuleAcl } from '../../common/decorators/user.decorator';
import { EventAdminService } from '../services/event.admin.service';

@Controller({ version: '1', path: `${Prefix.ADMIN}/wheel-lucky-block-user` })
@UseAdminWithAuthorizeBySetupAdminMenuModuleAcl()
@AdminMenuModuleDecorator(EnumCodeAdminMenuModule.QUAN_LY_SU_KIEN)
@ApiTags('Wheel Lucky Block User Admin Controller')
export class EventAdminController {
  constructor(private eventAdminService: EventAdminService) {}


  
}
