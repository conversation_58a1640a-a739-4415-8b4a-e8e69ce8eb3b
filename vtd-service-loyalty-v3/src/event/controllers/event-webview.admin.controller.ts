import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
  Query,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import {
  EnumCodeAdminMenuModule,
  EnumCodeActionOnAdminMenuModule,
} from 'vtd-common-v3';
import { Prefix } from '../../common/constants/index.constant';
import { AdminMenuModuleDecorator } from '../../admin_authorization/common/decorators/admin-menu-module.decorator';
import { ActionsOnAdminMenuModuleDecorator } from '../../admin_authorization/common/decorators/action-on-admin-menu-module.decorator';
import {
  AuthAdmin,
  UseAdminWithAuthorizeBySetupAdminMenuModuleAcl,
} from '../../common/decorators/user.decorator';
import {
  CreateEventWebviewAdminReqDto,
  GetListEventWebviewAdminReqDto,
  UpdateEventWebviewAdminReqDto,
} from '../dtos/admin/req/event-webview.admin.req.dto';
import { EventWebviewAdminService } from '../services/event-webview.admin.service';
import { AccountData } from '../../proto/account.pb';
import { AppResponseDto } from '../../common/dtos/app-response.dto';
import { EventWebviewAdminResDto } from '../dtos/admin/res/event-webview.admin.res.dto';

@Controller({ version: '1', path: `${Prefix.ADMIN}/event-webview` })
@UseAdminWithAuthorizeBySetupAdminMenuModuleAcl()
@AdminMenuModuleDecorator(EnumCodeAdminMenuModule.QUAN_LY_SU_KIEN)
@ApiTags('Event Webview Admin Controller')
export class EventWebviewAdminController {
  constructor(
    private readonly eventWebviewAdminService: EventWebviewAdminService,
  ) {}

  @Get()
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.READ,
  )
  async getListEventWebview(@Query() dto: GetListEventWebviewAdminReqDto) {
    const { items, meta } =
      await this.eventWebviewAdminService.getListEventWebview(dto);

    const dataResponse = items.map((item) => new EventWebviewAdminResDto(item));

    return AppResponseDto.fromNestJsPagination(dataResponse, meta);
  }

  @Get(':id')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.READ,
  )
  async getEventWebviewById(@Param('id') id: number) {
    return this.eventWebviewAdminService.getEventWebviewById(id);
  }

  @Post()
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.CREATE,
  )
  async createEventWebview(
    @Body() dto: CreateEventWebviewAdminReqDto,
    @AuthAdmin() admin: AccountData,
  ) {
    return this.eventWebviewAdminService.createEventWebview(dto, admin);
  }

  @Patch(':id')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.UPDATE,
  )
  async updateEventWebview(
    @Param('id') id: number,
    @Body() dto: UpdateEventWebviewAdminReqDto,
    @AuthAdmin() admin: AccountData,
  ) {
    return this.eventWebviewAdminService.updateEventWebview(id, dto, admin);
  }

  @Delete(':id')
  @ActionsOnAdminMenuModuleDecorator(
    EnumCodeActionOnAdminMenuModule.FULL_ACCESS,
    EnumCodeActionOnAdminMenuModule.DELETE,
  )
  async deleteEventWebview(
    @Param('id') id: number,
    @AuthAdmin() admin: AccountData,
  ) {
    return this.eventWebviewAdminService.deleteEventWebview(id, admin);
  }
}
