import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { join } from 'path';
import { StoreRepository } from 'src/point/repositories/store.repository';
import { AuthModule } from '../auth/auth.module';
import { AuthService } from '../auth/auth.service';
import { UserRepository } from '../auth/repositories/user.repository';
import { ConfigModule } from '../config/config.module';
import { GoogleSheetService } from '../external/services/google-sheet.service';
import { VgsService } from '../external/services/vgs.service';
import { NotificationHistoryRepository } from '../notification/repositories/notification-history.repository';
import { OutboxMessageRepository } from '../point/repositories/outbox-message.repository';
import {
  ACCOUNT_PACKAGE_NAME,
  ACCOUNT_SERVICE_NAME,
} from '../proto/account.pb';
import { USER_PACKAGE_NAME, USER_SERVICE_NAME } from '../proto/user.pb';
import { TierRepository } from '../tier/repositories/tier.repository';
import { EventUserController } from './controllers/customer/event.customer.controller';
import { EventDetailAdminController } from './controllers/event-detail.admin.controller';
import { EventDetailController } from './controllers/event-detail.controller';
import { EventGropAdminController } from './controllers/event-group.admin.controller';
import { EventPointHistoryAdminController } from './controllers/event-point-history.admin.controller';
import { EventWebviewAdminController } from './controllers/event-webview.admin.controller';
import { EventAdminController } from './controllers/event.admin.controller';
import { EventDetailSupplier } from './entities/event-detail-supplier.entity';
import { VoucherVacxinConsumerCustomerQueue } from './queues/customer/voucher-vacxin-consumer.customer.queue';
import { VoucherVacxinProducerCustomerQueue } from './queues/customer/voucher-vacxin-producer.customer.queue';
import { EventDetailBlockUserRepository } from './repositories/event-detail-block-user.repository';
import { EventDetailExclusionRepository } from './repositories/event-detail-exclusion.repository';
import { EventDetailIgnoreStoreRepository } from './repositories/event-detail-ignore-store.repository';
import { EventDetailPopupRepository } from './repositories/event-detail-popup.repository';
import { EventDetailProvinceRepository } from './repositories/event-detail-province.repository';
import { EventDetailSkuRepository } from './repositories/event-detail-sku.repository';
import { EventDetailToUserTypeRepository } from './repositories/event-detail-to-user-type.repository';
import { EventDetailSupplierRepository } from './repositories/event-detail-supplier.repository';
import { EventDetailSupplierV2Repository } from './repositories/event-detail-supplier-v2.repository';
import { EventDetailToStoreRepository } from './repositories/event-detail-to-store.repository';
import { EventDetailRepository } from './repositories/event-detail.repository';
import { EventGroupGiftExclusionRepository } from './repositories/event-group-gift-exclusion.repository';
import { EventGroupRepository } from './repositories/event-group.repository';
import { EventLimitRepository } from './repositories/event-limit.repository';
import { EventPointHistoryRepository } from './repositories/event-point-history.repository';
import { EventProductChildRepository } from './repositories/event-product-child.repository';
import { EventProductRepository } from './repositories/event-product.repository';
import { EventWebviewRepository } from './repositories/event-webview.repository';
import { EventRepository } from './repositories/event.repository';
import { SelfEventDetailExclusionRepository } from './repositories/self-event-detail-exclusion.repository';
import { VitaCodeRepository } from './repositories/vita-code.repository';
import { WheelLuckyBlockUserRepository } from './repositories/wheel-lucky-block-user.repository';
import { EventCustomerService } from './services/customer/event.customer.service';
import { EventDetailAdminService } from './services/event-detail.admin.service';
import { EventDetailService } from './services/event-detail.service';
import { EventGroupAdminService } from './services/event-group.admin.service';
import { EventPointHistoryAdminService } from './services/event-point-history.admin.service';
import { EventWebviewAdminService } from './services/event-webview.admin.service';
import { EventAdminService } from './services/event.admin.service';
import { CrmTransactionTypeRepository } from '../point/repositories/crm-transaction-type.repository';
import { EventCommonModule } from '../event-common/event-common.module';
import { ExternalModule } from '../external/external.module';
import { EventWebviewCustomerController } from './controllers/customer/event-webview.customer.controller';
import { EventWebviewCustomerService } from './services/customer/event-webview.customer.service';
import { NotiDisplayTemplateRepository } from '../noti/repositories/noti-display-template.repository';
import { EventPopupAdminService } from './services/event-popup.admin.service';
import { EventPopupRepository } from './repositories/event-popup.repository';
import { EventPopupAdminController } from './controllers/event-popup.admin.controller';
import { AdminAuthorizationModule } from '../admin_authorization/admin_authorization.module';
import { AdminActionHistoryModule } from '../admin-action-history/admin-action-history.module';
import { EventDetailTimeGiftingGiftRepository } from './repositories/event-detail-time-gifting-gift.repository';
import { WheelLuckyBlockUserAdminController } from './controllers/wheel-lucky-block-user.admin.controller';
import { WheelLuckyBlockUserAdminService } from './services/wheel-lucky-block-user.admin.service';

@Module({
  imports: [
    ConfigModule,
    HttpModule,
    AuthModule,
    ClientsModule.register([
      {
        name: USER_SERVICE_NAME,
        transport: Transport.GRPC,
        options: {
          url: 'vtd-service-user-v3:50051',
          package: USER_PACKAGE_NAME,
          protoPath: join(
            __dirname + '/../../../node_modules/vtd-common-v3/proto/user.proto',
          ),
        },
      },
      {
        name: ACCOUNT_SERVICE_NAME,
        transport: Transport.GRPC,
        options: {
          url: 'vtd-service-user-v3:50052',
          package: ACCOUNT_PACKAGE_NAME,
          protoPath: join(
            __dirname +
              '/../../../node_modules/vtd-common-v3/proto/account.proto',
          ),
        },
      },
    ]),
    EventCommonModule,
    ExternalModule,
    AdminAuthorizationModule,
    AdminActionHistoryModule,
  ],
  controllers: [
    EventDetailController,
    EventGropAdminController,
    EventAdminController,
    EventDetailAdminController,
    EventPointHistoryAdminController,
    EventUserController,
    EventWebviewAdminController,
    EventWebviewCustomerController,
    EventPopupAdminController,
    WheelLuckyBlockUserAdminController,
  ],
  providers: [
    StoreRepository,
    EventGroupAdminService,
    EventAdminService,
    EventDetailAdminService,
    EventPointHistoryAdminService,
    EventDetailService,
    EventDetailSupplier,
    GoogleSheetService,
    EventDetailToStoreRepository,
    SelfEventDetailExclusionRepository,
    EventRepository,
    EventGroupRepository,
    WheelLuckyBlockUserRepository,
    EventProductRepository,
    EventLimitRepository,
    EventDetailRepository,
    EventDetailProvinceRepository,
    EventDetailExclusionRepository,
    EventGroupGiftExclusionRepository,
    EventPointHistoryRepository,
    EventDetailIgnoreStoreRepository,
    EventProductChildRepository,
    EventDetailSupplierRepository,
    EventDetailSupplierV2Repository,
    EventDetailBlockUserRepository,
    EventDetailSkuRepository,
    EventDetailToUserTypeRepository,
    EventDetailTimeGiftingGiftRepository,
    EventCustomerService,
    AuthService,
    VgsService,
    VitaCodeRepository,
    UserRepository,
    OutboxMessageRepository,
    NotificationHistoryRepository,
    TierRepository,
    VoucherVacxinConsumerCustomerQueue,
    VoucherVacxinProducerCustomerQueue,
    EventWebviewRepository,
    EventWebviewAdminService,
    EventWebviewCustomerService,
    EventDetailPopupRepository,
    CrmTransactionTypeRepository,
    NotiDisplayTemplateRepository,
    EventPopupRepository,
    EventPopupAdminService,
    WheelLuckyBlockUserAdminService,
  ],
  exports: [
    EventRepository,
    EventGroupRepository,
    WheelLuckyBlockUserRepository,
    EventProductRepository,
    EventLimitRepository,
    EventDetailRepository,
    EventDetailProvinceRepository,
    EventDetailExclusionRepository,
    EventGroupGiftExclusionRepository,
    EventPointHistoryRepository,
    EventDetailToStoreRepository,
    SelfEventDetailExclusionRepository,
    EventDetailIgnoreStoreRepository,
    EventDetailSupplierRepository,
    EventDetailSupplierV2Repository,
    EventDetailBlockUserRepository,
    EventDetailSkuRepository,
    EventDetailToUserTypeRepository,
    EventCustomerService,
  ],
})
export class EventModule {}
