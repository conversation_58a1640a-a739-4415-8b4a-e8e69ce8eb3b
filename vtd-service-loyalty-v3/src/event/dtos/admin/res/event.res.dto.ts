import { UserTypeV2 } from '../../../../auth/enums/user.enum';
import { Event } from '../../../entities/event.entity';
import { UpdateEventReqDto } from '../req/event.req.dto';
import { EventStatus } from '../../../enums/event.enum';

export class GetListEventResDto {
  id: number;
  name: string;
  groupName: string;
  userRegisterDate: Date;
  startDate: Date;
  endDate: Date;
  userLimit: number;
  defaultWinRate: number;
  upRate: number;
  downRate: number;
  status: EventStatus;
  eventCustomerLimit: number;
  eventStoreLimit: number;
  eventNewUserLimit: number;
  eventCustomerDefaultWinRate: number;
  eventStoreDefaultWinRate: number;
  eventNewUserDefaultWinRate: number;
  eventCustomerUpRate: number;
  eventStoreUpRate: number;
  eventNewUserUpRate: number;
  eventCustomerDownRate: number;
  eventStoreDownRate: number;
  eventNewUserDownRate: number;

  constructor(event: Event) {
    this.id = event.id;
    this.name = event.name;
    this.userRegisterDate = event.userRegisterDate;
    this.startDate = event.startDate;
    this.endDate = event.endDate;
    this.userLimit = event.userLimit;
    this.defaultWinRate = event.winRateDefault;
    this.upRate = event.upRate;
    this.downRate = event.downRate;
    this.status = event.status;
    this.groupName = event.eventGroup?.name;
    this.setupDataEventLimit(event);
  }

  initializeDataEventLimit() {
    this.eventCustomerLimit = 0;
    this.eventStoreLimit = 0;
    this.eventNewUserLimit = 0;
    this.eventCustomerDefaultWinRate = 0;
    this.eventStoreDefaultWinRate = 0;
    this.eventNewUserDefaultWinRate = 0;
    this.eventCustomerUpRate = 0;
    this.eventStoreUpRate = 0;
    this.eventNewUserUpRate = 0;
    this.eventCustomerDownRate = 0;
    this.eventStoreDownRate = 0;
    this.eventNewUserDownRate = 0;
  }

  setupDataEventLimit(event: Event) {
    this.initializeDataEventLimit();
    event.eventLimits.forEach((eventLimit) => {
      if (UserTypeV2.CUSTOMER == eventLimit.type) {
        this.eventCustomerLimit = eventLimit.userLimit;
        this.eventCustomerDefaultWinRate = eventLimit.winRate;
        this.eventCustomerUpRate = eventLimit.upRate;
        this.eventCustomerDownRate = eventLimit.downRate;
      }
      if (UserTypeV2.STORE == eventLimit.type) {
        this.eventStoreLimit = eventLimit.userLimit;
        this.eventStoreDefaultWinRate = eventLimit.winRate;
        this.eventStoreUpRate = eventLimit.upRate;
        this.eventStoreDownRate = eventLimit.downRate;
      }
      if (UserTypeV2.NEW_USER == eventLimit.type) {
        this.eventNewUserLimit = eventLimit.userLimit;
        this.eventNewUserDefaultWinRate = eventLimit.winRate;
        this.eventNewUserUpRate = eventLimit.upRate;
        this.eventNewUserDownRate = eventLimit.downRate;
      }
    });
  }
}

export class GetDetailEventResDto extends UpdateEventReqDto {
  eventGroupName: number;
  id: number;

  constructor(event: Event) {
    super();

    this.id = event.id;
    this.name = event.name;
    this.eventGroupId = event.eventGroup?.id;
    this.startDate = event.startDate;
    this.endDate = event.endDate;
    this.status = event.status;
    this.skus = event.eventProducts.map((item) => item.sku);
    this.defaultWinRate = event.winRateDefault;
    this.upRate = event.upRate;
    this.downRate = event.downRate;
    this.setupDataEventLimit(event);
    this.activeBlacklist = event.activeBlacklist;
    this.activeLogic400gr = event.activeLogic400gr;
    this.enableGiftBy4Step = event.enableGiftBy4Step;
    this.giftBy4StepGiftOfNormalStep = event.giftBy4StepGiftOfNormalStep;
    this.giftBy4StepGiftOfSpecialStep = event.giftBy4StepGiftOfSpecialStep;
    this.loyaltyProgramSfId = event.loyaltyProgramSfId;
  }

  initializeDataEventLimit() {
    this.eventCustomerLimit = 0;
    this.eventStoreLimit = 0;
    this.eventNewUserLimit = 0;
    this.eventCustomerDefaultWinRate = 0;
    this.eventStoreDefaultWinRate = 0;
    this.eventNewUserDefaultWinRate = 0;
    this.eventCustomerUpRate = 0;
    this.eventStoreUpRate = 0;
    this.eventNewUserUpRate = 0;
    this.eventCustomerDownRate = 0;
    this.eventStoreDownRate = 0;
    this.eventNewUserDownRate = 0;
  }

  setupDataEventLimit(event: Event) {
    this.initializeDataEventLimit();
    event.eventLimits.forEach((eventLimit) => {
      if (UserTypeV2.CUSTOMER == eventLimit.type) {
        this.eventCustomerLimit = eventLimit.userLimit;
        this.eventCustomerDefaultWinRate = eventLimit.winRate;
        this.eventCustomerUpRate = eventLimit.upRate;
        this.eventCustomerDownRate = eventLimit.downRate;
      }
      if (UserTypeV2.STORE == eventLimit.type) {
        this.eventStoreLimit = eventLimit.userLimit;
        this.eventStoreDefaultWinRate = eventLimit.winRate;
        this.eventStoreUpRate = eventLimit.upRate;
        this.eventStoreDownRate = eventLimit.downRate;
      }
      if (UserTypeV2.NEW_USER == eventLimit.type) {
        this.eventNewUserLimit = eventLimit.userLimit;
        this.eventNewUserDefaultWinRate = eventLimit.winRate;
        this.eventNewUserUpRate = eventLimit.upRate;
        this.eventNewUserDownRate = eventLimit.downRate;
      }
    });
  }
}
