import { AutoMapDecorator } from '../../../../common/decorators/automap.decorator';
import { BaseMapperDto } from '../../../../common/dtos/base-mapper.dto';
import { File } from '../../../../file/entities/file.entity';
import { Event } from '../../../entities/event.entity';

export class EventWebviewAdminResDto extends BaseMapperDto {
  @AutoMapDecorator()
  id: number;

  @AutoMapDecorator()
  titleWebview: string;

  @AutoMapDecorator()
  linkWebview: string;

  @AutoMapDecorator()
  linkRule: string;

  @AutoMapDecorator()
  active: boolean;

  @AutoMapDecorator()
  eventId: number;

  @AutoMapDecorator()
  fileId: number;

  @AutoMapDecorator()
  event: Event;

  @AutoMapDecorator()
  file: File;

  @AutoMapDecorator({
    transformSource: (src) => src?.systemFeature?.code || null,
  })
  code: string | null;

  @AutoMapDecorator({
    transformSource: (src) => src?.systemFeature?.description || null,
  })
  description: string | null;
}
