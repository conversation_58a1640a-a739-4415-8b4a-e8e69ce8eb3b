import { PartialType } from '@nestjs/swagger';
import {
  IsValidBoolean,
  IsValidNumber,
  IsValidText,
} from 'src/common/decorators/custom-validator.decorator';
import { PaginationReqDto } from 'src/common/dtos/pagination.dto';

export class CreateEventWebviewAdminReqDto {
  @IsValidText()
  titleWebview: string;

  @IsValidText()
  linkWebview: string;

  @IsValidText({ required: false })
  linkRule: string;

  @IsValidBoolean()
  active: boolean;

  @IsValidNumber()
  eventId: number;

  @IsValidNumber()
  fileId: number;

  @IsValidText()
  code: string;

  @IsValidText({ required: false })
  description: string;

  @IsValidText({ required: false })
  fileUrl: string;
}

export class UpdateEventWebviewAdminReqDto extends PartialType(
  CreateEventWebviewAdminReqDto,
) {}

export class GetListEventWebviewAdminReqDto extends PaginationReqDto {
  @IsValidNumber({ required: false })
  eventId?: number;

  @IsValidText({ required: false })
  eventType?: string;
}
