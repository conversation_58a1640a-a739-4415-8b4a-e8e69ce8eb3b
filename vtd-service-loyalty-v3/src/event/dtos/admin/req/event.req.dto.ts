import {
  IsValidArrayEnum,
  IsValidArrayString,
  IsValidArrayNumber,
  IsValidBoolean,
  IsValidDate,
  IsValidEnum,
  IsValidNumber,
  IsValidText,
} from '../../../../common/decorators/custom-validator.decorator';
import { PaginationReqDto } from '../../../../common/dtos/pagination.dto';
import { EventStatus } from '../../../enums/event.enum';

export enum GetListEventSearchBy {
  EVENT_NAME = 'EVENT_NAME',
  EVENT_GROUP_NAME = 'EVENT_GROUP_NAME',
}

export class GetListEventReqDto extends PaginationReqDto {
  @IsValidText({ required: false })
  searchText?: string;

  @IsValidEnum({ enum: GetListEventSearchBy, required: false })
  searchBy?: GetListEventSearchBy;

  @IsValidEnum({ enum: EventStatus, required: false })
  status?: EventStatus;

  @IsValidDate({ required: false })
  startDate?: Date;

  @IsValidDate({ required: false })
  endDate?: Date;
}

export class CreateEventReqDto {
  @IsValidText()
  name: string;

  @IsValidNumber({ required: false, min: 1 })
  eventGroupId?: number;

  @IsValidDate()
  startDate: Date;

  @IsValidDate()
  endDate: Date;

  @IsValidNumber()
  eventCustomerLimit: number;

  @IsValidNumber()
  eventStoreLimit: number;

  @IsValidNumber()
  eventNewUserLimit: number;

  @IsValidNumber()
  eventCustomerDefaultWinRate: number;

  @IsValidNumber()
  eventStoreDefaultWinRate: number;

  @IsValidNumber()
  eventNewUserDefaultWinRate: number;

  @IsValidNumber()
  eventCustomerUpRate: number;

  @IsValidNumber()
  eventStoreUpRate: number;

  @IsValidNumber()
  eventNewUserUpRate: number;

  @IsValidNumber()
  eventCustomerDownRate: number;

  @IsValidNumber()
  eventStoreDownRate: number;

  @IsValidNumber()
  eventNewUserDownRate: number;

  @IsValidEnum({ enum: EventStatus, required: true })
  status: EventStatus;

  @IsValidArrayString({ unique: true })
  skus: string[];

  @IsValidNumber()
  defaultWinRate: number;

  @IsValidNumber()
  upRate: number;

  @IsValidNumber()
  downRate: number;

  @IsValidBoolean()
  activeBlacklist: boolean;

  @IsValidBoolean()
  activeLogic400gr: boolean;

  @IsValidBoolean()
  enableGiftBy4Step: boolean;

  @IsValidArrayNumber({
    minSize: 1,
    required: false,
  })
  giftBy4StepGiftOfNormalStep?: number[];

  @IsValidArrayNumber({
    minSize: 1,
    required: false,
  })
  giftBy4StepGiftOfSpecialStep?: number[];

  @IsValidText({
    required: false,
    maxLength: 255,
  })
  loyaltyProgramSfId?: string;
}

export class UpdateEventReqDto extends CreateEventReqDto {
  @IsValidNumber({ min: 1 })
  id: number;
}

export class UpdateEventStatusReqDto {
  @IsValidNumber({ min: 1 })
  id: number;

  @IsValidEnum({ enum: EventStatus })
  status: EventStatus;
}

export class CreateEventIgnoreStoreDto {
  @IsValidNumber()
  eventDetailId: number;
  @IsValidText()
  storeCode: string;
}

export class GetListEventIgnoreStoreDto extends PaginationReqDto {
  @IsValidNumber({ required: false })
  eventDetailId?: number;
  @IsValidText({ required: false })
  storeCode?: string;
}
