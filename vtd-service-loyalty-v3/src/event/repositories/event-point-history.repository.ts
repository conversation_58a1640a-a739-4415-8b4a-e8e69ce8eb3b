import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { EventPointHistory } from '../entities/event-point-history.entity';

@Injectable()
export class EventPointHistoryRepository extends BaseRepository<EventPointHistory> {
  constructor(dataSource: DataSource) {
    super(EventPointHistory, dataSource);
  }

  async checkEventPointHistoryExists(eventIds: number[]): Promise<boolean> {
    if (!eventIds?.length) {
      return false;
    }

    const result = await this.createQueryBuilder('eventPointHistory')
      .innerJoin('eventPointHistory.eventDetail', 'eventDetail')
      .where('eventDetail.eventId = ANY(:eventIds::int[])', { eventIds })
      .getRawOne();
    return result ? true : false;
  }
}
