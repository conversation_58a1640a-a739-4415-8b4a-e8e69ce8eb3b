import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  Join<PERSON>olumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { intArrayTransformer } from '../../common/entities/base.entity';
import { EventStatus } from '../enums/event.enum';
import { EventDetail } from './event-detail.entity';
import { EventGroup } from './event-group.entity';
import { EventLimit } from './event-limit.entity';
import { EventProduct } from './event-product.entity';
import { EventUser } from './event-users.entity';
import { EventWebview } from './event-webview.entity';
import { EventPopup } from './event-popup.entity';
import { MapUserToBrandPointEntity } from '../../map-user-to-brand-point/entities/map-user-to-brand-point.entity';

@Entity({ name: 'event' })
export class Event {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'text' })
  name: string;

  @Column({ name: 'user_register_date', type: 'timestamptz', nullable: true })
  userRegisterDate: Date;

  @Column({ name: 'start_date', nullable: true, type: 'timestamptz' })
  startDate: Date;

  @Column({ name: 'end_date', type: 'timestamptz', nullable: true })
  endDate: Date;

  @Column({ type: 'int4', name: 'user_limit' })
  userLimit: number;

  @Column({ type: 'int4', name: 'win_rate_default' })
  winRateDefault: number;

  @Column({ type: 'int4', name: 'up_rate' })
  upRate: number;

  @Column({ type: 'int4', name: 'down_rate' })
  downRate: number;

  @Column({ length: 255, default: EventStatus.ACTIVE })
  status: EventStatus;

  @Column({ name: 'active_blacklist', type: 'boolean', default: false })
  activeBlacklist: boolean;

  @Column({ name: 'event_popup_priority', type: 'int4', nullable: true })
  eventPopupPriority: number;

  @Column({ name: 'active_logic_400_gr', type: 'boolean', default: false })
  activeLogic400gr: boolean;

  @Column({ name: 'enable_gift_by_4_step', type: 'boolean', default: false })
  enableGiftBy4Step: boolean;

  @Column('int', {
    array: true,
    name: 'gift_by_4_step_gift_of_normal_step',
    transformer: intArrayTransformer,
  })
  giftBy4StepGiftOfNormalStep: number[];

  @Column('int', {
    array: true,
    name: 'gift_by_4_step_gift_of_special_step',
    transformer: intArrayTransformer,
  })
  giftBy4StepGiftOfSpecialStep: number[];

  @Column({
    type: 'varchar',
    length: 255,
    name: 'loyalty_program_sf_id',
    nullable: true,
  })
  loyaltyProgramSfId: string;

  // Join event_product
  @OneToMany(() => EventProduct, (ep) => ep.event)
  eventProducts: EventProduct[];
  // End join event_product

  // Join event_users
  @OneToMany(() => EventUser, (eu) => eu.event)
  eventUsers: EventUser[];
  // End join event_users

  // Join event_limit
  @OneToMany(() => EventLimit, (el) => el.event)
  eventLimits: EventLimit[];
  // End join event_limit

  // Join event_group
  @Column({ name: 'event_group_id' })
  eventGroupId: number;

  @ManyToOne(() => EventGroup, (eg) => eg.events, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'event_group_id' })
  eventGroup: EventGroup;
  // End join event_group

  // Join event_detail
  @OneToMany(() => EventDetail, (eu) => eu.event)
  eventDetails: EventDetail[];
  // End join event_detail

  @OneToOne(() => EventWebview, (eventWebview) => eventWebview.eventId)
  eventWebview: EventWebview;

  @OneToOne(() => EventPopup, (eventPopup) => eventPopup.eventId)
  eventPopup: EventPopup;

  @OneToMany(() => MapUserToBrandPointEntity, (p) => p.event)
  userBrandPoints: MapUserToBrandPointEntity[];
}
