import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { BaseEntityWithoutDeletedAtWithoutVersion } from '../../common/entities/base.entity';
import { File } from '../../file/entities/file.entity';
import { Event } from './event.entity';
import { SystemFeature } from '../../system-feature/entities/system-feature.entity';

@Entity({ name: 'event_webview' })
export class EventWebview extends BaseEntityWithoutDeletedAtWithoutVersion {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({ name: 'title_webview' })
  titleWebview: string;

  @Column({ name: 'link_webview' })
  linkWebview: string;

  @Column({ name: 'link_rule', nullable: true })
  linkRule: string;

  @Column({ name: 'active', type: 'boolean', default: false })
  active: boolean;

  @Column({ name: 'event_id', type: 'integer' })
  eventId: number;

  @OneToOne(() => Event, (event) => event.eventWebview)
  @JoinColumn({ name: 'event_id' })
  event: Event;

  @Column({ name: 'file_id' })
  fileId: number;

  @OneToOne(() => File, (file) => file.eventWebview)
  @JoinColumn({ name: 'file_id' })
  file: File;

  @OneToOne(() => SystemFeature, (systemFeature) => systemFeature.eventWebview)
  systemFeature?: SystemFeature;
}
