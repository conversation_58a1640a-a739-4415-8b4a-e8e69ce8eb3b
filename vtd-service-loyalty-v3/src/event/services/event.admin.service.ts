import { HttpException, Injectable } from '@nestjs/common';
import { isEqual } from 'lodash';
import { paginate } from 'nestjs-typeorm-paginate';
import { LoggerService } from 'src/core';
import { In, IsNull } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { AdminActionHistoryRepository } from '../../admin-action-history/repositories/admin-action-history.repository';
import { EnumNameAdminMenuModule } from '../../admin_authorization/common/enums/admin-menu-module.enum';
import { UserTypeV2 } from '../../auth/enums/user.enum';
import { StatusCode } from '../../common/constants/status-code.constant';
import {
  addDayToDate,
  compareDateWithCurrent,
} from '../../common/datetime.util';
import { AppResponseDto } from '../../common/dtos/app-response.dto';
import { DeleteMultipleByNumberIdsDto } from '../../common/dtos/delete-multiple.dto';
import { AppBaseExc } from '../../common/exceptions/custom-app.exception';
import {
  BadRequestExc,
  ConflictExc,
  InternalServerErrorExc,
  NotFoundExc,
} from '../../common/exceptions/custom-http.exception';
import {
  compareDateWithCurrentDateInTimezone,
  formatStringWithSuffix,
  validateArrayHasDuplicateItems,
} from '../../common/utils';
import { EventCommonPopupAdminService } from '../../event-common/services/admin/event-common-popup.admin.service';
import { GiftStatusEnum } from '../../external/java-loyalty';
import { VitaJavaService } from '../../external/services/vita-java.service';
import { CrmTransactionTypeRepository } from '../../point/repositories/crm-transaction-type.repository';
import { AccountData } from '../../proto/account.pb';
import { SystemFeatureRepository } from '../../system-feature/repositorires/system-feature.repository';
import { CreateEventDetailReqDto } from '../dtos/admin/req/event-detail.req.dto';
import {
  CreateEventReqDto,
  GetListEventReqDto,
  GetListEventSearchBy,
  UpdateEventReqDto,
  UpdateEventStatusReqDto,
} from '../dtos/admin/req/event.req.dto';
import {
  GetDetailEventResDto,
  GetListEventResDto,
} from '../dtos/admin/res/event.res.dto';
import { EventProduct } from '../entities/event-product.entity';
import { Event } from '../entities/event.entity';
import { EventStatus } from '../enums/event.enum';
import { EventDetailIgnoreStoreRepository } from '../repositories/event-detail-ignore-store.repository';
import { EventDetailRepository } from '../repositories/event-detail.repository';
import { EventGroupRepository } from '../repositories/event-group.repository';
import { EventLimitRepository } from '../repositories/event-limit.repository';
import { EventPointHistoryRepository } from '../repositories/event-point-history.repository';
import { EventPopupRepository } from '../repositories/event-popup.repository';
import { EventProductRepository } from '../repositories/event-product.repository';
import { EventWebviewRepository } from '../repositories/event-webview.repository';
import { EventRepository } from '../repositories/event.repository';
import { EventDetailAdminService } from './event-detail.admin.service';

@Injectable()
export class EventAdminService {
  private _logger = new LoggerService(EventAdminService.name);
  constructor(
    private eventRepo: EventRepository,
    private eventLimitRepo: EventLimitRepository,
    private eventGroupRepo: EventGroupRepository,
    private eventProductRepo: EventProductRepository,
    private eventDetailIgnoreStoreRepo: EventDetailIgnoreStoreRepository,
    private eventDetailRepo: EventDetailRepository,
    private eventDetailAdminService: EventDetailAdminService,
    private eventWebviewRepo: EventWebviewRepository,
    private crmTransactionTypeRepo: CrmTransactionTypeRepository,
    private eventCommonPopupAdminService: EventCommonPopupAdminService,
    private eventPopupRepo: EventPopupRepository,
    private vitaJavaService: VitaJavaService,
    private readonly adminActionHistoryRepo: AdminActionHistoryRepository,
    private readonly eventPointHistoryRepo: EventPointHistoryRepository,
    private readonly systemFeatureRepo: SystemFeatureRepository,
  ) {}

  async getList(dto: GetListEventReqDto) {
    const { endDate, searchBy, startDate, status, page, limit } = dto;
    let { searchText } = dto;

    const queryBuilder = this.eventRepo
      .createQueryBuilder('event')
      .select('event.id')
      .orderBy('event.id', 'DESC');

    if (searchText && searchBy) {
      searchText = `%${searchText}%`;

      switch (searchBy) {
        case GetListEventSearchBy.EVENT_NAME:
          queryBuilder.where('event.name ILIKE :name', { name: searchText });
          break;

        case GetListEventSearchBy.EVENT_GROUP_NAME:
          queryBuilder.leftJoin('event.eventGroup', 'eventGroup');
          queryBuilder.where('eventGroup.name ILIKE :name', {
            name: searchText,
          });
          break;
      }
    }

    if (status) {
      queryBuilder.andWhere('event.status = :status', { status });
    }

    if (startDate) {
      queryBuilder.andWhere('event.startDate >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('event.endDate <= :endDate', { endDate });
    }

    const { items, meta } = await paginate(queryBuilder, { page, limit });

    const events = await Promise.all(
      items.map((item) =>
        this.eventRepo.findOne({
          where: { id: item.id },
          relations: { eventGroup: true, eventLimits: true },
        }),
      ),
    );

    const eventResDto = events.map((item) => new GetListEventResDto(item));

    return AppResponseDto.fromNestJsPagination(eventResDto, meta);
  }

  async getListNotInGroup() {
    const events = await this.eventRepo.find({
      where: { eventGroupId: IsNull() },
    });
    return new AppResponseDto(events);
  }

  async getDetail(id: number) {
    const result = await this.eventRepo.findOne({
      where: { id },
      relations: {
        eventLimits: true,
        eventProducts: true,
        eventGroup: true,
      },
    });
    // if (!result) throw new AppBaseExc(StatusCode.EVENT_NOT_EXIST);
    if (!result) throw new NotFoundExc(StatusCode.EVENT_NOT_EXIST.msg);

    return new AppResponseDto(new GetDetailEventResDto(result));
  }

  @Transactional()
  async createEvent(dto: CreateEventReqDto, admin: AccountData) {
    const {
      defaultWinRate,
      downRate,
      endDate,
      eventGroupId,
      name,
      startDate,
      status,
      upRate,
      activeBlacklist,
      activeLogic400gr,
      enableGiftBy4Step,
      giftBy4StepGiftOfNormalStep,
      giftBy4StepGiftOfSpecialStep,
      loyaltyProgramSfId,
    } = dto;

    this.validateSetupEventWithEnableGiftBy4Step(dto);
    try {
      const event = await this.eventRepo.save({
        upRate,
        downRate,
        startDate,
        endDate,
        status,
        winRateDefault: defaultWinRate,
        name,
        userLimit: 0,
        activeBlacklist,
        activeLogic400gr,
        enableGiftBy4Step,
        giftBy4StepGiftOfNormalStep,
        giftBy4StepGiftOfSpecialStep,
        ...(eventGroupId && { eventGroupId }),
      });

      await Promise.all([
        this.saveEventLimits(dto, event.id, 'INSERT'),
        this.saveEventSkus([], dto, event.id),
        this.adminActionHistoryRepo.loggingCreateAction(
          admin.email,
          EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
          event.name,
        ),
      ]);

      return this.getDetail(event.id);
    } catch (error) {
      this._logger.error('Error createEvent', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorExc('Lỗi khi tạo sự kiện');
    }
  }

  @Transactional()
  async updateEvent(dto: UpdateEventReqDto, admin: AccountData) {
    const {
      defaultWinRate,
      downRate,
      endDate,
      eventGroupId,
      name,
      startDate,
      status,
      upRate,
      id,
      activeBlacklist,
      activeLogic400gr,
      enableGiftBy4Step,
      giftBy4StepGiftOfNormalStep,
      giftBy4StepGiftOfSpecialStep,
      loyaltyProgramSfId,
    } = dto;

    let event = await this.eventRepo.findOne({
      where: { id },
      relations: { eventProducts: true },
    });

    // if (!event) throw new AppBaseExc(StatusCode.EVENT_NOT_EXIST);
    if (!event) throw new NotFoundExc(StatusCode.EVENT_NOT_EXIST.msg);

    this.validateSetupEventWithEnableGiftBy4Step(dto, event);

    event = {
      ...event,
      name,
      startDate,
      endDate,
      status,
      winRateDefault: defaultWinRate,
      downRate,
      upRate,
      eventGroupId,
      activeBlacklist,
      activeLogic400gr,
      enableGiftBy4Step,
      giftBy4StepGiftOfNormalStep,
      giftBy4StepGiftOfSpecialStep,
      loyaltyProgramSfId,
    };
    try {
      await this.eventRepo.save(event);

      await Promise.all([
        this.saveEventLimits(dto, event.id, 'UPDATE'),
        this.saveEventSkus(event.eventProducts, dto, event.id),
        this.adminActionHistoryRepo.loggingUpdateAction(
          admin.email,
          EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
          event.name,
        ),
        this.updateSystemFeatureActiveByEventId(event.id, status),
      ]);

      return this.getDetail(event.id);
    } catch (error) {
      this._logger.error('Error updateEvent', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorExc('Lỗi khi cập nhật sự kiện');
    }
  }

  @Transactional()
  async updateStatus(dto: UpdateEventStatusReqDto, admin: AccountData) {
    const { id, status } = dto;

    const event = await this.eventRepo.findOne({ where: { id: id } });
    // if (!event) {
    //   throw new AppBaseExc(StatusCode.EVENT_NOT_EXIST);
    // }
    if (!event) throw new NotFoundExc(StatusCode.EVENT_NOT_EXIST.msg);

    try {
      await Promise.all([
        this.eventRepo.update(id, { status }),
        this.adminActionHistoryRepo.loggingUpdateAction(
          admin.email,
          EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
          event.name,
        ),
        this.updateSystemFeatureActiveByEventId(event.id, status),
      ]);

      return new AppResponseDto(StatusCode.SUCCESS);
    } catch (error) {
      this._logger.error('Error update status Event', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorExc('Lỗi khi cập nhật trạng thái sự kiện');
    }
  }

  private async saveEventLimits(
    dto: CreateEventReqDto,
    eventId: number,
    type: 'INSERT' | 'UPDATE',
  ) {
    const {
      eventCustomerLimit,
      eventStoreLimit,
      eventNewUserLimit,
      eventCustomerDefaultWinRate,
      eventStoreDefaultWinRate,
      eventNewUserDefaultWinRate,
      eventCustomerUpRate,
      eventStoreUpRate,
      eventNewUserUpRate,
      eventCustomerDownRate,
      eventStoreDownRate,
      eventNewUserDownRate,
    } = dto;

    if (type === 'INSERT') {
      return this.eventLimitRepo.insert([
        this.eventLimitRepo.create({
          eventId,
          type: UserTypeV2.CUSTOMER,
          userLimit: eventCustomerLimit,
          winRate: eventCustomerDefaultWinRate,
          upRate: eventCustomerUpRate,
          downRate: eventCustomerDownRate,
        }),
        this.eventLimitRepo.create({
          eventId,
          type: UserTypeV2.STORE,
          userLimit: eventStoreLimit,
          winRate: eventStoreDefaultWinRate,
          upRate: eventStoreUpRate,
          downRate: eventStoreDownRate,
        }),
        this.eventLimitRepo.create({
          eventId,
          type: UserTypeV2.NEW_USER,
          userLimit: eventNewUserLimit,
          winRate: eventNewUserDefaultWinRate,
          upRate: eventNewUserUpRate,
          downRate: eventNewUserDownRate,
        }),
      ]);
    }

    if (type === 'UPDATE') {
      return Promise.all([
        this.eventLimitRepo.update(
          { eventId, type: UserTypeV2.CUSTOMER },
          {
            userLimit: eventCustomerLimit,
            winRate: eventCustomerDefaultWinRate,
            upRate: eventCustomerUpRate,
            downRate: eventCustomerDownRate,
          },
        ),
        this.eventLimitRepo.update(
          { eventId, type: UserTypeV2.STORE },
          {
            userLimit: eventStoreLimit,
            winRate: eventStoreDefaultWinRate,
            upRate: eventStoreUpRate,
            downRate: eventStoreDownRate,
          },
        ),
        this.eventLimitRepo.update(
          { eventId, type: UserTypeV2.NEW_USER },
          {
            userLimit: eventNewUserLimit,
            winRate: eventNewUserDefaultWinRate,
            upRate: eventNewUserUpRate,
            downRate: eventNewUserDownRate,
          },
        ),
      ]);
    }
  }

  private async saveEventSkus(
    eventProducts: EventProduct[],
    dto: CreateEventReqDto,
    eventId: number,
  ) {
    const { skus } = dto;
    const eventProductIdsToDelete: number[] = [];
    const eventProductSkusToAdd: EventProduct[] = [];

    eventProducts.forEach((eventProduct) => {
      if (!skus.includes(eventProduct.sku))
        eventProductIdsToDelete.push(eventProduct.id);
    });

    skus.forEach((sku) => {
      if (!eventProducts.some((eventProduct) => eventProduct.sku === sku)) {
        eventProductSkusToAdd.push(
          this.eventProductRepo.create({
            eventId,
            sku,
          }),
        );
      }
    });

    await Promise.all([
      eventProductIdsToDelete.length &&
      this.eventProductRepo.delete(eventProductIdsToDelete),
      this.eventProductRepo.insert(eventProductSkusToAdd),
    ]);
  }

  @Transactional()
  async deleteSingle(id: number, admin: AccountData) {
    const event = await this.eventRepo.findOne({ where: { id: id } });
    // if (!event) {
    //   throw new AppBaseExc(StatusCode.EVENT_NOT_EXIST);
    // }
    if (!event) {
      throw new NotFoundExc(StatusCode.EVENT_NOT_EXIST.msg);
    }
    const eventPointHistoryExists =
      await this.eventPointHistoryRepo.checkEventPointHistoryExists([id]);
    if (eventPointHistoryExists) {
      throw new BadRequestExc('Lỗi khi xoá sự kiện đã có người trúng giải');
    }

    try {
      await this.eventRepo.delete(id);

      await this.adminActionHistoryRepo.loggingDeleteAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        event.name,
      );

      return new AppResponseDto();
    } catch (error) {
      this._logger.error('Error delete single Event', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorExc('Lỗi khi xoá sự kiện');
    }
  }

  @Transactional()
  async deleteMultiple(dto: DeleteMultipleByNumberIdsDto, admin: AccountData) {
    const { ids } = dto;

    const events = await this.eventRepo.findBy({ id: In(ids) });
    if (events.length !== ids.length) {
      // throw new AppBaseExc(StatusCode.EVENT_NOT_EXIST);
      throw new NotFoundExc(StatusCode.EVENT_NOT_EXIST.msg);
    }
    const eventPointHistoryExists =
      await this.eventPointHistoryRepo.checkEventPointHistoryExists(ids);
    if (eventPointHistoryExists) {
      throw new BadRequestExc('Lỗi khi xoá sự kiện đã có người trúng giải');
    }

    try {
      await this.eventRepo.delete(ids);

      // Prepare batch insertion for logging
      const logs = events.map((event) => {
        return {
          adminName: admin.email,
          adminMenu: EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
          recordIdentity: event.name,
        };
      });
      await this.adminActionHistoryRepo.loggingDeleteActions(logs);
      return new AppResponseDto();
    } catch (error) {
      this._logger.error('Error delete multiple Event', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorExc('Lỗi khi xoá sự kiện');
    }
  }

  @Transactional()
  async duplicateEvent(id: number, token: string, admin: AccountData) {
    const event = await this.eventRepo.findOne({
      where: { id },
      relations: {
        eventLimits: true,
        eventProducts: true,
        eventGroup: true,
      },
    });
    // if (!event) throw new AppBaseExc(StatusCode.EVENT_NOT_EXIST);
    if (!event) throw new NotFoundExc(StatusCode.EVENT_NOT_EXIST.msg);

    const newEvent = this.eventRepo.create({
      upRate: event.upRate,
      downRate: event.downRate,
      startDate: event.startDate,
      endDate: event.endDate,
      status: event.status,
      winRateDefault: event.winRateDefault,
      name: event.name,
      userLimit: 0,
      activeBlacklist: event.activeBlacklist,
      ...(event.eventGroupId && { eventGroupId: event.eventGroupId }),
    });
    try {
      await this.eventRepo.insert(newEvent);

      // Create a new event name
      const suffix = `${newEvent.id}`;
      const newEventName = formatStringWithSuffix(newEvent.name, suffix);
      await this.eventRepo.update(newEvent.id, { name: newEventName });

      const eventLimitRepoTypeCustomer = event.eventLimits?.find(
        (eventLimit) => eventLimit.type === UserTypeV2.CUSTOMER,
      );
      const eventLimitRepoTypeStore = event.eventLimits?.find(
        (eventLimit) => eventLimit.type === UserTypeV2.STORE,
      );
      const eventLimitRepoTypeNewUser = event.eventLimits?.find(
        (eventLimit) => eventLimit.type === UserTypeV2.NEW_USER,
      );

      const dto: CreateEventReqDto = {
        defaultWinRate: event.winRateDefault,
        downRate: event.downRate,
        endDate: event.endDate,
        eventGroupId: event.eventGroupId,
        name: event.name,
        startDate: event.startDate,
        status: event.status,
        upRate: event.upRate,
        skus: event.eventProducts.map((ep) => ep.sku),
        eventCustomerLimit: eventLimitRepoTypeCustomer.userLimit,
        eventStoreLimit: eventLimitRepoTypeStore.userLimit,
        eventNewUserLimit: eventLimitRepoTypeNewUser.userLimit,
        eventCustomerDefaultWinRate: eventLimitRepoTypeCustomer.winRate,
        eventStoreDefaultWinRate: eventLimitRepoTypeStore.winRate,
        eventNewUserDefaultWinRate: eventLimitRepoTypeNewUser.winRate,
        eventCustomerUpRate: eventLimitRepoTypeCustomer.upRate,
        eventStoreUpRate: eventLimitRepoTypeStore.upRate,
        eventNewUserUpRate: eventLimitRepoTypeNewUser.upRate,
        eventCustomerDownRate: eventLimitRepoTypeCustomer.downRate,
        eventStoreDownRate: eventLimitRepoTypeCustomer.downRate,
        eventNewUserDownRate: eventLimitRepoTypeCustomer.downRate,
        activeBlacklist: event.activeBlacklist,
        activeLogic400gr: event.activeLogic400gr,
        enableGiftBy4Step: event.enableGiftBy4Step,
        giftBy4StepGiftOfNormalStep: event.giftBy4StepGiftOfNormalStep,
        giftBy4StepGiftOfSpecialStep: event.giftBy4StepGiftOfSpecialStep,
      };

      await Promise.all([
        await this.saveEventLimits(dto, newEvent.id, 'INSERT'),
        await this.saveEventSkus([], dto, newEvent.id),
        await this.duplicateEventDetail(
          event.id,
          newEvent.id,
          suffix,
          token,
          admin,
        ),
        await this.duplicateWebview(event.id, newEvent.id, suffix, admin),
        await this.duplicateEventPopup(event.id, newEvent.id, suffix, admin),
        this.adminActionHistoryRepo.loggingCreateAction(
          admin.email,
          EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
          event.name,
        ),
      ]);

      return this.getDetail(newEvent.id);
    } catch (error) {
      this._logger.error('Error duplicateEvent', error);
      if (error instanceof HttpException) {
        throw error;
      }
      throw new InternalServerErrorExc('Lỗi khi nhân đôi sự kiện');
    }
  }

  private async duplicateEventDetail(
    eventId: number,
    newEventId: number,
    suffix: string | number,
    token: string,
    admin: AccountData,
  ) {
    const eventDetails = await this.eventDetailRepo.find({
      where: { eventId },
    });

    // const promises = eventDetails.map(async (ed) => {
    for (const ed of eventDetails) {
      const eventDetail = await this.eventDetailAdminService.getDetail(ed.id);
      // Create new crmTransactionType
      const crmTransactionType = await this.crmTransactionTypeRepo.findOne({
        where: { id: eventDetail.response.crmTransactionTypeId },
      });
      const newCrmTransactionType = this.crmTransactionTypeRepo.create({
        code: formatStringWithSuffix(crmTransactionType.code, suffix),
        name: 'Event',
        description: formatStringWithSuffix(
          crmTransactionType.description,
          suffix,
        ),
        mainCode: formatStringWithSuffix(crmTransactionType.mainCode, suffix),
        campaignName: formatStringWithSuffix(
          crmTransactionType.campaignName,
          suffix,
        ),
        displayName: formatStringWithSuffix(
          crmTransactionType.displayName,
          suffix,
        ),
      });
      await this.crmTransactionTypeRepo.insert(newCrmTransactionType);

      // Check code of eventDetailPopup
      if (eventDetail.response.eventDetailPopup?.code) {
        const isCodeExisted =
          await this.eventCommonPopupAdminService.checkCodeExist(
            eventDetail.response.eventDetailPopup.code,
          );
        if (isCodeExisted) {
          const newCode = formatStringWithSuffix(
            eventDetail.response.eventDetailPopup.code,
            suffix,
          );
          eventDetail.response.eventDetailPopup.code = newCode;
        }
      }

      // Get current gift
      const currentGift = await this.vitaJavaService.getGiftById(
        token,
        eventDetail.response.gsGiftId,
      );
      // Get gift category
      const giftCategory = await this.vitaJavaService.getGiftCategoryByCode(
        token,
        currentGift?.categoryCode,
      );

      // Get gift category by new code. If not exist -> create
      const request = {
        ...giftCategory,
        code: formatStringWithSuffix(giftCategory.code, suffix),
        name: formatStringWithSuffix(giftCategory.name, suffix),
        isActive: false,
      };

      let newCategory = await this.vitaJavaService.getGiftCategoryByCode(
        token,
        request.code,
      );

      if (!newCategory) {
        const responseCreateGiftCategory =
          await this.vitaJavaService.createGiftCategory(token, request);

        if (
          !responseCreateGiftCategory.success ||
          responseCreateGiftCategory.error
        ) {
          throw new ConflictExc(
            `Lỗi nhân đôi Danh mục quà: ${giftCategory.code}. ${responseCreateGiftCategory.error}`,
          );
        }

        newCategory = responseCreateGiftCategory.data;
      }

      // Calculate end date: if current end date < now -> add 1 day
      const endDate =
        compareDateWithCurrent(currentGift.endDate) < 1
          ? addDayToDate(new Date(), 1)
          : currentGift.endDate;

      // Create new gift
      const resposnseCreateGift = await this.vitaJavaService.createGift(token, {
        ...currentGift,
        name: formatStringWithSuffix(currentGift.name, suffix),
        categoryCode: newCategory.code,
        status: GiftStatusEnum.DISABLED,
        endDate: endDate,
      });
      if (!resposnseCreateGift.success || resposnseCreateGift.error) {
        throw new ConflictExc(
          `Lỗi nhân đôi Quà: ${currentGift.name}. ${resposnseCreateGift.error}`,
        );
      }

      // create new eventDetail
      const dto: CreateEventDetailReqDto = {
        ...eventDetail.response,
        crmTransactionTypeId: newCrmTransactionType.id,
        eventId: newEventId,
        gsGiftId: resposnseCreateGift.data.id,
        eventDetailPopup: eventDetail.response.eventDetailPopup
          ? {
            ...eventDetail.response.eventDetailPopup,
            id: undefined,
          }
          : null,
        eventDetailProvinces: eventDetail.response.eventDetailProvinces.map(
          (edp: any) => {
            delete edp.id;
            return edp;
          },
        ),
        eventDetailSkus: eventDetail.response.eventDetailSkus.map(
          (eds: any) => {
            delete eds.id;
            return eds;
          },
        ),
        eventDetailSupplierV2s: eventDetail.response.eventDetailSupplierV2s.map(
          (eds: any) => {
            delete eds.id;
            return eds;
          },
        ),
        eventDetailToUserTypes: eventDetail.response.eventDetailToUserTypes.map(
          (edtut: any) => {
            delete edtut.id;
            return edtut;
          },
        ),
        validStoreCodes: eventDetail.response.validStoreCodes.map(
          (vsc: any) => {
            delete vsc.id;
            return vsc;
          },
        ),
        eventDetailTimeGiftingGifts:
          eventDetail.response.eventDetailTimeGiftingGifts.map((edtgg: any) => {
            delete edtgg.id;
            return edtgg;
          }),
      };

      try {
        await this.eventDetailAdminService.create(dto, admin, token, true);
      } catch (error) {
        console.log(error);
      }
    }
    // });

    // await Promise.all(promises);
  }

  @Transactional()
  private async duplicateWebview(
    eventId: number,
    newEventId: number,
    suffix: string | number,
    admin: AccountData,
  ) {
    const eventWebview = await this.eventWebviewRepo.findOne({
      where: { eventId },
    });

    if (!eventWebview) return;

    const newEventWebview = {
      ...eventWebview,
      linkWebview: formatStringWithSuffix(eventWebview.linkWebview, suffix),
      eventId: newEventId,
    };

    await Promise.all([
      this.eventWebviewRepo.insert(newEventWebview),
      this.adminActionHistoryRepo.loggingCreateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        newEventWebview.linkWebview,
      ),
    ]);
  }

  @Transactional()
  private async duplicateEventPopup(
    eventId: number,
    newEventId: number,
    suffix: string | number,
    admin: AccountData,
  ) {
    const eventPopups = await this.eventPopupRepo.find({
      where: { eventId },
    });

    if (!eventPopups || !eventPopups.length) return;

    const newEventPopups = eventPopups.map((eventPopup) => {
      const newCode = formatStringWithSuffix(eventPopup.code, suffix);
      const newName = `${eventPopup.title}_${newEventId}`;
      const newEventPopup = this.eventPopupRepo.create({
        ...eventPopup,
        code: newCode,
        title: newName,
        eventId: newEventId,
      });
      return newEventPopup;
    });

    await Promise.all([
      this.eventPopupRepo.insert(newEventPopups),
      this.adminActionHistoryRepo.loggingCreateActions(
        newEventPopups.map((popup) => ({
          adminName: admin.email,
          adminMenu: EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
          // recordIdentity: popup.code,
          recordIdentity: popup.title,
        })),
      ),
    ]);
  }

  /**
   * Validates the configuration of an event when the "gift by 4-step" feature is enabled.
   *
   * This function checks:
   * - If an event is currently running, certain fields must not be changed.
   * - If the feature is enabled, all required fields must be present and valid.
   * - No duplicate gifts are allowed in the normal/special step arrays.
   * - The event's start and end dates must be in the future.
   *
   * @param dto - The event creation or update DTO
   * @param eventUpdating - Optional existing event (only passed during update)
   * @throws AppBaseExc if any validation fails
   */
  private validateSetupEventWithEnableGiftBy4Step(
    dto: CreateEventReqDto | UpdateEventReqDto,
    eventUpdating?: Event,
  ): void {
    const {
      startDate,
      endDate,
      enableGiftBy4Step,
      giftBy4StepGiftOfNormalStep,
      giftBy4StepGiftOfSpecialStep,
    } = dto;

    /**
     * Helper: Determine if the existing event is currently running.
     * An event is considered running if startDate <= now <= endDate.
     */
    const hasEventStarted = (event?: Event): boolean => {
      if (!event) {
        return false;
      }

      const isStarted =
        compareDateWithCurrentDateInTimezone(event.startDate) < 1;
      const isNotEnded =
        compareDateWithCurrentDateInTimezone(event.endDate) > -1;

      if (isStarted && isNotEnded) {
        return true;
      }

      return false;
    };

    /**
     * Helper: Throw a standardized exception when validation fails.
     */
    const throwInvalidGiftBy4StepConfig = (message: string): never => {
      throw new AppBaseExc(StatusCode.SBPS_DEFAULT_ERROR, message, true);
    };

    const configChanged = eventUpdating
      ? !isEqual(eventUpdating!.enableGiftBy4Step, enableGiftBy4Step) ||
      !isEqual(
        eventUpdating!.giftBy4StepGiftOfNormalStep,
        giftBy4StepGiftOfNormalStep,
      ) ||
      !isEqual(
        eventUpdating!.giftBy4StepGiftOfSpecialStep,
        giftBy4StepGiftOfSpecialStep,
      )
      : false;

    // Step 1: If the event is running, do not allow changes to 4-step gift configuration
    if (hasEventStarted(eventUpdating)) {
      if (configChanged) {
        throwInvalidGiftBy4StepConfig(
          // 'Gift-by-4-step event configuration failed. The event is currently running.',
          'Cấu hình sự kiện tặng quà theo 4 bước không thành công. Sự kiện hiện đang diễn ra.',
        );
      }
    }

    // Step 2: If the feature is not enabled, skip further validation
    if (!enableGiftBy4Step) {
      return;
    }

    // Step 3: Validate that both gift arrays are present and not empty
    const isNormalStepGiftInvalid =
      !Array.isArray(giftBy4StepGiftOfNormalStep) ||
      giftBy4StepGiftOfNormalStep.length === 0;

    const isSpecialStepGiftInvalid =
      !Array.isArray(giftBy4StepGiftOfSpecialStep) ||
      giftBy4StepGiftOfSpecialStep.length === 0;

    if (isNormalStepGiftInvalid || isSpecialStepGiftInvalid) {
      throwInvalidGiftBy4StepConfig(
        //  'Gift-by-4-step event configuration failed. Gift data is missing.',
        'Cấu hình sự kiện tặng quà theo 4 bước không thành công. Thiếu dữ liệu quà tặng.',
      );
    }

    // Step 4: Check for duplicate gift IDs across both arrays
    const mergedGifts = [
      ...giftBy4StepGiftOfNormalStep,
      ...giftBy4StepGiftOfSpecialStep,
    ];

    const hasDuplicateGifts = validateArrayHasDuplicateItems(mergedGifts);

    if (hasDuplicateGifts) {
      throwInvalidGiftBy4StepConfig(
        //  'Gift-by-4-step event configuration failed. Duplicate gift data found.',
        'Cấu hình sự kiện tặng quà theo 4 bước không thành công. Đã tìm thấy dữ liệu quà tặng trùng lặp.',
      );
    }

    // Step 5: Ensure both start and end dates are set in the future
    const isStartDateValid =
      compareDateWithCurrentDateInTimezone(startDate) === 1;
    const isEndDateValid = compareDateWithCurrentDateInTimezone(endDate) === 1;

    // Only check if:
    // 1. Create new
    // 2. Update existed and change config
    if (
      (!eventUpdating || configChanged) &&
      (!isStartDateValid || !isEndDateValid)
    ) {
      throwInvalidGiftBy4StepConfig(
        // 'Gift-by-4-step event configuration failed. Start or end date is not in the future.',
        'Cấu hình sự kiện tặng quà theo 4 bước không thành công. Ngày bắt đầu hoặc ngày kết thúc không nằm trong tương lai.',
      );
    }
  }

  private async updateSystemFeatureActiveByEventId(
    eventId: number,
    status: EventStatus,
  ) {
    const eventWebview = await this.systemFeatureRepo
      .createQueryBuilder('systemFeature')
      .leftJoinAndSelect('systemFeature.eventWebview', 'eventWebview')
      .where('eventWebview.eventId = :eventId', { eventId })
      .getOne();

    if (!eventWebview) {
      return;
    }

    const isActive = status === EventStatus.ACTIVE;
    await this.systemFeatureRepo.update(
      { code: eventWebview.code },
      { isActive },
    );
  }
}
