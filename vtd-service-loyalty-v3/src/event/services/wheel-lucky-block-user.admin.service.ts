import { Injectable } from '@nestjs/common';
import { AdminActionHistoryRepository } from '../../admin-action-history/repositories/admin-action-history.repository';
import { AccountData } from '../../proto/account.pb';
import { EnumNameAdminMenuModule } from '../../admin_authorization/common/enums/admin-menu-module.enum';

@Injectable()
export class WheelLuckyBlockUserAdminService {
  constructor(
    private readonly adminActionHistoryRepo: AdminActionHistoryRepository,
  ) {}

  async addUserToBlackList(admin: AccountData) {
    // TODO: Implement
    // this.adminActionHistoryRepo.loggingCreateAction(
    //         admin.email,
    //         EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
    //         event.name,
    //       ),
  }
}
