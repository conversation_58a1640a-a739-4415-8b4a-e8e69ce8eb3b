import { Injectable } from '@nestjs/common';
import { paginate } from 'nestjs-typeorm-paginate';
import { Transactional } from 'typeorm-transactional';
import { AdminActionHistoryRepository } from '../../admin-action-history/repositories/admin-action-history.repository';
import { EnumNameAdminMenuModule } from '../../admin_authorization/common/enums/admin-menu-module.enum';
import { AppResponseDto } from '../../common/dtos/app-response.dto';
import {
  ConflictExc,
  NotFoundExc,
} from '../../common/exceptions/custom-http.exception';
import { AccountData } from '../../proto/account.pb';
import { CreateSystemFeatureAdminReqDto } from '../../system-feature/dtos/req/create-system-feature.admin.req.dto';
import { UpdateSystemFeatureAdminReqDto } from '../../system-feature/dtos/req/update-system-feature.admin.req.dto';
import {
  SystemFeatureAttrCode,
  SystemFeatureGroup,
  SystemFeatureSource,
} from '../../system-feature/enums/system-feature.enum';
import { SystemFeatureAdminService } from '../../system-feature/services/system-feature.admin.service';
import {
  CreateEventWebviewAdminReqDto,
  GetListEventWebviewAdminReqDto,
  UpdateEventWebviewAdminReqDto,
} from '../dtos/admin/req/event-webview.admin.req.dto';
import { EventWebviewRepository } from '../repositories/event-webview.repository';
import { EventRepository } from '../repositories/event.repository';

@Injectable()
export class EventWebviewAdminService {
  constructor(
    private readonly eventWebviewRepo: EventWebviewRepository,
    private readonly eventRepo: EventRepository,
    private readonly adminActionHistoryRepo: AdminActionHistoryRepository,
    private readonly systemFeatureAdminService: SystemFeatureAdminService,
  ) {}
  async getListEventWebview(dto: GetListEventWebviewAdminReqDto) {
    const { page, limit, eventId, eventType } = dto;
    const queryBuilder =
      this.eventWebviewRepo.createQueryBuilder('eventWebview');
    queryBuilder.leftJoinAndSelect('eventWebview.event', 'event');
    queryBuilder.leftJoinAndSelect('eventWebview.file', 'file');
    queryBuilder.leftJoinAndSelect(
      'eventWebview.systemFeature',
      'systemFeature',
    );

    if (eventId) {
      queryBuilder.andWhere('event.id = :eventId', {
        eventId,
      });
    }

    if (eventType) {
      queryBuilder.andWhere('event.type = :eventType', {
        eventType,
      });
    }

    queryBuilder.orderBy('eventWebview.createdAt', 'DESC');
    const paginateResult = await paginate(queryBuilder, {
      limit,
      page,
    });

    return paginateResult;
  }

  async getEventWebviewById(id: number) {
    const eventWebview = await this.eventWebviewRepo.findOne({
      where: { id },
      relations: ['event'],
    });
    if (!eventWebview) {
      throw new NotFoundExc('Event Webview not found');
    }
    return new AppResponseDto(eventWebview);
  }

  @Transactional()
  async createEventWebview(
    dto: CreateEventWebviewAdminReqDto,
    admin: AccountData,
  ) {
    // check eventId is available
    const [eventWebView, event] = await Promise.all([
      this.eventWebviewRepo.findOne({
        where: { eventId: dto.eventId },
      }),
      this.eventRepo.findOneByOrFail({ id: dto.eventId }),
    ]);
    if (eventWebView) {
      throw new ConflictExc('Event đã tồn tại');
    }

    // Create and save EventWebview first to get the ID
    const newEventWebview = this.eventWebviewRepo.create(dto);
    await this.eventWebviewRepo.save(newEventWebview);

    const systemFeatureAttributes: CreateSystemFeatureAdminReqDto['attributes'] =
      [
        { code: SystemFeatureAttrCode.WHEEL_ICON, value: dto.fileUrl },
        { code: SystemFeatureAttrCode.WHEEL_URL, value: dto.linkWebview },
      ];

    // Create SystemFeature with the relationship
    const newSystemFeatureDTO: CreateSystemFeatureAdminReqDto = {
      isActive: true,
      code: dto.code,
      description: dto.description,
      priority: 1,
      group: SystemFeatureGroup.WHEEL,
      source: SystemFeatureSource.RA,
      eventWebviewId: newEventWebview.id,
      attributes: systemFeatureAttributes,
    };

    await Promise.all([
      this.adminActionHistoryRepo.loggingCreateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        event.name,
      ),
      this.systemFeatureAdminService.createSystemFeature(
        newSystemFeatureDTO,
        admin,
      ),
    ]);

    return new AppResponseDto(newEventWebview);
  }

  @Transactional()
  async updateEventWebview(
    id: number,
    dto: UpdateEventWebviewAdminReqDto,
    admin: AccountData,
  ) {
    // Check eventWebview is available
    const [eventWebview, event] = await Promise.all([
      this.eventWebviewRepo.findOne({
        where: { id },
        relations: ['systemFeature'],
      }),
      this.eventRepo.findOneByOrFail({ id: dto.eventId }),
    ]);
    if (!eventWebview) {
      throw new NotFoundExc('Event Webview not found');
    }
    // Update eventWebview
    this.eventWebviewRepo.merge(eventWebview, dto);

    await Promise.all([
      this.eventWebviewRepo.save(eventWebview),
      this.adminActionHistoryRepo.loggingUpdateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        event.name,
      ),
    ]);

    if (eventWebview.systemFeature) {
      const { code } = eventWebview.systemFeature;
      const systemFeatureAttributes: CreateSystemFeatureAdminReqDto['attributes'] =
        [
          { code: SystemFeatureAttrCode.WHEEL_ICON, value: dto.fileUrl },
          { code: SystemFeatureAttrCode.WHEEL_URL, value: dto.linkWebview },
        ];

      const updateSystemFeatureDTO: UpdateSystemFeatureAdminReqDto = {
        description: dto.description,
        attributes: systemFeatureAttributes,
      };

      await this.systemFeatureAdminService.updateSystemFeature(
        code,
        updateSystemFeatureDTO,
        admin,
        true, // skipEventStatusCheck: update ngầm từ webview
      );
    }

    return new AppResponseDto(eventWebview);
  }

  async deleteEventWebview(id: number, admin: AccountData) {
    const eventWebview = await this.eventWebviewRepo.findOne({
      where: { id },
    });
    if (!eventWebview) {
      throw new NotFoundExc('Event Webview not found');
    }
    const event = await this.eventRepo.findOneByOrFail({
      id: eventWebview.eventId,
    });

    await Promise.all([
      this.eventWebviewRepo.remove(eventWebview),
      this.adminActionHistoryRepo.loggingUpdateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        event.name,
      ),
    ]);
  }
}
