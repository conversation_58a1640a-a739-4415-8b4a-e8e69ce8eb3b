import { IStatusCode } from '../interfaces/index.interface';
export type AppStatusCode = Record<StatusCodeKey, IStatusCode>;
// SB & SBPS Add point code only
export const StatusCode: AppStatusCode = {
  // old statuses
  BLOCKED_ACCOUNT_WHEN_SCAN_FAILED: {
    status: 1305,
    msg: 'Tà<PERSON> khoản này đã bị khóa.',
    error: 'error.blocked_account_when_scan_failed',
  },
  BLOCKED_SCAN_SAME_QR: {
    status: 1411,
    msg: 'Tính năng tạm khóa',
    error: 'error.blocked_scan_same_qr',
  },
  SPOON_CODE_NOT_EXIST: {
    status: 1406,
    msg: 'Mã muỗng không tồn tại.',
    error: 'error.spoon_code_not_exist',
  },
  SPOON_CODE_ALREADY_IN_USE: {
    status: 1407,
    msg: 'Mã muỗng đã được sử dụng.',
    error: 'error.spoon_code_already_in_use',
  },
  QR_ALREADY_IN_USE: {
    status: 1404,
    msg: 'Mã QR đã được sử dụng.',
    error: 'error.qr_code_already_in_use',
  },

  // new statuses
  EVENT_DETAIL_ADMIN_DUPLICATE_STORE_CODE_ERROR: {
    status: 2806,
    msg: 'Code cửa hàng trùng',
    error: 'error.api_event_detail_admin_error',
  },
  SB_QR_OUTDATE: {
    status: 1405,
    msg: 'Ngày sản xuất QR không hợp lệ',
    error: 'SB_QR_OUTDATE',
  },
  SUCCESS: { status: 1000, msg: 'OK' },
  API_FAILED_UNKNOWN: {
    status: 1001,
    msg: 'Lỗi chưa xác định',
    error: 'error.api.failed.unknown',
  },
  SB_SPOON_NOEXIST: {
    status: 1413,
    msg: 'Mã muỗng không tồn tại.',
    error: 'SB_SPOON_NOEXIST',
  },
  SB_QR_USED: {
    status: 1402,
    msg: 'Mã QR đã được sử dụng.',
    error: 'SB_QR_USED',
  },
  SB_SPOON_CMD_MISMATCH: {
    status: 1403,
    msg: 'QR và SPOON không khớp.',
    error: 'SB_SPOON_CMD_MISMATCH',
  },
  SB_QR_MANUFACTURE_DATE_MISS: {
    status: 1404,
    msg: 'QR không có ngày sản xuất.',
    error: 'SB_QR_MANUFACTURE_DATE_MISS',
  },
  SB_QR_EXPIRE_DATE_MISS: {
    status: 1405,
    msg: 'QR không có ngày hết hạn.',
    error: 'SB_QR_EXPIRE_DATE_MISS',
  },
  SB_SPOON_APPLIED_DATE_MISS: {
    status: 1406,
    msg: 'SPOON không có ngày áp dụng.',
    error: 'SB_SPOON_APPLIED_DATE_MISS',
  },
  SB_QR_CMD_NULL: {
    status: 1407,
    msg: 'QR không có cmd code.',
    error: 'SB_QR_CMD_NULL',
  },
  SB_SPOON_CMD_NULL: {
    status: 1408,
    msg: 'SPOON không có cmd code.',
    error: 'SB_SPOON_CMD_NULL',
  },
  SB_QR_NOT_FOUND: {
    status: 1409,
    msg: 'QR không có.',
    error: 'SB_QR_NOT_FOUND',
  },
  SB_SPOON_NOT_FOUND: {
    status: 1410,
    msg: 'SPOON không có.',
    error: 'SB_SPOON_NOT_FOUND',
  },
  SB_SAP_NORESPONSE: {
    status: 1410,
    msg: 'SAP không phản hồi.',
    error: 'SB_SAP_NORESPONSE',
  },
  SBPS_QR_USED: {
    status: 1429,
    msg: 'Mã QR đã được sử dụng.',
    error: 'SBPS_QR_USED',
  },
  QR_CODE_INVALID: {
    status: 1403,
    msg: 'Mã QR không hợp lệ.',
    error: 'error.qr_code_not_mapping',
  },
  QR_CODE_ERROR_16: {
    status: 1403,
    msg: 'Mã QR không hợp lệ.',
    error: 'error.qr_code_not_mapping',
  },
  QR_CODE_ERROR_17: {
    status: 1403,
    msg: 'Mã QR không hợp lệ.',
    error: 'error.qr_code_not_mapping',
  },
  PRODUCT_NOT_EXIST: {
    status: 1405,
    msg: 'Sản phẩm không tồn tại.',
    error: 'error.product_not_exist',
  },
  SPOON_CODE_BRAND_ERROR_MAPPING: {
    status: 1413,
    msg: 'Mã muỗng không hợp lệ, bạn vui lòng kiểm tra lại và nhập lại mã muỗng để tham gia tích xu!',
    error: 'error.product_not_applicable',
  },
  SPOON_CODE_WEIGHT_ERROR_MAPPING: {
    status: 1413,
    msg: 'Mã muỗng không hợp lệ, bạn vui lòng kiểm tra lại và nhập lại mã muỗng để tham gia tích xu!',
    error: 'error.product_not_applicable',
  },
  SPOON_CODE_MANUFACTURE_DATE_ERROR_MAPPING: {
    status: 1414,
    msg: 'Mã muỗng không hợp lệ, bạn vui lòng kiểm tra lại và nhập lại mã muỗng để tham gia tích xu!',
    error: 'error.spoon_code_manufacture_date_error_mapping',
  },
  SB_SPOON_USED: {
    status: 1412,
    msg: 'Mã muỗng đã được sử dụng.',
    error: 'SB_SPOON_USED',
  },
  INVALID_TOKEN: {
    status: 1003,
    msg: 'Token không đúng',
    error: 'error.token_invalid',
  },
  EVENT_CODE_INVALID: {
    status: 1101,
    msg: 'Event code không hợp lệ.',
    error: 'error.event_code_not_mapping',
  },
  SBPS_BLOCK_ADDPOINT: {
    status: 1425,
    msg: 'Tính năng tạm khóa.',
    error: 'SBPS_BLOCK_ADDPOINT',
  },
  SPBS_ADD_EMPT: {
    status: 1427,
    msg: 'Chưa nhập địa chỉ mặc định.',
    error: 'SPBS_ADD_EMPT',
  },
  SBPS_EXPIRE: {
    status: 1432,
    msg: 'Mã qr đã hết hạn.',
    error: 'SBPS_EXPIRE',
  },
  SB_EXPIRE: {
    status: 1406,
    msg: 'SB_EXPIRE',
    error: 'SB_EXPIRE',
  },
  RUN_OUT_OF_SCAN_QR_IN_DAY: {
    status: 1000,
    msg: 'Bạn đã hết lượt đổi trong ngày.',
    error: 'error.run_out_of_scan_qr',
  },
  PRODUCT_LINE_NOT_FOUND: {
    status: 1422,
    msg: 'Không tìm thấy product line.',
    error: 'error.product_line_not_found',
  },
  SBPS_QR_NOEXIST: {
    status: 1430,
    msg: 'Mã QR không tồn tại.',
    error: 'SBPS_QR_NOEXIST',
  },
  SBPS_OUTOF_QUANTITY: {
    status: 1430,
    msg: 'Hết giải.',
    error: '2024_SBPS_HETGIAI',
  },
  EVENT_NOT_EXIST: {
    status: 2801,
    msg: 'Sự kiện không tồn tại.',
    error: 'error.event_not_exist',
  },
  EVENT_GROUP_NOT_EXIST: {
    status: 2803,
    msg: 'Nhóm sự kiện không tồn tại',
    error: 'error.event_group_not_exist',
  },
  EVENT_DETAIL_NOT_EXIST: {
    status: 2803,
    msg: 'Chi tiết sự kiện không tồn tại',
    error: 'error.event_detail_not_exist',
  },
  DUPLICATE_TRANSACTION_TYPE_ID: {
    status: 2804,
    msg: 'transaction type đã tồn tại',
    error: 'error.duplicate_transaction_type_id',
  },
  SB_BLOCK_ADDPOINT: {
    status: 1000,
    msg: 'Tính năng tạm khóa',
    error: 'SB_BLOCK_ADDPOINT',
  },
  SB_BLOCK_SAMEQR: {
    status: 1419,
    msg: 'Tính năng tạm khóa',
    error: 'SB_BLOCK_SAMEQR',
  },
  SB_ACCBLOCK_ADDPOINT: {
    status: 1321,
    msg: 'Tài khoản này đã bị khóa.',
    error: 'SB_ACCBLOCK_ADDPOINT',
  },
  SB_ADD_EMPTY: {
    status: 1409,
    msg: 'SB_ADD_EMPTY',
    error: 'SB_ADD_EMPTY',
  },
  SB_DATE_1722: {
    status: 1416,
    msg: 'SB_DATE_1722',
    error: 'SB_DATE_1722',
  },
  SB_DATE_AFTER722: {
    status: 1417,
    msg: 'SB_DATE_AFTER722',
    error: 'SB_DATE_AFTER722',
  },
  SB_DATE_AFTER223: {
    status: 1418,
    msg: 'SB_DATE_AFTER223',
    error: 'SB_DATE_AFTER223',
  },
  SB_QR_UNMATCH: {
    status: 1404,
    msg: 'SB_QR_UNMATCH',
    error: 'SB_QR_UNMATCH',
  },
  SBPS_QR_UNMATCH: {
    status: 1431,
    msg: 'SBPS_QR_UNMATCH',
    error: 'SBPS_QR_UNMATCH',
  },
  SB_DEFAULT_ERROR: {
    status: 1424,
    msg: 'SB_DEFAULT_ERROR',
    error: 'SB_DEFAULT_ERROR',
  },
  SBPS_DEFAULT_ERROR: {
    status: 1436,
    msg: 'SBPS_DEFAULT_ERROR',
    error: 'SBPS_DEFAULT_ERROR',
  },
  SB_QR_ERROR: {
    status: 1401,
    msg: 'SB_QR_ERROR',
    error: 'SB_QR_ERROR',
  },
  SB_QR_NOEXIST: {
    status: 1403,
    msg: 'SB_QR_NOEXIST',
    error: 'SB_QR_NOEXIST',
  },
  SB_OVERADDPOINT: {
    status: 1407,
    msg: 'SB_OVERADDPOINT',
    error: 'SB_OVERADDPOINT',
  },
  SB_SPOON_FORERR: {
    status: 1408,
    msg: 'SB_SPOON_FORERR',
    error: 'SB_SPOON_FORERR',
  },
  SB_SPOON_UNMATCH_BRAND: {
    status: 1414,
    msg: 'SB_SPOON_UNMATCH_BRAND',
    error: 'SB_SPOON_UNMATCH_BRAND',
  },
  SB_SPOON_UNMATCH_WEIGHT: {
    status: 1415,
    msg: 'SB_SPOON_UNMATCH_WEIGHT',
    error: 'SB_SPOON_UNMATCH_WEIGHT',
  },
  SB_BLOCK_SCANQR: {
    status: 1420,
    msg: 'SB_BLOCK_SCANQR',
    error: 'SB_BLOCK_SCANQR',
  },
  SB_QR_SYSTEM_ERROR: {
    status: 1423,
    msg: 'SB_QR_SYSTEM_ERROR',
    error: 'SB_QR_SYSTEM_ERROR',
  },
  SBPS_OVERADDPOINT: {
    status: 1433,
    msg: 'SBPS_OVERADDPOINT',
    error: 'SBPS_OVERADDPOINT',
  },
  SBPS_QR_FORERR: {
    status: 1426,
    msg: 'SBPS_QR_FORERR',
    error: 'SBPS_QR_FORERR',
  },
  EVENT_TIME_ALREADY_EXIST: {
    status: 2805,
    msg: 'thời gian diễn ra sự kiện đã tồn tại',
    error: 'error.event_time_already_existS',
  },
  EVENT_TIME_VALIDATE_ERROR: {
    status: 2806,
    msg: 'error.event_time_validate_error',
    error: 'error.event_time_validate_error',
  },
  GIFT_ADMIN_BKIDS_ERROR: {
    status: 2805,
    msg: 'Lỗi API bkids',
    error: 'error.api_bkids_error',
  },
  OTP_NOT_EXIST: {
    status: 1204,
    msg: 'Mã OTP đã hết hạn.',
    error: 'error.otp_not_exist',
  },
  OTP_INCORRECT: {
    status: 1207,
    msg: 'Mã OTP không chính xác.',
    error: 'OTP_INCORRECT',
  },
  BLOCKED_ACCOUNT_BY_ADMIN: {
    status: 1306,
    msg: 'Tài khoản này đã bị khóa.',
    error: 'error.blocked_account_by_admin',
  },
  OTP_TYPE_NOT_SUPPORT: {
    status: 1205,
    msg: 'Mã OTP không hỗ trợ tính năng này.',
    error: 'error.otp_type_not_support',
  },
  SPOON_CODE_ALREADY_USED_BY_HOTLINE: {
    status: 1209,
    msg: 'Mã QR này đã được sử dụng bởi hotline.',
    error: 'SPOON_CODE_ALREADY_USED_BY_HOTLINE',
  },
  EVENT_ADD_CAN_NOT_FOUND: {
    status: 2801,
    msg: 'Sự kiện không tồn tại.',
    error: 'error.event_add_can_not_found',
  },
  GIFT_CATEGORY_NOT_VALID: {
    status: 2802,
    msg: 'Gift category không đúng.',
    error: 'error.GIFT_CATEGORY_NOT_VALID',
  },
  EVENT_ADD_CAN_FAILED_UNIQUE_CONSTRAINT: {
    status: 2803,
    msg: 'Type must unique.',
    error: 'error.EVENT_ADD_CAN_FAILED_UNIQUE_CONSTRAINT',
  },
  EVENT_ADD_CAN_ALREADY_EXIST: {
    status: 2803,
    msg: 'Sự kiện đã tồn tại.',
    error: 'error.event_add_can_already_exist',
  },
  BLOCKED_ACCOUNT: {
    status: 1703,
    msg: 'Tài khoản này đã bị khóa.',
    error: 'error.blocked_account',
  },
  BANNER_NOT_EXIST: {
    status: 1206,
    msg: 'Banner không tồn tại.',
    error: 'error.banner_not_exist',
  },
  SBPS_ACCBLOCK_ADDPOINT: {
    status: 1434,
    msg: 'Tài khoản bị khoá do quét sbps nhiều lần fail.',
    error: 'SBPS_ACCBLOCK_ADDPOINT',
  },
  USER_NOT_EXIST: {
    status: 1434,
    msg: 'Tài khoản không tồn tại.',
    error: 'error.user_not_exist',
  },
  EVENT_DETAIL_SKU_ADMIN_INVALID: {
    status: 1434,
    msg: 'Dữ  liệu event detail sku không hợp lệ.',
    error: 'error.event_detail_sku_admin_invalid',
  },
  // Fix stupid thing. Ticket 3283
  POPUP_CODE_WELCOME: {
    status: 1430,
    msg: 'Welcome.',
    error: 'WELCOME',
  },
  // Identity code to show
  BLOCK_IDENTITY_SCAN: {
    status: 1434,
    msg: 'Tính năng tích xu đang tạm khóa',
    error: 'BLOCK_IDENTITY_SCAN',
  },
  // OAuth
  OAUTH_PROVIDER_NOT_SUPPORTED: {
    status: 1501,
    msg: 'Không hỗ trợ đăng nhập với Mạng xã hội này.',
    error: 'OAUTH_PROVIDER_NOT_SUPPORTED',
  },
  OAUTH_PROVIDER_NOT_LINKED: {
    status: 1502,
    msg: 'Tài khoản chưa được liên kết với Mạng xã hội.',
    error: 'OAUTH_PROVIDER_NOT_LINKED',
  },
  OAUTH_PROVIDER_LINKED: {
    status: 1503,
    msg: 'Tài khoản của bạn đã được liên kết với Mạng xã hội này rồi.',
    error: 'OAUTH_PROVIDER_LINKED',
  },
  OAUTH_PROVIDER_LINKED_WITH_ANOTHER_USER: {
    status: 1504,
    msg: 'Tài khoản {providerName} đã được liên kết với một tài khoản khác. Hãy gỡ liên kết với tài khoản trước đó trước khi thực hiện tiếp thao tác này.',
    error: 'OAUTH_PROVIDER_LINKED_WITH_ANOTHER_USER',
  },
  OAUTH_PROVIDER_LINK_FAILED: {
    status: 1505,
    msg: 'Liên kết Mạng xã hội thất bại.',
    error: 'OAUTH_PROVIDER_LINK_FAILED',
  },
  PHONE_NUMBER_LINKED_WITH_ANOTHER_OAUTH: {
    status: 1506,
    msg: 'Số điện thoại này đã được liên kết với Tài khoản {providerName}, vui lòng nhập số điện thoại khác.',
    error: 'PHONE_NUMBER_LINKED_WITH_ANOTHER_OAUTH',
  },
  OAUTH_PROVIDER_UNLINK_FAILED: {
    status: 1507,
    msg: 'Huỷ liên kết Mạng xã hội thất bại.',
    error: 'OAUTH_PROVIDER_UNLINK_FAILED',
  },
  PHONE_NUMBER_NOT_EXIST: {
    status: 1202,
    msg: 'Số điện thoại không tồn tại',
    error: 'error.phone_number_not_exist',
  },
  QR_SKU_SETUP_SKU_INVALID: {
    status: 1441,
    msg: 'SKU không hợp lệ',
    error: 'QR_SKU_SETUP_SKU_INVALID',
  },
  QR_SKU_SETUP_EXPIRED_MONTH_INVALID: {
    status: 1442,
    msg: 'Thời gian sản xuất không hợp lệ',
    error: 'QR_SKU_SETUP_EXPIRED_MONTH_INVALID',
  },
};

type StatusCodeKey =
  // identity statuses
  | 'BLOCK_IDENTITY_SCAN'
  // new statuses
  | 'EVENT_DETAIL_ADMIN_DUPLICATE_STORE_CODE_ERROR'
  | 'SB_QR_OUTDATE'
  | 'SUCCESS'
  | 'API_FAILED_UNKNOWN'
  | 'SB_SPOON_NOEXIST'
  | 'SB_QR_USED'
  | 'SB_SPOON_CMD_MISMATCH'
  | 'SB_QR_MANUFACTURE_DATE_MISS'
  | 'SB_QR_EXPIRE_DATE_MISS'
  | 'SB_SPOON_APPLIED_DATE_MISS'
  | 'SB_QR_CMD_NULL'
  | 'SB_SPOON_CMD_NULL'
  | 'SB_QR_NOT_FOUND'
  | 'SB_SPOON_NOT_FOUND'
  | 'SB_SAP_NORESPONSE'
  | 'SBPS_QR_USED'
  | 'QR_CODE_INVALID'
  | 'QR_CODE_ERROR_16'
  | 'QR_CODE_ERROR_17'
  | 'PRODUCT_NOT_EXIST'
  | 'SPOON_CODE_BRAND_ERROR_MAPPING'
  | 'SPOON_CODE_WEIGHT_ERROR_MAPPING'
  | 'SPOON_CODE_MANUFACTURE_DATE_ERROR_MAPPING'
  | 'SB_SPOON_USED'
  | 'INVALID_TOKEN'
  | 'EVENT_CODE_INVALID'
  | 'SBPS_BLOCK_ADDPOINT'
  | 'SPBS_ADD_EMPT'
  | 'SBPS_EXPIRE'
  | 'RUN_OUT_OF_SCAN_QR_IN_DAY'
  | 'PRODUCT_LINE_NOT_FOUND'
  | 'SBPS_QR_NOEXIST'
  | 'SBPS_OUTOF_QUANTITY'
  | 'EVENT_NOT_EXIST'
  | 'EVENT_GROUP_NOT_EXIST'
  | 'EVENT_DETAIL_NOT_EXIST'
  | 'DUPLICATE_TRANSACTION_TYPE_ID'
  | 'SB_BLOCK_ADDPOINT'
  | 'SB_BLOCK_SAMEQR'
  | 'SB_ACCBLOCK_ADDPOINT'
  | 'SB_ADD_EMPTY'
  | 'SB_DATE_1722'
  | 'SB_DATE_AFTER722'
  | 'SB_DATE_AFTER223'
  | 'SB_QR_UNMATCH'
  | 'SBPS_QR_UNMATCH'
  | 'SB_DEFAULT_ERROR'
  | 'SBPS_DEFAULT_ERROR'
  | 'SB_QR_ERROR'
  | 'SB_EXPIRE'
  | 'SB_QR_NOEXIST'
  | 'SB_OVERADDPOINT'
  | 'SB_SPOON_FORERR'
  | 'SB_SPOON_UNMATCH_BRAND'
  | 'SB_SPOON_UNMATCH_WEIGHT'
  | 'SB_BLOCK_SCANQR'
  | 'SB_QR_SYSTEM_ERROR'
  | 'SBPS_OVERADDPOINT'
  | 'SBPS_QR_FORERR'
  | 'EVENT_TIME_ALREADY_EXIST'
  | 'EVENT_TIME_VALIDATE_ERROR'
  | 'GIFT_ADMIN_BKIDS_ERROR'
  | 'OTP_NOT_EXIST'
  | 'OTP_INCORRECT'
  | 'OTP_TYPE_NOT_SUPPORT'
  | 'SPOON_CODE_ALREADY_USED_BY_HOTLINE'
  | 'EVENT_ADD_CAN_NOT_FOUND'
  | 'GIFT_CATEGORY_NOT_VALID'
  | 'EVENT_ADD_CAN_FAILED_UNIQUE_CONSTRAINT'
  | 'EVENT_ADD_CAN_ALREADY_EXIST'
  | 'BANNER_NOT_EXIST'
  | 'SBPS_ACCBLOCK_ADDPOINT'
  | 'EVENT_DETAIL_SKU_ADMIN_INVALID'
  | 'POPUP_CODE_WELCOME'

  // old statuses
  | 'BLOCKED_ACCOUNT_WHEN_SCAN_FAILED'
  | 'BLOCKED_SCAN_SAME_QR'
  | 'SPOON_CODE_NOT_EXIST'
  | 'SPOON_CODE_ALREADY_IN_USE'
  | 'SBPS_QR_FORERR'
  | 'QR_ALREADY_IN_USE'
  | 'BLOCKED_ACCOUNT_BY_ADMIN'
  | 'BLOCKED_ACCOUNT'
  | 'USER_NOT_EXIST'
  | 'OAUTH_PROVIDER_NOT_SUPPORTED'
  | 'OAUTH_PROVIDER_NOT_LINKED'
  | 'OAUTH_PROVIDER_LINKED'
  | 'OAUTH_PROVIDER_NOT_LINKED'
  | 'OAUTH_PROVIDER_LINKED_WITH_ANOTHER_USER'
  | 'OAUTH_PROVIDER_LINK_FAILED'
  | 'OAUTH_PROVIDER_UNLINK_FAILED'
  | 'PHONE_NUMBER_LINKED_WITH_ANOTHER_OAUTH'
  | 'PHONE_NUMBER_NOT_EXIST'
  | 'QR_SKU_SETUP_SKU_INVALID'
  | 'QR_SKU_SETUP_EXPIRED_MONTH_INVALID';
