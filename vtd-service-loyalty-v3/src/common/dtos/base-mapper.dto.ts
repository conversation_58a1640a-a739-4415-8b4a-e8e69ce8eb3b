import 'reflect-metadata';
import { getTransformer<PERSON>ey, PROPS_KEY } from '../decorators/automap.decorator';

export class BaseMapperDto {
  constructor(source: Record<string, any>) {
    if (!source) {
      return;
    }

    const ctor = this.constructor as any;
    const props: string[] = Reflect.getMetadata(PROPS_KEY, ctor) || [];

    for (const key of props) {
      const transformMeta = Reflect.getMetadata(
        getTransformerKey(),
        ctor.prototype,
        key,
      );
      if (transformMeta?.type === 'custom' && transformMeta?.transformSource) {
        this[key] = transformMeta.transformSource(source);
        continue;
      }

      const value = source[key];

      if (value === undefined) {
        this[key] = null;
      } else if (transformMeta) {
        if (transformMeta.type === 'custom') {
          this[key] = transformMeta.transform(value);
        } else if (transformMeta.type === 'single') {
          const TargetDto = transformMeta.ctor();
          this[key] = new TargetDto(value);
        } else if (transformMeta.type === 'array' && Array.isArray(value)) {
          const TargetDto = transformMeta.ctor();
          this[key] = value.map((v: any) => new TargetDto(v));
        } else {
          this[key] = null;
        }
      } else {
        this[key] = value;
      }
    }
  }
}
