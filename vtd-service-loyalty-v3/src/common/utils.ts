import { createHmac } from 'crypto';
import * as crypto from 'crypto-js';
// import * as Buffer from 'buffer';
import dayjs, { Dayjs } from 'dayjs';
import clone from 'clone';
import {
  TIME_ZONE,
  TIME_FORMAT_CRM,
  TIME_FORMAT_DAY,
} from './constants/index.constant';
import { parsePhoneNumber } from 'awesome-phonenumber';

// Use this function to create string value of enum, for Check constraint in Entity
export const getEnumStr = (enumData: Record<string, any>) => {
  const arrayVals = Object.values(enumData);
  let resultStr = '';
  arrayVals.forEach((val, index) => {
    const lastStr = index === arrayVals.length - 1 ? '' : ', ';
    resultStr = resultStr + `'${val}'` + lastStr;
  });

  return resultStr;
};

// Use this function to get enum value. Use for dto
export const getValEnumNumber = (enumData: Record<string, any>) => {
  return Object.values(enumData).filter((v) => Number.isFinite(v));
};

// Use this function to get enum value. Use for dto
export const getValEnumStr = (enumData: Record<string, any>) => {
  return Object.values(enumData).filter((v) => !Number.isFinite(v));
};

export const getKeyEnumStr = (enumData: Record<string, any>) => {
  return Object.keys(enumData).filter((v) => !Number.isFinite(v));
};

export const get = (obj, path, defaultValue = undefined) => {
  const travel = (regexp) =>
    String.prototype.split
      .call(path, regexp)
      .filter(Boolean)
      .reduce(
        (res, key) => (res !== null && res !== undefined ? res[key] : res),
        obj,
      );
  const result = travel(/[,[\]]+?/) || travel(/[,[\].]+?/);
  return result === undefined || result === obj ? defaultValue : result;
};

export const genHmac = (secret: string, data: string, algorithm = 'sha256') => {
  const hmac = createHmac(algorithm, secret).update(data);
  return hmac.digest('hex');
};

export const randomTransactionExternalId = () => {
  const max = 999;
  const min = 1;
  const time = dayjs().valueOf();
  const pre = time.toString(16);
  //const suffix = Math.floor(Math.random() * (max - min) + min);
  const suffix = Math.random().toString(36).substr(2, 5);
  return `${pre}_${suffix}`;
};

export const deepClone = <T extends Record<any, any>>(obj: T): T => {
  return clone(obj);
};

export const subtractMonthFromCurrent = (month: number) => {
  return dayjs().subtract(month, 'M');
};

export const subtractMinuteFromCurrent = (minute: number) => {
  return dayjs().subtract(minute, 'm');
};

export const getTimestampOfDate = (date: any) => {
  return date.valueOf();
};

export const getNowAtTimeZoneHcm = () => {
  // return new Date(
  //   dayjs().tz(TIME_ZONE).format('YYYY-MM-DDTHH:mm:ss.SSS+00:00'),
  // );
  return dayjs().tz(TIME_ZONE).format('YYYY-MM-DDTHH:mm:ss') as unknown as Date;
};

export const getNowAtTimeHcm = () => {
  return dayjs().tz(TIME_ZONE) as unknown as Date;
};

export const makeDateIsDateAtTimeHcm = (date: string | number | Date) => {
  //return dayjs(date).tz(TIME_ZONE) as unknown as Date;
  return dayjs.tz(date, TIME_ZONE) as unknown as Date;
};

export const convertDateToDateInTimeZoneAndFormatToString = (
  date: string | number | Date,
  timezone: string = TIME_ZONE,
  format: string = TIME_FORMAT_CRM,
): string => {
  if (!date) {
    return null;
  }
  //return dayjs(date).tz(TIME_ZONE) as unknown as Date;
  return dayjs(date)
    .tz(timezone)
    .format(format ?? TIME_FORMAT_CRM);
};

export const makeDateIsDateInTimeZoneAndFormatToString = (
  date: string | number | Date,
  timezone: string = TIME_ZONE,
  format: string = TIME_FORMAT_CRM,
) => {
  if (!date) {
    return null;
  }
  //return dayjs(date).tz(TIME_ZONE) as unknown as Date;
  return dayjs.tz(date, timezone).format(format ?? TIME_FORMAT_CRM);
};

export const addMonthToDateAndMakeDateIsDateInTimeZoneAndFormatToString = (
  date: string | number | Date,
  month: number,
  timezone: string = TIME_ZONE,
  format: string = TIME_FORMAT_CRM,
) => {
  //return dayjs(date).tz(TIME_ZONE) as unknown as Date;
  if (!date) {
    return null;
  }
  return dayjs
    .tz(date, timezone)
    .add(month, 'month')
    .format(format ?? TIME_FORMAT_CRM);
};

export const getNowInTimeZoneAndFormatToString = (
  timezone: string = TIME_ZONE,
  format: string = TIME_FORMAT_CRM,
) => {
  //return dayjs(date).tz(TIME_ZONE) as unknown as Date;
  return dayjs()
    .tz(timezone)
    .format(format ?? TIME_FORMAT_CRM);
};

export const getStartOfCurrentYearAtTimeHcm = (
  convertTimeToTimezone = false,
) => {
  const startOfCurrentYear: Dayjs = dayjs().tz(TIME_ZONE).startOf('year');

  return convertTimeToTimezone
    ? (startOfCurrentYear.format('YYYY-MM-DDTHH:mm:ss') as unknown as Date)
    : (startOfCurrentYear as unknown as Date);
};

export const getEndOfCurrentYearAtTimeHcm = (convertTimeToTimezone = false) => {
  const endOfCurrentYear: Dayjs = dayjs().tz(TIME_ZONE).endOf('year');

  return convertTimeToTimezone
    ? (endOfCurrentYear.format('YYYY-MM-DDTHH:mm:ss') as unknown as Date)
    : (endOfCurrentYear as unknown as Date);
};

export const formatDayjsToTimeZoneZero = (day: Dayjs) => {
  return day.tz(TIME_ZONE).format('YYYY-MM-DDTHH:mm:ss+00:00');
};

export const getCurrentTimestampInTimezone = (timezone = TIME_ZONE) => {
  return dayjs().tz(timezone);
};

export const getTimestampInTimezone = (date: Date, timezone = TIME_ZONE) => {
  return dayjs(date).tz(timezone);
};

export const getFormatCurrentDateInTimezone = (
  format = TIME_FORMAT_DAY,
  timezone = TIME_ZONE,
) => {
  return dayjs().tz(timezone).format(format);
};

export const getCurrentDateInTimezone = (timezone = TIME_ZONE) => {
  return dayjs().tz(timezone).toDate();
};

export const getStartOfDateInTimezone = (date: Date, timezone = TIME_ZONE) => {
  return dayjs(date).tz(timezone).startOf('day').toDate();
};

export const getEndOfDateInTimezone = (date: Date, timezone = TIME_ZONE) => {
  return dayjs(date).tz(timezone).endOf('day').toDate();
};

export const getStartOfTodayInTimezone = (timezone = TIME_ZONE) => {
  return dayjs().tz(timezone).startOf('day').toDate();
};

export const getEndOfTodayInTimezone = (timezone = TIME_ZONE) => {
  return dayjs().tz(timezone).endOf('day').toDate();
};

export const getFormattedDateAfterDays = (
  date: Date,
  afterDay: number,
  format = TIME_FORMAT_DAY,
  timezone = TIME_ZONE,
) => {
  return dayjs(date).tz(timezone).add(afterDay, 'day').format(format);
};

export const getFormattedCurrentDateAfterDays = (
  afterDay: number,
  format = TIME_FORMAT_DAY,
  timezone = TIME_ZONE,
) => {
  return getFormattedDateAfterDays(
    dayjs().toDate(),
    afterDay,
    format,
    timezone,
  );
};

export const isStartDayOfDate = (date: Date) => {
  return dayjs(date).isSame(dayjs(date).startOf('day'));
};

/**
 * Compare date with current date in timezone
 *
 * @param date Date to compare
 * @param timezone Default Asia/Ho_Chi_Minh
 * @return -1 if date before current date, 0 if date equal current date and 1 if date after current date
 */
export const compareDateWithCurrentDateInTimezone = (
  date: string | number | Date,
  timezone = TIME_ZONE,
) => {
  const currentTimestamp = getCurrentTimestampInTimezone(timezone);
  const timestamp = dayjs(date).tz(timezone);

  if (timestamp.isBefore(currentTimestamp)) {
    return -1;
  }
  if (timestamp.isAfter(currentTimestamp)) {
    return 1;
  }

  return 0;
};

export function formatDateTime(datetimeStr: string) {
  const date = new Date(datetimeStr);

  const day = String(date.getUTCDate()).padStart(2, '0');
  const month = String(date.getUTCMonth() + 1).padStart(2, '0'); // Months are 0-based
  const year = date.getUTCFullYear();

  const hours = String(date.getUTCHours()).padStart(2, '0');
  const minutes = String(date.getUTCMinutes()).padStart(2, '0');
  const seconds = String(date.getUTCSeconds()).padStart(2, '0');

  return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
}

export function calMonthDiff(d1: Date, d2: Date) {
  let months: number;
  months = Math.abs(d2.getFullYear() - d1.getFullYear()) * 12;
  months -= d1.getMonth();
  months += d2.getMonth();
  return Math.abs(months);
}

export const convertToVietnamTime = (utcDate: string): string => {
  const date = new Date(utcDate);
  date.setHours(date.getHours() + 7); // Cộng thêm 7 giờ để chuyển sang giờ Việt Nam
  return date.toISOString().replace('T', ' ').slice(0, -1); // Định dạng lại chuỗi
};

export const formatDayjsToTimeZoneStartDay = (day: Dayjs) => {
  return day.tz(TIME_ZONE).format('YYYY-MM-DD');
};

export const validateListObjectsIsUniqueBaseOnProperty = (
  listObjects: any[],
  property: string,
) => {
  if (!listObjects || !listObjects.length || !property) {
    return true;
  }

  const uniqueValuesOfProperty = [
    ...new Set(
      listObjects.map((item) => {
        if (!item.hasOwnProperty(property)) {
          return '';
        }
        return item[property];
      }),
    ),
  ];

  return listObjects.length == uniqueValuesOfProperty.length;
};

export const compareDateBetweenFromDateIAndToDateInTimezone = (
  date: string | number | Date,
  fromDate: string | number | Date,
  toDate: string | number | Date,
) => {
  const DayA = makeDateIsDateAtTimeHcm(fromDate);
  const DayB = makeDateIsDateAtTimeHcm(toDate);

  if (
    // if (from_date < date < to_date )=> return 1
    compareDateWithDateInTimezone(date, DayA) === 1 &&
    compareDateWithDateInTimezone(date, DayB) === -1
  ) {
    return 1;
  }
  return -1;
};

export const compareDateWithDateInTimezone = (
  date1: string | number | Date,
  date2: string | number | Date,
  timezone = TIME_ZONE,
) => {
  const timestampDate1 = dayjs(date1).tz(timezone, true);
  const timestampDate2 = dayjs(date2).tz(timezone, true);

  if (timestampDate1.isBefore(timestampDate2)) {
    return -1;
  }
  if (timestampDate1.isAfter(timestampDate2)) {
    return 1;
  }

  return 0;
};

export const compareDateWithDateInTimezoneNewVersion = (
  date1: string | number | Date,
  date2: string | number | Date,
  timezone = TIME_ZONE,
) => {
  const timestampDate1 = dayjs.tz(date1, timezone);
  const timestampDate2 = dayjs.tz(date2, timezone);

  if (timestampDate1.isBefore(timestampDate2)) {
    return -1;
  }
  if (timestampDate1.isAfter(timestampDate2)) {
    return 1;
  }

  return 0;
};

export const getDateDiffInDays = (
  date1: Date,
  date2: Date,
  timezone = TIME_ZONE,
) => {
  const timestampDate1 = dayjs(date1).tz(timezone);
  const timestampDate2 = dayjs(date2).tz(timezone);

  return timestampDate2.diff(timestampDate1, 'day');
};

export function getPhoneE164(
  phone: string,
  regionCode = 'VN',
): string | undefined {
  const phoneNumber = parsePhoneNumber(phone, { regionCode });

  return phoneNumber.possible ? phoneNumber.number.e164 : undefined;
}

// Mã hoá chuỗi thành Base64
export function encodeStringToBase64(str: string): string {
  const buffer = Buffer.from(str);
  return buffer.toString('base64');
}

// Giải mã chuỗi từ Base64
export function decodeBase64ToString(base64Str: string): string {
  const buffer = Buffer.from(base64Str, 'base64');
  return buffer.toString();
}

const secretKey = 'userRequestHistoryPointId_secret_key';

export function encrypto(str: string): string {
  const encryptedText = crypto.AES.encrypt(str, secretKey).toString();

  // Encode the encrypted text in Base64
  const encodedText = Buffer.from(encryptedText).toString('base64');
  return encodedText;
}

export function decrypto(str: string): string {
  const encryptedText = Buffer.from(str, 'base64').toString();

  // Decrypt the text using the secret key
  const decryptedText = crypto.AES.decrypt(encryptedText, secretKey).toString(
    crypto.enc.Utf8,
  );

  return decryptedText;
}

export const isValueInEnum = <T>(enumObj: T, value: any): boolean => {
  return Object.values(enumObj).includes(value);
};

export const sortByAppearance = <T>(sourceList: T[], destinationList: T[]) => {
  let result = sourceList.filter((item) => destinationList.includes(item));
  if (!result || !result.length) {
    result = destinationList;
  }
  destinationList.splice(0, destinationList.length, ...result);
};

export const formatStringWithSuffix = (
  value: string,
  suffix: string | number,
) => {
  return `${value}_${suffix}`;
};

export const sortByAttribute = <T, K extends keyof T>(
  list: T[],
  attribute: K,
  ascending = true,
): T[] => {
  return list.sort((a, b) => {
    const valueA = a[attribute] ?? Infinity;
    const valueB = b[attribute] ?? Infinity;

    if (valueA < valueB) return ascending ? -1 : 1;
    if (valueA > valueB) return ascending ? 1 : -1;
    return 0;
  });
};

export const mergeTwoArraysRemoveDuplicating = <T>(arr1: T[], arr2: T[]) => {
  return [...new Set([...arr1, ...arr2])];
};

export const validateArrayHasDuplicateItems = <T>(arr: T[]) => {
  const seen = new Set<T>();

  for (const item of arr) {
    if (seen.has(item)) {
      return true;
    }

    seen.add(item);
  }

  return false;
};

export const validateSetupTimeValidOnEachRange = <T>(
  item: T,
  nameOfStartTimeField: string,
  nameOfEndTimeField: string,
) => {
  if (!item || !nameOfStartTimeField || !nameOfEndTimeField) {
    return false;
  }
  if (
    !item.hasOwnProperty(nameOfStartTimeField) ||
    !item.hasOwnProperty(nameOfEndTimeField)
  ) {
    return false;
  }

  if (!item[nameOfStartTimeField] || !item[nameOfEndTimeField]) {
    return false;
  }

  if (
    compareDateWithDateInTimezone(
      item[nameOfStartTimeField],
      item[nameOfEndTimeField],
    ) != -1
  ) {
    return false;
  }

  return true;
};

export const validateTimeRangeOverlapInListObject = <T>(
  data: T[],
  nameOfStartTimeField: string,
  nameOfEndTimeField: string,
): boolean => {
  if (!data || !data.length || !nameOfStartTimeField || !nameOfEndTimeField) {
    return true;
  }

  for (const item of data) {
    if (
      validateItemOverlapTimeRangeWithOtherItemInListObject(
        item,
        data,
        nameOfStartTimeField,
        nameOfEndTimeField,
      )
    ) {
      return true;
    }
  }

  return false;
};

export const validateItemOverlapTimeRangeWithOtherItemInListObject = <T>(
  selectedItem: T,
  data: T[],
  nameOfStartTimeField: string,
  nameOfEndTimeField: string,
): boolean => {
  if (
    !selectedItem ||
    !data ||
    !data.length ||
    !nameOfStartTimeField ||
    !nameOfEndTimeField
  ) {
    return true;
  }
  if (
    !selectedItem.hasOwnProperty(nameOfStartTimeField) ||
    !selectedItem.hasOwnProperty(nameOfEndTimeField)
  ) {
    return true;
  }

  for (const item of data) {
    if (
      !item.hasOwnProperty(nameOfStartTimeField) ||
      !item.hasOwnProperty(nameOfEndTimeField)
    ) {
      return true;
    }
    if (selectedItem === item) {
      // Loại bỏ phần tử được chọn
      continue;
    }
    if (
      validateItemOverlapTimeRangeWithOtherItem(
        selectedItem,
        item,
        nameOfStartTimeField,
        nameOfEndTimeField,
      )
    ) {
      return true;
    }
  }

  return false;
};

export const validateItemOverlapTimeRangeWithOtherItem = <T>(
  selectedItem: T,
  otherItem: T,
  nameOfStartTimeField: string,
  nameOfEndTimeField: string,
): boolean => {
  if (
    !selectedItem ||
    !otherItem ||
    !nameOfStartTimeField ||
    !nameOfEndTimeField
  ) {
    return true;
  }
  if (
    !selectedItem.hasOwnProperty(nameOfStartTimeField) ||
    !selectedItem.hasOwnProperty(nameOfEndTimeField) ||
    !otherItem.hasOwnProperty(nameOfStartTimeField) ||
    !otherItem.hasOwnProperty(nameOfEndTimeField)
  ) {
    return true;
  }

  if (
    //if param 1 is between 2 and 3 ==> return 1
    compareDateBetweenFromDateIAndToDateInTimezone(
      selectedItem[nameOfStartTimeField],
      otherItem[nameOfStartTimeField],
      otherItem[nameOfEndTimeField],
    ) == 1 ||
    compareDateBetweenFromDateIAndToDateInTimezone(
      selectedItem[nameOfEndTimeField],
      otherItem[nameOfStartTimeField],
      otherItem[nameOfEndTimeField],
    ) == 1 ||
    compareDateBetweenFromDateIAndToDateInTimezone(
      otherItem[nameOfStartTimeField],
      selectedItem[nameOfStartTimeField],
      selectedItem[nameOfEndTimeField],
    ) == 1 ||
    compareDateBetweenFromDateIAndToDateInTimezone(
      otherItem[nameOfEndTimeField],
      selectedItem[nameOfStartTimeField],
      selectedItem[nameOfEndTimeField],
    ) == 1 ||
    //if param 1 is equal to 2  ==> return 0
    compareDateWithDateInTimezone(
      selectedItem[nameOfStartTimeField],
      otherItem[nameOfStartTimeField],
    ) == 0 ||
    compareDateWithDateInTimezone(
      selectedItem[nameOfEndTimeField],
      otherItem[nameOfEndTimeField],
    ) == 0 ||
    compareDateWithDateInTimezone(
      otherItem[nameOfStartTimeField],
      selectedItem[nameOfStartTimeField],
    ) == 0 ||
    compareDateWithDateInTimezone(
      otherItem[nameOfEndTimeField],
      selectedItem[nameOfEndTimeField],
    ) == 0
  ) {
    return true;
  }

  return false;
};

export const uniqueArray = <T>(array: T[]): T[] => [...new Set(array)];

export const generateRandomString = (length: number): string => {
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    result += characters[randomIndex];
  }

  return result;
};

export const convertObjectToString = (obj: any): string => {
  try {
    return JSON.stringify(obj);
  } catch (err) {
    return '';
  }
};

export const convertStringToObject = (str: string): any => {
  try {
    return JSON.parse(str);
  } catch (err) {
    return null;
  }
};

export const camelToSnake = (s: string) => {
  return s.replace(/[A-Z]/g, (m) => `_${m.toLowerCase()}`);
};

export const snakeToCamel = (s: string) => {
  return s.replace(/_([a-z])/g, (_, c) => c.toUpperCase());
};

export const mapKeysDeep = <T = any>(val: any, dir: 'to' | 'from'): T => {
  if (val === null || typeof val !== 'object' || val instanceof Date) {
    return val;
  }

  if (Array.isArray(val)) return val.map((v) => mapKeysDeep(v, dir)) as any;

  const out: Record<string, any> = {};
  for (const [k, v] of Object.entries(val)) {
    const nk = dir === 'to' ? camelToSnake(k) : snakeToCamel(k);
    out[nk] = mapKeysDeep(v, dir);
  }

  return out as T;
};
