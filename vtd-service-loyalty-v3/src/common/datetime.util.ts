import dayjs from 'dayjs';
import { TIME_FORMAT_CRM, TIME_ZONE } from './constants/index.constant';

export const getNowAtTimezone = (tz = TIME_ZONE) => {
  return dayjs().tz(tz) as unknown as Date;
};

export const makeDateIsDateAtTimezone = (
  date: string | number | Date,
  tz = TIME_ZONE,
) => {
  return dayjs.tz(date, tz) as unknown as Date;
};

export const convertDateToDateAtTimezone = (
  date: string | number | Date,
  tz = TIME_ZONE,
) => {
  return dayjs(date).tz(tz) as unknown as Date;
};

export const formatToString = (
  date: string | number | Date,
  format: string = TIME_FORMAT_CRM,
): string => {
  if (!date) {
    return null;
  }

  return dayjs(date).format(format ?? TIME_FORMAT_CRM);
};

export const addMonthToDate = (date: string | number | Date, month: number) => {
  if (!date) {
    return null;
  }

  return dayjs(date).add(month, 'month') as unknown as Date;
};

export const addMinuteToDate = (
  date: string | number | Date,
  minute: number,
) => {
  if (!date) {
    return null;
  }

  return dayjs(date).add(minute, 'minute').toDate();
};

export const compareDateWithDateInTimezone = (
  date1: string | number | Date,
  date2: string | number | Date,
) => {
  const timestampDate1 = dayjs(date1);
  const timestampDate2 = dayjs(date2);

  if (timestampDate1.isBefore(timestampDate2)) {
    return -1;
  }
  if (timestampDate1.isAfter(timestampDate2)) {
    return 1;
  }

  return 0;
};

export const compareDateWithCurrent = (date: string | number | Date) => {
  const current = dayjs().toDate();

  return compareDateWithDateInTimezone(date, current);
};

export const getStartOfNowInTimezone = (timezone = TIME_ZONE) => {
  return getStartOfDateInTimezone(getNowAtTimezone(timezone), timezone);
};

export const getStartOfDateInTimezone = (date: Date, timezone = TIME_ZONE) => {
  return dayjs(date).tz(timezone).startOf('day').toDate();
};
