import { boolean } from 'boolean';
import * as dotenv from 'dotenv';
import { RecursiveKeyOf } from '../types/utils.type';
dotenv.config();

const globalConfig = {
  nodeEnv: process.env.NODE_ENV,
  useNotiV3: boolean(process.env.USE_NOTI_V3),
  auth: {
    jwtSecretKey: process.env.JWT_SECRET_KEY,
    hotlineToken: process.env.HOTLINE_TOKEN,
    chatbotToken: process.env.CHATBOT_TOKEN,
    triplayzToken: process.env.TRIPLAYZ_AUTH_TOKEN,
    cronjobToken: process.env.CRONJOB_TOKEN,
  },
  port: +process.env.PORT || 5000,
  grpc: {
    userSerivce: {
      url: process.env.GRPC_USER_SERVICE_URL || 'vtd-service-user-v3:50051',
    },
    accountService: {
      url: process.env.GRPC_ACCOUNT_SERVICE_URL || 'vtd-service-user-v3:50052',
    },
  },
  qrBravo: {
    secretKey: process.env.BRAVO_SECRET_KEY,
    partner: process.env.BRAVO_PARTNER,
    baseUrl: process.env.BRAVO_BASE_URL,
    funcGetInfoEx: process.env.BRAVO_FUNC_GET_INFO,
  },
  triplayz: {
    apiUrl: process.env.TRIPLAYZ_API_URL,
    token: process.env.TRIPLAYZ_API_TOKEN,
  },
  crm: {
    baseUrl: process.env.CRM_BASE_URL,
    addPointUrl: process.env.CRM_ADD_POINT_URL,
    auth: {
      api: process.env.CRM_AUTH_API,
      grantType: process.env.CRM_AUTH_GRANT_TYPE,
      clientId: process.env.CRM_AUTH_CLIENT_ID,
      clientSecret: process.env.CRM_AUTH_CLIENT_SECRET,
      userName: process.env.CRM_AUTH_USER_NAME,
      password: process.env.CRM_AUTH_PASSWORD,
      programName: process.env.CRM_AUTH_PROGRAM_NAME,
      refreshToken: process.env.CRM_REFRESH_TOKEN,
      accessToken: process.env.CRM_ACCESS_TOKEN,
      createCaseSfOwnerId: process.env.CRM_CREATE_CASE_OWNER_ID,
    },
    api: {
      checkLeadByPhone: process.env.CRM_CHECK_LEAD_BY_PHONE_API,
      convertLead: process.env.CRM_CONVERT_LEAD_API,
      convertLeadByPhone: process.env.CRM_CONVERT_LEAD_BY_PHONE_API,
      insertTransaction: process.env.CRM_INSERT_TRANSACTION_API,
      redeemGiftTransaction: process.env.CRM_REDEEM_GIFT_TRANSACTION_API,
      redeemVoucherTransaction: process.env.CRM_REDEEM_VOUCHER_TRANSACTION_API,
      getLeadInfo: process.env.CRM_GET_LEAD_INFO_API,
      createCaseSf: process.env.CRM_CREATE_CASE_URL,
    },
    syncNotiStatus: {
      fullUrl: process.env.CRM_SYNC_NOTI_STATUS_FULL_URL,
      bearerToken: process.env.CRM_SYNC_NOTI_STATUS_BEARER_TOKEN,
      fullUrlLegacy: process.env.CRM_SYNC_NOTI_STATUS_FULL_URL_LEGACY,
      bearerTokenLegacy: process.env.CRM_SYNC_NOTI_STATUS_BEARER_TOKEN_LEGACY,
    },
    options: {
      syncUsingWh: boolean(process.env.CRM_SYNC_USING_WH),
    },
  },
  sap: {
    baseUrl: process.env.SAP_BASE_URL,
    authUrl: process.env.SAP_AUTH_URL,
    apiKey: process.env.SAP_API_KEY,
    auth: {
      requestTokenApi: process.env.SAP_AUTH_REQUEST_TOKEN_API,
      grantType: process.env.SAP_AUTH_GRANT_TYPE,
      clientId: process.env.SAP_AUTH_CLIENT_ID,
      clientSecret: process.env.SAP_AUTH_CLIENT_SECRET,
      scope: process.env.SAP_AUTH_SCOPE,
    },
    api: {
      getProductByQrApi: process.env.SAP_GET_PRODUCT_BY_QR_API,
      getProductBySpoonApi: process.env.SAP_GET_PRODUCT_BY_SPOON_API,
    },
    feature: {
      enableGetProduct: boolean(process.env.SAP_ENABLE_GET_PRODUCT),
      enableLoggingGetProduct: boolean(
        process.env.SAP_ENABLE_LOGGING_GET_PRODUCT,
      ),
    },
    requestTimeout: +process.env.SAP_REQUEST_TIMEOUT || 30000,
  },
  vgs: {
    baseUrl: process.env.VGS_BASE_URL,
    baseSmsUrl: process.env.VGS_SMS_BASE_URL,
    baseZaloUrl: process.env.VGS_ZALO_BASE_URL,
    username: process.env.VGS_USERNAME,
    cskhSmsUrl: process.env.VGS_CSKH_SMS_URL,
    token: process.env.VGS_TOKEN,
    znsOaId: process.env.VGS_ZNS_OA_ID,
    templateOtpId: process.env.VGS_TEMPLATE_OTP_ID,
    templateRemindDownTierRank180DaysId:
      process.env.VGS_TEMPLATE_REMIND_DOWN_TIER_RANK_AFTER_180_DAYS_ID,
    templateRemindDownTierRank365DaysId:
      process.env.VGS_TEMPLATE_REMIND_DOWN_TIER_RANK_AFTER_365_DAYS_ID,
    branchName: process.env.VGS_BRANCH_NAME,
    header: {
      accessToken: process.env.CRM_ACCESS_TOKEN,
    },
    api: {
      redeemStore: process.env.CRM_REDEEM_STORE_API,
    },
  },
  vitaGo: {
    baseUrl: process.env.VITA_GO_BASE_URL,
    api: {
      addPlayGameTimes: process.env.VITA_GO_ADD_PLAYGAME_TIMES_API,
      saveExternalApi: process.env.VITA_GO_SAVE_EXTERNAL_API,
    },
  },
  vitaJava: {
    notification: {
      baseUrl: process.env.VITA_JAVA_NOTIFCATION_BASE_URL,
      pushNotiApi: process.env.VITA_JAVA_NOTIFCATION_PUSH_NOTI_API,
    },
    spoon: {
      baseUrl: process.env.VITA_SPOON_BASE_URL,
      authenticate: process.env.VITA_SPOON_AUTHENTICATE,
      getAccountById: process.env.VITA_SPOON_GET_ACCOUNT_BY_ID,
    },
    application: {
      baseUrl: process.env.VITA_JAVA_APPLICATION_BASE_URL,
      exchangeGift: process.env.VITA_JAVA_APPLICATION_EXCHANGE_GIFT_V2,
    },
    application_v4: {
      enable: boolean(process.env.VITA_JAVA_APPLICATION_V4_ENABLE),
      baseUrl: process.env.VITA_JAVA_APPLICATION_V4_BASE_URL,
      giftingGift: process.env.VITA_JAVA_APPLICATION_V4_GIFTING_GIFT,
      giftingGiftNoAuthenToken:
        process.env.VITA_JAVA_APPLICATION_V4_GIFTING_GIFT_NO_AUTHEN_TOKEN,
      removeUserGiftGiftingNoAuthenToken:
        process.env
          .VITA_JAVA_APPLICATION_V4_REMOVE_GIFTING_GIFT_NO_AUTHEN_TOKEN,
      getQuantityGift: process.env.VITA_JAVA_APPLICATION_V4_GET_QUANTITY_GIFT,
      reuseUserGift: process.env.VITA_JAVA_APPLICATION_V4_REUSE_USER_GIFT,
      reuseUserGiftNoAuthenToken:
        process.env.VITA_JAVA_APPLICATION_V4_REUSE_USER_GIFT_NO_AUTHEN_TOKEN,
      getUserGiftPreOrderEnoughPointToSendNotify:
        process.env
          .VITA_JAVA_APPLICATION_V4_GET_USER_GIFT_PRE_ORDER_ENOUGH_POINT_TO_SEND_NOTIFY,
      gifts: process.env.VITA_JAVA_APPLICATION_V4_GIFTS,
      admin_gifts: process.env.VITA_JAVA_APPLICATION_V4_ADMIN_GIFTS,
      gift_categories: process.env.VITA_JAVA_APPLICATION_V4_GIFT_CAETGORIES,
      admin_gift_categories:
        process.env.VITA_JAVA_APPLICATION_V4_ADMIN_GIFT_CAETGORIES,
    },
    wh_v4: {
      baseUrl: process.env.VITA_JAVA_WH_V4_BASE_URL,
      hashKey: process.env.VITA_JAVA_WH_V4_HASH_KEY,
      insertEventPointHistoryToWh:
        process.env.VITA_JAVA_WH_V4_INSERT_EVENT_POINT_HISTORY_TO_WH,
      updateEventPointHistoryToWh:
        process.env.VITA_JAVA_WH_V4_UPDATE_EVENT_POINT_HISTORY_TO_WH,
      insertEventPointHistoryToWhNoAuthToken:
        process.env
          .VITA_JAVA_WH_V4_INSERT_EVENT_POINT_HISTORY_TO_WH_NO_AUTH_TOKEN,
      updateEventPointHistoryToWhNoAuthToken:
        process.env
          .VITA_JAVA_WH_V4_UPDATE_EVENT_POINT_HISTORY_TO_WH_NO_AUTH_TOKEN,
      syncTo3rdService: process.env.VITA_JAVA_WH_V4_SYNC_TO_3RD_SERVICE,
      syncTo3rdServiceNoAuthenToken:
        process.env.VITA_JAVA_WH_V4_SYNC_TO_3RD_SERVICE_NO_AUTHEN_TOKEN,
    },
  },
  bkids: {
    baseUrl: process.env.BEEKIDS_BASE_URL,
    getCourses: process.env.BEEKIDS_GET_COURSES,
    getTrainingPaths: process.env.BEEKIDS_GET_TRAINING_PATHS,
    apiKey: process.env.BEEKIDS_KEY_ID,
    apiSecret: process.env.BEEKIDS_KEY_SECRET,
  },
  popupTemplateId: {
    mum: process.env.POPUP_MUM_TEMPLATE_ID,
  },
  spoon: {
    esIndex: process.env.ES_SPOON_INDEX,
  },
  es: {
    syncSfFailedLogging: {
      index: process.env.ES_SYNC_SF_FAIL_LOGGING_INDEX,
    },
    node: process.env.ES_NODE,
    username: process.env.ES_USERNAME,
    password: process.env.ES_PASSWORD,
    logIndex: process.env.ES_LOG_INDEX,
    exchangeGiftLogging: {
      index: process.env.ES_EXCHANGE_GIFT_LOGGING_INDEX,
      monthKeep: process.env.ES_EXCHANGE_GIFT_LOGGING_MONTH_KEEP,
    },
  },
  trackingUserAction: {
    batchCycle: process.env.TRACKING_USER_ACTION_BATCH_CYCLE,
    batchSize: process.env.TRACKING_USER_ACTION_BATCH_SIZE,
    maxIterations: process.env.TRACKING_USER_ACTION_MAX_ITERATIONS,
    deleteAfterDays: process.env.TRACKING_USER_ACTION_DELETE_AFTER_DAYS,
  },
  event: {
    numberCheckQ2: process.env.VITA_CHECK_EVENT_Q2,
    numberCheckQ2TC: process.env.VITA_CHECK_EVENT_Q2_TC,
    numberCheckQ2CBB: process.env.VITA_CHECK_EVENT_Q2_CBB,
    numberCheckQ3CBB: process.env.VITA_CHECK_EVENT_Q3_CBB,
    numberCheckQ3DHA: process.env.VITA_CHECK_EVENT_Q3_DHA,
    numberCheckQ3OPTI: process.env.VITA_CHECK_EVENT_Q3_OPTI,
    numberCheckProduct01: process.env.VITA_CHECK_EVENT_PRODUCT_01,
    numberCheckEventDhaOpti: process.env.VITA_CHECK_EVENT_DHA_OPTI,
    numberCheckQ3CLG: process.env.VITA_CHECK_EVENT_Q3_CLG,
    numberCheckQ3OGGI: process.env.VITA_CHECK_EVENT_Q3_OGGI,
    numberCheckQ3CLS: process.env.VITA_CHECK_EVENT_Q3_CLS,
    numberCheckQ4DHA: process.env.VITA_CHECK_EVENT_Q4_DHA,
    numberCheckQ4CBB: process.env.VITA_CHECK_EVENT_Q4_CBB,
    numberCheckQ4X2Xu: process.env.VITA_CHECK_EVENT_Q4_X2_XU,
    eventQuy12024MayRui: process.env.VITA_EVENT_QUY1_2024_MAY_RUI_ID,
    eventThang52024BigC: process.env.VITA_EVENT_THANG5_2024_BIG_C,
    eventQuy32024UpRank: process.env.VITA_CHECK_EVENT_Q3_2024_UP_RANK,
    eventQuy32024ClbbMassSampling:
      process.env.VITA_CHECK_EVENT_Q3_2024_CLBB_MASS_SAMPLING,
    eventQuy32024ClbbMassSamplingExcludeSkus:
      process.env.VITA_EXCLUDE_SKUS_EVENT_Q3_2024_CLBB_MASS_SAMPLING,
    eventQuy32024ClbbVoucherTopup:
      process.env.VITA_CHECK_EVENT_Q3_2024_CLBB_VOUCHER_TOPUP,
    eventQuy42024WaSbps: process.env.VITA_CHECK_EVENT_Q4_2024_WA_SBPS,
    eventLatestId: process.env.VITA_EVENT_LATEST_ID,
    eventAddCanLatestId: process.env.VITA_EVENT_ADD_CAN_LATEST_ID,
    debugEventGiftBy4Step: boolean(
      process.env.ENABLE_DEBUG_EVENT_GIFT_BY_4_STEP,
    ),
  },
  gift: {
    gift400GrEventQuy32024UpRank:
      process.env.VITA_GIFT_400GR_EVENT_Q3_2024_UP_RANK,
  },
  webhook: {
    vgsAccessToken: process.env.WEBHOOK_VGS_ACCESS_TOKEN,
  },
  mail: {
    gmail: process.env.GMAIL,
    password: process.env.GMAIL_PASS,
  },
  app: {
    appVersionName: process.env.APP_VERSION_NAME,
    runningAsCronJob: boolean(process.env.RUNNING_AS_CRONJOB),
  },
  syncNoti: {
    syncCodeLimit: +process.env.SYNC_CODE_LIMIT,
    syncCodeEachTime: +process.env.SYNC_CODE_EACH_TIME,
  },
  // TODO: using tmp_redis because conflict config with code of branch feature/cong-tesosoft-implement-cron-process-resync-crm-transaction
  // TODO: remove it after merge branch to prod
  tmp_redis: {
    host: process.env.REDIS_HOST,
    port: process.env.REDIS_PORT,
  },
  googleSheet: {
    clientEmail: process.env.EVENT_4_LON_GOOGLE_SHEET_CLIENT_EMAIL,
    privateKey: process.env.EVENT_4_LON_GOOGLE_SHEET_PRIVATE_KEY,
    apisScopes: process.env.EVENT_4_LON_GOOGLE_SHEET_APIS_SCOPES,
    event4LonSpreadSheetID:
      process.env.EVENT_4_LON_GOOGLE_SHEET_SPREAD_SHEET_ID,
    event4LonSheetName: process.env.EVENT_4_LON_GOOGLE_SHEET_SHEET_NAME,
    startColumn: process.env.EVENT_4_LON_GOOGLE_SHEET_START_COLUMN,
    endColumn: process.env.EVENT_4_LON_GOOGLE_SHEET_END_COLUMN,
    evoucherVacxinSpreadSheetID:
      process.env.EVOUCHER_VACXIN_GOOGLE_SHEET_SPREAD_SHEET_ID,
    evoucherVacxinSheetName:
      process.env.EVOUCHER_VACXIN_GOOGLE_SHEET_SHEET_NAME,
    voucherVacxinStartColumn:
      process.env.EVOUCHER_VACXIN_GOOGLE_SHEET_START_COLUMN,
    voucherVacxinEndColumn: process.env.EVOUCHER_VACXIN_GOOGLE_SHEET_END_COLUMN,
  },
  telegram: {
    baseUrl: process.env.TELEGRAM_BASE_API_URL,
    api: {
      sendMessage: process.env.TELEGRAM_SEND_MESSAGE_END_POINT,
    },
    bot: {
      notify_data_web_app_sbps_changed: {
        token:
          process.env.TELEGRAM_NOTIFY_EVENT_WEBAPP_SBPS_DATA_CHANGE_BOT_TOKEN,
        chatId:
          process.env.TELEGRAM_NOTIFY_EVENT_WEBAPP_SBPS_DATA_CHANGE_CHAT_ID,
        threadId:
          process.env.TELEGRAM_NOTIFY_EVENT_WEBAPP_SBPS_DATA_CHANGE_THREAD_ID,
      },
    },
  },
  systemConfig: {
    limitNumberOfBlockedAccount:
      +process.env.LIMIT_NUMBER_OF_BLOCKED_ACCOUNT || 3,
  },
  googleSheetMigrateProvince: {
    clientEmail: process.env.MIGRATE_PROVINCE_GOOGLE_SHEET_CLIENT_EMAIL,
    privateKey: process.env.MIGRATE_PROVINCE_GOOGLE_SHEET_PRIVATE_KEY,
    apisScopes: process.env.MIGRATE_PROVINCE_GOOGLE_SHEET_APIS_SCOPES,
    spreadSheetID: process.env.MIGRATE_PROVINCE_GOOGLE_SHEET_SPREAD_SHEET_ID,
    sheetName: process.env.MIGRATE_PROVINCE_GOOGLE_SHEET_SHEET_NAME,
    startCell: process.env.MIGRATE_PROVINCE_GOOGLE_SHEET_START_CELL,
    endCell: process.env.MIGRATE_PROVINCE_GOOGLE_SHEET_END_CELL,
  },
};

export default () => globalConfig;
export type GlobalConfig = Record<RecursiveKeyOf<typeof globalConfig>, string>;
