import 'reflect-metadata';

const TRANSFORMER_KEY = Symbol('TRANSFORMER_KEY');
export const PROPS_KEY = 'props';

export const getTransformerKey = () => TRANSFORMER_KEY;

export interface AutoMapOptions {
  transform?: (value: any) => any;
  transformSource?: (source: any) => any;
}

export function AutoMapDecorator(options?: AutoMapOptions): PropertyDecorator {
  return (target: any, propertyKey: string | symbol) => {
    const props: string[] =
      Reflect.getMetadata(PROPS_KEY, target.constructor) || [];
    if (!props.includes(propertyKey as string)) {
      props.push(propertyKey as string);
      Reflect.defineMetadata(PROPS_KEY, props, target.constructor);
    }

    if (options?.transform) {
      Reflect.defineMetadata(
        TRANSFORMER_KEY,
        { type: 'custom', transform: options.transform },
        target,
        propertyKey,
      );
    }

    if (options?.transformSource) {
      Reflect.defineMetadata(
        TRANSFORMER_KEY,
        { type: 'custom', transformSource: options.transformSource },
        target,
        propertyKey,
      );
    }
  };
}

export function NestedDecorator(typeFn: () => any): PropertyDecorator {
  return (target: any, propertyKey: string | symbol) => {
    Reflect.defineMetadata(
      TRANSFORMER_KEY,
      { type: 'single', ctor: typeFn },
      target,
      propertyKey,
    );
    AutoMapDecorator()(target, propertyKey);
  };
}

export function NestedArrayDecorator(typeFn: () => any): PropertyDecorator {
  return (target: any, propertyKey: string | symbol) => {
    Reflect.defineMetadata(
      TRANSFORMER_KEY,
      { type: 'array', ctor: typeFn },
      target,
      propertyKey,
    );
    AutoMapDecorator()(target, propertyKey);
  };
}
