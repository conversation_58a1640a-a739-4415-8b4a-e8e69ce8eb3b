import { Column, ColumnOptions, ValueTransformer } from 'typeorm';

class DecimalColumnTransformer implements ValueTransformer {
  to(data?: number | null): number | null {
    if (data === undefined || data === null) return null;

    return data;
  }

  from(data?: string | null): number | null {
    if (data === undefined || data === null) return null;

    const res = parseFloat(data);

    if (isNaN(res)) {
      return null;
    } else {
      return res;
    }
  }
}

export function DecimalColumn({
  precision,
  scale,
  name,
  ...other
}: ColumnOptions) {
  return function (target: any, propertyKey: string | symbol): void {
    Column({
      ...other,
      type: 'decimal',
      precision,
      scale,
      name,
      transformer: new DecimalColumnTransformer(),
    })(target, propertyKey);
  };
}
