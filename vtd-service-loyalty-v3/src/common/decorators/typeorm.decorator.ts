import {
  Column,
  ColumnOptions,
  Index,
  IndexOptions,
  ValueTransformer,
} from 'typeorm';
import { mapKeysDeep } from '../utils';

class DecimalColumnTransformer implements ValueTransformer {
  to(data?: number | null): number | null {
    if (data === undefined || data === null) return null;

    return data;
  }

  from(data?: string | null): number | null {
    if (data === undefined || data === null) return null;

    const res = parseFloat(data);

    if (isNaN(res)) {
      return null;
    } else {
      return res;
    }
  }
}

export function DecimalColumn({
  precision,
  scale,
  name,
  ...other
}: ColumnOptions) {
  return function (target: any, propertyKey: string | symbol): void {
    Column({
      ...other,
      type: 'decimal',
      precision,
      scale,
      name,
      transformer: new DecimalColumnTransformer(),
    })(target, propertyKey);
  };
}

export function PartialIndexWithSoftDelete(
  fields: string[],
  options: Omit<IndexOptions, 'where'>,
  softDeletePropertyKey = 'deleted_at',
): ClassDecorator {
  return function (target: any): void {
    Index(fields, { ...options, where: `${softDeletePropertyKey} is null` })(
      target,
    );
  };
}

export const JsonCaseTransformer: ValueTransformer = {
  to: (value?: unknown) => (value == null ? value : mapKeysDeep(value, 'to')),
  from: (value?: unknown) =>
    value == null ? value : mapKeysDeep(value, 'from'),
};
