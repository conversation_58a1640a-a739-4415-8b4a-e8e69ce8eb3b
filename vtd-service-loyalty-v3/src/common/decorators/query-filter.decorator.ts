import 'reflect-metadata';

export const QUERY_FILTERS_KEY = Symbol('QUERY_FILTERS_KEY');

export interface QueryFilterMetadata {
  key: string | symbol;
  order: number;
}

export interface QueryFilterOptions {
  order?: number;
}

export function QueryFilter(options?: QueryFilterOptions): MethodDecorator {
  return (target, propertyKey, _descriptor) => {
    const ctor = target.constructor as any;

    const existing: QueryFilterMetadata[] =
      Reflect.getMetadata(QUERY_FILTERS_KEY, ctor) || [];

    existing.push({
      key: propertyKey,
      order: options?.order ?? 0,
    });

    Reflect.defineMetadata(QUERY_FILTERS_KEY, existing, ctor);
  };
}
