import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SyncEventPointHistoryToWhEntityRepository } from './repositories/sync-event-point-history-to-wh.repository';
import { EventPointHistoryRepository } from '../event/repositories/event-point-history.repository';
import { ExternalModule } from '../external/external.module';
import { SyncEventPointHistoryToWhCronJobController } from './controllers/sync-event-point-history-to-wh.cronjob.controller';
import { SyncEventPointHistoryToWhCronJobService } from './services/sync-event-point-history-to-wh.cronjob.service';
import { SyncEventPointHistoryToWhUserService } from './services/sync-event-point-history-to-wh.user.service';
import { AuthModule } from '../auth/auth.module';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { USER_PACKAGE_NAME, USER_SERVICE_NAME } from '../proto/user.pb';
import { join } from 'path';
import {
  ACCOUNT_PACKAGE_NAME,
  ACCOUNT_SERVICE_NAME,
} from '../proto/account.pb';

@Module({
  imports: [
    AuthModule,
    ClientsModule.register([
      {
        name: USER_SERVICE_NAME,
        transport: Transport.GRPC,
        options: {
          url: 'vtd-service-user-v3:50051',
          package: USER_PACKAGE_NAME,
          protoPath: join(
            __dirname + '/../../../node_modules/vtd-common-v3/proto/user.proto',
          ),
        },
      },
      {
        name: ACCOUNT_SERVICE_NAME,
        transport: Transport.GRPC,
        options: {
          url: 'vtd-service-user-v3:50052',
          package: ACCOUNT_PACKAGE_NAME,
          protoPath: join(
            __dirname +
              '/../../../node_modules/vtd-common-v3/proto/account.proto',
          ),
        },
      },
    ]),
    TypeOrmModule.forFeature([SyncEventPointHistoryToWhEntityRepository]),
    ExternalModule,
  ],
  controllers: [SyncEventPointHistoryToWhCronJobController],
  providers: [
    SyncEventPointHistoryToWhEntityRepository,
    SyncEventPointHistoryToWhCronJobService,
    SyncEventPointHistoryToWhUserService,
    EventPointHistoryRepository,
  ],
  exports: [SyncEventPointHistoryToWhUserService],
})
export class SyncEventPointHistoryToWhModule {}
