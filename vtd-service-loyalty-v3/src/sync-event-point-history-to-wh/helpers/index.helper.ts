import dayjs from 'dayjs';
import { User } from '../../auth/entities/user.entity';
import { Province } from '../../auth/entities/province.entity';
import { Product } from '../../product/entities/product.entity';
import { HistoryPoint } from '../../point/entities/history-point.entity';
import { Spoon } from '../../spoon/entities/spoon.entity';
import { EventPointHistory } from '../../event/entities/event-point-history.entity';
import {
  SyncEventPointHistoryToWhRequestInterface,
  UpdateEventPointHistoryToWhRequestInterface,
} from '../interfaces/index.interface';
import {
  TIME_FORMAT_CRM,
  TIME_ZONE,
} from '../../common/constants/index.constant';

export const generateSyncEventPointHistoryToWhRequestData = (
  eventPointHistory: EventPointHistory,
  userGiftStatus: string,
): SyncEventPointHistoryToWhRequestInterface => {
  let request: SyncEventPointHistoryToWhRequestInterface = null;
  if (!eventPointHistory || !userGiftStatus) {
    return request;
  }
  const user: User = eventPointHistory.user;
  if (!user) {
    return request;
  }
  const province: Province = user.province;
  if (!province) {
    return request;
  }
  const product: Product = eventPointHistory.product;
  if (!product) {
    return request;
  }
  const historyPoint: HistoryPoint = eventPointHistory.historyPoint;
  if (!historyPoint) {
    return request;
  }
  const spoon: Spoon = eventPointHistory.spoon;
  /*if (!spoon) {
    return request;
  }*/
  const event = eventPointHistory.eventDetail.event;
  if (!event) {
    return request;
  }

  request = {
    userId: user.id,
    phoneNumber: user.phoneNumber,
    userName: `${user.firstName} ${user.lastName}`,
    sku: product.code,
    giftName: eventPointHistory.giftName,
    brand: product.brand,
    // Type of createdDate in user is timestamp without timezone
    // We need convert to string to make sure dayjs parsing to tz work well
    // Type of createdDate in event point history is timestamp with timezone
    // Don't need do this
    transactionCode: historyPoint.transactionExternalId,
    receivedDate: makeDateIsDateInTimeZoneAndFormatToString(
      eventPointHistory.createdDate,
    ),
    registeredDate: makeDateIsDateInTimeZoneAndFormatToString(
      user.createdDate.toUTCString(),
    ),
    qrCode: spoon ? spoon.qrCode : eventPointHistory.qrCode,
    spoonCode: spoon ? spoon.spoonCode : '',
    province: province.name,
    status: userGiftStatus,
    eventId: event.id,
    eventName: event.name,
  };

  return request;
};

export const generateUpdateEventPointHistoryToWhRequestData = (
  eventPointHistory: EventPointHistory,
  userGiftStatus: string,
): UpdateEventPointHistoryToWhRequestInterface => {
  let request: UpdateEventPointHistoryToWhRequestInterface = null;
  if (!eventPointHistory || !userGiftStatus) {
    return request;
  }
  const historyPoint: HistoryPoint = eventPointHistory.historyPoint;
  if (!historyPoint) {
    return request;
  }

  request = {
    transactionCode: historyPoint.transactionExternalId,
    status: userGiftStatus,
  };

  return request;
};

// Clone makeDateIsDateInTimeZoneAndFormatToString from utils to here
// Because conflict code between branches
const makeDateIsDateInTimeZoneAndFormatToString = (
  date: string | number | Date,
  timezone: string = TIME_ZONE,
  format: string = TIME_FORMAT_CRM,
) => {
  //return dayjs(date).tz(TIME_ZONE) as unknown as Date;
  return dayjs.tz(date, timezone).format(format ?? TIME_FORMAT_CRM);
};
