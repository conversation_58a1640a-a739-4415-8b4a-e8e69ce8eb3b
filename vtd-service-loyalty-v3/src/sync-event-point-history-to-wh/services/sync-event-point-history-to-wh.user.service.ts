import { Injectable } from '@nestjs/common';
import { SyncEventPointHistoryToWhMethod } from '../constants/index.constant';
import { EventPointHistoryRepository } from '../../event/repositories/event-point-history.repository';
import { SyncEventPointHistoryToWhEntityRepository } from '../repositories/sync-event-point-history-to-wh.repository';
import { LoggerService } from '../../core';
import { generateSyncEventPointHistoryToWhRequestData } from '../helpers/index.helper';

@Injectable()
export class SyncEventPointHistoryToWhUserService {
  private _logger = new LoggerService(
    SyncEventPointHistoryToWhUserService.name,
  );

  constructor(
    private syncEventPointHistoryToWhEntityRepo: SyncEventPointHistoryToWhEntityRepository,
    private eventPointHistoryRepo: EventPointHistoryRepository,
  ) {}

  async insertSyncEventPointHistoryToWh(
    eventPointHistoryId: number,
    userGiftStatus: string,
    userAccessToken = null,
  ) {
    const eventPointHistorySaved = await this.eventPointHistoryRepo.findOne({
      where: {
        id: eventPointHistoryId,
      },
      relations: [
        'user',
        'user.province',
        'product',
        'historyPoint',
        'spoon',
        'eventDetail',
        'eventDetail.event',
      ],
    });
    if (!eventPointHistorySaved) {
      return;
    }
    const historyPoint = eventPointHistorySaved.historyPoint;
    if (!historyPoint) {
      return;
    }
    if (!userAccessToken) {
      userAccessToken = 'No auth token';
    }
    const syncEventPointHistoryToWhRequest =
      generateSyncEventPointHistoryToWhRequestData(
        eventPointHistorySaved,
        userGiftStatus,
      );
    await this.syncEventPointHistoryToWhEntityRepo.save({
      eventPointHistoryId: eventPointHistorySaved.id,
      transactionCode: historyPoint.transactionExternalId,
      request: syncEventPointHistoryToWhRequest
        ? syncEventPointHistoryToWhRequest
        : {},
      userAccessToken: userAccessToken,
      method: SyncEventPointHistoryToWhMethod.INSERT,
    });
  }
}
