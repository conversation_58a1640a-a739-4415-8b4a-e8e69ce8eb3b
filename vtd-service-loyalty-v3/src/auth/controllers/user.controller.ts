import { Controller, Get, Query, Req } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { UserService } from '../services/user.service';
import { AuthUser, UseAuth } from '../../common/decorators/user.decorator';
import { UserSessionData } from '../../proto/user.pb';
import { Prefix } from 'src/common/constants/index.constant';
import { AppResponseDto } from '../../common/dtos/app-response.dto';
import { UserBrandPointResDto } from '../dtos/res/user-brand-point.res.dto';

@Controller({ version: '1', path: `${Prefix.USER}` })
@ApiTags('User Controller')
@UseAuth()
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  async userHomeWithAuth(@AuthUser() userSessionData: UserSessionData) {
    return this.userService.getDetail(userSessionData);
  }

  @Get('brand-points')
  async getBrandPoints(@AuthUser() userSessionData: UserSessionData) {
    const userBrandPoints = await this.userService.getBrandPoints(
      userSessionData,
    );

    return new AppResponseDto(
      userBrandPoints.map((item) => new UserBrandPointResDto(item)),
    );
  }
}
