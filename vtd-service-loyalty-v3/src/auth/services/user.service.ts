import { Injectable } from '@nestjs/common';
import { AppResponseDto } from '../../common/dtos/app-response.dto';
import { UserRepository } from '../../auth/repositories/user.repository';
import { User } from '../../auth/entities/user.entity';
import { UserResponse } from '../../auth/dtos/res/user.res.dto';
import { TierRepository } from '../../tier/repositories/tier.repository';
import { Tier } from '../../tier/entities/tier.entity';
import { StatusCode } from '../../common/constants/status-code.constant';
import { ProvinceRepository } from '../../auth/repositories/province.repository';
import { UserSessionData } from '../../proto/user.pb';
import { ProvinceUserService } from '../../provinces/services/user/province.user.service';
import { MapUserToBrandPointRepository } from '../../map-user-to-brand-point/repositories/map-user-to-brand-point.repository';
import { MapUserToBrandPointEntity } from '../../map-user-to-brand-point/entities/map-user-to-brand-point.entity';

@Injectable()
export class UserService {
  constructor(
    private readonly userRepository: UserRepository,
    private readonly tierRepository: TierRepository,
    private readonly provinceRepository: ProvinceRepository,
    private readonly provinceUserService: ProvinceUserService,
    private readonly mapUserToBrandPointRepo: MapUserToBrandPointRepository,
  ) {}

  async getDetail(userSessionData: UserSessionData): Promise<AppResponseDto> {
    const userResponse: UserResponse = await this.getUserResponseById(
      userSessionData.userId,
    );
    const appResponse: AppResponseDto = {
      meta: StatusCode.SUCCESS,
      response: { user: userResponse },
    };
    return appResponse;
  }

  async getBrandPoints(
    userSessionData: UserSessionData,
  ): Promise<MapUserToBrandPointEntity[]> {
    return await this.mapUserToBrandPointRepo.find({
      where: {
        userId: userSessionData.userId,
      },
      relations: ['brand', 'event', 'eventAddCan'],
    });
  }

  async getUserResponseById(userId: number): Promise<UserResponse> {
    const [user] = await Promise.all([
      this.userRepository.findOne({
        where: { id: userId },
        relations: {
          province: true,
          district: true,
          ward: true,
        },
      }),
    ]);
    const newUserProvince = await this.provinceUserService.getUserNewProvice(
      user.id,
      user.phoneNumber,
    );

    if (!user) {
      return null;
    }
    // if (!newUserProvince) {
    //   newUserProvince = await this.provinceUserService.updateUserNewProvice(
    //     user,
    //   );
    // }

    const tier = await this.tierRepository.findOneBy({ code: user.tierCode });
    const userResponse: UserResponse = new UserResponse(
      user,
      tier,
      newUserProvince,
    );

    // It is required to pass a non-null argument, otherwise the query renders without a where clause
    const nextTier: Tier = await this.tierRepository.findOneBy({
      code: tier.nextTierCode ? tier.nextTierCode : '',
    });

    if (nextTier) {
      userResponse.nextTierCode = nextTier.code;
      userResponse.nextTierName = nextTier.name;
    } else {
      userResponse.nextTierCode = null;
      userResponse.nextTierName = null;
    }

    return userResponse;
  }

  public async userData(id: number) {
    return this.userRepository.findOneBy({ id });
  }
}
