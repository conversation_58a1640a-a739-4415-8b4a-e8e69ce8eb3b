import {
  AutoMapDecorator,
  NestedArrayDecorator,
  NestedDecorator,
} from '../../../common/decorators/automap.decorator';
import { BaseMapperDto } from '../../../common/dtos/base-mapper.dto';
import { BrandUserResponseDto } from '../../../brand/dtos/responses/user/brand.user.response.dto';
import { EventResponseCustomerDto } from '../../../event/dtos/customer/responses/event.response.customer.dto';
import { EventAddCanUserResDto } from '../../../event-add-can/dtos/res/user/event-add-can.user.res.dto';

export class UserBrandPointResDto extends BaseMapperDto {
  @NestedDecorator(() => BrandUserResponseDto)
  brand: BrandUserResponseDto;

  @NestedDecorator(() => EventResponseCustomerDto)
  event: EventResponseCustomerDto;

  @NestedDecorator(() => EventAddCanUserResDto)
  eventAddCan: EventAddCanUserResDto;

  @AutoMapDecorator()
  brandPoint: number;
}
