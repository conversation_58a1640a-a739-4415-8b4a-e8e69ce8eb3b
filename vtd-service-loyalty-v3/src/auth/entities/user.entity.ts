import { EventNumberUserCan } from 'src/event-add-can/entities/event-number-user-can.entity';
import {
  Colum<PERSON>,
  <PERSON><PERSON><PERSON>,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { EventPointHistory } from '../../event/entities/event-point-history.entity';
import { EventUser } from '../../event/entities/event-users.entity';
import { SfNotiUser } from '../../noti/entities/sf-noti-user.entity';
import { NotificationHistory } from '../../notification/entities/notification-history.entity';
import { NotificationUser } from '../../notification/entities/notification-user.entity';
import { PartnerNotification } from '../../notification/entities/partner-notification.entity';
import { BlockedHistory } from '../../point/entities/blocked-history.entity';
import { HistoryPoint } from '../../point/entities/history-point.entity';
import { ScanHistory } from '../../point/entities/scan-history.entity';
import { StoreInvitation } from '../../point/entities/store-invitation.entity';
import { BlockedScan } from '../../qr-code-sbps/entities/blocked-scan.entity';
import { UserShowPopup } from '../../user_show_popup/entities/user-show-popup.entity';
import { UserRequestHistoryPoint } from '../../webapp-calosure/entities/user-request-history-point.entity';
import { UserStatus, UserType } from '../enums/user.enum';
import { InactiveUser } from './inactive-user.entity';
import { Province } from './province.entity';
import { UserSession } from './user-session.entity';
import { UserPhoneMismatch } from './user-phone-mismatch.entity';
import { UserGroupToUser } from '../../noti/entities/user-group-to-user.entity';
import { NotiUser } from '../../notification/entities/noti-user.entity';
import { TrackingUserAction } from '../../feedback/entities/tracking-user-action.entity';
import { TrackingTimeLockHistory } from '../../tracking-time-lock/entities/tracking-time-lock-history.entity';
import { UserTrackingOnboarding } from '../../onboarding/entities/user-tracking-onboarding.entity';

@Entity({ name: 'users' })
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'phone_number', length: 255, nullable: true })
  phoneNumber: string;

  @Column({ length: 255, nullable: true })
  name: string;

  @Column({ name: 'birthday', nullable: true, type: 'timestamp' })
  birthDay: Date;

  @Column({ name: 'logged_date', nullable: true, type: 'timestamp' })
  loggedDate: Date;

  @Column({ name: 'add_point_date', nullable: true, type: 'timestamp' })
  addPointDate: Date;

  @Column({ name: 'tier', nullable: true, length: 255 })
  tier: string;

  @Column({ name: 'tier_rank_updated_date', type: 'timestamptz' })
  tierRankUpdatedDate: Date;

  @Column({ name: 'tier_point_added_after_up_rank', type: 'float4' })
  tierPointAddedAfterUpRank: number;

  @Column({ name: 'status', nullable: true, length: 255 })
  status: UserStatus;

  @Column({ name: 'created_date', nullable: true, type: 'timestamp' })
  createdDate: Date;

  @Column({ name: 'updated_date', nullable: true, type: 'timestamp' })
  updatedDate: Date;

  @Column({ name: 'invite_code', nullable: true, length: 255 })
  inviteCode: string;

  @Column({ name: 'password', nullable: true, length: 255 })
  password: string;

  @Column({ name: 'overage', type: 'int4' })
  overage: number;

  @Column({ name: 'gift_point', type: 'float4' })
  giftPoint: number;

  @Column({ name: 'stock_point', type: 'float4' })
  stockPoint: number;

  @Column({ name: 'tier_point', type: 'float4' })
  tierPoint: number;

  @Column({ name: 'tier_id', type: 'int4', nullable: true })
  tierId: number;

  @Column({ name: 'avatar', nullable: true, length: 255 })
  avatar: string;

  @Column({ name: 'crm_user_id', nullable: true, length: 255 })
  crmUserId: string;

  @Column({ name: 'tier_code', length: 255, nullable: true })
  tierCode: string;

  @Column({ name: 'total_money', type: 'int4', default: 0 })
  totalMoney: number;

  @Column({ name: 'total_point', type: 'float4', default: 0 })
  totalPoint: number;

  @Column({ name: 'purchase_point', type: 'int4', default: 0 })
  purchasePoint: number;

  @Column({ name: 'reward_point', type: 'int4', default: 0 })
  rewardPoint: number;

  @Column({ name: 'total_purchase_point', type: 'int4', default: 0 })
  totalPurchasePoint: number;

  @Column({ name: 'gender', length: 255, nullable: true })
  gender: string;

  @Column({ name: 'email', length: 255, nullable: true })
  email: string;

  @Column({ name: 'last_login_date', type: 'timestamp', nullable: true })
  lastLoginDate: Date;

  @Column({ name: 'last_scan_date', type: 'timestamp', nullable: true })
  lastScanDate: Date;

  @Column({ name: 'first_name', length: 255, nullable: true })
  firstName: string;

  @Column({ name: 'last_name', length: 255, nullable: true })
  lastName: string;

  @Column({ name: 'account_status', length: 255, nullable: true })
  accountStatus: string;

  @Column({ name: 'pg_employee_code', length: 255, nullable: true })
  pgEmployeeCode: string;

  @Column({ name: 'number_of_blocked_account', type: 'int4', nullable: true })
  numberOfBlockedAccount: number;

  @Column({ name: 'blocked_account', nullable: true, default: false })
  blockedAccount: boolean;

  @Column({ name: 'blocked_account_date', type: 'timestamp', nullable: true })
  blockedAccountDate: Date;

  @Column({
    name: 'blocked_scan_expiry_date',
    type: 'timestamp',
    nullable: true,
  })
  blockedScanExpiryDate: Date;

  // freeze identity
  @Column({
    name: 'start_freeze_point',
    type: 'timestamp',
    nullable: true,
  })
  startFreezePoint: Date;

  @Column({
    name: 'end_freeze_point',
    type: 'timestamp',
    nullable: true,
  })
  endFreezePoint: Date;

  @Column({ name: 'number_of_blocked_scan', type: 'int4', nullable: true })
  numberOfBlockedScan: number;

  @Column({ name: 'number_of_blocked_scan_sbps', type: 'int4', nullable: true })
  numberOfBlockedScanSbps: number;

  @Column({ name: 'blocked_scan', nullable: true, default: false })
  blockedScan: boolean;

  @Column({ name: 'blocked_scan_date', type: 'timestamp', nullable: true })
  blockedScanDate: Date;

  @Column({ name: 'blocked_scan_type', length: 255, nullable: true })
  blockedScanType: string;

  @Column({ name: 'blocked_account_type', length: 255, nullable: true })
  blockedAccountType: string;

  @Column({ name: 'address', length: 255, nullable: true })
  address: string;

  @Column({ name: 'user_type', length: 255, nullable: true })
  userType: UserType;

  @Column({ name: 'block_update_address', default: false, nullable: true })
  blockUpdateAddress: boolean;

  @Column({ name: 'is_ignore_invite_friend', default: false })
  isIgnoreInviteFriend: boolean;

  @Column({ name: 'is_congratulate', default: true })
  isCongratulate: boolean;

  @Column({ name: 'oggi_win_rate', type: 'float4', default: 0, nullable: true })
  oggiWinRate: number;

  @Column({
    name: 'monthly_ranking_congratulation',
    type: 'int4',
    nullable: true,
  })
  monthlyRankingCongratulation: number;

  @Column({ name: 'piece_win_rate', type: 'int4', nullable: true, default: 0 })
  pieceWinRate: string;

  @Column({ name: 'version', type: 'int4', default: 0 })
  version: number;

  @Column({ name: 'title', type: 'int4' })
  title?: number;

  @Column({
    name: 'number_of_cancel',
    type: 'int4',
    default: 0,
    nullable: true,
  })
  numberOfCancel: string;

  @Column({ name: 'should_show_new_year_wishes_popup', default: true })
  shouldShowNewYearWishesPopup: boolean;

  @Column({ name: 'gave_first_scan', default: false, nullable: true })
  gaveFirstScan: boolean;

  @Column({ name: 'cdp_sync_up', default: false, nullable: true })
  cdpSyncUp: boolean;

  @Column({ name: 'cbb_win_rate', type: 'int4', default: 0, nullable: true })
  cbbWinRate: number;

  @Column({
    name: 'sb_fail_add_point_counter',
    type: 'int4',
    default: 0,
    nullable: true,
  })
  sbFailAddPointCounter: number;

  @Column({
    name: 'sbps_fail_add_point_counter',
    type: 'int4',
    default: 0,
    nullable: true,
  })
  sbpsFailAddPointCounter: number;

  @Column({
    name: 'date_compare_reset_failed_add_point',
    type: 'timestamp',
    nullable: false,
  })
  dateCompareResetFailedAddPoint: Date;

  // Join blocked_history
  @OneToMany(() => BlockedHistory, (item) => item.user)
  blockedHistories: BlockedHistory[];
  // End join blocked_history

  // Join scan_history
  @OneToMany(() => ScanHistory, (scanHistory) => scanHistory.user)
  scanHistories: ScanHistory[];
  // End join scan_history

  // Join history_point
  @OneToMany(() => HistoryPoint, (hp) => hp.user)
  historyPoints: HistoryPoint[];
  // End join history_point

  // Join store_invitation
  @OneToOne(() => StoreInvitation, (si) => si.user)
  storeInvation: StoreInvitation;
  // End join store_invitation

  // Join province
  @Column({ name: 'province_id', type: 'int4', nullable: true })
  provinceId: number;

  @ManyToOne(() => Province, (p) => p.provinceUsers)
  @JoinColumn({ name: 'province_id' })
  province: Province;
  // End join province

  // Join district
  @Column({ name: 'district_id', type: 'int4', nullable: true })
  districtId: number;

  @ManyToOne(() => Province, (p) => p.districtUsers)
  @JoinColumn({ name: 'district_id' })
  district: Province;
  // End join district

  // Join ward
  @Column({ name: 'ward_id', type: 'int4', nullable: true })
  wardId: number;

  @ManyToOne(() => Province, (p) => p.wardUsers)
  @JoinColumn({ name: 'ward_id' })
  ward: Province;
  // End join ward

  // Join user_session
  @OneToMany(() => UserSession, (us) => us.user)
  userSessions: UserSession[];
  // End join user_session

  // Join notification_user
  @OneToMany(() => NotificationUser, (nu) => nu.user)
  notificationUsers: NotificationUser[];
  // End join notification_user

  // Join event_users
  @OneToMany(() => EventUser, (us) => us.user)
  eventUsers: EventUser[];
  // End join event_users

  // Join event_point_history
  @OneToMany(() => EventPointHistory, (eph) => eph.user)
  eventPointHistories: EventPointHistory[];
  // End join event_point_history

  @OneToMany(() => BlockedScan, (blockedScan) => blockedScan.user)
  blockedScans: BlockedScan[];

  @OneToMany(() => HistoryPoint, (historyPoint) => historyPoint.user)
  historyPoint: HistoryPoint[];

  @OneToMany(
    () => NotificationHistory,
    (notificationHistory) => notificationHistory.user,
  )
  notificationHistories: NotificationHistory[];

  getFullName() {
    return `${this.firstName} ${this.lastName}`;
  }

  getNameSyncSf() {
    if (this.firstName || this.lastName) {
      return this.getFullName();
    }

    return this.name;
  }

  // join inactive_user
  @OneToOne(() => InactiveUser, (inactiveUser) => inactiveUser.user)
  inactiveUser: InactiveUser;
  // end join inactive_user

  @OneToMany(
    () => EventNumberUserCan,
    (eventNumberUserCan) => eventNumberUserCan.user,
  )
  eventNumberUserCans: EventNumberUserCan[];

  getTierPoint(): number {
    return !this.tierPoint ? 0 : Math.round(this.tierPoint * 100) / 100;
  }

  @OneToMany(() => UserShowPopup, (userShowPopup) => userShowPopup.user)
  userShowPopups: UserShowPopup[];

  @OneToMany(() => UserRequestHistoryPoint, (uapr) => uapr.user)
  UserRequestHistoryPoints: UserRequestHistoryPoint[];

  @OneToOne(
    () => PartnerNotification,
    (partnerNotification) => partnerNotification.user,
  )
  partnerNotification: PartnerNotification;

  @OneToMany(() => SfNotiUser, (snu) => snu.user, { persistence: false })
  sfNotiUsers: SfNotiUser[];

  @OneToMany(() => UserPhoneMismatch, (upm) => upm.rootUser, {
    persistence: false,
  })
  userPhoneMismatchRootUser: UserPhoneMismatch[];

  @OneToMany(() => UserPhoneMismatch, (upm) => upm.duplicatedUser, {
    persistence: false,
  })
  userPhoneMismatchDuplicatedUser: UserPhoneMismatch[];

  @OneToMany(() => UserGroupToUser, (ugtu) => ugtu.user, { persistence: false })
  userGroupToUsers: UserGroupToUser[];

  @OneToMany(() => NotiUser, (nu) => nu.user, { persistence: false })
  notiUsers: NotiUser[];

  @OneToMany(
    () => TrackingUserAction,
    (trackingUserAction) => trackingUserAction.user,
  )
  trackingUserActions: TrackingUserAction[];

  @OneToMany(
    () => TrackingTimeLockHistory,
    (trackingTimeLockHistory) => trackingTimeLockHistory.user,
  )
  trackingTimeLockHistory: TrackingTimeLockHistory;

  @OneToMany(
    () => UserTrackingOnboarding,
    (userTrackingOnboarding) => userTrackingOnboarding.user,
  )
  userTrackingOnboardings: UserTrackingOnboarding[];
}
