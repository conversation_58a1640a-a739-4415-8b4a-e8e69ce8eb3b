import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { TypeOrmModule } from '@nestjs/typeorm';
import { join } from 'path';
import { GlobalConfig } from '../common/config/global.config';
import {
  ACCOUNT_PACKAGE_NAME,
  ACCOUNT_SERVICE_NAME,
} from '../proto/account.pb';
import { USER_PACKAGE_NAME, USER_SERVICE_NAME } from '../proto/user.pb';
import { UserSessionRepository } from '../qr-code-sbps/repositories/user-session.repository';
import { TierModule } from '../tier/tier.module';
import { AuthService } from './auth.service';
import { AuthUserService } from './auth.user.service';
import { AuthUserController } from './controllers/auth.user.controller';
import { UserController } from './controllers/user.controller';
import { User } from './entities/user.entity';
import { InactiveUserRepository } from './repositories/inactive-user.repository';
import { ProvinceRepository } from './repositories/province.repository';
import { UserRepository } from './repositories/user.repository';
import { UserService } from './services/user.service';
import { UserPhoneMismatchUserService } from './services/user-phone-mismatch.user.service';
import { UserPhoneMismatchUserController } from './controllers/user-phone-mismatch.user.controller';
import { UserPhoneMismatchRepository } from './repositories/user-phone-mismatch.repository';
import { UserPhoneMismatchBackupRepository } from './repositories/user-phone-mismatch-backup.repository';
import { HistoryPointRepository } from '../point/repositories/history-point.repository';
import { ScanHistoryRepository } from '../qr-code-sbps/repositories/scan-history.repository';
import { UserGiftRepository } from '../event/repositories/user-gift.repository';
import { EventPointHistoryRepository } from '../event/repositories/event-point-history.repository';
import { NotificationUserRepository } from '../notification/repositories/notification-user.repository';
import { EventNumberUserCanRepository } from '../event-add-can/repositories/event-number-user-can.repository';
import { UserRequestHistoryPointRepository } from '../webapp-calosure/repositories/user-request-history-point.repository';
import { UserNumberScanRepository } from '../point/repositories/user-number-scan.repository';
import { UserNumberScanSbpsRepository } from '../qr-code-sbps/repositories/user-number-scan-sbps.repository';
import { EventUsersRepository } from '../event/repositories/event-users.repository';
import { SfNotiUserRepository } from '../noti/repositories/sf-noti-user.repository';
import { UserGroupToUserRepository } from '../noti/repositories/user-group-to-user.repository';
import { BlockedScanRepository } from '../qr-code-sbps/repositories/blocked-scan.repository';
import { UserShowPopupRepository } from '../user_show_popup/repositories/user-show-popup.repository';
import { StoreInvitationRepository } from '../point/repositories/store-invitation.repository';
import { PartnerNotificationRepository } from '../notification/repositories/partner-notification.repository';
import { HttpModule } from '@nestjs/axios';
import { OAuthUserService } from './services/oauth.user.service';
import { OAuthUserController } from './controllers/oauth.user.controller';
import { OAuthUserRepository } from './repositories/oauth-user.repository';
import { JwtModule } from '@nestjs/jwt';
import { ExternalModule } from '../external/external.module';
import { ProvinceModule } from '../provinces/province.module';
import { MapUserToBrandPointRepository } from '../map-user-to-brand-point/repositories/map-user-to-brand-point.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([User]),
    ClientsModule.registerAsync([
      {
        inject: [ConfigService],
        name: USER_SERVICE_NAME,
        useFactory: (configSer: ConfigService<GlobalConfig>) => {
          return {
            transport: Transport.GRPC,
            options: {
              url: configSer.getOrThrow('grpc.userSerivce.url'),
              package: USER_PACKAGE_NAME,
              protoPath: join(
                __dirname +
                  '/../../../node_modules/vtd-common-v3/proto/user.proto',
              ),
            },
          };
        },
      },
      {
        inject: [ConfigService],
        name: ACCOUNT_SERVICE_NAME,
        useFactory: (configSer: ConfigService<GlobalConfig>) => {
          return {
            name: ACCOUNT_SERVICE_NAME,
            transport: Transport.GRPC,
            options: {
              url: configSer.getOrThrow('grpc.accountService.url'),
              package: ACCOUNT_PACKAGE_NAME,
              protoPath: join(
                __dirname +
                  '/../../../node_modules/vtd-common-v3/proto/account.proto',
              ),
            },
          };
        },
      },
    ]),
    TierModule,
    HttpModule,
    JwtModule.register({
      signOptions: {
        algorithm: 'HS512',
      },
    }),
    ExternalModule,
    ProvinceModule,
  ],
  controllers: [
    UserController,
    AuthUserController,
    UserPhoneMismatchUserController,
    OAuthUserController,
  ],
  providers: [
    // Repository
    UserRepository,
    InactiveUserRepository,
    ProvinceRepository,
    UserSessionRepository,
    UserPhoneMismatchRepository,
    UserPhoneMismatchBackupRepository,
    HistoryPointRepository,
    ScanHistoryRepository,
    UserGiftRepository,
    EventPointHistoryRepository,
    NotificationUserRepository,
    EventNumberUserCanRepository,
    UserRequestHistoryPointRepository,
    UserNumberScanRepository,
    UserNumberScanSbpsRepository,
    EventUsersRepository,
    SfNotiUserRepository,
    UserGroupToUserRepository,
    BlockedScanRepository,
    UserShowPopupRepository,
    StoreInvitationRepository,
    PartnerNotificationRepository,
    // Service
    ConfigService,
    AuthUserService,
    UserService,
    AuthService,
    UserPhoneMismatchUserService,
    OAuthUserService,
    OAuthUserRepository,
    MapUserToBrandPointRepository,
  ],
  exports: [
    InactiveUserRepository,
    ProvinceRepository,
    UserSessionRepository,
    UserRepository,
    AuthService,
    UserService,
  ],
})
export class AuthModule {}
