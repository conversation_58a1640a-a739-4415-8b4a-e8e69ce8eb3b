import { Module, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ElasticsearchModule } from '@nestjs/elasticsearch';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { join } from 'path';
import { AuthService } from '../auth/auth.service';
import { GlobalConfig } from '../common/config/global.config';
import {
  ACCOUNT_PACKAGE_NAME,
  ACCOUNT_SERVICE_NAME,
} from '../proto/account.pb';
import { USER_PACKAGE_NAME, USER_SERVICE_NAME } from '../proto/user.pb';
import { ElasticSearchController } from './elasticSearch.controller';
import { ElasticSearchService } from './elasticSearch.service';

@Module({
  imports: [
    ElasticsearchModule.registerAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService<GlobalConfig>) => ({
        node: configService.get('es.node'),
        auth: {
          username: configService.get('es.username'),
          password: configService.get('es.password'),
        },
        tls: { rejectUnauthorized: false },
      }),
    }),
    ClientsModule.register([
      {
        name: USER_SERVICE_NAME,
        transport: Transport.GRPC,
        options: {
          url: 'vtd-service-user-v3:50051',
          package: USER_PACKAGE_NAME,
          protoPath: join(
            __dirname + '/../../../node_modules/vtd-common-v3/proto/user.proto',
          ),
        },
      },
      {
        name: ACCOUNT_SERVICE_NAME,
        transport: Transport.GRPC,
        options: {
          url: 'vtd-service-user-v3:50052',
          package: ACCOUNT_PACKAGE_NAME,
          protoPath: join(
            __dirname +
              '/../../../node_modules/vtd-common-v3/proto/account.proto',
          ),
        },
      },
    ]),
  ],
  controllers: [ElasticSearchController],
  providers: [ElasticSearchService, AuthService],
  exports: [ElasticsearchModule, ElasticSearchService],
})
export class ElasticSearchModule implements OnModuleInit {
  constructor(private readonly searchService: ElasticSearchService) {}
  public async onModuleInit() {
    await Promise.all([
      this.searchService.createIndex(),
      this.searchService.createSyncSfFailedLoggingIndex(),
    ]);
  }
}
