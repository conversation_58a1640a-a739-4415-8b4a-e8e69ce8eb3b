import { Injectable } from '@nestjs/common';
import { paginate } from 'nestjs-typeorm-paginate';
import { In } from 'typeorm';
import { AppResponseDto } from '../../common/dtos/app-response.dto';
import { DeleteMultipleByNumberIdsDto } from '../../common/dtos/delete-multiple.dto';
import {
  BadRequestExc,
  ConflictExc,
  NotFoundExc,
} from '../../common/exceptions/custom-http.exception';
import { uniqueArray } from '../../common/utils';
import { VitaJavaService } from '../../external/services/vita-java.service';
import { CreateEventCanMarkAdminReqDto } from '../dtos/req/admin/create-event-can-mark.admin.req.dto';
import { GetListEventCanMarkAdminReqDto } from '../dtos/req/admin/get-list-event-can-mark.admin.req.dto';
import { UpdateEventCanMarkAdminReqDto } from '../dtos/req/admin/update-event-can-mark.admin.req.dto';
import { EventCanMarkRepository } from '../repositories/event-can-mark.repository';
import { EventAddCanRepository } from '../../event-add-can/repositories/event-add-can.repository';
import { AdminActionHistoryRepository } from '../../admin-action-history/repositories/admin-action-history.repository';
import { AccountData } from '../../proto/account.pb';
import { EnumNameAdminMenuModule } from '../../admin_authorization/common/enums/admin-menu-module.enum';
import { Transactional } from 'typeorm-transactional';

@Injectable()
export class EventCanMarkAdminService {
  constructor(
    private readonly eventCanMarkRepo: EventCanMarkRepository,
    private readonly vitaJavaService: VitaJavaService,
    private readonly adminActionHistoryRepo: AdminActionHistoryRepository,
    private readonly eventAddCanRepo: EventAddCanRepository,
  ) {}
  async getListEventCanMark(
    token: string,
    dto: GetListEventCanMarkAdminReqDto,
  ) {
    const { page, limit, eventAddCanId } = dto;
    const queryBuilder =
      this.eventCanMarkRepo.createQueryBuilder('eventCanMark');
    queryBuilder
      .leftJoin('eventCanMark.eventAddCan', 'eventAddCan')
      .addSelect([
        'eventAddCan.id',
        'eventAddCan.eventName',
        'eventAddCan.type',
      ]);
    queryBuilder
      .leftJoin('eventCanMark.gift', 'gift')
      .addSelect(['gift.id', 'gift.name']);

    if (eventAddCanId) {
      queryBuilder.andWhere('eventAddCan.id = :eventAddCanId', {
        eventAddCanId,
      });
    }

    queryBuilder.orderBy('eventCanMark.createdAt', 'DESC');
    const { items, meta } = await paginate(queryBuilder, {
      limit,
      page,
    });

    // Check if eventCanMark not has old gift -> try get new gift from JavaService
    await Promise.all(
      items.map(async (item) => {
        if (!item.gift) {
          const gsGift = await this.vitaJavaService.getGiftById(
            token,
            item.gsGiftId,
          );
          if (gsGift) {
            item.gift = gsGift as any;
          }
        }
      }),
    );
    return AppResponseDto.fromNestJsPagination(items, meta);
  }

  async getEventCanMarkById(token: string, id: number) {
    const eventCanMark = await this.eventCanMarkRepo.findOne({
      where: { id },
      relations: ['eventAddCan', 'gift'],
    });
    // Check if eventCanMark not has old gift -> try get new gift from JavaService
    if (!eventCanMark.gift) {
      const gsGift = await this.vitaJavaService.getGiftById(
        token,
        eventCanMark.gsGiftId,
      );
      if (gsGift) {
        eventCanMark.gift = gsGift as any;
      }
    }
    if (!eventCanMark) {
      throw new NotFoundExc('Event Can Mark not found');
    }
    return new AppResponseDto(eventCanMark);
  }

  @Transactional()
  async createEventCanMark(
    token: string,
    dto: CreateEventCanMarkAdminReqDto,
    admin: AccountData,
  ) {
    const { gsGiftId } = dto;
    // Check gift is already
    const eventCanMarkExist = await this.eventCanMarkRepo.findOne({
      where: { gsGiftId },
    });
    if (eventCanMarkExist) {
      throw new ConflictExc('Event Can Mark is already exist');
    }
    // Check gift is available
    const gift = await this.vitaJavaService.getGiftById(token, gsGiftId);
    if (!gift) {
      throw new NotFoundExc('Gift not found');
    }
    const newEventCanMark = this.eventCanMarkRepo.create(dto);

    const [eventAddCan] = await Promise.all([
      this.eventAddCanRepo.findOneBy({
        id: dto.eventAddCanId,
      }),
      this.eventCanMarkRepo.insert(newEventCanMark),
    ]);

    if (admin) {
      await this.adminActionHistoryRepo.loggingUpdateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        eventAddCan.eventName,
      );
    }

    return new AppResponseDto(newEventCanMark);
  }

  @Transactional()
  async updateEventCanMark(
    token: string,
    id: number,
    dto: UpdateEventCanMarkAdminReqDto,
    admin: AccountData,
  ) {
    // Check eventCanMark is available
    const [eventCanMark, eventAddCan] = await Promise.all([
      this.eventCanMarkRepo.findOne({
        where: { id },
      }),
      this.eventAddCanRepo.findOneByOrFail({ id: dto.eventAddCanId }),
    ]);
    if (!eventCanMark) {
      throw new NotFoundExc('Event Can Mark not found');
    }
    const { gsGiftId } = dto;
    // Check gift is already
    if (gsGiftId !== eventCanMark.gsGiftId) {
      const eventCanMarkExist = await this.eventCanMarkRepo.findOne({
        where: { gsGiftId },
      });
      if (eventCanMarkExist) {
        throw new ConflictExc('Event Can Mark is already exist');
      }
    }
    // Check gift is available
    const gift = await this.vitaJavaService.getGiftById(token, gsGiftId);
    if (!gift) {
      throw new NotFoundExc('Gift not found');
    }
    // Update eventCanMark
    this.eventCanMarkRepo.merge(eventCanMark, dto);
    await Promise.all([
      this.eventCanMarkRepo.save(eventCanMark),
      this.adminActionHistoryRepo.loggingUpdateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        eventAddCan.eventName,
      ),
    ]);
    return new AppResponseDto(eventCanMark);
  }

  @Transactional()
  async deleteEventCanMark(id: number, admin: AccountData) {
    const eventCanMark = await this.eventCanMarkRepo.findOne({
      where: { id },
      relations: ['eventAddCan'],
    });
    if (!eventCanMark) {
      throw new NotFoundExc('Event Can Mark not found');
    }
    await Promise.all([
      this.adminActionHistoryRepo.loggingUpdateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        eventCanMark.eventAddCan.eventName,
      ),
      this.eventCanMarkRepo.remove(eventCanMark),
    ]);
  }

  @Transactional()
  async deleteListEventCanMark(
    dto: DeleteMultipleByNumberIdsDto,
    admin: AccountData,
  ) {
    const { ids } = dto;

    const uniqueIds = uniqueArray(ids);

    // Fetch all records in one query
    const eventCanMarks = await this.eventCanMarkRepo.find({
      where: { id: In(ids) },
    });

    if (uniqueIds.length !== eventCanMarks.length) {
      throw new BadRequestExc('Some Event Can Mark not found');
    }

    const eventAddCanIds = eventCanMarks.map((eventCanMark) => {
      return eventCanMark.eventAddCanId;
    });
    const uniqueEventAddCanIds = uniqueArray(eventAddCanIds);
    const eventAddCans = await this.eventAddCanRepo.findBy({
      id: In(uniqueEventAddCanIds),
    });
    const logs = eventAddCans.map((eventAddCan) => {
      return {
        adminName: admin.email,
        adminMenu: EnumNameAdminMenuModule.QUAN_LY_SU_KIEN,
        recordIdentity: eventAddCan.eventName,
      };
    });

    // Bulk remove
    await Promise.all([
      this.eventCanMarkRepo.remove(eventCanMarks),
      this.adminActionHistoryRepo.loggingUpdateActions(logs),
    ]);
  }
}
