import { Modu<PERSON> } from '@nestjs/common';
import { AuthModule } from '../auth/auth.module';
import { OnboardingUserController } from './controllers/user/onboarding.user.controller';
import { UserTrackingOnboardingRepository } from './repositories/user-tracking-onboarding.repository';
import { OnboardingUserService } from './services/user/onboarding.user.service';

@Module({
  imports: [AuthModule],
  controllers: [OnboardingUserController],
  providers: [OnboardingUserService, UserTrackingOnboardingRepository],
  exports: [],
})
export class OnboardingModule {}
