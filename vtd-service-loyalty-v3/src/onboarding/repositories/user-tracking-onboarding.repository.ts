import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { UserTrackingOnboarding } from '../entities/user-tracking-onboarding.entity';

@Injectable()
export class UserTrackingOnboardingRepository extends BaseRepository<UserTrackingOnboarding> {
  constructor(dataSource: DataSource) {
    super(UserTrackingOnboarding, dataSource);
  }
}
