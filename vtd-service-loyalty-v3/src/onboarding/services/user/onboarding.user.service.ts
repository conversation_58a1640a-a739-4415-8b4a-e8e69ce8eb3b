import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { compareDateWithDateInTimezone } from 'vtd-common-v3';
import { User } from '../../../auth/entities/user.entity';
import { UserRepository } from '../../../auth/repositories/user.repository';
import { GlobalConfig } from '../../../common/config/global.config';
import { BadRequestExc } from '../../../common/exceptions/custom-http.exception';
import { UserSessionData } from '../../../proto/user.pb';
import { UpdateUserTrackingOnboardingUserReqDto } from '../../dtos/req/user/update-user-tracking-onboarding.user.req.dto';
import { OnboardingHistoryUserResDto } from '../../dtos/res/user/onboarding-history.user.res.dto';
import { UserTrackingOnboarding } from '../../entities/user-tracking-onboarding.entity';
import { OnboardingTriggerKeyEnum } from '../../enums/onboarding-trigger-key.enum';
import { UserTrackingOnboardingRepository } from '../../repositories/user-tracking-onboarding.repository';

@Injectable()
export class OnboardingUserService {
  private readonly logger = new Logger(OnboardingUserService.name);

  private readonly TRIGGER_KEY_TO_RESPONSE_FIELD: Record<
    OnboardingTriggerKeyEnum,
    keyof OnboardingHistoryUserResDto
  > = {
    [OnboardingTriggerKeyEnum.EVENT_FIRST_OPEN]: 'showFirstOpenMascotChatbot',
    [OnboardingTriggerKeyEnum.EVENT_FIRST_SCAN]: 'showFirstScanMascotChatbot',
    [OnboardingTriggerKeyEnum.SCREEN_HOME]: 'showTooltipMenu',
    [OnboardingTriggerKeyEnum.SCREEN_EXCHANGE_GIFT]: 'showTooltipExchangeGift',
    [OnboardingTriggerKeyEnum.SCREEN_AUTH_SPOON]: 'showTooltipAuthSpoon',
    [OnboardingTriggerKeyEnum.SCREEN_MY_GIFT]: 'showTooltipMyGift',
  };

  constructor(
    private readonly userRepo: UserRepository,
    private readonly userTrackingOnboardingRepo: UserTrackingOnboardingRepository,
    private readonly configService: ConfigService<GlobalConfig>,
  ) {}

  async updateOnboardingByTriggerKey(
    triggerKey: OnboardingTriggerKeyEnum,
    dto: UpdateUserTrackingOnboardingUserReqDto,
    userSessionData: UserSessionData,
  ): Promise<UserTrackingOnboarding> {
    const userId = userSessionData.userId;

    this.logger.log(
      `Updating onboarding for user ${userId}, trigger: ${triggerKey}, status: ${dto.status}`,
    );

    // Validate trigger key early to fail fast
    if (!this.TRIGGER_KEY_TO_RESPONSE_FIELD[triggerKey]) {
      this.logger.warn(`Invalid trigger key attempted: ${triggerKey}`);
      throw new BadRequestExc(`Invalid trigger key: ${triggerKey}`);
    }

    const user = await this.getUserById(userId);
    const isTooltipEnabled = await this.checkTooltipEnableForUser(user);

    if (!isTooltipEnabled) {
      this.logger.warn(
        `Tooltips are not enabled for user ${userId}. Skipping onboarding update.`,
      );
      throw new BadRequestExc('Tooltips are not enabled for this user');
    }

    // Insert tracking record if not exists, skip if already tracked
    await this.userTrackingOnboardingRepo
      .createQueryBuilder()
      .insert()
      .into(UserTrackingOnboarding)
      .values({
        userId,
        triggerKey,
        status: dto.status,
      })
      .orIgnore()
      .execute();

    // Fetch and return the updated record
    const updatedOnboarding = await this.userTrackingOnboardingRepo.findOne({
      where: { userId, triggerKey },
    });

    if (!updatedOnboarding) {
      this.logger.error(
        `Failed to retrieve onboarding record after upsert for user ${userId}, trigger: ${triggerKey}`,
      );
      throw new BadRequestExc('Failed to update onboarding tracking');
    }
    this.logger.log(
      `Successfully updated onboarding for user ${userId}, trigger: ${triggerKey}`,
    );

    return updatedOnboarding;
  }

  async getOnboardingHistory(
    userSessionData: UserSessionData,
  ): Promise<OnboardingHistoryUserResDto> {
    const userId = userSessionData.userId;

    this.logger.log(`Fetching onboarding history for user ${userId}`);

    const user = await this.getUserById(userId);
    const isTooltipEnabled = await this.checkTooltipEnableForUser(user);

    if (!isTooltipEnabled) {
      this.logger.log(
        `Tooltips disabled for user ${userId}. Returning default disabled state.`,
      );
      return this.getDisabledOnboardingState(user);
    }

    const trackingOnboardings = await this.userTrackingOnboardingRepo.find({
      where: { userId },
    });

    this.logger.log(
      `Found ${trackingOnboardings.length} onboarding records for user ${userId}`,
    );

    return this.mapOnboardingHistoryForUser(user, trackingOnboardings);
  }

  private async getUserById(userId: number): Promise<User> {
    const user = await this.userRepo.findOne({
      where: { id: userId },
    });

    if (!user) {
      this.logger.warn(`User not found: ${userId}`);
      throw new BadRequestExc('User not found');
    }

    return user;
  }

  private getDisabledOnboardingState(user: User): OnboardingHistoryUserResDto {
    return {
      showFirstOpenMascotChatbot: false,
      showFirstScanMascotChatbot: !user.lastScanDate,
      showTooltipMenu: false,
      showTooltipExchangeGift: false,
      showTooltipAuthSpoon: false,
      showTooltipMyGift: false,
    };
  }

  private mapOnboardingHistoryForUser(
    user: User,
    trackingOnboardings: UserTrackingOnboarding[],
  ): OnboardingHistoryUserResDto {
    // Initialize with all tooltips enabled by default
    const response: OnboardingHistoryUserResDto = {
      showFirstOpenMascotChatbot: true,
      showFirstScanMascotChatbot: !user.lastScanDate,
      showTooltipMenu: true,
      showTooltipExchangeGift: true,
      showTooltipAuthSpoon: true,
      showTooltipMyGift: true,
    };

    // Disable tooltips that have been completed or skipped
    trackingOnboardings.forEach((onboarding) => {
      const fieldName =
        this.TRIGGER_KEY_TO_RESPONSE_FIELD[onboarding.triggerKey];
      if (fieldName) {
        response[fieldName] = false;
      }
    });

    return response;
  }

  private async checkTooltipEnableForUser(user: User): Promise<boolean> {
    const tooltipReleaseDate = this.configService.get<string>(
      'onboarding.tooltip.releaseDate',
    );
    const tooltipCheckUserRegisterDateEnable = this.configService.get<boolean>(
      'onboarding.tooltip.checkUserRegisterDateEnable',
    );

    // Disable tooltips if feature flag is enabled and user registered before release date
    if (
      tooltipCheckUserRegisterDateEnable &&
      tooltipReleaseDate &&
      compareDateWithDateInTimezone(user.createdDate, tooltipReleaseDate) < 0
    ) {
      this.logger.log(
        `Tooltips disabled for user ${user.id} - registered before release date`,
      );
      return false;
    }

    return true;
  }
}
