import {
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyTo<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { User } from '../../auth/entities/user.entity';
import { BaseEntityWithoutDeletedAtWithoutVersion } from '../../common/entities/base.entity';
import { OnboardingTriggerKeyEnum } from '../enums/onboarding-trigger-key.enum';
import { UserTrackingOnboardingStatusEnum } from '../enums/user-tracking-onboarding-status.enum';

@Entity({ name: 'user_tracking_onboardings' })
export class UserTrackingOnboarding extends BaseEntityWithoutDeletedAtWithoutVersion {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    name: 'user_id',
  })
  userId: number;

  @Column({
    type: 'varchar',
    length: 50,
  })
  status: UserTrackingOnboardingStatusEnum;

  @Column({
    name: 'trigger_key',
    type: 'varchar',
    length: 255,
  })
  triggerKey: OnboardingTriggerKeyEnum;

  @ManyToOne(() => User, (user) => user.userTrackingOnboardings)
  @JoinColumn({ name: 'user_id' })
  user: User;
}
