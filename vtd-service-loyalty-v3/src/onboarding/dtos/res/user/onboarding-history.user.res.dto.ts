import { AutoMapDecorator } from '../../../../common/decorators/automap.decorator';
import { BaseMapperDto } from '../../../../common/dtos/base-mapper.dto';

export class OnboardingHistoryUserResDto extends BaseMapperDto {
  @AutoMapDecorator()
  showFirstOpenMascotChatbot: boolean;

  @AutoMapDecorator()
  showFirstScanMascotChatbot: boolean;

  @AutoMapDecorator()
  showTooltipMenu: boolean;

  @AutoMapDecorator()
  showTooltipExchangeGift: boolean;

  @AutoMapDecorator()
  showTooltipAuthSpoon: boolean;

  @AutoMapDecorator()
  showTooltipMyGift: boolean;
}
