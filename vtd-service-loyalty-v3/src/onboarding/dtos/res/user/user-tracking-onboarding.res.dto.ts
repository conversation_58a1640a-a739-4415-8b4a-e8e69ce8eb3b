import { AutoMapDecorator } from '../../../../common/decorators/automap.decorator';
import { BaseMapperDto } from '../../../../common/dtos/base-mapper.dto';
import { UserTrackingOnboardingStatusEnum } from '../../../enums/user-tracking-onboarding-status.enum';

export class UserTrackingOnboardingResDto extends BaseMapperDto {
  @AutoMapDecorator()
  id: number;

  @AutoMapDecorator()
  userId: number;

  @AutoMapDecorator()
  triggerKey: string;

  @AutoMapDecorator()
  status: UserTrackingOnboardingStatusEnum;

  @AutoMapDecorator()
  createdAt: Date;

  @AutoMapDecorator()
  updatedAt: Date;
}
