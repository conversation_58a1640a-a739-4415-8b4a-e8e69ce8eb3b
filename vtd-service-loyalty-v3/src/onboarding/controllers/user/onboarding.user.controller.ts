import { Body, Controller, Get, Param, Put } from '@nestjs/common';
import { ApiParam, ApiTags } from '@nestjs/swagger';
import { Prefix } from '../../../common/constants/index.constant';
import { AuthUser, UseAuth } from '../../../common/decorators/user.decorator';
import { AppResponseDto } from '../../../common/dtos/app-response.dto';
import { UserSessionData } from '../../../proto/user.pb';
import { UpdateUserTrackingOnboardingUserReqDto } from '../../dtos/req/user/update-user-tracking-onboarding.user.req.dto';
import { OnboardingHistoryUserResDto } from '../../dtos/res/user/onboarding-history.user.res.dto';
import { UserTrackingOnboardingResDto } from '../../dtos/res/user/user-tracking-onboarding.res.dto';
import { OnboardingTriggerKeyEnum } from '../../enums/onboarding-trigger-key.enum';
import { OnboardingUserService } from '../../services/user/onboarding.user.service';

@Controller({ version: '1', path: `${Prefix.USER}/onboardings` })
@UseAuth()
@ApiTags('Onboarding User Controller')
export class OnboardingUserController {
  constructor(private readonly onboardingUserService: OnboardingUserService) {}

  @Put(':key')
  @ApiParam({
    name: 'key',
    enum: OnboardingTriggerKeyEnum,
  })
  async updateOnboardingByTriggerKey(
    @Param('key') triggerKey: OnboardingTriggerKeyEnum,
    @Body() dto: UpdateUserTrackingOnboardingUserReqDto,
    @AuthUser() userSessionData: UserSessionData,
  ) {
    const result =
      await this.onboardingUserService.updateOnboardingByTriggerKey(
        triggerKey,
        dto,
        userSessionData,
      );
    return new AppResponseDto(new UserTrackingOnboardingResDto(result));
  }

  @Get('/history')
  async getOnboardingHistory(@AuthUser() userSessionData: UserSessionData) {
    const result = await this.onboardingUserService.getOnboardingHistory(
      userSessionData,
    );
    return new AppResponseDto(new OnboardingHistoryUserResDto(result));
  }
}
