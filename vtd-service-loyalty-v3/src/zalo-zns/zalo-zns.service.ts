import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GlobalConfig } from '../common/config/global.config';
import { NodeEnv } from '../common/constants/index.constant';
import { StatusCode } from '../common/constants/status-code.constant';
import { AppResponseDto } from '../common/dtos/app-response.dto';
import {
  CustomSmSVietGuysReqDto,
  ZaloZnsVietGuysReqDto,
} from '../external/dtos/viet-guys/req/zalo-zns.viet-guys.req.dto';
import { SendZnsSource } from '../external/enums/vgs.enum';
import { VgsService } from '../external/services/vgs.service';

@Injectable()
export class ZaloZnsService {
  constructor(
    private vietGuysService: VgsService,
    private configService: ConfigService<GlobalConfig>,
  ) {}

  async sendZaloZns(dto: ZaloZnsVietGuysReqDto) {
    // Send Voice OTP if phone number is in blacklist
    if (this.checkPhoneInVoiceBlacklist(dto.phone, dto.source)) {
      const result = await this.vietGuysService.sendVoiceOtp(
        dto.phone,
        dto.source,
      );

      if (!result) {
        return new AppResponseDto(null, {
          status: StatusCode.API_FAILED_UNKNOWN.status,
          msg: StatusCode.API_FAILED_UNKNOWN.msg,
          error: StatusCode.API_FAILED_UNKNOWN.error,
        });
      }

      return new AppResponseDto();
    }

    // Send Zalo ZNS
    const result = await this.vietGuysService.sendOtpZaloRequest(dto);
    const environment = this.configService.get('nodeEnv');
    if (environment == NodeEnv.DEVELOPMENT || environment == NodeEnv.TEST) {
      return new AppResponseDto();
    }
    if (result?.resultCode != '0') {
      return new AppResponseDto(null, {
        status: StatusCode.API_FAILED_UNKNOWN.status,
        msg: StatusCode.API_FAILED_UNKNOWN.msg,
        error: StatusCode.API_FAILED_UNKNOWN.error,
      });
    }
    return new AppResponseDto();
  }

  async sendCustomEventSms(body: CustomSmSVietGuysReqDto) {
    // const result = await this.vietGuysService.sendCustomSms(phone, content);
    // if (result.error != 0) {
    //   return new AppResponseDto(null, {
    //     status: StatusCode.API_FAILED_UNKNOWN.status,
    //     msg: StatusCode.API_FAILED_UNKNOWN.msg,
    //     error: StatusCode.API_FAILED_UNKNOWN.error,
    //   });
    // }
    // return new AppResponseDto();
    const rs = await this.vietGuysService.sendCustomZaloZNS(body);
    if (!rs) {
      return new AppResponseDto(null, {
        status: StatusCode.API_FAILED_UNKNOWN.status,
        msg: StatusCode.API_FAILED_UNKNOWN.msg,
        error: StatusCode.API_FAILED_UNKNOWN.error,
      });
    }

    return new AppResponseDto();
  }

  async sendSms(phone: string, source: SendZnsSource) {
    const result = await this.vietGuysService.sendOtpSms(phone, source);
    if (result.error != 0) {
      return new AppResponseDto(null, {
        status: StatusCode.API_FAILED_UNKNOWN.status,
        msg: StatusCode.API_FAILED_UNKNOWN.msg,
        error: StatusCode.API_FAILED_UNKNOWN.error,
      });
    }
    return new AppResponseDto();
  }

  async verifyOtp(phone: string, otp: string) {
    const result = await this.vietGuysService.verifyOtp(phone, otp);

    return new AppResponseDto();
  }

  private checkPhoneInVoiceBlacklist(phone: string, source: SendZnsSource) {
    const blockedPhonePrefixes = this.configService.get(
      'vgs.voice.blockedPhonePrefixes',
    );

    if (
      source == SendZnsSource.REGISTER ||
      source == SendZnsSource.OAUTH_LOGIN
    ) {
      const phoneNumberPrefix = phone.substring(0, 3);
      return blockedPhonePrefixes.includes(phoneNumberPrefix);
    }

    return false;
  }
}
