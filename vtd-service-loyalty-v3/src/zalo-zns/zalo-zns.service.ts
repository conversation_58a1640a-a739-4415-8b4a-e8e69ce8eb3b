import { Injectable } from '@nestjs/common';
import { ZaloZnsVietGuysReqDto } from '../external/dtos/viet-guys/req/zalo-zns.viet-guys.req.dto';
import { VgsService } from '../external/services/vgs.service';
import { SendZnsSource } from '../external/enums/vgs.enum';
import { AppResponseDto } from '../common/dtos/app-response.dto';
import { StatusCode } from '../common/constants/status-code.constant';
import {
  VIETGUYS_SMS_ERROR,
  VIETGUYS_ZALO_ZNC_ERROR,
} from '../external/constants/vietguys.constant';
import { ConfigService } from '@nestjs/config';
import { GlobalConfig } from '../common/config/global.config';
import { NodeEnv } from '../common/constants/index.constant';

@Injectable()
export class ZaloZnsService {
  constructor(
    private vietGuysService: VgsService,
    private configService: ConfigService<GlobalConfig>,
  ) {}
  async sendZaloZns(dto: ZaloZnsVietGuysReqDto) {
    const result = await this.vietGuysService.sendOtpZaloRequest(dto);
    const environment = this.configService.get('nodeEnv');
    if (environment == NodeEnv.DEVELOPMENT || environment == NodeEnv.TEST) {
      return new AppResponseDto();
    }
    if (result?.resultCode != '0') {
      return new AppResponseDto(null, {
        status: StatusCode.API_FAILED_UNKNOWN.status,
        msg: StatusCode.API_FAILED_UNKNOWN.msg,
        error: StatusCode.API_FAILED_UNKNOWN.error,
      });
    }
    return new AppResponseDto();
  }

  async sendCustomEventSms(phone: string, content: string) {
    const result = await this.vietGuysService.sendCustomSms(phone, content);
    if (result.error != 0) {
      return new AppResponseDto(null, {
        status: StatusCode.API_FAILED_UNKNOWN.status,
        msg: StatusCode.API_FAILED_UNKNOWN.msg,
        error: StatusCode.API_FAILED_UNKNOWN.error,
      });
    }
    return new AppResponseDto();
  }

  async sendSms(phone: string, source: SendZnsSource) {
    const result = await this.vietGuysService.sendOtpSms(phone, source);
    if (result.error != 0) {
      return new AppResponseDto(null, {
        status: StatusCode.API_FAILED_UNKNOWN.status,
        msg: StatusCode.API_FAILED_UNKNOWN.msg,
        error: StatusCode.API_FAILED_UNKNOWN.error,
      });
    }
    return new AppResponseDto();
  }

  async verifyOtp(phone: string, otp: string) {
    const result = await this.vietGuysService.verifyOtp(phone, otp);

    return new AppResponseDto();
  }
}
