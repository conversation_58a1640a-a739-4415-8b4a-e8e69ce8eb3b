import { Body, Controller, Get, Param, Post, Query } from '@nestjs/common';
import { ApiOkResponse, ApiTags } from '@nestjs/swagger';
import { ZaloZnsService } from './zalo-zns.service';
import {
  CustomSmSVietGuysReqDto,
  SmSVietGuysReqDto,
  VerifyOtpReqDto,
  ZaloZnsVietGuysReqDto,
} from '../external/dtos/viet-guys/req/zalo-zns.viet-guys.req.dto';

@Controller()
@ApiTags('Zalo ZNS controller')
export class ZaloZnsController {
  constructor(private readonly zaloZnsService: ZaloZnsService) {}

  @Post('send-zalo')
  async sendZaloRequest(@Body() body: ZaloZnsVietGuysReqDto) {
    return await this.zaloZnsService.sendZaloZns(body);
  }

  @Post('send-sms')
  async sendSmsRequest(@Body() body: SmSVietGuysReqDto) {
    return await this.zaloZnsService.sendSms(body.phone, body.source);
  }

  @Post('send-custom-sms')
  async sendCustomSmsRequest(@Body() body: CustomSmSVietGuysReqDto) {
    return await this.zaloZnsService.sendCustomEventSms(body.phone, body.content);
  }

  @Post('verify-otp')
  async verifyOtpRequest(@Body() body: VerifyOtpReqDto) {
    return await this.zaloZnsService.verifyOtp(body.phone, body.otp);
  }
}
