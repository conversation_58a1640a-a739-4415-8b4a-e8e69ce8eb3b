import { Injectable } from '@nestjs/common';
import { FindOptionsWhere, Between, In } from 'typeorm';
import { paginate } from 'nestjs-typeorm-paginate';
import {
  Transactional,
  runInTransaction,
  runOnTransactionCommit,
} from 'typeorm-transactional';
import { ConfigService } from '@nestjs/config';
import { LoggerService } from '../../../core';
import { AppResponseDto } from '../../../common/dtos/app-response.dto';
import { BadRequestExc } from '../../../common/exceptions/custom-http.exception';
import {
  isValueInEnum,
  convertObjectToString,
  randomTransactionExternalId,
  makeDateIsDateAtTimeHcm,
  getNowAtTimeZoneHcm,
} from '../../../common/utils';
import {
  compareDateWithCurrent,
  convertDateToDateAtTimezone,
} from '../../../common/datetime.util';
import {
  HotlineRequestAddPointStatusEnum,
  HotlineRequestAddPointTypeRequestEnum,
} from '../../common/enums/hotline-request-add-point.enum';
import { HotlineRequestAddPointRepository } from '../../repositories/hotline-request-add-point.repository';
import { HotlineRequestAddPointEntity } from '../../entities/hotline-request-add-point.entity';
import { ScanHistoryRepository } from '../../../point/repositories/scan-history.repository';
import { UserRepository } from '../../../auth/repositories/user.repository';
import { User } from '../../../auth/entities/user.entity';
import { BlockedHistoryRepository } from '../../../point/repositories/blocked-history.repository';
import { GetHotlineRequestAddPointsRequestDto } from '../../dtos/requests/get-hotline-request-add-points.request.dto';
import { CreateHotlineRequestAddPointDto } from '../../dtos/requests/create-hotline-request-add-point.request.dto';
import { HotlineRequestAddPointResponseDto } from '../../dtos/responses/hotline-request-add-point..response.dto';
import { AccountData } from '../../../proto/account.pb';
import {
  ScanHistoryApiType,
  ScanHistoryStatus,
} from '../../../point/enums/scan-history.enum';
import { UserStatus } from '../../../auth/enums/user.enum';
import { BlockedHistoryType } from '../../../point/enums/block-history.enum';
import { SapService } from '../../../external/services/sap.service';
import { AdminActionHistoryRepository } from '../../../admin-action-history/repositories/admin-action-history.repository';
import { EnumNameAdminMenuModule } from '../../../admin_authorization/common/enums/admin-menu-module.enum';
import { PointService } from '../../../point/point.service';
import {
  SapCallApiGetProductByQrResponse,
  SapCallApiGetProductBySpoonCodeResponse,
  SapProductByQrResponse,
  SapProductBySpoonCodeResponse,
} from '../../../external/interfaces/sap.interface';
import { AppBaseExc } from '../../../common/exceptions/custom-app.exception';
import { ProductDataGotByQrDto } from '../../../point/dto/misc/product-data-got-by-qr.dto';
import { ProductRepository } from '../../../product/repositories/product.repository';
import { SpoonRepository } from '../../../spoon/repositories/spoon.repository';
import { HistoryPointRepository } from '../../../point/repositories/history-point.repository';
import { AddPointReqDto } from '../../../point/dto/req/add-point.req.dto';
import { RequestOutboxMessage } from '../../../point/interfaces/outbox-message.interface';
import { SpoonStatus } from '../../../spoon/enums/spoon.enum';
import { Spoon } from '../../../spoon/entities/spoon.entity';
import { OutboxMessage } from '../../../point/entities/outbox-message.entity';
import { SyncData3rdServiceToWhRequestDetail } from '../../../external/interfaces/vita-java.interface';
import { GlobalConfig } from '../../../common/config/global.config';
import { EventAddCanType } from '../../../event-add-can/enums/event-add-can.enum';
import { EventType } from '../../../point/enums/event.enum';

@Injectable()
export class HotlineRequestAddPointAdminService {
  private _logger = new LoggerService(HotlineRequestAddPointAdminService.name);
  // Set app version to avoid push noti old way when trigger event from add point
  private _appVersionName = '6.1.0';
  private crmSyncUsingWh = false;

  constructor(
    private readonly configService: ConfigService<GlobalConfig>,
    private readonly hotlineRequestAddPointRepo: HotlineRequestAddPointRepository,
    private readonly scanHistoryRepo: ScanHistoryRepository,
    private readonly userRepo: UserRepository,
    private readonly blockedHistoryRepo: BlockedHistoryRepository,
    private readonly adminActionHistoryRepo: AdminActionHistoryRepository,
    private readonly sapService: SapService,
    private readonly pointService: PointService,
    private readonly productRepo: ProductRepository,
    private readonly spoonRepo: SpoonRepository,
    private readonly historyPointRepo: HistoryPointRepository,
  ) {
    this.crmSyncUsingWh = this.configService.get('crm.options.syncUsingWh');
  }

  async getHotlineRequestAddPoints(dto: GetHotlineRequestAddPointsRequestDto) {
    const { page, limit } = dto;
    const where = this.generateWhereConditionFromBaseRequest(dto);

    const { items, meta } = await paginate(
      this.hotlineRequestAddPointRepo,
      { limit, page },
      {
        where,
        order: {
          createdAt: 'DESC',
        },
      },
    );
    const data = items.map((m) => new HotlineRequestAddPointResponseDto(m));

    return AppResponseDto.fromNestJsPagination(data, meta);
  }

  async getHotlineRequestAddPoint(id: number): Promise<AppResponseDto> {
    const itemGet = await this.hotlineRequestAddPointRepo.findOneBy({
      id,
    });
    if (!itemGet) {
      throw new BadRequestExc('Data không tồn tại');
    }

    return new AppResponseDto(new HotlineRequestAddPointResponseDto(itemGet));
  }

  // @Transactional()
  async createHotlineRequestAddPoint(
    dto: CreateHotlineRequestAddPointDto,
    admin: AccountData,
  ): Promise<AppResponseDto> {
    // Validate data create
    this.validateCreateHotlineRequestAddPoint(dto);
    // Check duplicate data and allow add point for user
    let user: User = null;
    try {
      const results = await Promise.all([
        this.checkDuplicateDataCreateHotlineRequestAddPoint(dto),
        this.pointService.checkFeatureScanQr(),
      ]);
      user = results[0];
    } catch (err) {
      if (err instanceof AppBaseExc) {
        throw new BadRequestExc('Tính năng Tích Xu đang không hoạt động');
      } else {
        throw err;
      }
    }

    // Insert new data hotline
    const newHotlineRequestAddPoint =
      await this.insertNewHotlineRequestAddPoint(dto, admin);
    let msg = 'ok';
    const source = ScanHistoryApiType.HOTLINE;

    try {
      await this.processAddPoint(newHotlineRequestAddPoint, source);
    } catch (err) {
      console.log(err);
      msg = await this.processAddPointFail(
        err,
        user,
        newHotlineRequestAddPoint,
        source,
      );
      throw new BadRequestExc(msg);
    }

    return new AppResponseDto(msg);
  }

  @Transactional()
  private async insertNewHotlineRequestAddPoint(
    dto: CreateHotlineRequestAddPointDto,
    admin: AccountData,
  ) {
    await Promise.all([
      this.hotlineRequestAddPointRepo.insert({
        ...dto,
        status: HotlineRequestAddPointStatusEnum.PENDDING,
      }),
      this.adminActionHistoryRepo.loggingCreateAction(
        admin.email,
        EnumNameAdminMenuModule.QUAN_LY_DANH_SACH_TICH_XU_HO_ONLINE,
        dto.keyRequest,
      ),
    ]);
    const newHotlineRequestAddPoint =
      await this.hotlineRequestAddPointRepo.findOneBy({
        keyRequest: dto.keyRequest,
      });
    if (!newHotlineRequestAddPoint) {
      throw new BadRequestExc('Lưu data lỗi');
    }

    return newHotlineRequestAddPoint;
  }

  @Transactional()
  private async processAddPoint(
    hotlineRequestAddPoint: HotlineRequestAddPointEntity,
    source: ScanHistoryApiType,
  ): Promise<void> {
    const { oldQrCode, oldSpoonCode, keyRequest, phoneNumber, typeRequest } =
      hotlineRequestAddPoint;
    const identity = `processAddPoint ${convertObjectToString(
      hotlineRequestAddPoint,
    )}`;
    const transactionExternalId = randomTransactionExternalId();
    const addPointReqDto = this.generateAddPointReqDto(hotlineRequestAddPoint);
    this._debugInConsole(`${identity}: Begin`);

    const user = await this.userRepo
      .createQueryBuilder('user')
      .where('user.phoneNumber = :phoneNumber', { phoneNumber })
      .setLock('pessimistic_write')
      .maxExecutionTime(60000) // lock timeout 60s
      .getOne();
    await this.validateUserAddPoint(user);
    this._debugInConsole(`${identity}: Pass validate user`);

    let isFirstScan = false;
    // const dataFirstScan = await this.historyPointRepo
    //   .createQueryBuilder('historyPoint')
    //   .where('historyPoint.customerId = :customerId', { customerId: user.id })
    //   .andWhere(
    //     `historyPoint.actionType LIKE 'FIRST_SCAN%' AND historyPoint.actionType != 'FIRST_SCAN_SBPS'`,
    //   )
    //   .getOne();
    // this._debugInConsole(`${identity}: Pass check first scan`);

    const [productOnSap, dataFirstScan] = await Promise.all([
      this.pointService.getProductByQrOnSap(hotlineRequestAddPoint.oldQrCode),
      this.historyPointRepo
        .createQueryBuilder('historyPoint')
        .where('historyPoint.customerId = :customerId', { customerId: user.id })
        .andWhere(
          `historyPoint.actionType LIKE 'FIRST_SCAN%' AND historyPoint.actionType != 'FIRST_SCAN_SBPS'`,
        )
        .getOne(),
      this.pointService.checkLimitNumberOfScan(user.id),
      // this.getProductBySpoonOnSap(hotlineRequestAddPoint.oldSpoonCode),
    ]);
    if (!dataFirstScan) {
      const dataQrCode = await this.historyPointRepo
        .createQueryBuilder('historyPoint')
        .where('historyPoint.customerId = :customerId', { customerId: user.id })
        .andWhere('historyPoint.actionType LIKE :actionType', {
          actionType: 'QR_CODE',
        })
        .getOne();
      isFirstScan = dataQrCode ? false : true;
    }
    const productData: ProductDataGotByQrDto =
      ProductDataGotByQrDto.fromSapRes(productOnSap);
    if (!productData.code) {
      throw new BadRequestExc('SB_HOTLINE_CODE_NF');
    }
    const product = await this.productRepo.findOneBy({
      code: productData.code,
    });
    if (!product || !product.isActive) {
      throw new BadRequestExc('SB_QR_UNMATCH');
    }
    this._debugInConsole(
      `${identity}: Pass validate old qr code and check first scan`,
    );

    let value;
    let spoon;
    await Promise.allSettled([
      this.createScanHistory(
        user,
        addPointReqDto,
        ScanHistoryStatus.SUCCESS,
        null,
        source,
      ),
      this.pointService.createOrUpdateUserNumberScan(user.id),
      this.useSpoon(addPointReqDto, productData.manufactureDate),
      this.pointService.updateUserPoint(
        user,
        product,
        transactionExternalId,
        isFirstScan,
      ),
    ]).then((results) =>
      results.forEach((result, index) => {
        if (result.status === 'rejected') {
          throw result.reason;
        }
        if (index === 2) {
          spoon = result.value;
        }
        if (index === 3) {
          value = result.value;
        }
      }),
    );
    const userUpdated = value?.userUpdate;
    const notiUpRank = value?.notiUpRank;
    const listTransGiftPoint = value?.listTransGiftPoint;
    this._debugInConsole(
      `${identity}: Pass update scan history and user point`,
    );

    const requestOutboxMessage: RequestOutboxMessage = [];
    const outboxMessageGift: OutboxMessage[] = [];
    const syncData3rdServiceToWhRequestDetails: SyncData3rdServiceToWhRequestDetail[] =
      [];
    const historyPoint = await this.pointService.createAddPointTransaction(
      addPointReqDto,
      userUpdated,
      product,
      productData,
      isFirstScan,
      spoon,
      null,
      transactionExternalId,
      requestOutboxMessage,
    );
    if (listTransGiftPoint && !!listTransGiftPoint?.length) {
      for (let i = 0; i < listTransGiftPoint?.length; i++) {
        if (listTransGiftPoint[i]?.user) {
          await this.pointService.createTranGiftPoint(
            listTransGiftPoint[i]?.user,
            listTransGiftPoint[i]?.transactionExternalId,
            listTransGiftPoint[i]?.actionType,
            listTransGiftPoint[i]?.point,
            listTransGiftPoint[i]?.crmCode,
            requestOutboxMessage,
            listTransGiftPoint[i]?.notiDisplayTemplateType,
            listTransGiftPoint[i]?.notiParams,
            listTransGiftPoint[i]?.totalPoint,
          );
        }
      }
    }
    if (notiUpRank && notiUpRank?.userId) {
      await this.pointService.pushUpdateRankNotification(
        notiUpRank?.userId,
        notiUpRank?.title,
        notiUpRank?.content,
        notiUpRank?.description,
        user,
      );
    }
    this._debugInConsole(`${identity}: Pass create history point`);

    runOnTransactionCommit(() => {
      this.pointService.pushAddPointToPubsub(
        user,
        addPointReqDto,
        productData,
        product,
        historyPoint,
      );
      this._debugInConsole(`${identity}: Process pushAddPointToPubsub`);
    });

    const eventAddCanTypeTrigger: EventAddCanType[] = [];
    const arrTemplate2468Noti = [];
    const eventTrigger: EventType[] = [];
    const arrNoti = [];
    await this.pointService.triggerEvents(
      user,
      userUpdated,
      product,
      spoon,
      productData,
      transactionExternalId,
      arrNoti,
      this._appVersionName,
      requestOutboxMessage,
      null,
      outboxMessageGift,
      syncData3rdServiceToWhRequestDetails,
      'SB',
      source,
      eventTrigger,
      eventAddCanTypeTrigger,
      arrTemplate2468Noti,
      this.crmSyncUsingWh,
      historyPoint,
      addPointReqDto,
    );
    this._debugInConsole(`${identity}: Pass trigger events`);

    hotlineRequestAddPoint.status = HotlineRequestAddPointStatusEnum.SUCCESS;
    await this.hotlineRequestAddPointRepo.save(hotlineRequestAddPoint);

    this._debugInConsole(`${identity}: End`);
  }

  @Transactional()
  private async processAddPointFail(
    error: any,
    user: User,
    hotlineRequestAddPoint: HotlineRequestAddPointEntity,
    source: ScanHistoryApiType,
  ): Promise<string> {
    const { oldQrCode, oldSpoonCode, keyRequest, phoneNumber, typeRequest } =
      hotlineRequestAddPoint;
    const identity = `processAddPointFail ${convertObjectToString(
      hotlineRequestAddPoint,
    )}`;
    const addPointReqDto = this.generateAddPointReqDto(hotlineRequestAddPoint);
    this._debugInConsole(`${identity}: Begin`);

    let errorCode: string;
    if (error instanceof AppBaseExc) {
      errorCode = error.error;
    } else {
      errorCode = error.message;
    }

    const promisesUpdate: any[] = [];
    if ('SB_DEFAULT_ERROR' == errorCode) {
      promisesUpdate.push(
        this.pointService.handleAddPointError(
          error,
          addPointReqDto,
          user.id,
          source,
        ),
      );
    } else {
      // const user = await this.userRepo
      //   .createQueryBuilder('user')
      //   .where('user.phoneNumber = :phoneNumber', { phoneNumber })
      //   // .setLock('pessimistic_write')
      //   // .maxExecutionTime(60000) // lock timeout 60s
      //   .getOne();
      promisesUpdate.push(
        this.createScanHistory(
          user,
          addPointReqDto,
          ScanHistoryStatus.FAILED,
          errorCode,
          source,
        ),
      );
    }
    promisesUpdate.push(
      this.hotlineRequestAddPointRepo.delete({
        id: hotlineRequestAddPoint.id,
      }),
    );

    await Promise.all(promisesUpdate);

    // hotlineRequestAddPoint.status = HotlineRequestAddPointStatusEnum.FAIL;
    // hotlineRequestAddPoint.error = convertObjectToString(error);

    // await Promise.all([
    //   this.createScanHistory(
    //     user,
    //     addPointReqDto,
    //     ScanHistoryStatus.FAILED,
    //     errorCode,
    //     source,
    //   ),
    //   this.pointService.handleAddPointError(
    //     error,
    //     addPointReqDto,
    //     userId,
    //     source,
    //   ),
    //   this.hotlineRequestAddPointRepo.delete({
    //     id: hotlineRequestAddPoint.id,
    //   }),
    // ]);

    this._debugInConsole(`${identity}: End`);

    return errorCode;
  }

  private validateCreateHotlineRequestAddPoint<
    T extends CreateHotlineRequestAddPointDto,
  >(dto: T): void {
    const { oldQrCode, oldSpoonCode, keyRequest, phoneNumber, typeRequest } =
      dto;

    // Phone number
    if (!phoneNumber) {
      throw new BadRequestExc('Số điện thoại không hợp lệ');
    }

    // QR
    if (!oldQrCode || oldQrCode.length < 16 || oldQrCode.length > 18) {
      throw new BadRequestExc('Mã QR cũ không hợp lệ');
    }

    // Spoon
    if (
      !oldSpoonCode &&
      typeRequest == HotlineRequestAddPointTypeRequestEnum.QR_CODE
    ) {
      throw new BadRequestExc('Mã Muỗng cũ không được bỏ trống');
    }
    if (oldSpoonCode && (oldSpoonCode.length < 8 || oldSpoonCode.length > 10)) {
      throw new BadRequestExc('Mã Muỗng cũ không hợp lệ');
    }

    // Type request
    if (!isValueInEnum(HotlineRequestAddPointTypeRequestEnum, typeRequest)) {
      throw new BadRequestExc('Type không hợp lệ');
    }

    // Key request
    if (HotlineRequestAddPointTypeRequestEnum.QR_CODE == typeRequest) {
      if (keyRequest.length < 16 || keyRequest.length > 18) {
        throw new BadRequestExc('Mã QR mới không hợp lệ');
      }
      if (oldQrCode == keyRequest) {
        throw new BadRequestExc('Mã QR mới trùng mã QR cũ');
      }
    }
    if (HotlineRequestAddPointTypeRequestEnum.SPOON_CODE == typeRequest) {
      if (keyRequest.length < 8 || keyRequest.length > 10) {
        throw new BadRequestExc('Mã Muỗng mới không hợp lệ');
      }
      if (oldSpoonCode == keyRequest) {
        throw new BadRequestExc('Mã Muỗng mới trùng mã muỗng cũ');
      }
    }
  }

  private async checkDuplicateDataCreateHotlineRequestAddPoint<
    T extends CreateHotlineRequestAddPointDto,
  >(dto: T): Promise<User> {
    const { oldQrCode, oldSpoonCode, keyRequest, phoneNumber, typeRequest } =
      dto;

    const promisesGet: any[] = [];
    const keyRequestFilterValues = [keyRequest, oldQrCode];
    if (oldSpoonCode) {
      keyRequestFilterValues.push(oldSpoonCode);
    }
    const oldQrCodeFilterValues = [keyRequest, oldQrCode];

    promisesGet.push(
      this.userRepo.findOneBy({
        phoneNumber,
      }),
    );
    promisesGet.push(
      this.hotlineRequestAddPointRepo.findOneBy({
        oldQrCode: In(oldQrCodeFilterValues),
      }),
    );
    promisesGet.push(
      this.hotlineRequestAddPointRepo.findOneBy({
        keyRequest: In(keyRequestFilterValues),
      }),
    );
    if (oldSpoonCode) {
      const oldSpoonCodeFilterValues = [keyRequest, oldSpoonCode];
      promisesGet.push(
        this.hotlineRequestAddPointRepo.findOneBy({
          oldSpoonCode: In(oldSpoonCodeFilterValues),
        }),
      );
    }
    if (HotlineRequestAddPointTypeRequestEnum.QR_CODE == typeRequest) {
      promisesGet.push(
        this.scanHistoryRepo.findOneBy({
          qrCode: keyRequest,
          status: ScanHistoryStatus.SUCCESS,
        }),
      );
    }
    if (HotlineRequestAddPointTypeRequestEnum.SPOON_CODE == typeRequest) {
      promisesGet.push(
        this.scanHistoryRepo.findOneBy({
          spoonCode: keyRequest,
          status: ScanHistoryStatus.SUCCESS,
        }),
      );
    }

    const resultsGet: any[] = await Promise.all(promisesGet);
    if (!resultsGet || !resultsGet.length) {
      throw new BadRequestExc('Kiểm tra dữ liệu bị lỗi. Thao tác lại');
    }

    // Check user
    const user: User = resultsGet.shift();
    if (!user) {
      throw new BadRequestExc('Số điện thoại không tồn tại');
    }
    // Check old qr code
    const hotlineRequestAddPointGetByOldQrCode: HotlineRequestAddPointEntity =
      resultsGet.shift();
    if (hotlineRequestAddPointGetByOldQrCode) {
      if (hotlineRequestAddPointGetByOldQrCode.oldQrCode == oldQrCode) {
        throw new BadRequestExc('Mã QR cũ trùng');
      } else if (hotlineRequestAddPointGetByOldQrCode.oldQrCode == keyRequest) {
        throw new BadRequestExc('Key mới trùng');
      }
    }
    // Check key request
    const hotlineRequestAddPointGetByKeyRequest: HotlineRequestAddPointEntity =
      resultsGet.shift();
    if (hotlineRequestAddPointGetByKeyRequest) {
      if (hotlineRequestAddPointGetByKeyRequest.keyRequest == oldQrCode) {
        throw new BadRequestExc('Mã QR cũ trùng');
      } else if (
        hotlineRequestAddPointGetByKeyRequest.keyRequest == oldSpoonCode
      ) {
        throw new BadRequestExc('Mã Muỗng cũ trùng');
      } else {
        throw new BadRequestExc('Key mới trùng');
      }
    }
    if (oldSpoonCode) {
      // Check old spoon code
      const hotlineRequestAddPointGetByOldSpoonCode: HotlineRequestAddPointEntity =
        resultsGet.shift();
      if (hotlineRequestAddPointGetByOldSpoonCode) {
        if (
          hotlineRequestAddPointGetByOldSpoonCode.oldSpoonCode == oldSpoonCode
        ) {
          throw new BadRequestExc('Mã Muỗng cũ trùng');
        } else if (
          hotlineRequestAddPointGetByOldSpoonCode.oldSpoonCode == keyRequest
        ) {
          throw new BadRequestExc('Key mới trùng');
        }
      }
    }
    if (HotlineRequestAddPointTypeRequestEnum.QR_CODE == typeRequest) {
      // Check scanned by user
      const scanHistory = resultsGet.shift();
      if (scanHistory) {
        throw new BadRequestExc('Key mới đã được user sử dụng cho tích xu');
      }
    }
    if (HotlineRequestAddPointTypeRequestEnum.SPOON_CODE == typeRequest) {
      // Check scanned by user
      const scanHistory = resultsGet.shift();
      if (scanHistory) {
        throw new BadRequestExc('Key mới đã được user sử dụng cho tích xu');
      }
    }

    return user;
  }

  private async validateUserAddPoint(user: User): Promise<void> {
    if (!user) {
      throw new BadRequestExc('SB_USER_NOT_FOUND');
    }
    if (user.blockedAccount) {
      throw new BadRequestExc('BLOCKED_ACCOUNT');
    }
    if (user.status === UserStatus.NON_ACTIVE) {
      throw new BadRequestExc('SB_DEFAULT_ERROR');
    }
    if (!user?.provinceId) {
      throw new BadRequestExc('SB_ADD_EMPTY');
    }
    if (user.startFreezePoint && user.endFreezePoint) {
      if (
        1 != compareDateWithCurrent(user.startFreezePoint) &&
        -1 != compareDateWithCurrent(user.endFreezePoint)
      ) {
        throw new BadRequestExc('BLOCK_IDENTITY_SCAN');
      }
    }
    if (user.blockedScan) {
      const blockedScanHistory = await this.blockedHistoryRepo.findOne({
        where: { userId: user.id },
        order: { actionDate: 'DESC' },
      });
      if (!blockedScanHistory) {
        throw new BadRequestExc('SB_DEFAULT_ERROR');
      }
      if (
        blockedScanHistory.type ===
        BlockedHistoryType.BLOCKED_ACCOUNT_WHEN_SCAN_FAILED
      ) {
        throw new BadRequestExc('SB_ACCBLOCK_ADDPOINT');
      }
      if (
        !user.blockedScanExpiryDate ||
        compareDateWithCurrent(user.blockedScanExpiryDate) == -1
      ) {
        return;
      }
      switch (blockedScanHistory.type) {
        case BlockedHistoryType.BLOCKED_SCAN_QR:
          throw new BadRequestExc('SB_BLOCK_SCANQR');
        case BlockedHistoryType.BLOCKED_SCAN_SAME_QR:
          throw new BadRequestExc('SB_BLOCK_SAMEQR');
        default:
          throw new BadRequestExc('SB_DEFAULT_ERROR');
      }
    }
  }

  private generateWhereConditionFromBaseRequest(
    dto: GetHotlineRequestAddPointsRequestDto,
  ):
    | FindOptionsWhere<HotlineRequestAddPointEntity>[]
    | FindOptionsWhere<HotlineRequestAddPointEntity> {
    const { search, keyRequest, phoneNumber } = dto;

    if (search && (phoneNumber || keyRequest)) {
      const conditions: FindOptionsWhere<HotlineRequestAddPointEntity>[] = [];

      if (phoneNumber && keyRequest) {
        conditions.push({
          oldQrCode: search,
          phoneNumber: phoneNumber,
          keyRequest: keyRequest,
        });

        conditions.push({
          oldSpoonCode: search,
          phoneNumber: phoneNumber,
          keyRequest: keyRequest,
        });
      } else if (phoneNumber) {
        conditions.push({
          oldQrCode: search,
          phoneNumber: phoneNumber,
        });

        conditions.push({
          oldSpoonCode: search,
          phoneNumber: phoneNumber,
        });
      } else if (keyRequest) {
        conditions.push({
          oldQrCode: search,
          keyRequest: keyRequest,
        });

        conditions.push({
          oldSpoonCode: search,
          keyRequest: keyRequest,
        });
      }

      return conditions;
    }

    if (search) {
      const conditions: FindOptionsWhere<HotlineRequestAddPointEntity>[] = [];

      conditions.push({
        oldQrCode: search,
      });

      conditions.push({
        oldSpoonCode: search,
      });

      return conditions;
    }

    const where: FindOptionsWhere<HotlineRequestAddPointEntity> = {};

    if (phoneNumber) {
      where.phoneNumber = phoneNumber;
    }

    if (keyRequest) {
      where.keyRequest = keyRequest;
    }

    return where;
  }

  // private async getProductByQrOnSap(
  //   qrCode: string,
  // ): Promise<SapProductByQrResponse> {
  //   let response: SapCallApiGetProductByQrResponse = null;

  //   try {
  //     response = await this.sapService.getProductByQr(qrCode);
  //     if (!response) {
  //       throw new BadRequestExc('SB_QR_NOEXIST');
  //     }
  //     if (204 == response.sapResStatus) {
  //       throw new BadRequestExc('SB_QR_NOT_FOUND');
  //     }
  //     const products = response.sapResult?.Data?.Products;
  //     if (!products || !products.length) {
  //       throw new BadRequestExc('SB_QR_NOEXIST');
  //     }

  //     return products[0];
  //   } catch (err) {
  //     if (408 == err?.response?.status) {
  //       throw new BadRequestExc('SB_SAP_NORESPONSE');
  //     }

  //     throw new BadRequestExc('SB_QR_SYSTEM_ERROR');
  //   }
  // }

  // private async getProductBySpoonOnSap(
  //   spoonCode: string,
  // ): Promise<SapProductBySpoonCodeResponse> {
  //   let response: SapCallApiGetProductBySpoonCodeResponse = null;

  //   try {
  //     response = await this.sapService.getProductBySpoon(spoonCode);
  //     if (!response) {
  //       throw new BadRequestExc('SB_SPOON_NOEXIST');
  //     }
  //     if (204 == response.sapResStatus) {
  //       throw new BadRequestExc('SB_SPOON_NOT_FOUND');
  //     }
  //     const products = response.sapResult?.Data?.response;
  //     if (!products || !products.length) {
  //       throw new BadRequestExc('SB_SPOON_NOEXIST');
  //     }

  //     const spoonOnSap: SapProductBySpoonCodeResponse = products[0];
  //     // Field Applied_date has no data -> set null to throw error
  //     // Ref ticket 3155
  //     if (!spoonOnSap?.Applied_date) {
  //       throw new BadRequestExc('SB_SPOON_APPLIED_DATE_MISS');
  //     }
  //     spoonOnSap.code = spoonCode;
  //     // SAP TODO: kiểm tra format từ SAP
  //     spoonOnSap.applied_date_obj = makeDateIsDateAtTimeHcm(
  //       spoonOnSap.Applied_date,
  //     );

  //     return spoonOnSap;
  //   } catch (err) {
  //     if (408 == err?.response?.status) {
  //       throw new BadRequestExc('SB_SAP_NORESPONSE');
  //     }

  //     throw new BadRequestExc('SB_SPOON_NOEXIST');
  //   }
  // }

  private _debugInConsole(debug: string): void {
    this._logger.debug(`${debug}`);
  }

  private async useSpoon(
    dto: AddPointReqDto,
    // spoonOnSap: SapProductBySpoonCodeResponse,
    qrManufactureDate: string,
  ) {
    const { qrCode, spoonCode } = dto;
    const spoon = this.spoonRepo.create({
      spoonCode,
      createdDate: getNowAtTimeZoneHcm(),
    });

    spoon.status = SpoonStatus.USED;
    spoon.scanDate = getNowAtTimeZoneHcm();
    spoon.qrCode = qrCode;
    spoon.qrManufactureDate = convertDateToDateAtTimezone(qrManufactureDate);

    return await this.spoonRepo.save(spoon);
  }

  private async createScanHistory(
    user: User,
    dto: AddPointReqDto,
    status: ScanHistoryStatus,
    errorCode?: string,
    source?: ScanHistoryApiType,
  ) {
    const { qrCode, spoonCode } = dto;

    const scanHistory = this.scanHistoryRepo.create({
      userId: user.id,
      userName: user.getFullName(),
      phoneNumber: user.phoneNumber,
      request: JSON.stringify({ spoonCode, qrCode }),
      qrCode,
      spoonCode,
      status,
      apiType:
        source && source == ScanHistoryApiType.HOTLINE
          ? ScanHistoryApiType.HOTLINE
          : ScanHistoryApiType.APP,
      errorCode,
    });
    await this.scanHistoryRepo.save(scanHistory);
  }

  private generateAddPointReqDto(
    hotlineRequestAddPoint: HotlineRequestAddPointEntity,
  ): AddPointReqDto {
    const { oldQrCode, oldSpoonCode, keyRequest, typeRequest } =
      hotlineRequestAddPoint;
    let spoonCode = '';
    let qrCode = '';
    if (HotlineRequestAddPointTypeRequestEnum.QR_CODE == typeRequest) {
      qrCode = keyRequest;
      spoonCode = oldSpoonCode;
    }
    if (HotlineRequestAddPointTypeRequestEnum.SPOON_CODE == typeRequest) {
      qrCode = oldQrCode;
      spoonCode = keyRequest;
    }

    const addPointReqDto = new AddPointReqDto();
    addPointReqDto.qrCode = qrCode;
    addPointReqDto.spoonCode = spoonCode;

    return addPointReqDto;
  }
}
