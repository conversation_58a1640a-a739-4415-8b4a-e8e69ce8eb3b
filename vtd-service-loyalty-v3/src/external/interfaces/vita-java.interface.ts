import {
  JavaV4WhSyncTo3rdServiceCode,
  JavaV4WhSyncTo3rdServiceDestination,
} from '../constants/java_v4.constant';

export interface PushPopupRequest {
  token?: string;
  topic?: string;
  title: string;
  message: string;
  type: number;
  createDate?: Date;
  tokenOfTopic?: string[];
  hmData: {
    code: string;
    title: string;
    content: string;
    image: string;
    text_1: string;
    link_1: string;
    type_1: string;
    text_2: string;
    link_2: string;
    type_2: string;
    notification_title: string;
  };
  image?: string;
  deepLink?: string;
}

export interface PushNotificationRequest {
  token?: string;
  message: string;
  title: string;
  topic: string;
  type: number;
  tokenOfTopic: string[];
  image?: string;
  deep_link?: string;
}

export interface PushPopupRequestV2 {
  token?: string;
  topic?: string;
  title: string;
  message: string;
  type: number;
  createDate?: Date;
  tokenOfTopic?: string[];
  hmData: {
    code: string;
    notification_title: string;
  };
  image?: string;
  deepLink?: string;
}

export interface ExchangeGiftRequest {
  giftId: number;
  quantity: number;
  transactionRefId: string;
  productId: number;
  spoonCode: string;
  giftName: string;
  eventDetailId: number;
}

export interface GiftingGiftRequest {
  oldGiftId?: number;
  giftId?: number;
  crmTransactionTypeId: number;
  quantity: number;
  point: number;
  brand: string;
  decreaseGift: boolean;
  userId?: number;
  transactionDate?: string;
  eventId?: number;
}

export interface RemoveUserGiftGiftingRequest {
  giftId: number;
  quantity: number;
  increaseGift: boolean;
  userGiftId: number;
  vitaCodeId?: number;
}

export interface GetQuantityGiftRequest {
  oldGiftId: number;
}

export interface ReuseUserGiftV4Request {
  userGiftId: number;
  userId?: number;
  transactionDate?: string;
}

export interface GetUserGiftPreOrderEnoughPointToSendNotifyRequest {
  userTotalPoint: number;
}

export interface ExchangeGiftNotificationResponse {
  codes: string[];
}

export interface ExchangeGiftResponse {
  notification: ExchangeGiftNotificationResponse;
}

export interface GiftingGiftDataResponse {
  userGiftId: number;
  //historyPointId: number;
  transactionExternalId: string;
  userGiftStatus: string;
  sourceGift: string;
  giftType: string;
  vitaCode: string;
}

export interface GetQuantityGiftDataResponse {
  quantity: number;
}

export interface GetUserGiftPreOrderEnoughPointToSendNotifyGiftInUserGiftDataResponse {
  name: string[];
}

export interface GetUserGiftPreOrderEnoughPointToSendNotifyUserGiftDataResponse {
  gift: GetUserGiftPreOrderEnoughPointToSendNotifyGiftInUserGiftDataResponse;
}

export interface GiftingGiftResponse {
  data: GiftingGiftDataResponse;
  code: number;
  msg: string;
  status: string;
}

export interface GetQuantityGiftResponse {
  data: GetQuantityGiftDataResponse;
  code: number;
  msg: string;
  status: string;
}
export interface ReuseUserGiftV4Response {
  data: boolean;
  code: number;
  msg: string;
  status: string;
}

export interface GetUserGiftPreOrderEnoughPointToSendNotifyResponse {
  data: GetUserGiftPreOrderEnoughPointToSendNotifyUserGiftDataResponse;
  code: number;
  msg: string;
  status: string;
}

export type UserGiftReuseRequest = {
  userGiftId: number;
  userId?: number;
  transactionDate?: string;
};

export type UserGiftReuseResponse = {
  meta: Meta;
  response: Response;
  notification: Notification;
};

export interface InsertEventPointHistoryToWhResponse {
  data: any;
  code: number;
  msg: string;
  status: string;
}

export interface UpdateEventPointHistoryToWhResponse {
  data: any;
  code: number;
  msg: string;
  status: string;
}

export interface SyncData3rdServiceToWhRequest {
  transactions: SyncData3rdServiceToWhRequestDetail[];
}

export interface SyncData3rdServiceToWhRequestDetail {
  code: JavaV4WhSyncTo3rdServiceCode;
  destination: JavaV4WhSyncTo3rdServiceDestination;
  tListPayload?: any[];
  payload?: any;
}

export interface WhOutboxMessageSyncData3rdServiceRequestData {
  authentication_token: string;
  data: SyncData3rdServiceToWhRequest;
}

export interface WhCallApiResponse {
  isOk: boolean;
  response?: any;
  error?: any;
}

export type Meta = {
  status: number;
  msg: string;
};

export type Response = {
  id: number;
  gift: Gift;
  createdDate: string;
  expiryDate: any;
  voucherRefId: string;
  voucherLink: string;
  voucherCode: string;
  usedDate: string;
  status: string;
  recipientName: any;
  recipientPhone: string;
  recipientAddress: any;
  product: any;
  isCongratulate: any;
  isSentExpirationNotification: any;
  deadlineReward: string;
  rollbackedAt: any;
  telco: string;
  quantity: number;
  giftTransportInfo: any;
};

export type Gift = {
  id: number;
  type: string;
  name: string;
  image: any;
  coverImage: any;
  link: any;
  categoryId: number;
  categoryName: string;
  subCategoryId: any;
  subCategoryName: any;
  description: any;
  point: number;
  priceId: number;
  priceName: string;
  priceValue: number;
  total: number;
  startDate: string;
  endDate: string;
  createdDate: string;
  updatedDate: string;
  money: number;
  priority: number;
  subGiftType: any;
  active: boolean;
  code: string;
  isShowDetail: any;
  expireTime: number;
  activeSmsNotification: any;
  productList: any[];
  subGiftList: any[];
  brandId: any;
  smsGiftId: any;
  telco: any;
  weight: any;
  storageAmountHcm: any;
  storageAmountHn: any;
  transportType: any;
  width: any;
  height: any;
  length: any;
  campaignCode: any;
  bkidsCourseId: any;
  bkidsCourseName: any;
  bkidsCourseClass: any;
  bkidsCoursePrice: any;
  bkidsCourseType: any;
  tierCodes: string[];
  redeemCount: any;
  deliveryCount: any;
  canRedeem: boolean;
  preferential_point: any;
  eproductId: number;
  eproductName: string;
};

export type Notification = {
  title: string;
  notificationTitle: string;
  code: string[];
};

export interface GiftDetailResponse {
  id: number;
  images: string[];
  transportTypeCode: string;
  status: string;
  name: string;
  badgeCodes: string[];
  categoryCode: string;
  tierCodes: string[];
  type: string;
  point: number;
  price: number;
  hiddenTags: any[];
  sfNumber: number;
  startDate: string;
  endDate: string;
  expireHour: number;
  sctNumber: string;
  quantity: number;
  inventory: number;
  quantityLimitForBooking: number;
  purchaseOption: string;
  quantityReward: number;
  quantityReservation: number;
  canRedeem: boolean;
  isAllowReservation: boolean;
  priority: number;
  dynamicData: any;
  giftReservation: any;
}
