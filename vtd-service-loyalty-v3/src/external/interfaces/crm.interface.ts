export interface CrmAuthResponse {
  access_token: string;
  instance_url: string;
  id: string;
  token_type: string;
  issued_at: string;
  signature: string;
  error: string;
  error_description: string;
}

export type CrmCheckLeadByPhoneResponse = {
  Id: string;
  Name: string;
  Phone: string;
  birthday: string;
  email: string;
  sf_id: string;
  LastModifiedDate?: string;
  CreatedDate?: string;
  attributes: { type: string; url: string };
}[][];

export interface CrmAccountRequest {
  id: string;
  name: string;
  LastName: string;
  FirstName: string;
  phone: string;
  email?: string;
  Birthday__c: string;
  customerExternalId: string;
  PersonEmail?: string;
  Invite_Code__c: string;
  Loyalty_Customer_Status__c: string;
  Billing_Address__c: string;
  Billing_Country__c: string;
  Billing_Province__c?: string;
  Billing_District__c?: string;
  Billing_Ward__c?: string;
  Billing_Province_New__c: string;
  Billing_Ward_New__c: string;
  Billing_Street__c?: string;
  Shipping_Address__c?: string;
  Lead_Origin_Detail__c?: string;
  Lead_Origin__c?: string;
  sf_id: string;
  birthday: string;
}

export type CrmConvertLeadResponse = {
  data: {
    Id: string;
    Name: string;
    Phone: string;
    birthday: string; // Format: YYYY-MM-DD, Exp: 1997-04-24
    email: string;
    sf_id: string;
    attributes: { type: string; url: string };
  };
  sf_id: string;
  message: string;
  status: number; // 0: success, 1: failure
}[];

export interface BaseResponseTransaction {
  data: TransactionResponse;
  message: string;
  status: number;
  sf_id: string;
}

export interface TransactionResponse {
  userId: string;
  Spoon_Code__c: string;
  Expiry_date__c: string;
  Manufacturing_Date__c: string;
  Quality__c: string;
  Program_Name__c: string;
  Amount__c: number;
  Rule_Name__c: string;
  Product_Name__c: string;
  Product_Code__c: string;
  Rejected_Reason__c: string;
  Point_Transfer_Status__c: string;
  QR_Code__c: string;
  Transaction_Date__c: string;
  Transaction_External_ID__c: string;
  Description__c: string;
  Redeem_Points__c: number;
  Level_Points__c: number;
  Type__c: string;
  Transaction_Type__c: string;
}

export class CreateAddPointTransactionReqDto {
  userId: number;
  Transaction_Type__c: string;
  Type__c: string;
  Program_Name__c: string;
  Level_Points__c: number;
  Redeem_Points__c: number;
  Description__c?: string;
  Transaction_External_ID__c: string;
  Transaction_Date__c: string;
  QR_Code__c: string;
  Point_Transfer_Status__c: string;
  Rejected_Reason__c?: string;
  Product_Code__c: string;
  Product_Name__c: string;
  Amount__c: number;
  Rule_Name__c: string;
  giftId?: string;
  giftName?: string;
  Quality__c?: string;
  Manufacturing_Date__c: string;
  Expiry_date__c: string;
  Spoon_Code__c: string;
  Campaign__c?: string;
  Distributor_Code__c: string;
  Distributor_Name__c: string;
  Distributor_Address__c: string;
  Store_ID__c: string;
  Store_Name__c: string;
  Store_Address__c: string;
  Spoon_Code_Create_Date__c: string;
  Tier__c: string;
  Tier_Points__c: number;
  Earned_Tier_Point__c: number;
}

export interface CreateAddPointSBPSTransactionReqDto {
  userId: number;
  Transaction_Type__c: string;
  Type__c: string;
  Level_Points__c: number;
  Redeem_Points__c: number;
  Transaction_External_ID__c: string;
  Transaction_Date__c: string;
  QR_Code__c: string;
  Point_Transfer_Status__c: string;
  Product_Code__c: string;
  Product_Name__c: string;
  Amount__c: number;
  Tier__c: string;
  Tier_Points__c: number;
  Earned_Tier_Point__c: number;
  Rule_Name__c: string;
  Expiry_date__c: string;
  Manufacturing_Date__c: string;
  Program_Name__c?: string;
  Loyalty_Member__c?: string;
}

export interface CrmRedeemGiftRequest {
  status_user_gift: string;
  user_gift_id: string;
  user_id: number;
  phoneNumber: string;
  gift_id: string;
  gift_name: string;
  recipientName: string;
  recipientPhone: string;
  recipientAddress: string;
  action_type: string;
  Description__c: string;
  point: number;
  ref_id: string;
  Campaign__c: string;
  quantity: string;
  Expiry_date__c: string;
  Type__c: string;
  Transaction_Type__c: string;
  Rule_Name__c: string;
  Transaction_Date__c: string;
  order_id: string;
  Billing_District__c: string;
  Billing_Province__c: string;
  Billing_Street__c: string;
  Billing_Ward__c: string;
  Quality__c: string;
  Manufacturing_Date__c: string;
  Tier__c: string;
  Tier_Points__c: number;
  Earned_Tier_Point__c: number;
  Transaction_Trigger_External_ID__c?: string;
  Gifts_Source__c?: string;
  Program_Name__c?: string;
  Loyalty_Member__c?: string;
}

export interface CrmPointGiftingRequest {
  userId: number;
  Transaction_Type__c: string;
  Type__c: string;
  Level_Points__c: number;
  Redeem_Points__c: number;
  Transaction_External_ID__c: string;
  Transaction_Date__c: string;
  Rule_Name__c: string;
  Campaign__c: string;
  Tier__c: string;
  Tier_Points__c: number;
  Earned_Tier_Point__c: number;
  Transaction_Trigger_External_ID__c?: string;
}

export interface CrmResponse {
  message: string;
  ref_id: string;
  ref_sf_id: string;
  status: number;
}

export interface CrmRedeemVoucherRequest {
  ref_id: string;
  user_id: number;
  phoneNumber: string;
  productId: string;
  productName: string;
  priceValue: string;
  quantity: string;
  createdDate: string;
  status: string;
  evoucherId: string;
  productPriceId: string;
  Redeem_Points__c: number;
  Rule_Name__c: string;
  Campaign__c: string;
  Transaction_Type__c: string;
  Type__c: string;
  Transaction_Date__c: string;
  Tier__c: string;
  Tier_Points__c: number;
  Earned_Tier_Point__c: number;
  Transaction_Trigger_External_ID__c?: string;
  User_Gift_Id__c?: number;
  EV_Source__c?: string;
  Program_Name__c?: string;
  Loyalty_Member__c?: string;
}

export interface CrmRedeemVitaCodeRequest {
  ref_id: string;
  user_id: string;
  phoneNumber: string;
  productId: string;
  productName: string;
  priceValue: string;
  quantity: string;
  createdDate: string;
  status: string;
  evoucherId: string;
  productPriceId: string;
  Redeem_Points__c: number;
  Rule_Name__c: string;
  Campaign__c: string;
  Transaction_Type__c: string;
  Type__c: string;
  Transaction_Date__c: string;
  Tier__c: string;
  Tier_Points__c: number;
  Earned_Tier_Point__c: number;
  voucherCode: string;
  expiryDate: string;
  // Created_Date__c: string;
  Transaction_Trigger_External_ID__c?: string;
  User_Gift_Id__c?: number;
  EV_Source__c?: string;
  Program_Name__c?: string;
  Loyalty_Member__c?: string;
}

export interface CrmCreateCaseSfRequest {
  Lead__c: string;
  AccountId: string;
  RecipientPhone__c: string;
  OwnerId: string;
  Status: string;
  Case_Source__c: string;
  Origin: string;
  Case_Origin_Detail__c: string;
  Reason: string;
  Case_sub_reason__c: string;
  Case_Reported_By__c: string;
  Subject: string;
  Priority: string;
  Description: string;
  Link__c: string;
}

export interface GetLeadInfoRequest {
  userId: number;
}

export interface GetLeadInfoResponse {
  message: string;
  status: number;
  data: GetLeadInfoResponseData;
  sf_id: string;
  id: string;
}

export interface GetLeadInfoResponseData {
  Lead_Origin_Detail__c: string;
  Lead_Origin__c: string;
  Billing_Street__c: string;
  Billing_Ward__c: string;
  Billing_Province__c: string;
  Billing_District__c: string;
  Billing_Country__c: string;
  Billing_Address__c: string;
  Shipping_Address__c: string;
  Birthdate__c: string;
  Phone: string;
  name: string;
  id: string;
}

export interface CreateCaseSfRequest {
  Lead__c: string;
  AccountId: string;
  RecipientPhone__c: string;
  OwnerId: string;
  Status: string;
  Case_Source__c: string;
  Origin: string;
  Case_Origin_Detail__c: string;
  Reason: string;
  Case_sub_reason__c: string;
  Case_Reported_By__c: string;
  Subject: string;
  Priority: string;
  Description: string;
  Link__c: string;
}

export interface CreateCaseSfResponse {
  id: string;
  success: boolean;
  error: any[];
}
