export interface RedeemStoreApiResponse {
  resp_code: number;
  resp_desc: string;
  tid: string;
}

export interface VgsRedeemStoreRequest {
  tid: string;
  store_id: string;
  store_phone: string;
  store_name: string;
  customer_phone: string;
  transaction_date: string;
  rule_name: string;
}

export interface VgsZaloZnsRequest {
  tid: string;
  customer_phone: string;
  transaction_date: string;
  channel: string;
  data?: any;
}

export interface VgsVariable {
  smsUrl: string;
  zaloUrl: string;
  authorization: string;
  znsOaId: string;
  username: string;
  transactionExternalId: string;
  znsOaTemplateId: string;
  branchName: string;
  znsOaTemplateRemindDownTierRank180DaysId: string;
  znsOaTemplateRemindDownTierRank365DaysId: string;
}
