export enum SendZnsSource {
  REGISTER = 'REGISTER',
  FORGOT_PASSWORD = 'FORGOT_PASSWORD',
  CHANGE_PASSWORD = 'CHANGE_PASSWORD',
  EXCHANGE_GIFT = 'EXCHANGE_GIFT',
  VERIFY_DEACTIVATE_ACCOUNT = 'DELETE_ACCOUNT',
  ADD_CAN_EVENT_4_LON = 'ADD_CAN_EVENT_4_LON',
  HOTLINE_HISTORY_POINT = 'HOTLINE_HISTORY_POINT',
  EVOUCHER_VACXIN = 'EVOUCHER_VACXIN',
  CUSTOM_EVENT = 'CUSTOM_EVENT',
  LOGIN_WA = 'LOGIN_WA',
}

export enum Vgschannel {
  SMS = 'SMS',
  ZALO = 'ZALO',
}
