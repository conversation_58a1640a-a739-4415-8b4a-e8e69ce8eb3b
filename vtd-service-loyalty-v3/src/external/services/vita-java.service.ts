import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { lastValueFrom } from 'rxjs';
import { GlobalConfig } from '../../common/config/global.config';
import { LoggerService } from '../../core';
import {
  JavaV4WhSyncTo3rdServiceCode,
  JavaV4WhSyncTo3rdServiceDestination,
} from '../constants/java_v4.constant';
import {
  ExchangeGiftRequest,
  ExchangeGiftResponse,
  PushNotificationRequest,
  PushPopupRequest,
  PushPopupRequestV2,
  UserGiftReuseRequest,
  UserGiftReuseResponse,
  GiftingGiftRequest,
  GiftingGiftResponse,
  GetQuantityGiftRequest,
  GetQuantityGiftResponse,
  ReuseUserGiftV4Request,
  GetUserGiftPreOrderEnoughPointToSendNotifyRequest,
  GetUserGiftPreOrderEnoughPointToSendNotifyResponse,
  InsertEventPointHistoryToWhResponse,
  UpdateEventPointHistoryToWhResponse,
  SyncData3rdServiceToWhRequest,
  SyncData3rdServiceToWhRequestDetail,
  WhOutboxMessageSyncData3rdServiceRequestData,
  GiftDetailResponse,
  RemoveUserGiftGiftingRequest,
} from '../interfaces/vita-java.interface';

@Injectable()
export class VitaJavaService {
  private _baseUrl: string;
  private _baseUrlV4: string;
  private _baseUrlWhV4: string;
  private _hashKey: string;
  private _logger = new LoggerService(VitaJavaService.name);
  constructor(
    private httpService: HttpService,
    private configService: ConfigService<GlobalConfig>,
  ) {
    this._baseUrl = this.configService.get('vitaJava.application.baseUrl');
    this._baseUrlV4 = this.configService.get('vitaJava.application_v4.baseUrl');
    this._baseUrlWhV4 = this.configService.get('vitaJava.wh_v4.baseUrl');
    this._hashKey = this.configService.get('vitaJava.wh_v4.hashKey');
  }
  buildApplicationHeader(authenticateToken: string) {
    const header: any = {
      'Content-Type': 'application/json',
    };
    if (authenticateToken) {
      header['Authorization'] = authenticateToken;
    }

    return header;
  }

  async pushPopup(request: PushPopupRequest | PushPopupRequestV2) {
    const baseUrl = this.configService.get('vitaJava.notification.baseUrl');
    const pushNotiApi = this.configService.get(
      'vitaJava.notification.pushNotiApi',
    );

    await this.httpService.axiosRef.post(`${baseUrl}${pushNotiApi}`, request);
  }

  async pushNotification(request: PushNotificationRequest) {
    const baseUrl = this.configService.get('vitaJava.notification.baseUrl');
    const pushNotiApi = this.configService.get(
      'vitaJava.notification.pushNotiApi',
    );

    await this.httpService.axiosRef.post(`${baseUrl}${pushNotiApi}`, request);
  }

  async authenticateAdminSpoon(token: string) {
    const baseUrl = this.configService.get('vitaJava.spoon.baseUrl');
    const authenticate = this.configService.get('vitaJava.spoon.authenticate');
    return this.httpService.axiosRef.get(`${baseUrl}${authenticate}`, {
      headers: {
        Authorization: `Bearer ${token}`,
        ['Content-Type']: 'application/json',
      },
    });
  }

  async getAccountSpoonById(userId: number, token: string) {
    const baseUrl = this.configService.get('vitaJava.spoon.baseUrl');
    const getAccountUrl = this.configService.get(
      'vitaJava.spoon.getAccountById',
    );
    return this.httpService.axiosRef.get(
      `${baseUrl}${getAccountUrl}/${userId}`,
      {
        headers: {
          Authorization: `${token}`,
          ['Content-Type']: 'application/json',
        },
      },
    );
  }

  async exchangeGift(
    authenticateToken: string,
    request: ExchangeGiftRequest,
  ): Promise<ExchangeGiftResponse> {
    const baseUrl = this.configService.get('vitaJava.application.baseUrl');
    const exchangeGift = this.configService.get(
      'vitaJava.application.exchangeGift',
    );

    return await this.httpService.axiosRef.post(
      `${baseUrl}${exchangeGift}`,
      request,
      {
        headers: this.buildApplicationHeader(authenticateToken),
      },
    );
  }

  async removeUserGiftGifting(
    authenticateToken: string,
    request: RemoveUserGiftGiftingRequest,
  ): Promise<string> {
    const baseUrl = this._baseUrlV4;
    const giftingGiftNoAuthenToken = this.configService.get(
      'vitaJava.application_v4.removeUserGiftGiftingNoAuthenToken',
    );

    try {
      const endPoint = giftingGiftNoAuthenToken;
      const result = await this.httpService.axiosRef.post(
        `${baseUrl}${endPoint}`,
        request,
        {
          headers: this.buildApplicationHeader(authenticateToken),
        },
      );
      // console.log(result);
      if (!result) {
        return null;
      }
      const data = result.data;
      if (!data) {
        return null;
      }

      return data;
    } catch (err) {
      this._logger.error(
        'Error when call VitaJavaService: ',
        err.message,
        request,
      );
      return null;
    }
  }

  async giftingGift(
    authenticateToken: string,
    request: GiftingGiftRequest,
  ): Promise<GiftingGiftResponse> {
    const baseUrl = this._baseUrlV4;
    const giftingGift = this.configService.get(
      'vitaJava.application_v4.giftingGift',
    );
    const giftingGiftNoAuthenToken = this.configService.get(
      'vitaJava.application_v4.giftingGiftNoAuthenToken',
    );

    try {
      const endPoint = this.isAuthenticateBearerToken(authenticateToken)
        ? giftingGift
        : giftingGiftNoAuthenToken;
      const result = await this.httpService.axiosRef.post(
        `${baseUrl}${endPoint}`,
        request,
        {
          headers: this.buildApplicationHeader(authenticateToken),
        },
      );
      if (!result) {
        return null;
      }
      const data = result.data;
      if (!data || !data.hasOwnProperty('response')) {
        return null;
      }

      return data['response'];
    } catch (err) {
      this._logger.error(
        'Error when call VitaJavaService: ',
        err.message,
        request,
      );
      return null;
    }
  }

  async getQuantityGift(
    authenticateToken: string,
    request: GetQuantityGiftRequest,
  ): Promise<GetQuantityGiftResponse> {
    const baseUrl = this._baseUrlV4;
    const getQuantityGift = this.configService.get(
      'vitaJava.application_v4.getQuantityGift',
    );

    try {
      const result = await this.httpService.axiosRef.get(
        `${baseUrl}${getQuantityGift}`,
        {
          headers: this.buildApplicationHeader(authenticateToken),
          params: request,
        },
      );
      if (!result) {
        return null;
      }
      const data = result.data;
      if (!data || !data.hasOwnProperty('response')) {
        return null;
      }

      return data['response'];
    } catch (err) {
      this._logger.error(
        'Error when call VitaJavaService: ',
        err.message,
        request,
      );
      return null;
    }
  }

  async getUserGiftPreOrderEnoughPointToSendNotify(
    authenticateToken: string,
    request: GetUserGiftPreOrderEnoughPointToSendNotifyRequest,
  ): Promise<GetUserGiftPreOrderEnoughPointToSendNotifyResponse> {
    const baseUrl = this._baseUrlV4;
    const getUserGiftPreOrderEnoughPointToSendNotify = this.configService.get(
      'vitaJava.application_v4.getUserGiftPreOrderEnoughPointToSendNotify',
    );

    try {
      const result = await this.httpService.axiosRef.get(
        `${baseUrl}${getUserGiftPreOrderEnoughPointToSendNotify}`,
        {
          headers: this.buildApplicationHeader(authenticateToken),
          params: request,
        },
      );
      if (!result) {
        return null;
      }
      const data = result.data;
      if (!data || !data.hasOwnProperty('response')) {
        return null;
      }

      return data['response'];
    } catch (err) {
      this._logger.error(
        'Error when call VitaJavaService: ',
        err.message,
        request,
      );
      return null;
    }
  }

  public async userGiftReuse(
    authenticateToken: string,
    request: UserGiftReuseRequest,
    errorHandler = this._defaultErrorHandler,
  ) {
    this._logger.debug(
      'VitaJavaService userGiftReuse Data',
      request,
      authenticateToken,
    );
    const enableGiftingGiftOnJavaV4 = this.configService.get<boolean>(
      'vitaJava.application_v4.enable',
    );
    try {
      if (enableGiftingGiftOnJavaV4) {
        const requestReuseUserGiftOnJavaV4: ReuseUserGiftV4Request = {
          userGiftId: request.userGiftId,
        };
        const baseUrl = this._baseUrlV4;
        const reuseUserGift = this.configService.get(
          'vitaJava.application_v4.reuseUserGift',
        );
        const reuseUserGiftNoAuthenToken = this.configService.get(
          'vitaJava.application_v4.reuseUserGiftNoAuthenToken',
        );
        let endPoint = null;
        if (this.isAuthenticateBearerToken(authenticateToken)) {
          endPoint = reuseUserGift;
        } else {
          endPoint = reuseUserGiftNoAuthenToken;
          if (request.userId) {
            requestReuseUserGiftOnJavaV4.userId = request.userId;
          }
          if (request.transactionDate) {
            requestReuseUserGiftOnJavaV4.transactionDate =
              request.transactionDate;
          }
        }
        const result = await this.httpService.axiosRef.post(
          `${baseUrl}${endPoint}`,
          requestReuseUserGiftOnJavaV4,
          {
            headers: this.buildApplicationHeader(authenticateToken),
          },
        );

        const data = result.data;
        return data['response'];
      } else {
        const endPoint = this.isAuthenticateBearerToken(authenticateToken)
          ? 'user-gift-reuse'
          : 'user-gift-reuse-no-token';
        this._logger.debug('User gift reuse with end point', endPoint);
        const response = await lastValueFrom(
          this.httpService.post<UserGiftReuseResponse>(
            `${this._baseUrl}/api/point/${endPoint}`,
            request,
            {
              headers: this.buildApplicationHeader(authenticateToken),
            },
          ),
        );
        return response.data;
      }
    } catch (error) {
      this._logger.error('Request call VitaJavaService fail', request);
      errorHandler?.(error);
    }
  }

  async getGiftById(
    authenticateToken: string,
    giftId: number,
  ): Promise<GiftDetailResponse> {
    const baseUrl = this._baseUrlV4;
    const giftsUrl = this.configService.get('vitaJava.application_v4.gifts');
    

    let endPoint: string;
    if (this.isAuthenticateBearerToken(authenticateToken)) {
      endPoint = `${baseUrl}${giftsUrl}/${giftId}`;
    } else {
      endPoint = `${baseUrl}${giftsUrl}/no-auth-token/${giftId}`;
    }

    try {
      const result = await this.httpService.axiosRef.get(endPoint, {
        headers: this.buildApplicationHeader(authenticateToken),
      });

      if (!result) {
        return null;
      }
      const data = result.data;
      if (!data || !data.hasOwnProperty('response')) {
        return null;
      }

      return data['response'].data;
    } catch (err) {
      this._logger.error(
        'Error when call VitaJavaService getGiftById: ',
        err.message,
        giftId,
      );
      return null;
    }
  }

  async createGift(
    authenticateToken: string,
    request: any,
  ): Promise<GiftDetailResponse> {
    const baseUrl = this._baseUrlV4;
    const giftsUrl = this.configService.get(
      'vitaJava.application_v4.admin_gifts',
    );

    try {
      const result = await this.httpService.axiosRef.post(
        `${baseUrl}${giftsUrl}`,
        request,
        {
          headers: this.buildApplicationHeader(authenticateToken),
        },
      );
      if (!result) {
        return null;
      }
      const data = result.data;
      if (!data || !data.hasOwnProperty('response')) {
        return null;
      }

      return data['response'].data;
    } catch (err) {
      this._logger.error(
        'Error when call VitaJavaService createGift: ',
        err.message,
        request,
      );
      return null;
    }
  }

  async getGiftCategoryByCode(
    authenticateToken: string,
    giftCategoryCode: string,
  ): Promise<any> {
    const baseUrl = this._baseUrlV4;
    const giftCategoriesUrl = this.configService.get(
      'vitaJava.application_v4.gift_categories',
    );

    try {
      const result = await this.httpService.axiosRef.get(
        `${baseUrl}${giftCategoriesUrl}/${giftCategoryCode}`,
        {
          headers: this.buildApplicationHeader(authenticateToken),
        },
      );
      if (!result) {
        return null;
      }
      const data = result.data;
      if (!data || !data.hasOwnProperty('response')) {
        return null;
      }

      return data['response'].data;
    } catch (err) {
      this._logger.error(
        'Error when call VitaJavaService getGiftCategoryByCode: ',
        err.message,
        giftCategoryCode,
      );
      return null;
    }
  }

  async createGiftCategory(
    authenticateToken: string,
    request: any,
  ): Promise<any> {
    const baseUrl = this._baseUrlV4;
    const giftsUrl = this.configService.get(
      'vitaJava.application_v4.admin_gift_categories',
    );

    try {
      const result = await this.httpService.axiosRef.post(
        `${baseUrl}${giftsUrl}`,
        request,
        {
          headers: this.buildApplicationHeader(authenticateToken),
        },
      );
      if (!result) {
        return null;
      }
      const data = result.data;
      if (!data || !data.hasOwnProperty('response')) {
        return null;
      }

      return data['response'].data;
    } catch (err) {
      this._logger.error(
        'Error when call VitaJavaService createGiftCategory: ',
        err.message,
        request,
      );
      return null;
    }
  }

  async insertEventPointHistoryToWh(
    authenticateToken: string,
    request: any,
    throwExceptionIfError = false,
  ): Promise<InsertEventPointHistoryToWhResponse> {
    const baseUrl = this._baseUrlWhV4;
    const syncEventPointHistoryToWh = this.configService.get(
      'vitaJava.wh_v4.insertEventPointHistoryToWhNoAuthToken',
    );

    try {
      const result = await this.httpService.axiosRef.post(
        `${baseUrl}${syncEventPointHistoryToWh}?hashedKey=${this._hashKey}`,
        request,
        {
          headers: this.buildApplicationHeader(''),
        },
      );
      if (!result) {
        return null;
      }
      const data = result.data;
      if (!data || !data.hasOwnProperty('response')) {
        return null;
      }

      return data['response'];
    } catch (err) {
      this._logger.error(
        'Error when call VitaJavaService insertEventPointHistoryToWh: ',
        err.message,
        request,
      );
      if (throwExceptionIfError) {
        throw err;
      }
      return null;
    }
  }

  async updateEventPointHistoryToWh(
    authenticateToken: string,
    request: any,
    throwExceptionIfError = false,
  ): Promise<UpdateEventPointHistoryToWhResponse> {
    const baseUrl = this._baseUrlWhV4;
    const syncEventPointHistoryToWh = this.configService.get(
      'vitaJava.wh_v4.updateEventPointHistoryToWhNoAuthToken',
    );

    try {
      const result = await this.httpService.axiosRef.put(
        `${baseUrl}${syncEventPointHistoryToWh}?hashedKey=${this._hashKey}`,
        request,
        {
          headers: this.buildApplicationHeader(''),
        },
      );
      if (!result) {
        return null;
      }
      const data = result.data;
      if (!data || !data.hasOwnProperty('response')) {
        return null;
      }

      return data['response'];
    } catch (err) {
      this._logger.error(
        'Error when call VitaJavaService updateEventPointHistoryToWh: ',
        err.message,
        request,
      );
      if (throwExceptionIfError) {
        throw err;
      }
      return null;
    }
  }

  async sendSyncData3rdServiceToWh(
    data: WhOutboxMessageSyncData3rdServiceRequestData,
  ): Promise<any> {
    if (!data.data) {
      throw new Error('Data invalid');
    }

    const baseUrl = this._baseUrlWhV4;
    const endpoint = this.configService.get('vitaJava.wh_v4.syncTo3rdService');
    const endpointNoAuthToken = this.configService.get(
      'vitaJava.wh_v4.syncTo3rdServiceNoAuthenToken',
    );
    //this._logger.debug('Sync to wh', `${baseUrl}${endpoint}`, dataRequest);
    let fullUrl = null;
    // Remove using user authentication token because it can be deleted by user log in on multi device
    data.authentication_token = '';
    if (this.isAuthenticateBearerToken(data.authentication_token)) {
      fullUrl = `${baseUrl}${endpoint}`;
    } else {
      fullUrl = `${baseUrl}${endpointNoAuthToken}`;
    }

    try {
      const result = await this.httpService.axiosRef.post(
        `${fullUrl}`,
        data.data,
        {
          headers: this.buildApplicationHeader(data.authentication_token),
        },
      );
      //this._logger.debug('Sync to wh result', result);

      return result;
    } catch (err) {
      this._logger.error(
        'Error when call VitaJavaService sendSync3rdServiceToWh: ',
        err.message,
        data,
      );

      throw err;
    }
  }

  generateSyncData3rdServiceToWhRequestDetail(
    request: any,
    destination: JavaV4WhSyncTo3rdServiceDestination,
    code: JavaV4WhSyncTo3rdServiceCode,
  ): SyncData3rdServiceToWhRequestDetail {
    const dataRequestDetail: SyncData3rdServiceToWhRequestDetail = {
      code,
      destination,
    };
    if (
      JavaV4WhSyncTo3rdServiceCode.ADD_POINT == code ||
      JavaV4WhSyncTo3rdServiceCode.ADD_POINT_MAMUONG == code ||
      JavaV4WhSyncTo3rdServiceCode.ADD_POINT_MATHECAO == code
    ) {
      dataRequestDetail.tListPayload = request;
    } else {
      dataRequestDetail.payload = request;
    }

    return dataRequestDetail;
  }

  generateWhOutboxMessageSyncData3rdServiceRequestData(
    authenticateToken: string,
    request: any,
    destination: JavaV4WhSyncTo3rdServiceDestination,
    code: JavaV4WhSyncTo3rdServiceCode,
  ): WhOutboxMessageSyncData3rdServiceRequestData {
    const dataRequestDetail = this.generateSyncData3rdServiceToWhRequestDetail(
      request,
      destination,
      code,
    );
    const dataRequest: SyncData3rdServiceToWhRequest = {
      transactions: [dataRequestDetail],
    };
    const rs: WhOutboxMessageSyncData3rdServiceRequestData = {
      authentication_token: authenticateToken,
      data: dataRequest,
    };

    return rs;
  }

  generateWhOutboxMessageSyncData3rdServiceRequestDataInBatch(
    authenticateToken: string,
    request: SyncData3rdServiceToWhRequestDetail[],
  ): WhOutboxMessageSyncData3rdServiceRequestData {
    const dataRequest: SyncData3rdServiceToWhRequest = {
      transactions: request,
    };
    const rs: WhOutboxMessageSyncData3rdServiceRequestData = {
      authentication_token: authenticateToken,
      data: dataRequest,
    };

    return rs;
  }

  private async _defaultErrorHandler(error: Error): Promise<unknown> {
    this._logger.error('Error when call VitaJavaService: ', error.message);
    throw error;
  }

  private isAuthenticateBearerToken(authenticateToken: string) {
    if (!authenticateToken) {
      return false;
    }

    const strAuthenticateBearerToken = 'Bearer ';
    return authenticateToken.startsWith(strAuthenticateBearerToken);
  }
}
