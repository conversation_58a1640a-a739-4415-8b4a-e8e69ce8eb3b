import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import dayjs from 'dayjs';
import formData from 'form-data';
import Redis from 'ioredis';
import { lastValueFrom } from 'rxjs';
import { Transactional } from 'typeorm-transactional';
import { User } from '../../auth/entities/user.entity';
import { BlockedAccountType } from '../../auth/enums/user.enum';
import { InactiveUserRepository } from '../../auth/repositories/inactive-user.repository';
import { UserRepository } from '../../auth/repositories/user.repository';
import { GlobalConfig } from '../../common/config/global.config';
import {
  NodeEnv,
  TIME_FORMAT_VGS,
  TIME_ZONE,
} from '../../common/constants/index.constant';
import { StatusCode } from '../../common/constants/status-code.constant';
import { AppBaseExc } from '../../common/exceptions/custom-app.exception';
import {
  ConflictExc,
  NotFoundExc,
} from '../../common/exceptions/custom-http.exception';
import { randomTransactionExternalId } from '../../common/utils';
import { VitaCode } from '../../event/entities/vita-code.entity';
import {
  NotificationHistoryStatus,
  NotificationHistoryType,
} from '../../notification/enums/notification-history.enum';
import { NotificationHistoryRepository } from '../../notification/repositories/notification-history.repository';
import {
  CallType,
  OutboxMessageStatus,
  SyncProvider,
  SyncType,
} from '../../point/enums/outbox-message.enum';
import { OutboxMessageRepository } from '../../point/repositories/outbox-message.repository';
import { Tier } from '../../tier/entities/tier.entity';
import { DownTierRankRule } from '../../tier/enums/tier.enum';
import { TierRepository } from '../../tier/repositories/tier.repository';
import { OTP_CACHE } from '../constants/index.constant';
import { CskhSmsVietGuysReqDto } from '../dtos/viet-guys/req/cskh-sms.viet-guys.req.dto';
import { ZaloZnsVietGuysReqDto } from '../dtos/viet-guys/req/zalo-zns.viet-guys.req.dto';
import { CskhSmsVietGuysResDto } from '../dtos/viet-guys/res/cskh-sms.viet-guys.res.dto';
import { ZaloZncVietGuysResDto } from '../dtos/viet-guys/res/zalo-znc.viet-guys.res.dto';
import { SendZnsSource, Vgschannel } from '../enums/vgs.enum';
import {
  RedeemStoreApiResponse,
  VgsRedeemStoreRequest,
  VgsVariable,
  VgsZaloZnsRequest,
} from '../interfaces/vgs.interface';
@Injectable()
export class VgsService {
  private vietGuysSmsBaseUrl: string;
  private vietGuysZaloBaseUrl: string;
  private vietGuysCskhSmsUrl: string;
  private vietGuysToken: string;
  private vietGuysZnsOaId: string;
  private vietGuysUsername: string;
  private templateOtpId: string;
  private templateRemindDownTierRank180DaysId: string;
  private templateRemindDownTierRank365DaysId: string;
  private vietGuysBranchName: string;
  private logger = new Logger(VgsService.name);
  constructor(
    private readonly httpService: HttpService,
    private configService: ConfigService<GlobalConfig>,
    private outboxMessageRepo: OutboxMessageRepository,
    private notificationHistoryRepo: NotificationHistoryRepository,
    private userRepo: UserRepository,
    private inactiveUserRepo: InactiveUserRepository,
    @InjectRedis() private readonly redis: Redis,
    private tierRepo: TierRepository,
  ) {
    this.vietGuysSmsBaseUrl = configService.get('vgs.baseSmsUrl');
    this.vietGuysZaloBaseUrl = configService.get('vgs.baseZaloUrl');
    this.vietGuysCskhSmsUrl = configService.get('vgs.cskhSmsUrl');
    this.vietGuysToken = configService.get('vgs.token');
    this.vietGuysZnsOaId = configService.get('vgs.znsOaId');
    this.vietGuysUsername = configService.get('vgs.username');
    this.templateOtpId = configService.get('vgs.templateOtpId');
    this.vietGuysBranchName = configService.get('vgs.branchName');
    this.templateRemindDownTierRank180DaysId = configService.get(
      'vgs.templateRemindDownTierRank180DaysId',
    );
    this.templateRemindDownTierRank365DaysId = configService.get(
      'vgs.templateRemindDownTierRank365DaysId',
    );
  }

  async redeemMoneyStore(body: VgsRedeemStoreRequest) {
    const baseUrl = this.configService.get('vgs.baseUrl');
    const redeemStoreApiUrl = this.configService.get('vgs.api.redeemStore');
    const accessToken = this.configService.get('vgs.header.accessToken');

    const { data } =
      await this.httpService.axiosRef.post<RedeemStoreApiResponse>(
        `${baseUrl}${redeemStoreApiUrl}`,
        body,
        { headers: { 'Access-Token': accessToken } },
      );
    return data;
  }

  @Transactional()
  async sendCustomSms(phone: string, content: string) {
    const variables = this.getVgsVariable();
    const { transactionExternalId, username, branchName } = variables;

    const environtment = this.configService.get('nodeEnv');
    if (environtment == NodeEnv.DEVELOPMENT) return;

    const dto = new CskhSmsVietGuysReqDto({
      from: branchName,
      u: username,
      pwd: `${this.vietGuysToken}`,
      phone: phone,
      sms: content,
      bid: transactionExternalId,
      type: '0',
      json: '1',
    });

    const data = await this.sendSMS(dto, SendZnsSource.CUSTOM_EVENT, variables);

    return data;
  }

  @Transactional()
  async sendOtpSms(phone: string, source: SendZnsSource) {
    await this.validateUser(phone, source);

    const variables = this.getVgsVariable();
    const { transactionExternalId, username, branchName } = variables;

    const OTP = await this.generateOTP(phone);

    const environtment = this.configService.get('nodeEnv');
    if (environtment == NodeEnv.DEVELOPMENT) return;

    const dto = new CskhSmsVietGuysReqDto({
      from: branchName,
      u: username,
      pwd: `${this.vietGuysToken}`,
      phone: phone,
      sms: `Ma xac nhan ${branchName} cua ban la ${OTP}. Nhap ma tren ung dung ${branchName} de xac minh tai khoan.`,
      bid: transactionExternalId,
      type: '0',
      json: '1',
    });

    const data = await this.sendSMS(dto, source, variables);

    return data;
  }

  @Transactional()
  async sendVitaCodeSms(
    phone: string,
    source: SendZnsSource,
    vitaCode: VitaCode,
  ) {
    // (Ticket #3618): Không gửi SMS khi User trúng quà VNVC
    return;

    const variables = this.getVgsVariable();
    const { transactionExternalId, username, branchName } = variables;
    const { code, createdDate, expiryDate, hospital } = vitaCode;
    const dto = new CskhSmsVietGuysReqDto({
      from: branchName,
      u: username,
      pwd: `${this.vietGuysToken}`,
      phone: phone,
      sms:
        `${branchName} gui ban ma E-Voucher tri gia 50,000 VND su dung cac dich vu tiem chung tai he thong VNVC. Ma code ${code}. ` +
        `Ap dung tu ngay ${dayjs(createdDate).format(
          'DD/MM/YYYY',
        )} den het ngay ${dayjs(expiryDate).format(
          'DD/MM/YYYY',
        )}. Chi tiet lien he 1900633559`,
      bid: transactionExternalId,
      type: '0',
      json: '1',
    });

    const data = await this.sendSMS(dto, source, variables);

    return data;
  }

  async verifyOtp(phone: string, otp: string) {
    const cacheOtp = await this.redis.get(OTP_CACHE.REDIS_KEY + phone);
    const data = JSON.parse(cacheOtp);
    // const otp = data.otp;

    if (!cacheOtp) throw new AppBaseExc(StatusCode.OTP_NOT_EXIST);

    if (data.otp !== otp) throw new AppBaseExc(StatusCode.OTP_INCORRECT);

    return;
  }

  // this function return otp:string
  @Transactional()
  async sendOtpZaloRequest(dto: ZaloZnsVietGuysReqDto) {
    await this.validateUser(dto.phone, dto.source);

    const {
      zaloUrl,
      authorization,
      transactionExternalId,
      username,
      znsOaId,
      znsOaTemplateId,
      branchName,
    } = this.getVgsVariable();
    const OTP = await this.generateOTP(dto.phone);

    const environment = this.configService.get('nodeEnv');
    if (environment == NodeEnv.DEVELOPMENT) {
      return;
    }

    const data = {
      username: username,
      mobile: dto.phone,
      bid: transactionExternalId,
      failover: 'sms',
      zns: {
        oa_id: znsOaId,
        template_id: znsOaTemplateId,
        template_data: {
          otp: `${OTP}`,
        },
      },
      sms: {
        message: `Ma xac nhan ${branchName} cua ban la ${OTP}. Nhap ma tren ung dung ${branchName} de xac minh tai khoan.`,
        brand: branchName,
        unicode: false,
      },
    };

    const config = {
      headers: {
        Authorization: authorization,
        'Content-Type': 'application/json',
      },
    };
    console.log('zaloUrl', zaloUrl);
    console.log('data', data);
    console.log('config', config);
    const response = await lastValueFrom(
      this.httpService.post<ZaloZncVietGuysResDto>(zaloUrl, data, config),
    );
    console.log(JSON.stringify(response.data));

    const request: VgsZaloZnsRequest = {
      tid: transactionExternalId,
      customer_phone: dto.phone,
      transaction_date: dayjs().tz(TIME_ZONE).format(TIME_FORMAT_VGS),
      channel: Vgschannel.ZALO,
      data,
    };

    const newOutboxMessage = this.outboxMessageRepo.create({
      provider: SyncProvider.VGS,
      callType: CallType.SYNC,
      syncType: SyncType.IMMEDIATE,
      request: JSON.stringify(request),
      status: OutboxMessageStatus.PROCESSING,
      response: `${JSON.stringify(response.data)}`,
      retryNumber: 0,
    });
    const outboxMessageSaved = await this.outboxMessageRepo.save(
      newOutboxMessage,
    );

    const newNotificationHistory = this.notificationHistoryRepo.create({
      recipient: dto.phone,
      content: data.sms.message,
      type: NotificationHistoryType.ZALO,
      sentTime: dayjs().tz(TIME_ZONE).format(TIME_FORMAT_VGS),
      response: `${JSON.stringify(response.data)}`,
      source: dto.source,
    });

    await this.notificationHistoryRepo.save(newNotificationHistory);

    if (response.data.resultCode != '0') {
      await this.updateStatusOutboxMessageAndNoti(
        outboxMessageSaved.id,
        newNotificationHistory.id,
        OutboxMessageStatus.FAILED,
        NotificationHistoryStatus.FAILED,
      );

      return response.data;
    } else {
      await this.updateStatusOutboxMessageAndNoti(
        outboxMessageSaved.id,
        newNotificationHistory.id,
        OutboxMessageStatus.SUCCESS,
        NotificationHistoryStatus.SUCCESS,
      );
    }

    return response.data;
  }

  private async checkSendSmsSuccess(
    data: CskhSmsVietGuysResDto,
    outboxMessageId: number,
    notificationHistoryId: number,
  ) {
    const { error } = data;
    if (error == 0) {
      await this.updateStatusOutboxMessageAndNoti(
        outboxMessageId,
        notificationHistoryId,
        OutboxMessageStatus.SUCCESS,
        NotificationHistoryStatus.SUCCESS,
      );

      return data;
    }

    await this.updateStatusOutboxMessageAndNoti(
      outboxMessageId,
      notificationHistoryId,
      OutboxMessageStatus.FAILED,
      NotificationHistoryStatus.FAILED,
    );

    const errMsg = `Call API vietguys error, data=${JSON.stringify(data)}`;
    this.logger.error(errMsg);

    return data;
  }

  private async generateOTP(phone: string) {
    const digits = 4;
    const environtment = this.configService.get('nodeEnv');
    if (environtment == NodeEnv.DEVELOPMENT) {
      await this.redis.set(
        OTP_CACHE.REDIS_KEY + phone,
        `{"otp":"0000","phoneNumber":"${phone}","times":1,"ttl":300.000000000}`,
        'EX',
        OTP_CACHE.EXPIRATION_TIME,
      );

      return '0000';
    }

    const otp = Math.floor(Math.random() * (9999 - 1000 + 1) + 1000);

    await this.redis.set(
      OTP_CACHE.REDIS_KEY + phone,
      `{"otp":"${otp}","phoneNumber":"${phone}","times":1,"ttl":300.000000000}`,
      'EX',
      OTP_CACHE.EXPIRATION_TIME,
    );
    return otp.toString().padStart(digits, '0');
  }

  private async updateStatusOutboxMessageAndNoti(
    outboxMessageId: number,
    notificationHistoryId: number,
    statusOutbox: OutboxMessageStatus,
    statusNoti: NotificationHistoryStatus,
  ) {
    await this.outboxMessageRepo.update(outboxMessageId, {
      status: statusOutbox,
    });

    await this.notificationHistoryRepo.update(notificationHistoryId, {
      status: statusNoti,
    });
  }

  private async validateUser(phone: string, source: SendZnsSource) {
    const checkUser = await this.userRepo.findOne({
      where: { phoneNumber: phone },
      relations: { inactiveUser: true },
    });

    switch (source) {
      case SendZnsSource.REGISTER:
        if (!checkUser) {
          // Handle case when user doesn't exist
          // pass or perform necessary actions
        } else if (checkUser.inactiveUser) {
          // Handle case when user exists in inactive_user
          // Re-register or perform necessary actions
          // pass or perform necessary actions
        } else {
          // Handle case when user already exists but not in inactive_user
          throw new ConflictExc(`số điện thoại đã tồn tại`);
        }
        break;

      case SendZnsSource.CHANGE_PASSWORD:
        if (!checkUser) {
          throw new NotFoundExc(`số điện thoại không tồn tại`);
        }
        if (checkUser.blockedAccount) {
          if (
            BlockedAccountType.BLOCKED_ACCOUNT_WHEN_SCAN_FAILED ===
            checkUser.blockedAccountType
          ) {
            throw new AppBaseExc(StatusCode.BLOCKED_ACCOUNT_WHEN_SCAN_FAILED);
          } else {
            throw new AppBaseExc(StatusCode.BLOCKED_ACCOUNT_BY_ADMIN);
          }
        }
        break;

      case SendZnsSource.VERIFY_DEACTIVATE_ACCOUNT:
        if (!checkUser) {
          throw new NotFoundExc(`số điện thoại không tồn tại`);
        }
        break;

      case SendZnsSource.EXCHANGE_GIFT:
        if (!checkUser) {
          throw new NotFoundExc(`số điện thoại không tồn tại`);
        }
        break;

      case SendZnsSource.FORGOT_PASSWORD:
        if (!checkUser) {
          throw new NotFoundExc(`số điện thoại không tồn tại`);
        }
        break;

      case SendZnsSource.LOGIN_WA:
        if (!checkUser) {
          throw new NotFoundExc(`số điện thoại không tồn tại`);
        }
        break;

      default:
        throw new AppBaseExc(StatusCode.OTP_TYPE_NOT_SUPPORT);
    }
  }

  private async sendSMS(
    dto: CskhSmsVietGuysReqDto,
    source: SendZnsSource,
    variables: VgsVariable,
  ) {
    const { smsUrl, transactionExternalId } = variables;

    const body = new formData();

    Object.keys(dto).forEach((key) => body.append(key, dto[key]));
    const headers = {
      ...body.getHeaders(),
    };
    console.log('smsUrl', smsUrl);
    console.log('body', body);
    console.log('headers', headers);
    const { data } = await lastValueFrom(
      this.httpService.post<CskhSmsVietGuysResDto>(smsUrl, body, headers),
    );

    const request: VgsZaloZnsRequest = {
      tid: transactionExternalId,
      customer_phone: dto.phone,
      transaction_date: dayjs().tz(TIME_ZONE).format(TIME_FORMAT_VGS),
      channel: Vgschannel.SMS,
      data: {
        body,
        headers,
      },
    };

    const newOutboxMessage = this.outboxMessageRepo.create({
      provider: SyncProvider.VGS,
      callType: CallType.SYNC,
      syncType: SyncType.IMMEDIATE,
      request: JSON.stringify(request),
      status: OutboxMessageStatus.PROCESSING,
      response: `${JSON.stringify(data)}`,
      retryNumber: 0,
    });
    const outboxMessageSaved = await this.outboxMessageRepo.save(
      newOutboxMessage,
    );

    const newNotificationHistory = this.notificationHistoryRepo.create({
      recipient: dto.phone,
      content: dto.sms,
      type: NotificationHistoryType.SMS,
      sentTime: dayjs().tz(TIME_ZONE).format(TIME_FORMAT_VGS),
      response: `${JSON.stringify(data)}`,
      source: source,
    });

    const NotificationHistorySaved = await this.notificationHistoryRepo.save(
      newNotificationHistory,
    );

    await this.checkSendSmsSuccess(
      data,
      outboxMessageSaved.id,
      NotificationHistorySaved.id,
    );

    return data;
  }

  private getVgsVariable(): VgsVariable {
    const smsUrl = `${this.vietGuysSmsBaseUrl}${this.vietGuysCskhSmsUrl}`;
    const zaloUrl = `${this.vietGuysZaloBaseUrl}`;
    const authorization = `Bearer ${this.vietGuysToken}`;
    const znsOaId = `${this.vietGuysZnsOaId}`;
    const username = `${this.vietGuysUsername}`;
    const transactionExternalId = randomTransactionExternalId();
    const znsOaTemplateId = `${this.templateOtpId}`;
    const branchName = `${this.vietGuysBranchName}`;
    const znsOaTemplateRemindDownTierRank180DaysId = `${this.templateRemindDownTierRank180DaysId}`;
    const znsOaTemplateRemindDownTierRank365DaysId = `${this.templateRemindDownTierRank365DaysId}`;
    return {
      smsUrl,
      zaloUrl,
      authorization,
      znsOaId,
      username,
      transactionExternalId,
      znsOaTemplateId,
      branchName,
      znsOaTemplateRemindDownTierRank180DaysId,
      znsOaTemplateRemindDownTierRank365DaysId,
    };
  }

  @Transactional()
  async sendSmsAddPointByHotlineStatus(
    phone: string,
    source: SendZnsSource,
    qrCode: string,
  ) {
    console.log('send sms');

    const variables = this.getVgsVariable();
    const { transactionExternalId, username, branchName } = variables;
    const dto = new CskhSmsVietGuysReqDto({
      from: branchName,
      u: username,
      pwd: `${this.vietGuysToken}`,
      phone: phone,
      sms:
        `${branchName} da tich lon Calosure QR: ${qrCode} thanh cong cho ban. ` +
        `Ban doi qua tai day: https://doimuongnhanqua.vn. Neu can ho tro lien he hotline 1900633559`,
      bid: transactionExternalId,
      type: '0',
      json: '1',
    });
    const data = await this.sendSMS(dto, source, variables);

    return data;
  }

  async sendZaloRemindDownTierRankUser(
    user: User,
    downRankRule: DownTierRankRule,
  ) {
    console.log('on send zns');
    const {
      zaloUrl,
      authorization,
      transactionExternalId,
      username,
      znsOaId,
      znsOaTemplateRemindDownTierRank180DaysId,
      znsOaTemplateRemindDownTierRank365DaysId,
      branchName,
    } = this.getVgsVariable();
    console.log('after get variable');
    const tierRank = await this.tierRepo.findOne({
      where: { code: user.tierCode },
    });

    const nextTierRank = await this.tierRepo.findOne({
      where: { code: tierRank.nextTierCode },
    });

    const data = this.configDataRemindDownTierRank(
      nextTierRank,
      username,
      user,
      transactionExternalId,
      branchName,
      downRankRule,
      znsOaId,
      znsOaTemplateRemindDownTierRank180DaysId,
      znsOaTemplateRemindDownTierRank365DaysId,
    );
    console.log('-----------data remind down rank -----------');
    console.log(data);
    console.log('-----------data remind down rank end -----------');
    const config = {
      headers: {
        Authorization: authorization,
        'Content-Type': 'application/json',
      },
    };
    console.log('after config data');

    const response = await lastValueFrom(
      this.httpService.post<ZaloZncVietGuysResDto>(zaloUrl, data, config),
    );
    console.log(JSON.stringify(response.data));

    const request: VgsZaloZnsRequest = {
      tid: transactionExternalId,
      customer_phone: user.phoneNumber,
      transaction_date: dayjs().tz(TIME_ZONE).format(TIME_FORMAT_VGS),
      channel: Vgschannel.ZALO,
      data,
    };

    const newOutboxMessage = this.outboxMessageRepo.create({
      provider: SyncProvider.VGS,
      callType: CallType.SYNC,
      syncType: SyncType.IMMEDIATE,
      request: JSON.stringify(request),
      status: OutboxMessageStatus.PROCESSING,
      response: `${JSON.stringify(response.data)}`,
      retryNumber: 0,
    });
    const outboxMessageSaved = await this.outboxMessageRepo.save(
      newOutboxMessage,
    );

    let content = ``;
    const date = dayjs().tz(TIME_ZONE).add(2, 'day').format('DD/MM/YYYY');
    if (downRankRule === DownTierRankRule.AFTER_180_DAYS) {
      const coin = nextTierRank.conditionPoint - user.tierPoint;
      content =
        `Chào bạn ` +
        `${user.lastName && user.lastName}.` +
        ` Bạn cần tích thêm ${coin} xu trước ngày ${date} theo chính sách duy trì hạng thành viên 180 ngày kể từ ngày đạt hạng thành viên hiện tại. Nếu không tích đủ xu, hạng thành viên sẽ bị giảm trên ứng dụng VitaDairy - Đổi muỗng nhận quà. Lưu ý: số xu tích lũy vẫn được giữ nguyên.`;
    } else {
      content = `Chào bạn ${user.lastName}. Bạn cần thực hiện tích xu trước ngày ${date} theo chính sách duy trì giao dịch tích xu 365 ngày. Nếu không thực hiện tích xu, xu tích lũy sẽ bị giảm về 0 và hạng thành viên bị giảm về hạng MEMBER. Trường hợp có thắc mắc vui lòng liên hệ VitaDairy để được hỗ trợ.`;
    }

    const newNotificationHistory = this.notificationHistoryRepo.create({
      recipient: user.phoneNumber,
      content: content,
      type: NotificationHistoryType.ZALO,
      sentTime: dayjs().tz(TIME_ZONE).format(TIME_FORMAT_VGS),
      response: `${JSON.stringify(response.data)}`,
    });

    await this.notificationHistoryRepo.save(newNotificationHistory);

    if (response.data.resultCode != '0') {
      await this.updateStatusOutboxMessageAndNoti(
        outboxMessageSaved.id,
        newNotificationHistory.id,
        OutboxMessageStatus.FAILED,
        NotificationHistoryStatus.FAILED,
      );

      return response.data;
    } else {
      await this.updateStatusOutboxMessageAndNoti(
        outboxMessageSaved.id,
        newNotificationHistory.id,
        OutboxMessageStatus.SUCCESS,
        NotificationHistoryStatus.SUCCESS,
      );
    }

    return response.data;
  }

  private configDataRemindDownTierRank(
    nextTierRank: Tier,
    username: string,
    user: User,
    transactionExternalId: string,
    branchName: string,
    downRankRule: DownTierRankRule,
    znsOaId: string,
    znsOaTemplateRemindDownTierRank180DaysId: string,
    znsOaTemplateRemindDownTierRank365DaysId: string,
  ) {
    const data = {
      username: username,
      mobile: user.phoneNumber,
      bid: transactionExternalId,
      failover: 'sms',
      zns: {},
      sms: { message: ``, brand: branchName, unicode: false },
    };

    const date = dayjs().tz(TIME_ZONE).add(2, 'day').format('DD/MM/YYYY');

    if (downRankRule === DownTierRankRule.AFTER_180_DAYS) {
      const coin = nextTierRank.conditionPoint - user.tierPoint;

      data.zns = {
        oa_id: znsOaId,
        template_id: znsOaTemplateRemindDownTierRank180DaysId,
        template_data: {
          name: `${user.lastName}`,
          coin: `${coin}`,
          date: `${date}`,
        },
      };

      data.sms.message = `Thong bao ban can tich them xu truoc ${date} de duy tri HANG THANH VIEN hien tai tren app VitaDairy - Doi Muong Nhan Qua. Chi tiet lien he hotline 1900633559`;
    }

    if (downRankRule === DownTierRankRule.AFTER_365_DAYS) {
      data.zns = {
        oa_id: znsOaId,
        template_id: znsOaTemplateRemindDownTierRank365DaysId,
        template_data: {
          name: `${user.lastName}`,
          date: `${date}`,
        },
      };

      data.sms.message = `Thong bao ban can tich them xu truoc ${date} de duy tri HANG THANH VIEN hien tai tren app VitaDairy - Doi Muong Nhan Qua. Chi tiet lien he hotline 1900633559`;
    }

    return data;
  }

  @Transactional()
  async sendEvoucherVacxinVitaCodeSms(phone: string, vitaCode: VitaCode) {
    const variables = this.getVgsVariable();
    const { transactionExternalId, username, branchName } = variables;
    const { code, createdDate, expiryDate, hospital } = vitaCode;
    const dto = new CskhSmsVietGuysReqDto({
      from: branchName,
      u: username,
      pwd: `${this.vietGuysToken}`,
      phone: phone,
      sms:
        `${branchName} gui ban ma E-Voucher tri gia 100,000 VND su dung cac dich vu tiem chung tai he thong VNVC. Ma code: ${code}. ` +
        `Ap dung tu ngay ${dayjs(createdDate).format(
          'DD/MM/YYYY',
        )} den het ngay ${dayjs(expiryDate).format(
          'DD/MM/YYYY',
        )}. Chi tiet lien he 1900633559`,
      bid: transactionExternalId,
      type: '0',
      json: '1',
    });
    const data = await this.sendSMS(
      dto,
      SendZnsSource.EVOUCHER_VACXIN,
      variables,
    );

    return data;
  }
}
