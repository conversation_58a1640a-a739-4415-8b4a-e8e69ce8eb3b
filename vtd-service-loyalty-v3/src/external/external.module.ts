import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { AuthModule } from '../auth/auth.module';
import { ElasticSearchModule } from '../elasticSearch/elasticSearch.module';
import { NotificationHistoryRepository } from '../notification/repositories/notification-history.repository';
import { OutboxMessageRepository } from '../point/repositories/outbox-message.repository';
import { TierRepository } from '../tier/repositories/tier.repository';
import { ThirdPartyNotificationService } from './abstracts';
import { JavaLoyaltyExternalModule } from './java-loyalty';
import { BkidsService } from './services/bkids.service';
import { BravoQrService } from './services/bravo-qr.service';
import { CrmService } from './services/crm.service';
import { TelegramService } from './services/telegram.service';
import { ThirdPartyNotificationServiceImpl } from './services/third-party-notification.service';
import { VgsService } from './services/vgs.service';
import { VitaGoService } from './services/vita-go.service';
import { VitaJavaService } from './services/vita-java.service';
import { ZenposQrService } from './services/zenpos-qr.service';
import { SapService } from './services/sap.service';

@Module({
  imports: [
    HttpModule,
    AuthModule,
    ElasticSearchModule,
    JavaLoyaltyExternalModule,
  ],
  controllers: [],
  providers: [
    ZenposQrService,
    BravoQrService,
    CrmService,
    TelegramService,
    VitaGoService,
    VitaJavaService,
    SapService,
    VgsService,
    OutboxMessageRepository,
    BkidsService,
    NotificationHistoryRepository,
    TierRepository,
    {
      useClass: ThirdPartyNotificationServiceImpl,
      provide: ThirdPartyNotificationService,
    },
  ],
  exports: [
    ZenposQrService,
    BravoQrService,
    CrmService,
    TelegramService,
    VitaGoService,
    VitaJavaService,
    SapService,
    VgsService,
    BkidsService,
    ThirdPartyNotificationService,
    JavaLoyaltyExternalModule,
  ],
})
export class ExternalModule {}
