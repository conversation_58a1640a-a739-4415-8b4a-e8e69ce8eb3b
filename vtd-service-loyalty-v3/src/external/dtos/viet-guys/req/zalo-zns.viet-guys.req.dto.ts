import { IsEmpty } from 'class-validator';
import {
  IsValidEnum,
  IsValidNumber,
  IsValidText,
} from '../../../../common/decorators/custom-validator.decorator';
import { ApiProperty } from '@nestjs/swagger';
import { SendZnsSource } from '../../../enums/vgs.enum';

export class ZaloZnsVietGuysReqDto {
  @IsValidText({ maxLength: 12, minLength: 1, required: true })
  phone: string; //sms

  @IsValidEnum({ enum: SendZnsSource, required: true })
  source: SendZnsSource; // register - change passwd - forgot pw - exchange gift
  constructor(data: ZaloZnsVietGuysReqDto) {
    Object.assign(this, data);
  }
}

export class CustomSmSVietGuysReqDto {
  @IsValidText({ required: true })
  phone: string; //sms

  @IsValidText({ required: true })
  content: string;
}

export class SmSVietGuysReqDto {
  @IsValidText({ maxLength: 12, minLength: 1, required: true })
  phone: string; //sms

  @IsValidEnum({ enum: SendZnsSource, required: true })
  source: SendZnsSource; // register - change passwd - forgot pw - exchange gift
}

export class VerifyOtpReqDto {
  @IsValidText({ maxLength: 12, minLength: 1, required: true })
  phone: string; //sms

  @IsValidText({ required: true, maxLength: 4 })
  otp: string; // register - change passwd - forgot pw - exchange gift
}
