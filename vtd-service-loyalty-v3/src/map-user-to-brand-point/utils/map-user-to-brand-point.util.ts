import { MapUserToBrandPointEntity } from '../entities/map-user-to-brand-point.entity';
import { ParseIdSendSfUniqueRetsultInterface } from '../interfaces/map-user-to-brand-point.interface';

export const generateIdSendSfUnique = (
  item: MapUserToBrandPointEntity,
): string => {
  if (!item) {
    return '';
  }

  return `${item.idSendSf}-${item.id}`;
};

export const parseIdSendSfUnique = (
  idSendSfUnique: string,
): ParseIdSendSfUniqueRetsultInterface => {
  const rs: ParseIdSendSfUniqueRetsultInterface = {
    id: 0,
    idSendSf: '',
  };
  if (!idSendSfUnique) {
    return rs;
  }

  const parse = idSendSfUnique.split('-');
  if (!parse || parse.length <= 1) {
    return rs;
  }

  rs.id = Number(parse[parse.length - 1]);
  rs.idSendSf = parse.slice(0, parse.length - 2).join('-');

  return rs;
};
