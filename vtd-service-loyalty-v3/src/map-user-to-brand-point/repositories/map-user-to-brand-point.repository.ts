import { Injectable } from '@nestjs/common';
import { DataSource, UpdateResult } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { MapUserToBrandPointEntity } from '../entities/map-user-to-brand-point.entity';

@Injectable()
export class MapUserToBrandPointRepository extends BaseRepository<MapUserToBrandPointEntity> {
  constructor(dataSource: DataSource) {
    super(MapUserToBrandPointEntity, dataSource);
  }

  public async increaseBrandPoint(
    oldMapUserToBrandPoint: MapUserToBrandPointEntity,
    brandPoint: number,
  ): Promise<UpdateResult> {
    return this.updateBrandPoint(
      oldMapUserToBrandPoint,
      oldMapUserToBrandPoint.brandPoint + brandPoint,
    );
  }

  public async decreaseBrandPoint(
    oldMapUserToBrandPoint: MapUserToBrandPointEntity,
    brandPoint: number,
  ): Promise<UpdateResult> {
    if (oldMapUserToBrandPoint.brandPoint < brandPoint) {
      return null;
    }

    return this.updateBrandPoint(
      oldMapUserToBrandPoint,
      oldMapUserToBrandPoint.brandPoint - brandPoint,
    );
  }

  private async updateBrandPoint(
    oldMapUserToBrandPoint: MapUserToBrandPointEntity,
    newBrandPoint: number,
  ): Promise<UpdateResult> {
    return this.createQueryBuilder()
      .update(MapUserToBrandPointEntity)
      .set({
        brandPoint: () => `${newBrandPoint}`,
        updatedAt: () => 'NOW()',
      })
      .where('id = :id and brand_point = :brand_point', {
        id: oldMapUserToBrandPoint.id,
        brand_point: oldMapUserToBrandPoint.brandPoint,
      })
      .execute();
  }
}
