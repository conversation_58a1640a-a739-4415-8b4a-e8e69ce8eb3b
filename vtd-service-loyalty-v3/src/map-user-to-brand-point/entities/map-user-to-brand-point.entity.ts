import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { BaseEntityWithoutDeletedAtWithoutVersion } from '../../common/entities/base.entity';
import { BrandEntity } from '../../brand/entities/brand.entity';
import { Event } from '../../event/entities/event.entity';
import { EventAddCan } from '../../event-add-can/entities/event-add-can.entity';

@Entity({ name: 'map_user_to_brand_points' })
export class MapUserToBrandPointEntity extends BaseEntityWithoutDeletedAtWithoutVersion {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id', type: 'int4' })
  userId: number;

  @Column({ name: 'brand_id', type: 'int4' })
  brandId: number;

  @ManyToOne(() => BrandEntity, (e) => e.userBrandPoints, {
    onDelete: 'RESTRICT',
  })
  @JoinColumn({ name: 'brand_id' })
  brand: BrandEntity;

  @Column({ name: 'event_id', type: 'int8', nullable: true })
  eventId: number;

  @ManyToOne(() => Event, (e) => e.userBrandPoints, {
    onDelete: 'RESTRICT',
  })
  @JoinColumn({ name: 'event_id' })
  event: Event;

  @Column({ name: 'event_add_can_id', type: 'int4', nullable: true })
  eventAddCanId: number;

  @ManyToOne(() => EventAddCan, (e) => e.userBrandPoints, {
    onDelete: 'RESTRICT',
  })
  @JoinColumn({ name: 'event_add_can_id' })
  eventAddCan: EventAddCan;

  @Column({ name: 'brand_point', type: 'float8' })
  brandPoint: number;

  @Column({
    name: 'id_send_sf',
    type: 'uuid',
    default: () => 'gen_random_uuid()',
  })
  idSendSf: string;

  @Column({ name: 'synced_sf', type: 'boolean', default: false })
  syncedSf: number;

  @Column({ name: 'created_on_sf', type: 'boolean', default: false })
  createdOnSf: number;
}
