{"name": "vtd-service-loyalty-v3", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "dev": "nest start --watch", "start": "nest start", "start:debug": "nest start --debug --watch", "start:prod": "node --max-old-space-size=8192 dist/src/main", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "proto:user": "protoc --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./node_modules/vtd-common-v3/proto --ts_proto_out=src/proto/ node_modules/vtd-common-v3/proto/user.proto --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb", "cron-job": "node dist/src/cron-job/execute-command.js"}, "dependencies": {"@elastic/elasticsearch": "8.5.0", "@fastify/multipart": "^7.7.3", "@fastify/static": "6.5.1", "@google-cloud/pubsub": "3.2.1", "@google-cloud/storage": "6.7.0", "@grpc/grpc-js": "1.7.3", "@grpc/proto-loader": "0.7.3", "@kafkajs/confluent-schema-registry": "3.3.0", "@liaoliaots/nestjs-redis": "9.0.5", "@nestjs-modules/mailer": "1.8.1", "@nestjs/axios": "1.0.0", "@nestjs/bull": "0.6.3", "@nestjs/common": "9.2.1", "@nestjs/config": "2.2.0", "@nestjs/core": "9.2.1", "@nestjs/elasticsearch": "9.0.0", "@nestjs/event-emitter": "^1.4.1", "@nestjs/jwt": "10.0.0", "@nestjs/mapped-types": "*", "@nestjs/microservices": "9.2.1", "@nestjs/platform-express": "9.2.1", "@nestjs/platform-fastify": "9.2.1", "@nestjs/schedule": "2.1.0", "@nestjs/swagger": "6.1.3", "@nestjs/terminus": "9.2.2", "@nestjs/typeorm": "9.0.1", "awesome-phonenumber": "5.10.0", "bcrypt": "^5.1.1", "boolean": "^3.2.0", "bull": "4.10.4", "canvas": "2.10.2", "class-transformer": "0.5.1", "class-validator": "0.13.2", "clone": "2.1.2", "commander": "^10.0.1", "crypto-js": "^4.2.0", "dayjs": "1.11.7", "googleapis": "^126.0.1", "ioredis": "5.3.1", "jimp": "0.22.8", "json2csv": "5.0.7", "kafkajs": "2.2.3", "mssql": "^9.1.2", "mustache": "4.2.0", "nestjs-typeorm-paginate": "4.0.3", "newrelic": "^11.19.0", "nodemailer": "6.9.1", "pg": "8.8.0", "protobufjs": "7.1.2", "qrcode": "1.5.1", "qrcode-reader": "1.0.4", "radash": "^12.1.0", "random-number-csprng": "1.0.2", "reflect-metadata": "0.1.13", "rimraf": "3.0.2", "rxjs": "7.2.0", "serialize-error": "8.0.1", "typeorm": "0.3.10", "typeorm-transactional": "0.4.1", "verify-apple-id-token": "^3.1.2", "vtd-common-v3": "git+https://github.com/bilisoftware/vtd-common-v3.git#77006b6", "xlsx": "0.18.5"}, "devDependencies": {"@nestjs/cli": "9.1.5", "@nestjs/schematics": "9.0.3", "@nestjs/testing": "9.2.1", "@types/bcrypt": "^5.0.2", "@types/clone": "2.1.1", "@types/cron": "2.0.0", "@types/express": "4.17.13", "@types/jest": "27.0.2", "@types/json2csv": "5.0.3", "@types/lodash": "4.14.191", "@types/multer": "^1.4.12", "@types/node": "20.3.2", "@types/pg": "8.6.6", "@types/random-number-csprng": "1.0.0", "@types/supertest": "2.0.11", "@types/validator": "13.11.9", "@typescript-eslint/eslint-plugin": "5.0.0", "@typescript-eslint/parser": "5.0.0", "eslint": "8.0.1", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "4.0.0", "jest": "27.2.5", "prettier": "2.3.2", "source-map-support": "0.5.20", "supertest": "6.1.3", "ts-jest": "27.0.3", "ts-loader": "9.2.3", "ts-node": "10.0.0", "ts-proto": "1.135.0", "tsconfig-paths": "3.10.1", "typescript": "4.3.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}