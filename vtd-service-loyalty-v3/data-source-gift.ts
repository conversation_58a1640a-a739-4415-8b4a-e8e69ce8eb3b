import { types } from 'pg';
import { boolean } from 'boolean';
import { DataSource, DataSourceOptions } from 'typeorm';
import { PostgresConnectionOptions } from 'typeorm/driver/postgres/PostgresConnectionOptions';
import { NodeEnv } from './src/common/constants/index.constant';
import { DATASOURCE_NAME } from './src/external/constants/index.constant';
require('dotenv').config({ override: true });

types.setTypeParser(20, Number);
types.setTypeParser(types.builtins.NUMERIC, (value: string): number =>
  parseFloat(value),
);

let config: DataSourceOptions & PostgresConnectionOptions = {
  type: 'postgres',
  name: DATASOURCE_NAME.DATASOUCE_GIFT,
  host: process.env.DB_HOST_GIFT,
  port: +process.env.DB_PORT_GIFT,
  username: process.env.DB_USERNAME_GIFT,
  password: process.env.DB_PASSWORD_GIFT,
  database: process.env.DB_DATABASE_GIFT,
  entities: ['dist/**/*.entity.js'],
  synchronize: false,
  migrationsRun: false,
  migrations: ['dist/migrations/*.js'],
  logger: 'simple-console',
  logging: false,
  migrationsTransactionMode: 'all',
};

switch (process.env.NODE_ENV) {
  case NodeEnv.TEST:
    break;
  case NodeEnv.DEVELOPMENT:
    config = {
      ...config,
      logging: false,
    };
    break;
  case NodeEnv.PRODUCTION:
    config = {
      ...config,
      logging: false,
    };
    break;

  // default is local
  default:
    config = {
      ...config,
      migrationsRun: false,
      logging: boolean(process.env.SHOW_SQL),
      synchronize: false,
    };
    break;
}

export const dataSourceGift = new DataSource(config);
