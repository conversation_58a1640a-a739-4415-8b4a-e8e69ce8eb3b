import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { NotificationHistory } from '../entities/notification-history.entity';

@Injectable()
export class NotificationHistoryRepository extends BaseRepository<NotificationHistory> {
  constructor(dataSource: DataSource) {
    super(NotificationHistory, dataSource);
  }
}
