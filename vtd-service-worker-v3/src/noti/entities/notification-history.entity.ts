import { Column, <PERSON>tity, PrimaryGeneratedColumn } from 'typeorm';
import {
  NotificationHistoryStatus,
  NotificationHistoryType,
} from '../enums/notification-history.enum';
@Entity({ name: 'notification_history' })
export class NotificationHistory {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  recipient: string;

  @Column({ nullable: true })
  content: string;

  @Column({ name: 'sent_time' })
  sentTime: Date;

  @Column({ nullable: true })
  status: NotificationHistoryStatus;

  @Column({ nullable: true })
  type: NotificationHistoryType;

  @Column({ nullable: true })
  source: string;

  @Column({ nullable: true })
  response: string;
}
