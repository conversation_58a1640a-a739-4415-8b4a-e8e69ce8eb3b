import { BullBoardModule } from '@bull-board/nestjs';
import { RedisModule } from '@liaoliaots/nestjs-redis';
import { BullModule } from '@nestjs/bullmq';
import {
  MiddlewareConsumer,
  Module,
  NestModule,
  OnModuleInit,
  ValidationPipe,
} from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_FILTER, APP_PIPE, ModuleRef } from '@nestjs/core';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import dayjs from 'dayjs';
import vi from 'dayjs/locale/vi';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import duration from 'dayjs/plugin/duration';
import localeData from 'dayjs/plugin/localeData';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import weekday from 'dayjs/plugin/weekday';
import firebaseAdmin from 'firebase-admin';
import {
  addTransactionalDataSource,
  initializeTransactionalContext,
} from 'typeorm-transactional';
import { KafkaModule } from 'utility';
import serviceAccount from '../configs/credentials/vitadairy-d6644-firebase-adminsdk-fco4s-e4c9b29989.json';
import { dataSource } from '../data-source';
import { AppController } from './app.controller';
import { AuthModule } from './auth/auth.module';
import {
  appConfig,
  appConfigValidationSchema,
} from './common/configs/app.config';
import {
  bullBoardConfig,
  bullBoardQueues,
  bullConfigs,
} from './common/configs/bull.config';
import { kafkaConfig } from './common/configs/kafka.config';
import { redisConfig } from './common/configs/redis.config';
import { TIME_ZONE_HCM } from './common/constants/app.constant';
import { AppFilter } from './common/filters/app.filter';
import { BullAuthMiddleware } from './common/middlewares/bull-board-auth.middleware';
import { HealthModule } from './health/health.module';
import { ImportModule } from './import/import.module';
import { NotiModule } from './noti/noti.module';
import { UserModule } from './user/user.module';
import { QueueModule } from './queues/queue.module';
import { ExportModule } from './export/export.module';
import { ExternalModule } from './external/external.module';

@Module({
  imports: [
    // KafkaModule.forRoot(kafkaConfig),
    ScheduleModule.forRoot(),
    BullModule.forRootAsync(bullConfigs),
    BullBoardModule.forRoot(bullBoardConfig),
    BullBoardModule.forFeature(...bullBoardQueues),
    ConfigModule.forRoot({
      isGlobal: true,
      load: [() => appConfig],
      cache: true,
      validationSchema: appConfigValidationSchema,
    }),
    TypeOrmModule.forRootAsync({
      useFactory: () => ({}),
      dataSourceFactory: async () => {
        initializeTransactionalContext();
        return addTransactionalDataSource(dataSource);
      },
    }),
    EventEmitterModule.forRoot(),
    RedisModule.forRootAsync(redisConfig),
    HealthModule,
    AuthModule,
    NotiModule,
    ImportModule,
    UserModule,
    QueueModule,
    ExportModule,
    ExternalModule,
  ],
  providers: [
    { provide: APP_PIPE, useValue: new ValidationPipe({ transform: true }) },
    { provide: APP_FILTER, useValue: new AppFilter() },
  ],
  controllers: [AppController],
})
export class AppModule implements OnModuleInit, NestModule {
  constructor(
    private moduleRef: ModuleRef,
    private configService: ConfigService,
  ) {}

  onModuleInit() {
    dayjs.extend(utc);
    dayjs.extend(timezone);
    dayjs.tz.setDefault(TIME_ZONE_HCM);
    dayjs.extend(weekday);
    dayjs.extend(localeData);
    dayjs.locale(vi);
    dayjs.extend(customParseFormat);
    dayjs.extend(duration);

    firebaseAdmin.initializeApp({
      credential: firebaseAdmin.credential.cert({
        clientEmail: serviceAccount.client_email,
        privateKey: serviceAccount.private_key,
        projectId: serviceAccount.project_id,
      }),
    });
  }

  configure(consumer: MiddlewareConsumer) {
    consumer.apply(BullAuthMiddleware).forRoutes('(.*)queues(.*)');
  }
}
