import { Column, <PERSON>tity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import {
  CleanupStatusEnum,
  ExportStatusEnum,
  ExportTypeEnum,
} from 'vtd-common-v3';
import { ExportJob } from './export-job.entity';
import { ExportSnapshotItem } from './export-snapshot-item.entity';
import { BaseEntityWithoutDeletedAtWithoutVersion } from '../../common/entities/base.entity';

@Entity('export_requests')
export class ExportRequest extends BaseEntityWithoutDeletedAtWithoutVersion {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number;

  @Column({ name: 'export_type', type: 'varchar', length: 100 })
  exportType: ExportTypeEnum;

  @Column({ name: 'requester_id', type: 'bigint' })
  requesterId: number;

  @Column({ name: 'filter_json', type: 'text', nullable: true })
  filterJson: string | null;

  @Column({ name: 'sort_spec', type: 'text', nullable: true })
  sortSpec: string | null;

  @Column({ name: 'status', type: 'varchar', length: 50 })
  status: ExportStatusEnum;

  @Column({ name: 'completed_at', type: 'timestamptz', nullable: true })
  completedAt: Date | null;

  @Column({ name: 'gcs_final_uri', type: 'text', nullable: true })
  gcsFinalUri: string | null;

  @Column({
    name: 'cleanup_status',
    type: 'varchar',
    length: 50,
    nullable: true,
  })
  cleanupStatus: CleanupStatusEnum | null;

  @Column({ name: 'cleanup_attempt', type: 'int', default: 0 })
  cleanupAttempt: number;

  @Column({ name: 'cleanup_next_run_at', type: 'timestamptz', nullable: true })
  cleanupNextRunAt: Date | null;

  @Column({ name: 'cleanup_error', type: 'text', nullable: true })
  cleanupError: string | null;

  @Column({ name: 'gcs_parts_prefix', type: 'text', nullable: true })
  gcsPartsPrefix: string | null;

  @Column({ name: 'error', type: 'text', nullable: true })
  error: string | null;

  @Column({ name: 'total_jobs', type: 'int', default: 0 })
  totalJobs: number;

  @OneToMany(() => ExportJob, (job) => job.exportRequest)
  exportJobs: ExportJob[];

  @OneToMany(() => ExportSnapshotItem, (snapshot) => snapshot.exportRequest)
  snapshotItems: ExportSnapshotItem[];
}
