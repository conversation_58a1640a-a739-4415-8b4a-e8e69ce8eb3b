import { BullModule } from '@nestjs/bullmq';
import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { QueueName } from 'vtd-common-v3';
import { AppConfig } from '../common/configs/app.config';
import { getBullConnectionOpts } from '../common/configs/bull.config';
import { NotificationHistoryRepository } from '../noti/repositories/notification-history.repository';
import { ExportJobItemRepository } from './repositories/export-job-item.repository';
import { ExportJobRepository } from './repositories/export-job.repository';
import { ExportRequestRepository } from './repositories/export-request.repository';
import { ExportSnapshotItemRepository } from './repositories/export-snapshot-item.repository';
import { ExportSplitterProcessor } from './services/processors/export-splitter.processor';
import { ExportWorkerProcessor } from './services/processors/export-worker.processor';

@Module({
  imports: [
    BullModule.registerQueueAsync(
      ...Object.values(QueueName).map((name) => ({
        name,
        inject: [ConfigService],
        useFactory: (cfg: ConfigService<AppConfig>) => ({
          connection: getBullConnectionOpts(cfg),
        }),
      })),
    ),
  ],
  providers: [
    ExportSplitterProcessor,
    ExportWorkerProcessor,
    ExportRequestRepository,
    ExportJobRepository,
    ExportJobItemRepository,
    ExportSnapshotItemRepository,
    NotificationHistoryRepository,
  ],
})
export class ExportModule {}
