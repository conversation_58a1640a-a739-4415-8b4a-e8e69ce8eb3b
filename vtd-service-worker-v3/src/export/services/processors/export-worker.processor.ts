import { OnWorkerEvent, Processor, WorkerHost } from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Job, WorkerOptions } from 'bullmq';
import { Writable } from 'stream';
import { pipeline } from 'stream/promises';
import { In } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import {
  convertDateToDateAtTimezone,
  ExportJobStatusEnum,
  ExportStatusEnum,
  ExportTypeEnum,
  formatToString,
  getNowAtTimezone,
  QueueName,
} from 'vtd-common-v3';
import { AppConfig, appConfig } from '../../../common/configs/app.config';
import { createCsvStream } from '../../../common/utils';
import { GcsService } from '../../../external/services/gcs.service';
import { NotificationHistoryRepository } from '../../../noti/repositories/notification-history.repository';
import { ExportRequest } from '../../entities/export-request.entity';
import { ExportJobItemRepository } from '../../repositories/export-job-item.repository';
import { ExportJobRepository } from '../../repositories/export-job.repository';
import { ExportRequestRepository } from '../../repositories/export-request.repository';
import { ExportSnapshotItemRepository } from '../../repositories/export-snapshot-item.repository';

@Processor(QueueName.EXPORT_WORKER, {
  concurrency: appConfig.export.processorConcurrency,
  lockDuration: appConfig.export.processorLockDuration,
} as WorkerOptions)
export class ExportWorkerProcessor extends WorkerHost {
  private readonly logger = new Logger(ExportWorkerProcessor.name);

  constructor(
    private readonly configService: ConfigService<AppConfig>,
    private readonly gcsService: GcsService,
    private readonly exportRequestRepo: ExportRequestRepository,
    private readonly exportJobRepo: ExportJobRepository,
    private readonly exportJobItemRepo: ExportJobItemRepository,
    private readonly exportSnapshotItemRepo: ExportSnapshotItemRepository,
    private readonly notificationHistoryRepo: NotificationHistoryRepository,
  ) {
    super();
  }

  @OnWorkerEvent('failed')
  async onFailed(job: Job, err: Error) {
    this.logger.error(
      `Export worker job ${job.id} failed: ${err.message}`,
      err.stack,
    );
  }

  async process(job: Job<{ exportRequestId: number; exportJobId: number }>) {
    const { exportRequestId, exportJobId } = job.data;

    const exportRequest = await this.exportRequestRepo.findOne({
      where: { id: exportRequestId },
    });

    if (!exportRequest) {
      throw new Error(`Export request ${exportRequestId} not found`);
    }

    if (exportRequest.status === ExportStatusEnum.CANCELLED) {
      this.logger.warn(`Export request ${exportRequestId} is cancelled`);
      return;
    }

    // If the request is queued, update the status to running
    if (exportRequest.status === ExportStatusEnum.QUEUED) {
      exportRequest.status = ExportStatusEnum.RUNNING;
      await this.exportRequestRepo.save(exportRequest);
    }

    await this.processJob(exportRequest, exportJobId);
  }

  @Transactional()
  private async processJob(exportRequest: ExportRequest, exportJobId: number) {
    const exportJob = await this.exportJobRepo.findOne({
      where: { id: exportJobId },
    });

    if (!exportJob) {
      throw new Error(`Export job id ${exportJobId} not found`);
    }

    if (exportJob.status !== ExportJobStatusEnum.WAITING) {
      this.logger.warn(`Export job id ${exportJobId} is not in WAITING status`);
      return;
    }

    // Update job to running
    exportJob.status = ExportJobStatusEnum.RUNNING;
    exportJob.attempt = exportJob.attempt + 1;
    exportJob.startedAt = getNowAtTimezone();
    await this.exportJobRepo.save(exportJob);

    // Get job items
    const jobItems = await this.exportJobItemRepo.find({
      where: { exportJobId },
    });

    if (jobItems.length === 0) {
      throw new Error(`Export job id ${exportJobId} has no job items`);
    }

    try {
      // Stream export data to GCS
      const csvStream = createCsvStream();
      const destination = `exports/${exportRequest.id}/part_${exportJob.jobIndex}.csv`;
      const gcsStream = this.gcsService.createWriteStream(
        destination,
        'text/csv',
      );
      const pipelinePromise = pipeline(csvStream, gcsStream);

      const recordIds = jobItems.map((item) => item.recordId);
      await this.writeStreamExportData(csvStream, exportRequest, recordIds);

      csvStream.end();
      await pipelinePromise;

      // Update export job to completed
      exportJob.status = ExportJobStatusEnum.SUCCESS;
      exportJob.endedAt = getNowAtTimezone();
      exportJob.gcsPartUri = this.gcsService.getGsUri(destination);
      await this.exportJobRepo.save(exportJob);

      // TODO: Check and trigger finalizer queue
      this.logger.log(`Export job ${exportJobId} completed`);
    } catch (error) {
      this.logger.error(
        `Failed to process export job ${exportJobId}: ${error.message}`,
      );

      // Retry export job if attempt < max attempt
      if (exportJob.attempt < exportJob.maxAttempt) {
        exportJob.attempt = exportJob.attempt + 1;
        exportJob.status = ExportJobStatusEnum.WAITING;
        await this.exportJobRepo.save(exportJob);

        // Try again
        return await this.processJob(exportRequest, exportJobId);
      }

      // Update export job to failed
      exportJob.status = ExportJobStatusEnum.FAILED;
      exportJob.endedAt = getNowAtTimezone();
      exportJob.error = error.message;
      await this.exportJobRepo.save(exportJob);

      // Update export request to failed
      exportRequest.status = ExportStatusEnum.FAILED;
      exportRequest.error = `Job ${exportJobId} failed: ${error.message}`;
      await this.exportRequestRepo.save(exportRequest);
    }
  }

  private async writeStreamExportData(
    writeStream: Writable,
    exportRequest: ExportRequest,
    recordIds: number[],
  ) {
    switch (exportRequest.exportType) {
      case ExportTypeEnum.NOTIFICATION_HISTORIES:
        await this.writeStreamNotificationHistoriesExportData(
          writeStream,
          recordIds,
        );
        break;
      // TODO: Implement other types
      default:
        throw new Error(`Unsupported export type: ${exportRequest.exportType}`);
    }
  }

  private async writeStreamNotificationHistoriesExportData(
    writeStream: Writable,
    recordIds: number[],
  ) {
    const chunkSize = this.configService.get<number>('export.chunkSize');
    for (let i = 0; i < recordIds.length; i += chunkSize) {
      const chunkIds = recordIds.slice(i, i + chunkSize);
      const notificationHistories = await this.notificationHistoryRepo.find({
        where: { id: In(chunkIds) },
      });

      const data = notificationHistories.map((notificationHistory) => {
        const response = notificationHistory.response
          ? JSON.parse(notificationHistory.response)
          : null;

        return {
          id: notificationHistory.id,
          recipient: `="${notificationHistory.recipient}"`,
          content: notificationHistory.content,
          sent_time: formatToString(
            convertDateToDateAtTimezone(notificationHistory.sentTime),
          ),
          status: notificationHistory.status,
          type: notificationHistory.type,
          transaction_id: response?.transaction_id,
          msgid: response?.msgid,
          carrier: response?.carrier,
          source: notificationHistory.source,
        };
      });

      for (const item of data) {
        if (!writeStream.write(item)) {
          await new Promise((resolve) => writeStream.once('drain', resolve));
        }
      }
    }
  }
}
