import {
  InjectQueue,
  OnWorkerEvent,
  Processor,
  WorkerHost,
} from '@nestjs/bullmq';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Job, Queue, WorkerOptions } from 'bullmq';
import { cluster, parallel } from 'radash';
import { Transactional } from 'typeorm-transactional';
import {
  ExportJobStatusEnum,
  ExportStatusEnum,
  ExportTypeEnum,
  getNowAtTimezone,
  QueueName,
} from 'vtd-common-v3';
import { AppConfig, appConfig } from '../../../common/configs/app.config';
import { NotificationHistoryRepository } from '../../../noti/repositories/notification-history.repository';
import { ExportRequest } from '../../entities/export-request.entity';
import { ExportJobItemRepository } from '../../repositories/export-job-item.repository';
import { ExportJobRepository } from '../../repositories/export-job.repository';
import { ExportRequestRepository } from '../../repositories/export-request.repository';
import { ExportSnapshotItemRepository } from '../../repositories/export-snapshot-item.repository';

@Processor(QueueName.EXPORT_SPLITTER, {
  concurrency: appConfig.export.processorConcurrency,
  lockDuration: appConfig.export.processorLockDuration,
} as WorkerOptions)
export class ExportSplitterProcessor extends WorkerHost {
  private readonly logger = new Logger(ExportSplitterProcessor.name);

  constructor(
    @InjectQueue(QueueName.EXPORT_WORKER)
    private readonly workerQueue: Queue,
    private readonly configService: ConfigService<AppConfig>,
    private readonly exportRequestRepo: ExportRequestRepository,
    private readonly exportJobRepo: ExportJobRepository,
    private readonly exportJobItemRepo: ExportJobItemRepository,
    private readonly exportSnapshotItemRepo: ExportSnapshotItemRepository,
    private readonly notificationHistoryRepo: NotificationHistoryRepository,
  ) {
    super();
  }

  @OnWorkerEvent('failed')
  async onFailed(job: Job, err: Error) {
    this.logger.error(
      `Splitter job ${job.id} failed: ${err.message}`,
      err.stack,
    );
    try {
      const { exportRequestId } = job.data;
      const request = await this.exportRequestRepo.findOne({
        where: { id: exportRequestId },
      });
      if (request) {
        request.status = ExportStatusEnum.FAILED;
        request.error = `Split failed: ${err.message}`;
        await this.exportRequestRepo.save(request);
      }
    } catch (error) {
      this.logger.error(
        `Failed to update export request status: ${error.message}`,
      );
    }
  }

  async process(job: Job<{ exportRequestId: number }>) {
    const { exportRequestId } = job.data;
    this.logger.log(`Processing split for export request: ${exportRequestId}`);

    const request = await this.exportRequestRepo.findOne({
      where: { id: exportRequestId },
    });

    if (!request) {
      throw new Error(`Export request ${exportRequestId} not found`);
    }

    if (request.status !== ExportStatusEnum.PENDING_SPLIT) {
      this.logger.warn(
        `Export request ${exportRequestId} is not in PENDING_SPLIT status`,
      );
      return;
    }

    await this.processSplit(request);
  }

  @Transactional()
  private async processSplit(request: ExportRequest) {
    try {
      const recordIds = await this.createSnapshot(request);

      // If no records found, set status to SUCCESS and return
      if (recordIds.length === 0) {
        request.status = ExportStatusEnum.SUCCESS;
        request.completedAt = getNowAtTimezone();
        request.totalJobs = 0;
        await this.exportRequestRepo.save(request);
        this.logger.log(
          `Export request ${request.id} has no records, setting status to SUCCESS`,
        );
        return;
      }

      // Dynamic calculate max records per job based on total records
      const maxRecordsPerJob = this.calculateMaxRecordsPerJob(recordIds.length);
      const totalJobs = Math.ceil(recordIds.length / maxRecordsPerJob);

      // Create and save jobs in bulk
      const jobsToCreate = Array.from({ length: totalJobs }).map((_, i) =>
        this.exportJobRepo.create({
          exportRequestId: request.id,
          jobIndex: i + 1,
          status: ExportJobStatusEnum.WAITING,
          attempt: 0,
          maxAttempt: 3,
        }),
      );
      const savedJobs = await this.exportJobRepo.save(jobsToCreate);

      // Parallel process create snapshot items
      const { chunkSize, processorConcurrency } =
        this.configService.get('export');

      await parallel(processorConcurrency, savedJobs, async (job) => {
        const startIdx = (job.jobIndex - 1) * maxRecordsPerJob;
        const endIdx = Math.min(startIdx + maxRecordsPerJob, recordIds.length);
        const jobRecordIds = recordIds.slice(startIdx, endIdx);

        // Cluster items for this job
        const jobItemChunks = cluster(jobRecordIds, chunkSize).map((chunkIds) =>
          chunkIds.map((recordId) => ({
            exportJobId: job.id,
            recordId: recordId,
          })),
        );

        await Promise.all(
          jobItemChunks.map(async (itemChunk) => {
            await this.exportJobItemRepo.insert(itemChunk);
          }),
        );
      });

      // Update request status
      request.status = ExportStatusEnum.QUEUED;
      request.totalJobs = totalJobs;
      await this.exportRequestRepo.save(request);

      this.logger.log(
        `Export request ${request.id} split into ${totalJobs} jobs`,
      );

      // Trigger worker queue
      await this.workerQueue.addBulk(
        savedJobs.map((job) => ({
          name: `${QueueName.EXPORT_WORKER}_${job.id}`,
          data: {
            exportRequestId: request.id,
            exportJobId: job.id,
          },
        })),
      );
    } catch (error) {
      this.logger.error(
        `Failed to split export request ${request.id}: ${error.message}`,
      );
      request.status = ExportStatusEnum.FAILED;
      request.error = `Split failed: ${error.message}`;
      await this.exportRequestRepo.save(request);
    }
  }

  private async createSnapshot(request: ExportRequest) {
    switch (request.exportType) {
      case ExportTypeEnum.USERS:
        // TODO: Implement create users snapshot
        return [];
      case ExportTypeEnum.HISTORY_POINTS:
        // TODO: Implement create history points snapshot
        return [];
      case ExportTypeEnum.RETURN_POINTS:
        // TODO: Implement create return points snapshot
        return [];
      case ExportTypeEnum.USER_GIFTS:
        // TODO: Implement create user gifts snapshot
        return [];
      case ExportTypeEnum.GOT_IT_TRANSACTION_LOGS:
        // TODO: Implement create got it transaction logs snapshot
        return [];
      case ExportTypeEnum.NOTIFICATION_HISTORIES:
        return this.createNotificationHistoriesSnapshot(request);
      default:
        throw new Error(`Unsupported export type: ${request.exportType}`);
    }
  }

  private async createNotificationHistoriesSnapshot(request: ExportRequest) {
    const { filter, sortField, sortDirection } =
      this.parseFilterAndSort(request);

    const queryBuilder = this.notificationHistoryRepo
      .createQueryBuilder('nh')
      .select(['nh.id']);

    // Apply sort
    queryBuilder.orderBy(sortField, sortDirection);

    // Limit export to max 3 months
    queryBuilder.andWhere(`nh.sentTime >= NOW() - INTERVAL '3 months'`);

    // TODO: Apply filter

    const notificationHistories = await queryBuilder.getMany();
    const recordIds = notificationHistories.map((nh) => nh.id);

    return recordIds;
  }

  private parseFilterAndSort(request: ExportRequest): {
    filter: any;
    sortField: string;
    sortDirection: 'ASC' | 'DESC';
  } {
    let filter: any = {};
    let sortField: any = 'id';
    let sortDirection: 'ASC' | 'DESC' = 'DESC';

    // Parse filter
    if (request.filterJson) {
      try {
        filter = JSON.parse(request.filterJson);
      } catch (error) {
        this.logger.warn(
          `Invalid filter JSON for request ${request.id}: ${error.message}`,
        );
      }
    }

    // Parse sort
    if (request.sortSpec) {
      try {
        const sortParsed = JSON.parse(request.sortSpec);
        sortField = sortParsed.field;
        sortDirection = sortParsed.direction.toUpperCase();
      } catch (error) {
        this.logger.warn(
          `Invalid sort JSON for request ${request.id}: ${error.message}`,
        );
      }
    }

    return { filter, sortField, sortDirection };
  }

  /**
   * Dynamically calculate max records per job based on performance guidelines:
   * - For small datasets (<= minRecordsPerJob), keep in 1 job to minimize overhead.
   * - For large datasets, aim for ~targetJobs jobs to balance parallelization and overhead.
   * - Cap records per job at maxRecordsPerJob to avoid memory/timeout issues.
   */
  private calculateMaxRecordsPerJob(totalRecords: number): number {
    const { targetJobs, minRecordsPerJob, maxRecordsPerJob } =
      this.configService.get('export');

    if (totalRecords <= minRecordsPerJob) {
      return Math.max(totalRecords, 1);
    }

    // Aim for targetJobs for moderate to large datasets
    const recordsPerJob = Math.ceil(totalRecords / targetJobs);

    // Clamp records per job between min and max
    return Math.max(
      minRecordsPerJob,
      Math.min(recordsPerJob, maxRecordsPerJob),
    );
  }
}
