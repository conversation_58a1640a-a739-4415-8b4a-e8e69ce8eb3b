import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { ExportSnapshotItem } from '../entities/export-snapshot-item.entity';

@Injectable()
export class ExportSnapshotItemRepository extends BaseRepository<ExportSnapshotItem> {
  constructor(dataSource: DataSource) {
    super(ExportSnapshotItem, dataSource);
  }
}
