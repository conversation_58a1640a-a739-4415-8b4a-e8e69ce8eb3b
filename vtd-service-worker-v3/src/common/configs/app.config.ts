import * as dotenv from 'dotenv';
import { boolean } from 'boolean';
import joi from 'joi';
import { NodeEnv } from '../enums/app.enum';
import { RecursiveKeyOf } from '../types/util.type';
dotenv.config();

export const appConfig = {
  nodeEnv: process.env.NODE_ENV,
  port: +process.env.PORT,

  redisV2: {
    cloud: {
      host: process.env.REDIS_V2_CLOUD_HOST,
      port: process.env.REDIS_V2_CLOUD_PORT,
      password: process.env.REDIS_V2_CLOUD_PASSWORD,
      caPem: process.env.REDIS_V2_CLOUD_CA_PEM
        ? process.env.REDIS_V2_CLOUD_CA_PEM.replace(/\\n/gm, '\n')
        : '',
    },
    standAlone: {
      host: process.env.REDIS_V2_HOST,
      port: process.env.REDIS_V2_PORT,
    },
    sentinels:
      process.env.REDIS_V2_SENTINELS?.split('|')?.map((item) => {
        const [host, port] = item?.split(':') || [];
        return { host, port };
      }) || [],
    password: process.env.REDIS_V2_PASSWORD,
    redisGroupName: 'myMaster',
    usingRedisSentinel: boolean(process.env.REDIS_V2_USING_REDIS_SENTINEL),
    usingRedisCloud: boolean(process.env.REDIS_V2_USING_REDIS_CLOUD),
  },
  crm: {
    syncNotiStatus: {
      newMethodPhoneNumbers:
        process.env.SF_SYNC_NOTI_STATUS_NEW_METHOD_PHONE_NUMBER?.split(','),
    },
  },

  grpc: {
    userSerivce: {
      url: process.env.GRPC_USER_SERVICE_URL,
    },
    accountService: {
      url: process.env.GRPC_ACCOUNT_SERVICE_URL,
    },
  },

  bullBoard: {
    user: process.env.BULL_BOARD_USER || 'vita',
    pass: process.env.BULL_BOARD_PASS || 'vita',
  },

  queue: {
    numberOfBatchAddEventUserAddCan:
      +process.env.QUEUE_NUMBER_OF_BATCH_ADD_EVENT_USER_ADD_CAN || 5,
    numberOfBatchVNVC: +process.env.QUEUE_NUMBER_OF_BATCH_VNVC || 5,
    chunkOfBatchVNVC: +process.env.QUEUE_CHUNK_OF_BATCH_VNVC || 12000,
    chunkOfBatchAddEventUserAddCan:
      +process.env.QUEUE_CHUNK_OF_BATCH_ADD_EVENT_USER_ADD_CAN || 12000,
  },

  export: {
    processorConcurrency: +process.env.EXPORT_PROCESSOR_CONCURRENCY || 1,
    processorLockDuration:
      +process.env.EXPORT_PROCESSOR_LOCK_DURATION || 300000,
    targetJobs: +process.env.EXPORT_TARGET_JOBS || 20,
    minRecordsPerJob: +process.env.EXPORT_MIN_RECORDS_PER_JOB || 5000,
    maxRecordsPerJob: +process.env.EXPORT_MAX_RECORDS_PER_JOB || 50000,
    chunkSize: +process.env.EXPORT_CHUNK_SIZE || 10000,
  },
};

export type AppConfig = Record<RecursiveKeyOf<typeof appConfig>, string>;

export const appConfigValidationSchema = joi.object({
  NODE_ENV: joi
    .string()
    .valid(...Object.values(NodeEnv))
    .required(),
  PORT: joi.number().required(),

  DB_HOST: joi.string().required(),
  DB_PORT: joi.number().required(),
  DB_USERNAME: joi.string().required(),
  DB_PASSWORD: joi.string().required(),
  DB_DATABASE: joi.string().required(),

  KAFKA_BROKER: joi.string().required(),

  GRPC_USER_SERVICE_URL: joi.string().required(),
  GRPC_ACCOUNT_SERVICE_URL: joi.string().required(),

  EXPORT_PROCESSOR_CONCURRENCY: joi.number().required(),
  EXPORT_PROCESSOR_LOCK_DURATION: joi.number().required(),
  EXPORT_TARGET_JOBS: joi.number().required(),
  EXPORT_MIN_RECORDS_PER_JOB: joi.number().required(),
  EXPORT_MAX_RECORDS_PER_JOB: joi.number().required(),
  EXPORT_CHUNK_SIZE: joi.number().required(),
});
