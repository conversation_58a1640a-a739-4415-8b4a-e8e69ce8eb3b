import { createHmac } from 'crypto';
// import * as Buffer from 'buffer';
import { parsePhoneNumber } from 'awesome-phonenumber';
import clone from 'clone';
import dayjs from 'dayjs';
import { TIME_ZONE_HCM } from './constants/app.constant';
import { Parser, Transform } from 'json2csv';

export const genHmac = (secret: string, data: string, algorithm = 'sha256') => {
  const hmac = createHmac(algorithm, secret).update(data);
  return hmac.digest('hex');
};

export const deepClone = <T extends Record<any, any>>(obj: T): T => {
  return clone(obj);
};

export const getNowAtTimeZoneHcm = () => {
  // return new Date(
  //   dayjs().tz(TIME_ZONE).format('YYYY-MM-DDTHH:mm:ss.SSS+00:00'),
  // );
  return dayjs()
    .tz(TIME_ZONE_HCM)
    .format('YYYY-MM-DDTHH:mm:ss') as unknown as Date;
};

export const getNowAtTimeHcm = () => {
  return dayjs().tz(TIME_ZONE_HCM) as unknown as Date;
};

export function getPhoneE164(
  phone: string,
  regionCode = 'VN',
): string | undefined {
  const phoneNumber = parsePhoneNumber(phone, { regionCode });

  return phoneNumber.possible ? phoneNumber.number.e164 : undefined;
}

export function encodeStringToBase64(str: string): string {
  const buffer = Buffer.from(str);
  return buffer.toString('base64');
}

export function decodeBase64ToString(base64Str: string): string {
  const buffer = Buffer.from(base64Str, 'base64');
  return buffer.toString();
}

export function isNullOrUndefined(obj: any) {
  if (typeof obj === 'undefined' || obj === null) return true;
  return false;
}

export const camelToSnakeCase = (str: string) => {
  return (
    str[0].toLowerCase() +
    str.slice(1).replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`)
  );
};

export const isKeyInEnum = <T extends object>(
  enumObj: T,
  key: any,
): boolean => {
  return key in enumObj;
};

export const generateCsvBuffer = async (data: any[]): Promise<Buffer> => {
  if (data.length === 0) {
    return Buffer.from('');
  }

  const json2csvParser = new Parser();
  const csv = json2csvParser.parse(data);
  return Buffer.from(csv);
};

export const createCsvStream = (fields?: string[]) => {
  return new Transform({ fields }, { objectMode: true });
};
