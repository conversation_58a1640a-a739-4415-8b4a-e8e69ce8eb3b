import { Bucket, Storage } from '@google-cloud/storage';
import { Injectable } from '@nestjs/common';
import { Writable } from 'stream';
import StorageConfig from '../../common/configs/ggcloud-storage-config';

@Injectable()
export class GcsService {
  private bucket: Bucket;
  private storage: Storage;

  constructor() {
    this.storage = new Storage({
      projectId: StorageConfig.projectId,
      credentials: {
        client_email: StorageConfig.clientEmail,
        private_key: StorageConfig.privateKey,
      },
    });
    this.bucket = this.storage.bucket(StorageConfig.mediaBucket);
  }

  async uploadBuffer(
    buffer: Buffer,
    destination: string,
    contentType: string,
  ): Promise<string> {
    const file = this.bucket.file(destination);
    await file.save(buffer, {
      contentType,
      metadata: {
        cacheControl: 'no-cache, max-age=0',
      },
    });
    return `gs://${this.bucket.name}/${destination}`;
  }

  createWriteStream(destination: string, contentType: string): Writable {
    const file = this.bucket.file(destination);
    return file.createWriteStream({
      metadata: {
        contentType,
        cacheControl: 'no-cache, max-age=0',
      },
      resumable: false,
    });
  }

  getGsUri(destination: string): string {
    return `gs://${this.bucket.name}/${destination}`;
  }
}
