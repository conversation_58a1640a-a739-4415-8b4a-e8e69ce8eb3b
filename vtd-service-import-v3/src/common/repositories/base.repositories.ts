import {
  DataSource,
  EntityTarget,
  FindOptionsWhere,
  ObjectLiteral,
  Repository,
  SelectQueryBuilder,
} from 'typeorm';
import { IStatusCode } from '../interfaces/index.interface';
import {
  QUERY_FILTERS_KEY,
  QueryFilterMetadata,
} from '../decorators/query-filter.decorator';

export class BaseRepository<T extends ObjectLiteral> extends Repository<T> {
  constructor(entity: EntityTarget<T>, dataSource: DataSource) {
    super(entity, dataSource.createEntityManager());
  }

  async findOneOrThrowExc(
    conditions: FindOptionsWhere<T>,
    statusCode: IStatusCode,
  ) {
    const result = await this.findOneBy(conditions);
    if (!result) {
      // throw new AppBaseExc(statusCode, `${this.metadata.name} is not found `);
    }
    return result;
  }

  protected _greatest(query: string, compareNumber: number) {
    return `GREATEST(${query},${compareNumber})`;
  }

  protected _least(query: string, compareNumber: number) {
    return `LEAST(${query},${compareNumber})`;
  }

  async applyAllFilters<TDto, TEntity = T>(
    queryBuilder: SelectQueryBuilder<TEntity>,
    dto: TDto,
    target: any,
  ): Promise<SelectQueryBuilder<TEntity>> {
    if (!target) {
      return queryBuilder;
    }

    const ctor = target.constructor as any;
    const filters: QueryFilterMetadata[] =
      Reflect.getMetadata(QUERY_FILTERS_KEY, ctor) || [];

    if (!filters.length) {
      return queryBuilder;
    }

    const sorted = [...filters].sort((a, b) => a.order - b.order);

    for (const { key } of sorted) {
      const fn = (target as any)[key];
      if (typeof fn !== 'function') {
        // eslint-disable-next-line no-continue
        continue;
      }
      // Support both sync and async filter functions
      // eslint-disable-next-line no-await-in-loop
      await fn.call(target, queryBuilder, dto);
    }

    return queryBuilder;
  }
}
