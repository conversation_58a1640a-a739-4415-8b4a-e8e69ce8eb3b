import dayjs from 'dayjs';

export const splitDateRange = (
  startDate: string,
  endDate: string,
  monthsPerChunk: number,
) => {
  const start = dayjs(startDate);
  const end = dayjs(endDate);
  const chunks: { startDate: string; endDate: string }[] = [];

  let currentStart = start;
  while (currentStart.isBefore(end)) {
    let currentEnd = currentStart
      .add(monthsPerChunk, 'month')
      .subtract(1, 'ms');
    if (currentEnd.isAfter(end)) {
      currentEnd = end;
    }
    chunks.push({
      startDate: currentStart.toISOString(),
      endDate: currentEnd.toISOString(),
    });
    currentStart = currentEnd.add(1, 'ms');
    if (currentStart.isSame(end) || currentStart.isAfter(end)) break;
  }
  return chunks;
};

export const isValidDate = (date: string) => {
  return dayjs(date).isValid();
};

export const isDateRangeExceedMonths = (
  startDate: string,
  endDate: string,
  month: number,
) => {
  const start = dayjs(startDate);
  const end = dayjs(endDate);
  return end.diff(start, 'month', true) > month;
};
