import {
  applyDecorators,
  createParamDecorator,
  ExecutionContext,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth } from '@nestjs/swagger';
import { AdminMenuModuleGuard } from '../../admin-authorization/common/guards/admin-menu-module.guard';
import { AccountData } from '../../proto/account.pb';
import { AdminGuard } from '../guards/admin.guard';

export const AuthAdmin = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    return request.user as AccountData;
  },
);

export const UseAdminWithAuthorizeBySetupAdminMenuModuleAcl = () =>
  applyDecorators(UseGuards(AdminGuard, AdminMenuModuleGuard), ApiBearerAuth());

export const UseAdmin = () =>
  applyDecorators(UseGuards(AdminGuard), ApiBearerAuth());
