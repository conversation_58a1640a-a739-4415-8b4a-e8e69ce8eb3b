import { boolean } from 'boolean';
import * as dotenv from 'dotenv';
import { RecursiveKeyOf } from '../types/utils.type';
dotenv.config();

const globalConfig = {
  grpc: {
    userSerivce: {
      url: process.env.GRPC_USER_SERVICE_URL,
    },
    accountService: {
      url: process.env.GRPC_ACCOUNT_SERVICE_URL,
    },
  },

  redisV2: {
    cloud: {
      host: process.env.REDIS_V2_CLOUD_HOST,
      port: process.env.REDIS_V2_CLOUD_PORT,
      password: process.env.REDIS_V2_CLOUD_PASSWORD,
      caPem: process.env.REDIS_V2_CLOUD_CA_PEM
        ? process.env.REDIS_V2_CLOUD_CA_PEM.replace(/\\n/gm, '\n')
        : '',
    },
    sentinels:
      process.env.REDIS_V2_SENTINELS?.split('|')?.map((item) => {
        const [host, port] = item?.split(':') || [];
        return { host, port };
      }) || [],
    standAlone: {
      host: process.env.REDIS_V2_HOST,
      port: process.env.REDIS_V2_PORT,
    },
    password: process.env.REDIS_V2_PASSWORD,
    redisGroupName: 'myMaster',
    usingRedisSentinel: boolean(process.env.REDIS_V2_USING_REDIS_SENTINEL),
    usingRedisCloud: boolean(process.env.REDIS_V2_USING_REDIS_CLOUD),
  },

  export: {
    maxDateRangeMonthAllowed:
      +process.env.EXPORT_MAX_DATE_RANGE_MONTH_ALLOWED || 3,
  },
};

export default () => globalConfig;
export type GlobalConfig = Record<RecursiveKeyOf<typeof globalConfig>, string>;
