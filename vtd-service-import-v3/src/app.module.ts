import {
  MiddlewareConsumer,
  Module,
  NestModule,
  OnModuleInit,
  RequestMethod,
  ValidationPipe,
} from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_PIPE, ModuleRef } from '@nestjs/core';
import { TypeOrmModule } from '@nestjs/typeorm';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import {
  addTransactionalDataSource,
  initializeTransactionalContext,
} from 'typeorm-transactional';
import { dataSource } from '../data-source';
import { AdminActionHistoryModule } from './admin-action-history/admin-action-history.module';
import { AdminAuthorizationModule } from './admin-authorization/admin-authorization.module';
import { AppController } from './app.controller';
import { AuthModule } from './auth/auth.module';
import globalConfig from './common/configs/global.config';
import { TIME_ZONE_HCM } from './common/constants/index.constant';
import { CoreModule } from './core/core.module';
import { EventAddCanModule } from './event-add-cans/event-add-can.module';
import { HealthModule } from './health/health.module';
import { LoggerMiddleware } from './middlewares/logger.middleware';
import { QueueModule } from './queues/queue.module';
import { VitaCodeModule } from './vita-codes/vita-code.module';
import { ExportModule } from './export/export.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [globalConfig],
      cache: true,
    }),
    TypeOrmModule.forRootAsync({
      useFactory: () => ({}),
      dataSourceFactory: async () => {
        initializeTransactionalContext();
        return addTransactionalDataSource(dataSource);
      },
    }),
    CoreModule,
    HealthModule,
    AuthModule,
    AdminAuthorizationModule,
    AdminActionHistoryModule,
    QueueModule,
    VitaCodeModule,
    EventAddCanModule,
    ExportModule,
  ].filter(Boolean),
  providers: [
    { provide: APP_PIPE, useValue: new ValidationPipe({ transform: true }) },
  ],
  controllers: [AppController],
})
export class AppModule implements OnModuleInit, NestModule {
  constructor(private moduleRef: ModuleRef) {}
  onModuleInit() {
    dayjs.extend(utc);
    dayjs.extend(timezone);
    dayjs.tz.setDefault(TIME_ZONE_HCM);
  }
  public configure(consumer: MiddlewareConsumer) {
    consumer.apply(LoggerMiddleware).exclude().forRoutes({
      path: '*',
      method: RequestMethod.ALL,
    });
  }
}
