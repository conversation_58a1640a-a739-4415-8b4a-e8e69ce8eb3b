import {
  CleanupStatusEnum,
  ExportStatusEnum,
  ExportTypeEnum,
} from 'vtd-common-v3';
import { AutoMapDecorator } from '../../../../common/decorators/automap.decorator';
import { BaseMapperDto } from '../../../../common/dtos/base-mapper.dto';

export class ExportRequestAdminResDto extends BaseMapperDto {
  @AutoMapDecorator()
  id: number;

  @AutoMapDecorator()
  exportType: ExportTypeEnum;

  @AutoMapDecorator()
  requesterId: number;

  @AutoMapDecorator()
  filterJson: string | null;

  @AutoMapDecorator()
  sortSpec: string | null;

  @AutoMapDecorator()
  status: ExportStatusEnum;

  @AutoMapDecorator()
  completedAt: Date | null;

  @AutoMapDecorator()
  gcsFinalUri: string | null;

  @AutoMapDecorator()
  cleanupStatus: CleanupStatusEnum | null;

  @AutoMapDecorator()
  cleanupAttempt: number;

  @AutoMapDecorator()
  cleanupNextRunAt: Date | null;

  @AutoMapDecorator()
  cleanupError: string | null;

  @AutoMapDecorator()
  gcsPartsPrefix: string | null;

  @AutoMapDecorator()
  error: string | null;

  @AutoMapDecorator()
  totalJobs: number;

  @AutoMapDecorator()
  createdAt: Date;

  @AutoMapDecorator()
  updatedAt: Date;
}
