import { AutoMapDecorator } from '../../../../common/decorators/automap.decorator';
import { BaseMapperDto } from '../../../../common/dtos/base-mapper.dto';

export class ExportJobAdminResDto extends BaseMapperDto {
  @AutoMapDecorator()
  id: number;

  @AutoMapDecorator()
  exportRequestId: number;

  @AutoMapDecorator()
  jobIndex: number;

  @AutoMapDecorator()
  status: string;

  @AutoMapDecorator()
  attempt: number;

  @AutoMapDecorator()
  maxAttempt: number;

  @AutoMapDecorator()
  startedAt: Date | null;

  @AutoMapDecorator()
  endedAt: Date | null;

  @AutoMapDecorator()
  gcsPartUri: string | null;

  @AutoMapDecorator()
  error: string | null;
}
