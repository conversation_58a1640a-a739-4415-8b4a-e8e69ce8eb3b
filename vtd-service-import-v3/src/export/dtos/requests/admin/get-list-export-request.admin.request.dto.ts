import { ExportStatusEnum, ExportTypeEnum } from 'vtd-common-v3';
import {
  IsValidEnum,
  IsValidNumber,
} from '../../../../common/decorators/custom-validator.decorator';
import { PaginateRequestDto } from '../../../../common/dtos/requests/paginate.request.dto';

export class GetListExportRequestAdminRequestDto extends PaginateRequestDto {
  @IsValidEnum({
    enum: ExportTypeEnum,
    required: false,
  })
  exportType?: ExportTypeEnum;

  @IsValidEnum({
    enum: ExportStatusEnum,
    required: false,
  })
  status?: ExportStatusEnum;

  @IsValidNumber({ required: false })
  requesterId?: number;
}
