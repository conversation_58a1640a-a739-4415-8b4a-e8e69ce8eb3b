import { ExportTypeEnum } from 'vtd-common-v3';
import {
  IsValidEnum,
  IsValidText,
} from '../../../../common/decorators/custom-validator.decorator';

export class RequestExportAdminRequestDto {
  @IsValidEnum({
    enum: ExportTypeEnum,
    required: true,
  })
  exportType: ExportTypeEnum;

  @IsValidText({ required: false })
  filterJson?: string;

  @IsValidText({ required: false })
  sortSpec?: string;
}
