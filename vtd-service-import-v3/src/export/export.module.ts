import { BullModule } from '@nestjs/bullmq';
import { Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AuthModule } from '../auth/auth.module';
import { getBullConnectionOpts } from '../common/configs/bull.config';
import { GlobalConfig } from '../common/configs/global.config';
import { QUEUE_NAMES } from '../queues/common/constants/index.constant';
import { ExportAdminController } from './controllers/admin/export.admin.controller';
import { ExportJobItemRepository } from './repositories/export-job-item.repository';
import { ExportJobRepository } from './repositories/export-job.repository';
import { ExportRequestRepository } from './repositories/export-request.repository';
import { ExportSnapshotItemRepository } from './repositories/export-snapshot-item.repository';
import { ExportAdminService } from './services/admin/export.admin.service';

@Module({
  imports: [
    AuthModule,
    BullModule.registerQueueAsync(
      ...QUEUE_NAMES.map((name) => ({
        name,
        inject: [ConfigService],
        useFactory: (cfg: ConfigService<GlobalConfig>) => ({
          connection: getBullConnectionOpts(cfg),
        }),
      })),
    ),
  ],
  controllers: [ExportAdminController],
  providers: [
    ExportAdminService,
    ExportRequestRepository,
    ExportJobRepository,
    ExportJobItemRepository,
    ExportSnapshotItemRepository,
  ],
  exports: [],
})
export class ExportModule {}
