import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Query,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { Prefix } from '../../../common/constants/index.constant';
import { AuthAdmin, UseAdmin } from '../../../common/decorators/user.decorator';
import {
  AppResponseDto,
  AppResponseWithPaginateDto,
} from '../../../common/dtos/app-response.dto';
import { AccountData } from '../../../proto/account.pb';
import { GetListExportRequestAdminRequestDto } from '../../dtos/requests/admin/get-list-export-request.admin.request.dto';
import { RequestExportAdminRequestDto } from '../../dtos/requests/admin/request-export.admin.request.dto';
import { RetryExportJobAdminRequestDto } from '../../dtos/requests/admin/retry-export-job.admin.request.dto';
import { ExportRequestAdminResDto } from '../../dtos/responses/admin/export-request.admin.response.dto';
import { ExportAdminService } from '../../services/admin/export.admin.service';

@Controller({ version: '1', path: `${Prefix.ADMIN}/exports` })
@ApiTags('Export Admin Controller')
@UseAdmin()
export class ExportAdminController {
  constructor(private readonly exportAdminService: ExportAdminService) {}

  @Post('request')
  async requestExport(
    @Body() dto: RequestExportAdminRequestDto,
    @AuthAdmin() admin: AccountData,
  ) {
    const result = await this.exportAdminService.requestExport(
      dto,
      admin.accountId,
    );
    return new AppResponseDto(
      result.map((item) => new ExportRequestAdminResDto(item)),
    );
  }

  @Get()
  async getList(@Query() dto: GetListExportRequestAdminRequestDto) {
    const { items, meta } = await this.exportAdminService.getList(dto);

    return new AppResponseWithPaginateDto(
      items.map((item) => new ExportRequestAdminResDto(item)),
      meta,
    );
  }

  @Get(':id')
  async getById(@Param('id', ParseIntPipe) id: number) {
    const result = await this.exportAdminService.getById(id);
    return new AppResponseDto(new ExportRequestAdminResDto(result));
  }

  @Post(':id/cancel')
  async cancelExport(
    @Param('id', ParseIntPipe) id: number,
    @AuthAdmin() admin: AccountData,
  ) {
    const result = await this.exportAdminService.cancelExport(
      id,
      admin.accountId,
    );
    return new AppResponseDto(result);
  }

  @Post('retry-job')
  async retryJob(
    @Body() dto: RetryExportJobAdminRequestDto,
    @AuthAdmin() admin: AccountData,
  ) {
    const result = await this.exportAdminService.retryJob(dto);
    return new AppResponseDto(result);
  }
}
