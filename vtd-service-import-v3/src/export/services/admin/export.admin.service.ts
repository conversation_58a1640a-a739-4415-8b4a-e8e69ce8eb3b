import { InjectQueue } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Queue } from 'bullmq';
import { paginate } from 'nestjs-typeorm-paginate';
import { In, SelectQueryBuilder } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { ExportStatusEnum, QueueName } from 'vtd-common-v3';
import { GlobalConfig } from '../../../common/configs/global.config';
import { QueryFilter } from '../../../common/decorators/query-filter.decorator';
import {
  BadRequestExc,
  NotFoundExc,
} from '../../../common/exceptions/custom-http.exception';
import {
  isDateRangeExceedMonths,
  isValidDate,
  splitDateRange,
} from '../../../common/utils/datetime.util';
import { GetListExportRequestAdminRequestDto } from '../../dtos/requests/admin/get-list-export-request.admin.request.dto';
import { RequestExportAdminRequestDto } from '../../dtos/requests/admin/request-export.admin.request.dto';
import { RetryExportJobAdminRequestDto } from '../../dtos/requests/admin/retry-export-job.admin.request.dto';
import { ExportRequest } from '../../entities/export-request.entity';
import { ExportRequestRepository } from '../../repositories/export-request.repository';

@Injectable()
export class ExportAdminService {
  private readonly logger = new Logger(ExportAdminService.name);
  private readonly maxDateRangeMonthAllowed: number;

  constructor(
    @InjectQueue(QueueName.EXPORT_SPLITTER)
    private readonly exportSplitterQueue: Queue,
    private readonly exportRequestRepo: ExportRequestRepository,
    private readonly configService: ConfigService<GlobalConfig>,
  ) {
    this.maxDateRangeMonthAllowed = this.configService.get(
      'export.maxDateRangeMonthAllowed',
    );
  }

  @Transactional()
  async requestExport(dto: RequestExportAdminRequestDto, requesterId: number) {
    const { exportType, filterJson, sortSpec } = dto;

    // Check if there's already a non-terminal export for this type
    // Only allow one export per type with status from PENDING_SPLIT, QUEUED, RUNNING
    const existingExport = await this.exportRequestRepo.findOne({
      where: {
        exportType,
        filterJson: filterJson || null,
        sortSpec: sortSpec || null,
        status: In([
          ExportStatusEnum.PENDING_SPLIT,
          ExportStatusEnum.QUEUED,
          ExportStatusEnum.RUNNING,
        ]),
      },
    });

    if (existingExport) {
      throw new BadRequestExc(
        `An export request for type ${exportType} is already in progress (status: ${existingExport.status})`,
      );
    }

    // Handle splitting logic if filterJson contains startDate and endDate
    if (filterJson) {
      let filterJsonParsed: any;
      try {
        filterJsonParsed = JSON.parse(filterJson);
      } catch (e) {
        throw new BadRequestExc('Invalid filterJson');
      }

      const { startDate, endDate } = filterJsonParsed;
      if (startDate && endDate) {
        if (!isValidDate(startDate) || !isValidDate(endDate)) {
          throw new BadRequestExc('Invalid date range');
        }

        if (
          isDateRangeExceedMonths(
            startDate,
            endDate,
            this.maxDateRangeMonthAllowed,
          )
        ) {
          // Split into maxDateRangeMonthAllowed chunks
          const chunks = splitDateRange(
            startDate,
            endDate,
            this.maxDateRangeMonthAllowed,
          );
          const exportRequests = chunks.map((chunk) => {
            const chunkFilter = {
              ...filterJsonParsed,
              startDate: chunk.startDate,
              endDate: chunk.endDate,
            };
            return this.exportRequestRepo.create({
              exportType,
              requesterId,
              filterJson: JSON.stringify(chunkFilter),
              sortSpec: sortSpec || null,
              status: ExportStatusEnum.PENDING_SPLIT,
            });
          });

          await this.exportRequestRepo.insert(exportRequests);

          this.logger.log(
            `${exportRequests.length} split export requests created for type: ${exportType}`,
          );

          // Add job to queue
          exportRequests.forEach((exportRequest) => {
            this.exportSplitterQueue.add(
              `${QueueName.EXPORT_SPLITTER}_${exportRequest.id}`,
              {
                exportRequestId: exportRequest.id,
              },
            );
          });

          return exportRequests;
        }
      }
    }

    // Create export request if no splitting date range
    const exportRequest = this.exportRequestRepo.create({
      exportType,
      requesterId,
      filterJson: filterJson || null,
      sortSpec: sortSpec || null,
      status: ExportStatusEnum.PENDING_SPLIT,
    });

    await this.exportRequestRepo.insert(exportRequest);

    this.exportSplitterQueue.add(
      `${QueueName.EXPORT_SPLITTER}_${exportRequest.id}`,
      {
        exportRequestId: exportRequest.id,
      },
    );

    this.logger.log(
      `Export request created: ${exportRequest.id} for type: ${exportType}`,
    );

    return [exportRequest];
  }

  async getList(dto: GetListExportRequestAdminRequestDto) {
    const { page, limit } = dto;

    const queryBuilder =
      this.exportRequestRepo.createQueryBuilder('exportRequest');

    // Apply filters via reusable pipeline in BaseRepository
    await this.exportRequestRepo.applyAllFilters(queryBuilder, dto, this);

    const { items, meta } = await paginate(queryBuilder, {
      limit,
      page,
    });

    return {
      items,
      meta,
    };
  }

  async getById(id: number) {
    const exportRequest = await this.exportRequestRepo.findOne({
      where: { id },
      relations: ['exportJobs'],
    });

    if (!exportRequest) {
      throw new NotFoundExc('Export request not found');
    }

    return exportRequest;
  }

  @Transactional()
  async cancelExport(id: number, requesterId: number) {
    // TODO: implement cancel export

    return 'ok';
  }

  @Transactional()
  async retryJob(dto: RetryExportJobAdminRequestDto) {
    // TODO: implement retry job

    return 'ok';
  }

  /**
   * Applies export type filter
   */
  @QueryFilter({ order: 10 })
  private applyExportTypeFilter(
    queryBuilder: SelectQueryBuilder<ExportRequest>,
    dto: GetListExportRequestAdminRequestDto,
  ): void {
    if (dto.exportType) {
      queryBuilder.andWhere('exportRequest.exportType = :exportType', {
        exportType: dto.exportType,
      });
    }
  }

  /**
   * Applies export type filter
   */
  @QueryFilter({ order: 10 })
  private applyStatusFilter(
    queryBuilder: SelectQueryBuilder<ExportRequest>,
    dto: GetListExportRequestAdminRequestDto,
  ): void {
    if (dto.status) {
      queryBuilder.andWhere('exportRequest.status = :status', {
        status: dto.status,
      });
    }
  }

  /**
   * Applies requester id filter
   */
  @QueryFilter({ order: 10 })
  private applyRequesterIdFilter(
    queryBuilder: SelectQueryBuilder<ExportRequest>,
    dto: GetListExportRequestAdminRequestDto,
  ): void {
    if (dto.requesterId) {
      queryBuilder.andWhere('exportRequest.requesterId = :requesterId', {
        requesterId: dto.requesterId,
      });
    }
  }
}
