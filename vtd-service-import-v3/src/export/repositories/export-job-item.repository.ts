import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { ExportJobItem } from '../entities/export-job-item.entity';

@Injectable()
export class ExportJobItemRepository extends BaseRepository<ExportJobItem> {
  constructor(dataSource: DataSource) {
    super(ExportJobItem, dataSource);
  }
}
