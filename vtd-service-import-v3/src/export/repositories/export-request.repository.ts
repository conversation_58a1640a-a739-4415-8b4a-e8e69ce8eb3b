import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { ExportRequest } from '../entities/export-request.entity';

@Injectable()
export class ExportRequestRepository extends BaseRepository<ExportRequest> {
  constructor(dataSource: DataSource) {
    super(ExportRequest, dataSource);
  }
}
