import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyTo<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { BaseEntityWithoutDeletedAtWithoutVersion } from '../../common/entities/base.entity';
import { ExportRequest } from './export-request.entity';

@Entity('export_snapshot_items')
export class ExportSnapshotItem extends BaseEntityWithoutDeletedAtWithoutVersion {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number;

  @Column({ name: 'export_request_id', type: 'bigint' })
  exportRequestId: number;

  @Column({ name: 'record_id', type: 'bigint' })
  recordId: number;

  @ManyToOne(() => ExportRequest, (request) => request.snapshotItems)
  @JoinColumn({ name: 'export_request_id' })
  exportRequest: ExportRequest;
}
