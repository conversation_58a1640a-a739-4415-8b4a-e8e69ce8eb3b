import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { ExportJobStatusEnum } from 'vtd-common-v3';
import { BaseEntityWithoutDeletedAtWithoutVersion } from '../../common/entities/base.entity';
import { ExportJobItem } from './export-job-item.entity';
import { ExportRequest } from './export-request.entity';

@Entity('export_jobs')
export class ExportJob extends BaseEntityWithoutDeletedAtWithoutVersion {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number;

  @Column({ name: 'export_request_id', type: 'bigint' })
  exportRequestId: number;

  @Column({ name: 'job_index', type: 'int' })
  jobIndex: number;

  @Column({ name: 'status', type: 'varchar', length: 50 })
  status: ExportJobStatusEnum;

  @Column({ name: 'attempt', type: 'int', default: 0 })
  attempt: number;

  @Column({ name: 'max_attempt', type: 'int', default: 3 })
  maxAttempt: number;

  @Column({ name: 'started_at', type: 'timestamptz', nullable: true })
  startedAt: Date | null;

  @Column({ name: 'ended_at', type: 'timestamptz', nullable: true })
  endedAt: Date | null;

  @Column({ name: 'gcs_part_uri', type: 'text', nullable: true })
  gcsPartUri: string | null;

  @Column({ name: 'error', type: 'text', nullable: true })
  error: string | null;

  @ManyToOne(() => ExportRequest, (request) => request.exportJobs, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'export_request_id' })
  exportRequest: ExportRequest;

  @OneToMany(() => ExportJobItem, (item) => item.exportJob)
  jobItems: ExportJobItem[];
}
