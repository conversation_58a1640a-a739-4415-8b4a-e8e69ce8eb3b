import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyTo<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { BaseEntityWithoutDeletedAtWithoutVersion } from '../../common/entities/base.entity';
import { ExportJob } from './export-job.entity';

@Entity('export_job_items')
export class ExportJobItem extends BaseEntityWithoutDeletedAtWithoutVersion {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number;

  @Column({ name: 'export_job_id', type: 'bigint' })
  exportJobId: number;

  @Column({ name: 'record_id', type: 'bigint' })
  recordId: number;

  @ManyToOne(() => ExportJob, (job) => job.jobItems)
  @JoinColumn({ name: 'export_job_id' })
  exportJob: ExportJob;
}
