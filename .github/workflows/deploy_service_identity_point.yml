name: Deploy vtd-service-identity-point-v3

on:
  push:
    branches: [ "main" ]
    paths:
      - "vtd-service-identity-point-v3/**"
  workflow_dispatch: {}

concurrency: 
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  checking-deploy-k8s-change:
    if: github.ref != 'refs/heads/v.*.*.*'
    name: Check Deploy Staging config change to GKE
    runs-on: ubuntu-latest

    steps:
      - name: Checkout 
        uses: actions/checkout@v3
        with:
          fetch-depth: 2

      - name: Get specific changed files 
        uses: tj-actions/changed-files@v27
        id: check-k8s-changes
        with:
          files: |
            infra/k8s/**
          sha: ${{ github.sha }}

      - name: Wait for deploy infra to succeed
        if: steps.check-k8s-changes.outputs.any_changed == 'true'
        uses: fountainhead/action-wait-for-check@v1.0.0
        id: wait-for-deploy-infra
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          checkName: Deploy Infra Staging
          ref: ${{ github.event.pull_request.head.sha || github.sha }}
          timeoutSeconds: 100

      - if: steps.check-k8s-changes.outputs.any_changed == 'true' && steps.wait-for-deploy-infra.outputs.conclusion == 'failure'
        name: Checking the deploy status
        uses: actions/github-script@v3
        with:
          script: |
              core.setFailed('Deploy to env infra fail, we will not deploy service cause service need env to run')

  build-and-deploy-to-dev:
    needs: checking-deploy-k8s-change
    if: ${{ github.ref == 'refs/heads/main' }}
    name: Deploy Dev to GKE
    runs-on: ubuntu-latest
    env:
      IMAGE_NAME: vtd-service-identity-point-v3
      GCP_PROJECT_ID: spartan-impact-319504
      GCP_REGISTRY_HOST: asia.gcr.io
      GKE_CLUSTER: vitadairy-k8s-dev
      GKE_ZONE: asia-southeast1-b
      DEPLOYMENT_NAME: vtd-service-identity-point-v3
      GAR_NAME: vtd-dev
      GAR_HOST: asia-southeast1-docker.pkg.dev
      GIT_USER_NAME: ${{ secrets.GIT_USER_NAME }}
      GIT_PAT: ${{ secrets.GIT_PAT }}

    steps:
      - name: Checkout 
        uses: actions/checkout@v3

      - name: Authen GCP
        uses: 'google-github-actions/auth@v0.4.0'
        with:
          credentials_json: '${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}'

      - name: Setup GCP
        uses: google-github-actions/setup-gcloud@v0.6.0
      
      - name: Get GKE credentials
        run: |-
          gcloud components install gke-gcloud-auth-plugin
          export USE_GKE_GCLOUD_AUTH_PLUGIN=True
          gcloud container clusters get-credentials $GKE_CLUSTER --zone=$GKE_ZONE

      - name: Configure Docker Client
        run: |-
          gcloud auth configure-docker --quiet
          gcloud auth configure-docker $GAR_HOST --quiet

      - name: Build Docker
        run: cd vtd-service-identity-point-v3 && docker build -t $IMAGE_NAME:latest --build-arg GIT_USER_NAME=$GIT_USER_NAME --build-arg GIT_PAT=$GIT_PAT .

      - name: Push Docker Image to Artifact Registry
        run: |-
          docker tag $IMAGE_NAME:latest $GAR_HOST/$GCP_PROJECT_ID/$GAR_NAME/$IMAGE_NAME:latest
          docker push $GAR_HOST/$GCP_PROJECT_ID/$GAR_NAME/$IMAGE_NAME:latest

      # - name: Deploy to GKE
      #   run: |-
      #     kubectl apply -f infra/dev/k8s/$DEPLOYMENT_NAME


      - name: Deploy to GKE
        run: |-
          kubectl rollout restart deployment $DEPLOYMENT_NAME

  # build-and-deploy-to-staging:
  #   needs: checking-deploy-k8s-change
  #   if: ${{ github.ref != 'refs/heads/main' }}
  #   name: Deploy Staging to GKE
  #   runs-on: ubuntu-latest
  #   env:
  #     IMAGE_NAME: vtd-service-identity-point-v3
  #     GCP_PROJECT_ID: spartan-impact-319504
  #     GCP_REGISTRY_HOST: asia.gcr.io
  #     GKE_CLUSTER: vitadairy-k8s-stage
  #     GKE_ZONE: asia-southeast1-b
  #     DEPLOYMENT_NAME: vtd-service-identity-point-v3
  #     GAR_NAME: vtd-stage
  #     GAR_HOST: asia-southeast1-docker.pkg.dev
  #     GIT_USER_NAME: ${{ secrets.GIT_USER_NAME }}
  #     GIT_PAT: ${{ secrets.GIT_PAT }}

  #   steps:
  #     - name: Checkout 
  #       uses: actions/checkout@v3

  #     - name: Authen GCP
  #       uses: 'google-github-actions/auth@v0.4.0'
  #       with:
  #         credentials_json: '${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}'

  #     - name: Setup GCP
  #       uses: google-github-actions/setup-gcloud@v0.6.0
      
  #     - name: Get GKE credentials
  #       run: |-
  #         gcloud components install gke-gcloud-auth-plugin
  #         export USE_GKE_GCLOUD_AUTH_PLUGIN=True
  #         gcloud container clusters get-credentials $GKE_CLUSTER --zone=$GKE_ZONE

  #     - name: Configure Docker Client
  #       run: |-
  #         gcloud auth configure-docker --quiet
  #         gcloud auth configure-docker $GAR_HOST --quiet

  #     - name: Build Docker
  #       run: cd vtd-service-identity-point-v3 && docker build -t $IMAGE_NAME:latest --build-arg GIT_USER_NAME=$GIT_USER_NAME --build-arg GIT_PAT=$GIT_PAT .

  #     - name: Push Docker Image to Artifact Registry
  #       run: |-
  #         docker tag $IMAGE_NAME:latest $GAR_HOST/$GCP_PROJECT_ID/$GAR_NAME/$IMAGE_NAME:latest
  #         docker push $GAR_HOST/$GCP_PROJECT_ID/$GAR_NAME/$IMAGE_NAME:latest
  #     # - name: Deploy to GKE
  #     #   run: |-
  #     #     kubectl rollout restart deployment $DEPLOYMENT_NAME
  #     - name: Deploy to GKE
  #       run: |-
  #         kubectl apply -f infra/dev/k8s/$DEPLOYMENT_NAME
