{"name": "vtd-service-noti-v3", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "dev": "nest start --watch", "start": "nest start", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "proto:user": "protoc --plugin=node_modules/.bin/protoc-gen-ts_proto -I=./node_modules/vtd-common-v3/proto --ts_proto_out=src/proto/ node_modules/vtd-common-v3/proto/user.proto --ts_proto_opt=nestJs=true --ts_proto_opt=fileSuffix=.pb", "cron-job": "node dist/src/cron-job/execute-command.js"}, "dependencies": {"newrelic": "^11.19.0", "@fastify/static": "6.5.1", "@grpc/grpc-js": "1.7.3", "@liaoliaots/nestjs-redis": "9.0.5", "@nestjs/axios": "1.0.0", "@nestjs/common": "9.2.1", "@nestjs/config": "2.2.0", "@nestjs/core": "9.2.1", "@nestjs/microservices": "9.2.1", "@nestjs/platform-fastify": "9.2.1", "@nestjs/swagger": "6.1.3", "@nestjs/terminus": "9.2.2", "@nestjs/throttler": "^5.1.1", "@nestjs/typeorm": "9.0.1", "awesome-phonenumber": "5.10.0", "boolean": "3.2.0", "class-transformer": "0.5.1", "class-validator": "0.13.2", "clone": "2.1.2", "commander": "^13.1.0", "dayjs": "1.11.7", "exceljs": "^4.4.0", "form-data": "^4.0.1", "ioredis": "5.3.1", "joi": "17.11.0", "lodash": "^4.17.21", "moment": "^2.30.1", "moment-timezone": "^0.5.47", "nestjs-typeorm-paginate": "4.0.3", "pg": "8.8.0", "radash": "^11.0.0", "rxjs": "7.2.0", "typeorm": "0.3.10", "typeorm-transactional": "0.4.1", "utility": "git+https://github.com/bilisoftware/nestjs-utility.git#852f52e", "vtd-common-v3": "git+https://github.com/bilisoftware/vtd-common-v3.git#e2a6bb5"}, "devDependencies": {"@nestjs/cli": "9.1.5", "@nestjs/testing": "9.2.1", "@types/clone": "2.1.1", "@types/express": "4.17.13", "@types/node": "20.3.2", "@types/pg": "8.6.6", "@types/supertest": "2.0.11", "@types/validator": "13.11.7", "@typescript-eslint/eslint-plugin": "5.0.0", "@typescript-eslint/parser": "5.0.0", "eslint": "8.0.1", "eslint-config-prettier": "8.3.0", "eslint-plugin-prettier": "4.0.0", "jest": "27.2.5", "prettier": "2.3.2", "supertest": "6.1.3", "ts-jest": "27.0.3", "ts-node": "10.0.0", "typescript": "^5.8.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}