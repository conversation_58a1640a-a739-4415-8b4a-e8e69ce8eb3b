import { Module, OnModuleInit } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import dayjs from 'dayjs';
import vi from 'dayjs/locale/vi';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import duration from 'dayjs/plugin/duration';
import localeData from 'dayjs/plugin/localeData';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import weekday from 'dayjs/plugin/weekday';
import {
  addTransactionalDataSource,
  initializeTransactionalContext,
} from 'typeorm-transactional';
import { dataSource } from '../data-source';
import { AppController } from './app.controller';
import { AuthModule } from './auth/auth.module';
import {
  appConfig,
  appConfigValidationSchema,
} from './common/configs/app.config';
import { TIME_ZONE_HCM } from './common/constants/app.constant';
import { ResetPointModule } from './ddx_reset_gift_point/reset_point.module';
import { SyncEventPointHistoryModule } from './sync-event-point-history/sync-event-point-history.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [() => appConfig],
      cache: true,
      validationSchema: appConfigValidationSchema,
    }),
    TypeOrmModule.forRootAsync({
      useFactory: () => ({}),
      dataSourceFactory: async () => {
        initializeTransactionalContext();
        return addTransactionalDataSource(dataSource);
      },
    }),
    AuthModule,
    ResetPointModule,
    SyncEventPointHistoryModule,
  ],
  providers: [],
  controllers: [AppController],
})
export class AppModule implements OnModuleInit {
  constructor() {}

  onModuleInit() {
    dayjs.extend(utc);
    dayjs.extend(timezone);
    dayjs.tz.setDefault(TIME_ZONE_HCM);
    dayjs.extend(weekday);
    dayjs.extend(localeData);
    dayjs.locale(vi);
    dayjs.extend(customParseFormat);
    dayjs.extend(duration);
  }
}
