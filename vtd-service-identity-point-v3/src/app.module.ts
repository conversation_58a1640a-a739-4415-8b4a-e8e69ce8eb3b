import { Mo<PERSON><PERSON>, OnModuleInit, ValidationPipe } from '@nestjs/common';
import { RedisModule } from '@liaoliaots/nestjs-redis';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { APP_FILTER, APP_INTERCEPTOR, APP_PIPE } from '@nestjs/core';
import dayjs from 'dayjs';
import vi from 'dayjs/locale/vi';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import duration from 'dayjs/plugin/duration';
import localeData from 'dayjs/plugin/localeData';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import weekday from 'dayjs/plugin/weekday';
import {
  addTransactionalDataSource,
  initializeTransactionalContext,
} from 'typeorm-transactional';
import { dataSource } from '../data-source';
import { AppController } from './app.controller';
import { AuthModule } from './auth/auth.module';
import {
  appConfig,
  appConfigValidationSchema,
} from './common/configs/app.config';
import { AppFilter } from './common/filters/app.filter';
import { redisConfig } from './common/configs/redis.config';
import { TIME_ZONE_HCM } from './common/constants/app.constant';
import { ResetPointModule } from './ddx_reset_gift_point/reset_point.module';
import { SyncEventPointHistoryModule } from './sync-event-point-history/sync-event-point-history.module';
import { HealthModule } from './health/health.module';
import { RedisCacheInterceptor } from './redis-cache/common/interceptors/redis-cache.interceptor';
import { RedisCacheModule } from './redis-cache/redis-cache.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [() => appConfig],
      cache: true,
      validationSchema: appConfigValidationSchema,
    }),
    TypeOrmModule.forRootAsync({
      useFactory: () => ({}),
      dataSourceFactory: async () => {
        initializeTransactionalContext();
        return addTransactionalDataSource(dataSource);
      },
    }),
    AuthModule,
    ResetPointModule,
    SyncEventPointHistoryModule,
    HealthModule,
    RedisModule.forRootAsync(redisConfig),
    RedisCacheModule,
  ],
  providers: [
    { provide: APP_PIPE, useValue: new ValidationPipe({ transform: true }) },
    { provide: APP_FILTER, useValue: new AppFilter() },
    // {
    //   provide: APP_INTERCEPTOR,
    //   useClass: RedisCacheInterceptor,
    // },
  ],
  controllers: [AppController],
})
export class AppModule implements OnModuleInit {
  constructor() {}

  onModuleInit() {
    dayjs.extend(utc);
    dayjs.extend(timezone);
    dayjs.tz.setDefault(TIME_ZONE_HCM);
    dayjs.extend(weekday);
    dayjs.extend(localeData);
    dayjs.locale(vi);
    dayjs.extend(customParseFormat);
    dayjs.extend(duration);
  }
}
