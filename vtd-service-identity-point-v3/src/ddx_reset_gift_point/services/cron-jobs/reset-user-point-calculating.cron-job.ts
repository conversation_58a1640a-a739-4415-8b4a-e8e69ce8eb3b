import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { In } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { performance } from 'perf_hooks';
import { group, objectify, listify } from 'radash';
import { BaseCronJob } from './base.cron-job';
import {
  TIME_FORMAT_ONLY_DATE,
  TIME_FORMAT_ONLY_MONTH,
} from '../../../common/constants/app.constant';
import { LoggerService } from '../../../core';
import {
  convertErrorToStringifyAbleObject,
  safeJsonStringify,
  sortByAttribute,
  removeDuplicateInArray,
} from '../../../common/utils';
import { formatToString } from '../../../common/datetime.util';
import {
  calculateTimeToResetUserPoint,
  formatValueOfPointResetOfUser,
} from '../../utils/reset-user-point.util';
import { AppConfig } from '../../../common/configs/app.config';
import { CronJobCodeEnum } from '../../enums/cron-job.enum';
import { CronJobResetUserPointStatusEnum } from '../../enums/reset-point.enum';
import {
  UpdateCronJobResetUserPointInterface,
  DataOfCronJobResetUserPointInterface,
  CronJobResetUserPointDataInterface,
  CronJobResetUserPointInterface,
} from '../../interfaces/reset-user-point.interface';
import {
  UpdateDdxTrackingUserResetPointInterface,
  TrackingUserResetPointDataInterface,
  TrackingUserResetPointInterface,
  CombineTrackingResetPointOfUser,
} from '../../interfaces/tracking-user-reset-point.interface';
import { IdentityType } from '../../constant/index.enum';
import { CommonIdentityService } from '../common.service';
import { DDXCronjobManagement } from '../../entities/ddx_cronjob_management.entity';
import { DdxTrackingUserResetPointRepository } from '../../repositories/ddx-tracking-user-reset-point.repository';
import { DDXCronjobResetUserGiftPointRepository } from '../../repositories/ddx_cronjob_reset_user_gift_point.repository';
import { DDXTriggerCalculateUserResetGiftPointRepository } from '../../repositories/ddx_trigger_calculate_user_reset_gift_point.repository';
import { DDXTriggerCalculateUserResetGiftPoint } from '../../entities/ddx_trigger_calculate_user_reset_gift_point.entity';
import { DDXCronjobResetUserGiftPoint } from '../../entities/ddx_cronjob_reset_user_gift_point.entity';
import { DDXCronjobResetUserGiftPointDetailRepository } from '../../repositories/ddx-cronjob-reset-user-gift-point-detail.repository';
import { DdxTrackingUserResetPointEntity } from '../../entities/ddx-tracking-user-reset-point.entity';
import { DDXReportUserResetPointByMonthRepository } from '../../repositories/ddx-report-user-reset-point-by-month.repository';
import {
  StatusUpdateDdxTrackingUserResetPointEnum,
  TrackingUserResetPointStatusEnum,
} from '../../enums/tracking-user-reset-point.enum';

@Injectable()
export class ResetUserPointCalculatingCronJob extends BaseCronJob {
  constructor(
    private readonly configSer: ConfigService<AppConfig>,
    private readonly commonIdentityService: CommonIdentityService,
    private readonly dDXTriggerCalculateUserResetGiftPointRepo: DDXTriggerCalculateUserResetGiftPointRepository,
    private readonly ddxTrackingUserResetPointRepo: DdxTrackingUserResetPointRepository,
    private readonly dDXCronjobResetUserGiftPointRepo: DDXCronjobResetUserGiftPointRepository,
    private readonly dDXCronjobResetUserGiftPointDetailRepo: DDXCronjobResetUserGiftPointDetailRepository,
    private readonly dDXReportUserResetPointByMonthRepo: DDXReportUserResetPointByMonthRepository,
  ) {
    super();
    this._logger = new LoggerService(ResetUserPointCalculatingCronJob.name);
  }

  async run(): Promise<void> {
    let cronJob: DDXCronjobManagement = null;
    let data: DDXTriggerCalculateUserResetGiftPoint[] = [];
    let allProcessed = true;

    try {
      const startTime = performance.now();

      cronJob = await this.commonIdentityService.getCronJob(
        CronJobCodeEnum.RESET_USER_POINT_CALCULATING,
      );
      if (!cronJob) {
        throw new Error('Stop process. Cron job null');
      }

      if (cronJob.isEnabled) {
        await this.calculatingResetUserPoint(data);

        await this.commonIdentityService.loggingCronJobRunSuccess(
          cronJob,
          data,
        );
      }

      const endTime = performance.now();

      this.debugTimeRun('ResetUserPointCalculatingCronJob', startTime, endTime);
    } catch (err) {
      const errObject = convertErrorToStringifyAbleObject(err);
      if (cronJob && cronJob.isEnabled) {
        await this.commonIdentityService.loggingCronJobRunFail(
          cronJob,
          data,
          errObject,
        );
      }
      this._logger.error('Process failed: ', errObject);
      allProcessed = false;
    }

    if (data.length) {
      const userIdsNeedUpdateResetPointByMonth = data.map((item) =>
        Number(item.customerId),
      );
      const uniqueUserIdsNeedUpdateResetPointByMonth = removeDuplicateInArray(
        userIdsNeedUpdateResetPointByMonth,
      );
      try {
        const startTime = performance.now();

        await this.updateUsersResetPointByMonth(
          uniqueUserIdsNeedUpdateResetPointByMonth,
        );

        const endTime = performance.now();

        this.debugTimeRun('updateUsersResetPointByMonth', startTime, endTime);
      } catch (err) {
        const errObject = convertErrorToStringifyAbleObject(err);
        this._logger.error('updateUsersResetPointByMonth failed: ', errObject);
      }
    }
  }

  private async calculatingResetUserPoint(
    dataGot: DDXTriggerCalculateUserResetGiftPoint[],
  ): Promise<void> {
    const numBatchs = this.configSer.get<number>(
      'cronjob.resetUserPointCalculating.numberBatchsInOneRunTime',
    );
    const limitInOneBatch = this.configSer.get<number>(
      'cronjob.resetUserPointCalculating.limitInOneBatch',
    );
    const startTime = performance.now();

    for (let batchCount = 0; batchCount < numBatchs; batchCount++) {
      const dataProcess =
        await this.dDXTriggerCalculateUserResetGiftPointRepo.find({
          where: {
            processed: false,
            // customerId: In(['137797']),
          },
          order: {
            transactionTime: 'ASC',
          },
          take: limitInOneBatch,
          // select: ['id', 'customerId', 'transactionTime'],
        });
      if (!dataProcess || !dataProcess.length) {
        break;
      }

      await this.processCalculateResetUserPoint(dataProcess);

      dataProcess.forEach((item) => {
        dataGot.push(item);
      });
    }

    const endTime = performance.now();

    this.debugTimeRun('calculatingResetUserPoint', startTime, endTime);
  }

  @Transactional()
  private async processCalculateResetUserPoint(
    data: DDXTriggerCalculateUserResetGiftPoint[],
  ): Promise<void> {
    const startTime = performance.now();

    const customerIds: number[] = data.map((item) => Number(item.customerId));
    const trackingUserResetPointGet =
      // await this.ddxTrackingUserResetPointRepo.find({
      //   where: {
      //     userId: In(customerIds),
      //   },
      //   order: {
      //     cronjobResetUserGiftPointId: 'ASC',
      //   },
      // });
      await this.ddxTrackingUserResetPointRepo
        .createQueryBuilder('tracking')
        .innerJoin(
          'tracking.cronjobResetUserGiftPoint',
          'cronjobResetUserGiftPoint',
          'processed = :processed',
          {
            processed: false,
          },
        )
        .where('user_id IN (:...userIds)', {
          userIds: customerIds,
        })
        // .andWhere('reset_point > 0')
        .orderBy('cronjobResetUserGiftPoint.time_run', 'ASC')
        .getMany();
    const cronJobInTrackingIds = trackingUserResetPointGet.map(
      (item) => item.cronjobResetUserGiftPointId,
    );
    // console.log(cronJobInTrackingIds);
    const groupTrackingByUserId = group(
      trackingUserResetPointGet,
      (f) => f.userId,
    );
    // const updateTrackingUserResetPoint: UpdateDdxTrackingUserResetPointInterface[] =
    //   [];
    const dataUpdateCronJobResetUserPoint: Record<
      string,
      UpdateCronJobResetUserPointInterface
    > = {};
    const dDXTriggerCalculateUserResetGiftPointIdsUpdate: number[] = [];
    const timeRunsSearch: Date[] = [];
    let promisesUpdate: any[] = [];
    let resultsUpdated: any[] = [];
    let updateCronJobResetUserPoint: CronJobResetUserPointInterface[] = [];

    for (const item of data) {
      let initPoint = 0;

      dDXTriggerCalculateUserResetGiftPointIdsUpdate.push(item.id);
      const timeRunObj = calculateTimeToResetUserPoint(item.transactionTime);
      const timeToReset = formatToString(timeRunObj);
      if (!(timeToReset in dataUpdateCronJobResetUserPoint)) {
        timeRunsSearch.push(timeRunObj);
        dataUpdateCronJobResetUserPoint[timeToReset] = {
          users: {},
          timeRunObject: timeRunObj,
        };
      }
      const userId = Number(item.customerId);
      if (!(userId in dataUpdateCronJobResetUserPoint[timeToReset].users)) {
        dataUpdateCronJobResetUserPoint[timeToReset].users[userId] = {
          add: initPoint,
          spend: initPoint,
        };
      }
      switch (item.type) {
        case IdentityType.ADD_POINT:
          dataUpdateCronJobResetUserPoint[timeToReset].users[userId].add +=
            item.numberPoint;
          break;
        case IdentityType.SPEND_POINT:
          dataUpdateCronJobResetUserPoint[timeToReset].users[userId].spend +=
            item.numberPoint;
          break;
      }
    }
    // console.log(dataUpdateCronJobResetUserPoint);
    const cronJobsExistedGet =
      await this.dDXCronjobResetUserGiftPointRepo.findBy({
        timeRun: In(timeRunsSearch),
        processed: false,
      });
    const mapCronJobsExistedGetByTimeRun = objectify(cronJobsExistedGet, (f) =>
      formatToString(f.timeRun),
    );
    this.updateDDXTriggerCalculateUserResetGiftPointProcessed(
      dDXTriggerCalculateUserResetGiftPointIdsUpdate,
      promisesUpdate,
    );
    this.updateDataResetUserPointCronjob(
      dataUpdateCronJobResetUserPoint,
      mapCronJobsExistedGetByTimeRun,
      groupTrackingByUserId,
      updateCronJobResetUserPoint,
      promisesUpdate,
    );

    resultsUpdated = await Promise.all(promisesUpdate);

    this.validateUpdateDDXTriggerCalculateUserResetGiftPointProcessed(
      dDXTriggerCalculateUserResetGiftPointIdsUpdate,
      resultsUpdated,
    );
    const cronJobsInsertAndUpdate: DDXCronjobResetUserGiftPoint[] = [];
    await this.validateUpdateDataResetUserPointCronjob(
      dataUpdateCronJobResetUserPoint,
      updateCronJobResetUserPoint,
      cronJobsInsertAndUpdate,
      cronJobInTrackingIds,
      resultsUpdated,
    );
    // console.log('cronJobsInsertAndUpdate');
    // console.log(cronJobsInsertAndUpdate);

    promisesUpdate = [];
    updateCronJobResetUserPoint = [];
    let updateTrackingUserResetPoint: TrackingUserResetPointInterface[] = [];
    const mapCronJobsInsertAndUpdateById = objectify(
      cronJobsInsertAndUpdate,
      (f) => f.id,
    );
    updateTrackingUserResetPoint = this.updateDdxTrackingUserResetPoint(
      dataUpdateCronJobResetUserPoint,
      groupTrackingByUserId,
      mapCronJobsInsertAndUpdateById,
      updateCronJobResetUserPoint,
      updateTrackingUserResetPoint,
      promisesUpdate,
    );
    resultsUpdated = await Promise.all(promisesUpdate);
    this.validateUpdateDdxTrackingUserResetPoint(
      updateCronJobResetUserPoint,
      updateTrackingUserResetPoint,
      resultsUpdated,
    );

    await this.validateDataAfterProcessedByCronJob(
      updateCronJobResetUserPoint,
      updateTrackingUserResetPoint,
    );

    // throw new Error('Test');

    const endTime = performance.now();

    this.debugTimeRun('processCalculateResetUserPoint', startTime, endTime);
  }

  private updateDDXTriggerCalculateUserResetGiftPointProcessed(
    idsUpdate: any[],
    promisesUpdate: any[],
  ): void {
    if (!idsUpdate || !idsUpdate.length) {
      throw new Error(
        'updateDDXTriggerCalculateUserResetGiftPointProcessed failed. Ids empty.',
      );
    }

    promisesUpdate.push(
      this.dDXTriggerCalculateUserResetGiftPointRepo.update(
        {
          id: In(idsUpdate),
        },
        {
          processed: true,
        },
      ),
    );
  }

  private validateUpdateDDXTriggerCalculateUserResetGiftPointProcessed(
    idsUpdate: any[],
    resultsUpdated: any[],
  ): void {
    const resultUpdated = resultsUpdated.shift();
    if (
      !idsUpdate ||
      !idsUpdate.length ||
      !resultUpdated ||
      !resultUpdated.affected
    ) {
      throw new Error(
        'updateDDXTriggerCalculateUserResetGiftPointProcessed failed. Update failed.',
      );
    }
    if (idsUpdate.length != resultUpdated.affected) {
      throw new Error(
        'updateDDXTriggerCalculateUserResetGiftPointProcessed failed. Update failed.',
      );
    }
  }

  private updateDdxTrackingUserResetPoint(
    dataUpdateCronJobResetUserPoint: Record<
      string,
      UpdateCronJobResetUserPointInterface
    >,
    groupTrackingByUserId: Record<number, DdxTrackingUserResetPointEntity[]>,
    mapCronJobsInsertAndUpdateById: Record<
      number,
      DDXCronjobResetUserGiftPoint
    >,
    updateCronJobResetUserPoint: CronJobResetUserPointInterface[],
    updateTrackingUserResetPoint: TrackingUserResetPointInterface[],
    promisesUpdate: any[],
  ): TrackingUserResetPointInterface[] {
    if (!dataUpdateCronJobResetUserPoint) {
      throw new Error('updateDdxTrackingUserResetPoint failed. Data empty.');
    }

    // console.log('dataUpdateCronJobResetUserPoint');
    // console.log(dataUpdateCronJobResetUserPoint);
    // console.log('groupTrackingByUserId');
    // console.log(groupTrackingByUserId);
    // console.log('mapCronJobsInsertAndUpdateById');
    // console.log(mapCronJobsInsertAndUpdateById);
    // console.log('updateCronJobResetUserPoint');
    // console.log(updateCronJobResetUserPoint);

    const dataTrackingsInsert: DdxTrackingUserResetPointEntity[] = [];
    const updateTrackingIds: number[] = [];
    const deleteTrackingIds: number[] = [];
    let sqlUpdateTracking = '';
    const updateCronJobIds: number[] = [];
    let sqlUpdateCronJob = '';

    // First: process tracking add
    for (const timeRun in dataUpdateCronJobResetUserPoint) {
      if (dataUpdateCronJobResetUserPoint.hasOwnProperty(timeRun)) {
        const dataUpdateCronJobResetUserPointDetail =
          dataUpdateCronJobResetUserPoint[timeRun];
        if (
          !(
            dataUpdateCronJobResetUserPointDetail.id in
            mapCronJobsInsertAndUpdateById
          )
        ) {
          throw new Error(
            'updateDdxTrackingUserResetPoint failed. Cron job process add not found.',
          );
        }
        const cronJobExists =
          mapCronJobsInsertAndUpdateById[
            dataUpdateCronJobResetUserPointDetail.id
          ];
        const dataRun = cronJobExists.dataRun;
        const mapDataRunCronJobByUserId = objectify(
          dataRun,
          (f) => f.user_id,
          (f) => f.point,
        );
        for (const userId in dataUpdateCronJobResetUserPointDetail.users) {
          if (
            dataUpdateCronJobResetUserPointDetail.users.hasOwnProperty(userId)
          ) {
            const dataUpdateCronJobResetUserPointDetailOfUser =
              dataUpdateCronJobResetUserPointDetail.users[userId];
            if (!(userId in mapDataRunCronJobByUserId)) {
              throw new Error(
                'updateDdxTrackingUserResetPoint failed. User not found in data cron job.',
              );
            }
            const userPointResetInCronJob = mapDataRunCronJobByUserId[userId];
            let groupTrackingOfUser: DdxTrackingUserResetPointEntity[] = [];
            if (userId in groupTrackingByUserId) {
              groupTrackingOfUser = groupTrackingByUserId[userId];
            }
            const mapTrackingUserByCronJobId = objectify(
              groupTrackingOfUser,
              (f) => f.cronjobResetUserGiftPointId,
            );

            if (
              dataUpdateCronJobResetUserPointDetail.id in
              mapTrackingUserByCronJobId
            ) {
              updateTrackingUserResetPoint.push({
                data: {
                  id: mapTrackingUserByCronJobId[
                    dataUpdateCronJobResetUserPointDetail.id
                  ].id,
                  userId: Number(userId),
                  cronjobResetUserGiftPointId:
                    dataUpdateCronJobResetUserPointDetail.id,
                  resetPoint: userPointResetInCronJob,
                },
                status: TrackingUserResetPointStatusEnum.UPDATE,
              });
            } else {
              updateTrackingUserResetPoint.push({
                data: {
                  userId: Number(userId),
                  cronjobResetUserGiftPointId:
                    dataUpdateCronJobResetUserPointDetail.id,
                  resetPoint: userPointResetInCronJob,
                },
                status: TrackingUserResetPointStatusEnum.INSERT,
              });
            }
          }
        }
      }
    }
    // console.log('updateTrackingUserResetPoint');
    // console.log(updateTrackingUserResetPoint);

    // Second: combine old tracking of user with new tracking add of user
    const fullGroupTrackingByUserId: Record<
      number,
      CombineTrackingResetPointOfUser[]
    > = {};
    let mapLeftPointOfUser: Record<number, number> = {};
    // console.log(groupTrackingByUserId);
    // console.log('groupTrackingByUserId');
    // this.debugConsoleLog(groupTrackingByUserId);
    for (const userId in groupTrackingByUserId) {
      if (groupTrackingByUserId.hasOwnProperty(userId)) {
        if (!(userId in fullGroupTrackingByUserId)) {
          fullGroupTrackingByUserId[userId] = [];
        }
        if (!(userId in mapLeftPointOfUser)) {
          mapLeftPointOfUser[userId] = 0;
        }

        for (const trackingOfUser of groupTrackingByUserId[userId]) {
          fullGroupTrackingByUserId[userId].push({
            entity: trackingOfUser,
          });
          mapLeftPointOfUser[userId] += trackingOfUser.resetPoint;
        }
      }
    }
    // console.log('updateTrackingUserResetPoint');
    // this.debugConsoleLog(updateTrackingUserResetPoint);
    for (let index = 0; index < updateTrackingUserResetPoint.length; index++) {
      const element = updateTrackingUserResetPoint[index];
      const { userId } = element.data;
      // console.log(userId);
      // console.log(index);
      // console.log(userId in fullGroupTrackingByUserId);
      if (!(userId in fullGroupTrackingByUserId)) {
        fullGroupTrackingByUserId[userId] = [];
      }
      if (!(userId in mapLeftPointOfUser)) {
        mapLeftPointOfUser[userId] = 0;
      }
      fullGroupTrackingByUserId[userId].push({
        data: element.data,
        indexOfUpdateTrackingUserResetPoint: index,
      });
      mapLeftPointOfUser[userId] += element.data.resetPoint;
    }
    // console.log('fullGroupTrackingByUserId');
    // this.debugConsoleLog(fullGroupTrackingByUserId);
    // console.log('updateTrackingUserResetPoint');
    // this.debugConsoleLog(updateTrackingUserResetPoint);

    // Third: process spend tracking, run from top to down of full tracking of user
    const mapCronJobResetPointNeedUpdateById: Record<
      number,
      DataOfCronJobResetUserPointInterface[]
    > = {};
    for (const timeRun in dataUpdateCronJobResetUserPoint) {
      if (dataUpdateCronJobResetUserPoint.hasOwnProperty(timeRun)) {
        const dataUpdateCronJobResetUserPointDetail =
          dataUpdateCronJobResetUserPoint[timeRun];

        for (const userId in dataUpdateCronJobResetUserPointDetail.users) {
          if (
            dataUpdateCronJobResetUserPointDetail.users.hasOwnProperty(userId)
          ) {
            let newFullTrackingsOfUser: CombineTrackingResetPointOfUser[] = [];
            if (!(userId in mapLeftPointOfUser)) {
              mapLeftPointOfUser[userId] = 0;
            }

            const dataUpdateCronJobResetUserPointDetailOfUser =
              dataUpdateCronJobResetUserPointDetail.users[userId];
            if (!(userId in fullGroupTrackingByUserId)) {
              throw new Error(
                'updateDdxTrackingUserResetPoint failed. Can not process spend.',
              );
            }
            // console.log('dataUpdateCronJobResetUserPointDetailOfUser');
            // console.log(dataUpdateCronJobResetUserPointDetailOfUser);
            const fullTrackingsOfUser = fullGroupTrackingByUserId[userId];
            // console.log('fullTrackingsOfUser');
            // console.log(fullTrackingsOfUser);
            let { spend } = dataUpdateCronJobResetUserPointDetailOfUser;
            // console.log('spend');
            // console.log(spend);
            // console.log('spend > 0');
            // console.log(spend > 0);

            mapLeftPointOfUser[userId] -= spend;
            if (spend > 0) {
              for (const fullTrackingOfUser of fullTrackingsOfUser) {
                if (!fullTrackingOfUser.entity && !fullTrackingOfUser.data) {
                  throw new Error(
                    'updateDdxTrackingUserResetPoint failed. Group tracking of user empty.',
                  );
                }
                // console.log('fullTrackingOfUser');
                // console.log(fullTrackingOfUser);

                const pointToProcess = fullTrackingOfUser.entity
                  ? fullTrackingOfUser.entity.resetPoint
                  : fullTrackingOfUser.data.resetPoint;
                // console.log('pointToProcess');
                // console.log(pointToProcess);

                if (spend <= 0) {
                  newFullTrackingsOfUser.push(fullTrackingOfUser);
                  continue;
                }

                const cronJobId = fullTrackingOfUser.entity
                  ? fullTrackingOfUser.entity.cronjobResetUserGiftPointId
                  : fullTrackingOfUser.data.cronjobResetUserGiftPointId;
                if (!(cronJobId in mapCronJobsInsertAndUpdateById)) {
                  throw new Error(
                    'updateDdxTrackingUserResetPoint failed. Cron job process spend not found.',
                  );
                }
                let dataRun: DataOfCronJobResetUserPointInterface[] = [];
                if (cronJobId in mapCronJobResetPointNeedUpdateById) {
                  dataRun = mapCronJobResetPointNeedUpdateById[cronJobId];
                } else {
                  dataRun = mapCronJobsInsertAndUpdateById[cronJobId].dataRun;
                }
                const mapDataRunCronJobByUserId = objectify(
                  dataRun,
                  (f) => f.user_id,
                  (f) => f.point,
                );
                // console.log('mapDataRunCronJobByUserId');
                // console.log(mapDataRunCronJobByUserId);
                // if (spend > mapDataRunCronJobByUserId[userId]) {
                //   console.log('userId');
                //   console.log(userId);
                //   console.log('spend');
                //   console.log(spend);
                //   console.log('mapDataRunCronJobByUserId[userId]');
                //   console.log(mapDataRunCronJobByUserId[userId]);
                //   throw new Error(
                //     'updateDdxTrackingUserResetPoint failed. Spend point more than point in cron job process spend.',
                //   );
                // }
                mapDataRunCronJobByUserId[userId] -= spend;
                mapDataRunCronJobByUserId[userId] =
                  mapDataRunCronJobByUserId[userId] >= 0
                    ? mapDataRunCronJobByUserId[userId]
                    : 0;

                if (spend >= pointToProcess) {
                  spend -= pointToProcess;
                  if (
                    'indexOfUpdateTrackingUserResetPoint' in fullTrackingOfUser
                  ) {
                    delete updateTrackingUserResetPoint[
                      fullTrackingOfUser.indexOfUpdateTrackingUserResetPoint
                    ];
                  } else {
                    updateTrackingUserResetPoint.push({
                      data: {
                        id: fullTrackingOfUser.entity.id,
                        userId: fullTrackingOfUser.entity.userId,
                        cronjobResetUserGiftPointId:
                          fullTrackingOfUser.entity.cronjobResetUserGiftPointId,
                        resetPoint: 0,
                      },
                      status: TrackingUserResetPointStatusEnum.DELETE,
                    });
                  }
                } else {
                  if (
                    'indexOfUpdateTrackingUserResetPoint' in fullTrackingOfUser
                  ) {
                    updateTrackingUserResetPoint[
                      fullTrackingOfUser.indexOfUpdateTrackingUserResetPoint
                    ].data.resetPoint -= spend;
                    fullTrackingOfUser.data.resetPoint =
                      updateTrackingUserResetPoint[
                        fullTrackingOfUser.indexOfUpdateTrackingUserResetPoint
                      ].data.resetPoint;
                  } else {
                    let isNew = true;
                    for (
                      let index = 0;
                      index < updateTrackingUserResetPoint.length;
                      index++
                    ) {
                      const item = updateTrackingUserResetPoint[index];
                      if (item?.data?.id == fullTrackingOfUser?.entity?.id) {
                        isNew = false;
                        item.data.resetPoint -= spend;
                        updateTrackingUserResetPoint[index] = item;
                        break;
                      }
                    }
                    if (isNew) {
                      updateTrackingUserResetPoint.push({
                        data: {
                          id: fullTrackingOfUser.entity.id,
                          userId: fullTrackingOfUser.entity.userId,
                          cronjobResetUserGiftPointId:
                            fullTrackingOfUser.entity
                              .cronjobResetUserGiftPointId,
                          resetPoint:
                            fullTrackingOfUser.entity.resetPoint - spend,
                        },
                        status: TrackingUserResetPointStatusEnum.UPDATE,
                      });
                    }

                    fullTrackingOfUser.entity.resetPoint -= spend;
                  }
                  spend = 0;

                  newFullTrackingsOfUser.push(fullTrackingOfUser);
                }

                let newDataRunCronJob: DataOfCronJobResetUserPointInterface[] =
                  listify(mapDataRunCronJobByUserId, (key, value) => ({
                    user_id: key,
                    point: value,
                  }));
                mapCronJobResetPointNeedUpdateById[cronJobId] =
                  newDataRunCronJob;
              }
            } else {
              newFullTrackingsOfUser = fullTrackingsOfUser;
            }

            fullGroupTrackingByUserId[userId] = newFullTrackingsOfUser;
          }
        }
      }
    }
    // console.log('mapLeftPointOfUser');
    // console.log(mapLeftPointOfUser);
    const mapLeftPointOfUserWithNegativeValue: Record<number, number> = {};

    for (const [key, value] of Object.entries(mapLeftPointOfUser)) {
      if (value < 0) {
        mapLeftPointOfUserWithNegativeValue[Number(key)] = value;
      }
    }
    const userIdsWithNegativeValue = Object.keys(
      mapLeftPointOfUserWithNegativeValue,
    );
    if (userIdsWithNegativeValue.length) {
      throw new Error(
        `updateDdxTrackingUserResetPoint failed. Spend point more than point in cron job process spend. User ids: ${userIdsWithNegativeValue.join(
          ', ',
        )}`,
      );
    }
    updateTrackingUserResetPoint = updateTrackingUserResetPoint.filter(
      (item) => item,
    );
    // Run to delete all items mark update before but mark delete finally
    // Exp:
    // {
    //   data: {
    //     id: 245177,
    //     userId: 304199,
    //     cronjobResetUserGiftPointId: 3262,
    //     resetPoint: 3
    //   },
    //   status: 'UPDATE'
    // },
    // {
    //   data: {
    //     id: 245177,
    //     userId: 304199,
    //     cronjobResetUserGiftPointId: 3262,
    //     resetPoint: 0
    //   },
    //   status: 'DELETE'
    // }
    updateTrackingUserResetPoint = this.removeObsoleteUpdates(
      updateTrackingUserResetPoint,
    );
    // console.log('updateTrackingUserResetPoint');
    // this.debugConsoleLog(updateTrackingUserResetPoint);
    // console.log('mapCronJobResetPointNeedUpdateById');
    // this.debugConsoleLog(mapCronJobResetPointNeedUpdateById);
    // console.log('mapCronJobResetPointNeedUpdateById');
    // console.log(mapCronJobResetPointNeedUpdateById);
    // console.log('updateTrackingUserResetPoint');
    // console.log(updateTrackingUserResetPoint);
    for (const record of updateTrackingUserResetPoint) {
      record.data.resetPoint = formatValueOfPointResetOfUser(
        record.data.resetPoint,
      );
      if (TrackingUserResetPointStatusEnum.INSERT == record.status) {
        const newTracking = new DdxTrackingUserResetPointEntity();
        newTracking.userId = record.data.userId;
        newTracking.cronjobResetUserGiftPointId =
          record.data.cronjobResetUserGiftPointId;
        newTracking.resetPoint = record.data.resetPoint;

        dataTrackingsInsert.push(newTracking);
      } else if (TrackingUserResetPointStatusEnum.UPDATE == record.status) {
        sqlUpdateTracking += `
            WHEN id = ${record.data.id} THEN ${record.data.resetPoint}
          `;
        updateTrackingIds.push(record.data.id);
      } else {
        deleteTrackingIds.push(record.data.id);
      }
    }
    for (const cronJobId in mapCronJobResetPointNeedUpdateById) {
      if (mapCronJobResetPointNeedUpdateById.hasOwnProperty(cronJobId)) {
        const dataRun = mapCronJobResetPointNeedUpdateById[cronJobId];
        for (let index = 0; index < dataRun.length; index++) {
          dataRun[index].point = formatValueOfPointResetOfUser(
            dataRun[index].point,
          );
        }

        sqlUpdateCronJob += `
            WHEN id = ${cronJobId} THEN '${safeJsonStringify(dataRun)}'
          `;
        updateCronJobIds.push(Number(cronJobId));

        updateCronJobResetUserPoint.push({
          data: {
            id: Number(cronJobId),
            dataRun,
          },
          status: CronJobResetUserPointStatusEnum.UPDATE,
        });
      }
    }

    if (dataTrackingsInsert.length) {
      promisesUpdate.push(
        this.ddxTrackingUserResetPointRepo.insert(dataTrackingsInsert),
      );
    }
    // console.log('updateTrackingUserResetPoint');
    // console.log(updateTrackingUserResetPoint);
    if (updateTrackingIds.length) {
      const sqlUpdate = `
        UPDATE ddx_tracking_user_reset_point
        SET reset_point = CASE
          ${sqlUpdateTracking}
          ELSE reset_point
        END
        WHERE id IN (${updateTrackingIds.join(',')})
      `;
      // console.log('sqlUpdate');
      // console.log(sqlUpdate);
      promisesUpdate.push(
        this.dDXCronjobResetUserGiftPointRepo.query(sqlUpdate),
      );
    }
    if (deleteTrackingIds.length) {
      promisesUpdate.push(
        this.ddxTrackingUserResetPointRepo.delete({
          id: In(deleteTrackingIds),
        }),
      );
    }
    if (updateCronJobIds.length) {
      const sqlUpdate = `
        UPDATE ddx_cronjob_reset_user_gift_point
        SET data_run = CASE
          ${sqlUpdateCronJob}
          ELSE data_run
        END
        WHERE id IN (${updateCronJobIds.join(',')}) AND processed = false
      `;
      promisesUpdate.push(
        this.dDXCronjobResetUserGiftPointRepo.query(sqlUpdate),
      );
    }

    return updateTrackingUserResetPoint;
  }

  private validateUpdateDdxTrackingUserResetPoint(
    updateCronJobResetUserPoint: CronJobResetUserPointInterface[],
    updateTrackingUserResetPoint: TrackingUserResetPointInterface[],
    resultsUpdated: any[],
  ): void {
    let countTrackingsInsert = 0;
    let countTrackingsUpdate = 0;
    let countTrackingsDelete = 0;
    let countCronJobsInsert = 0;
    let countCronJobsUpdate = 0;

    for (const record of updateTrackingUserResetPoint) {
      if (TrackingUserResetPointStatusEnum.INSERT == record.status) {
        countTrackingsInsert++;
      } else if (TrackingUserResetPointStatusEnum.UPDATE == record.status) {
        countTrackingsUpdate++;
      } else {
        countTrackingsDelete++;
      }
    }

    for (const record of updateCronJobResetUserPoint) {
      if (CronJobResetUserPointStatusEnum.INSERT == record.status) {
        countCronJobsInsert++;
      } else {
        countCronJobsUpdate++;
      }
    }

    if (countTrackingsInsert > 0) {
      const resultInserted = resultsUpdated.shift();
      if (
        !resultInserted ||
        !resultInserted.identifiers ||
        resultInserted.identifiers.length != countTrackingsInsert
      ) {
        throw new Error(
          'updateDdxTrackingUserResetPoint failed. Insert failed.',
        );
      }
    }
    // console.log('updateTrackingUserResetPoint');
    // console.log(updateTrackingUserResetPoint);
    if (countTrackingsUpdate > 0) {
      const resultUpdated = resultsUpdated.shift();
      // console.log('resultUpdated');
      // console.log(resultUpdated);
      if (
        !resultUpdated ||
        !Array.isArray(resultUpdated) ||
        resultUpdated.length < 2 ||
        !resultUpdated[1] ||
        resultUpdated[1] != countTrackingsUpdate
      ) {
        throw new Error(
          'updateDdxTrackingUserResetPoint failed. Update failed.',
        );
      }
    }
    if (countTrackingsDelete > 0) {
      const resultDeleted = resultsUpdated.shift();
      if (
        !resultDeleted ||
        !resultDeleted.affected ||
        resultDeleted.affected != countTrackingsDelete
      ) {
        throw new Error(
          'updateDdxTrackingUserResetPoint failed. Delete failed.',
        );
      }
    }
    if (countCronJobsInsert > 0) {
      const resultInserted = resultsUpdated.shift();
      if (
        !resultInserted ||
        !resultInserted.identifiers ||
        resultInserted.identifiers.length != countCronJobsInsert
      ) {
        throw new Error(
          'updateDataResetUserPointCronjob in updateDdxTrackingUserResetPoint failed. Insert failed.',
        );
      }
    }
    if (countCronJobsUpdate > 0) {
      const resultUpdated = resultsUpdated.shift();
      if (
        !resultUpdated ||
        !Array.isArray(resultUpdated) ||
        resultUpdated.length < 2 ||
        !resultUpdated[1] ||
        resultUpdated[1] != countCronJobsUpdate
      ) {
        throw new Error(
          'updateDataResetUserPointCronjob in updateDdxTrackingUserResetPoint failed. Update failed.',
        );
      }
    }
  }

  private updateDataResetUserPointCronjob(
    dataUpdateCronJobResetUserPoint: Record<
      string,
      UpdateCronJobResetUserPointInterface
    >,
    mapCronJobsExistedGetByTimeRun: Record<
      string,
      DDXCronjobResetUserGiftPoint
    >,
    groupTrackingByUserId: Record<number, DdxTrackingUserResetPointEntity[]>,
    updateCronJobResetUserPoint: CronJobResetUserPointInterface[],
    promisesUpdate: any[],
  ): void {
    if (!dataUpdateCronJobResetUserPoint) {
      throw new Error('updateDataResetUserPointCronjob failed. Data empty.');
    }

    const dataCronJobsInsert: DDXCronjobResetUserGiftPoint[] = [];
    const idsUpdate: number[] = [];
    let sqlUpdateDataRunCronJob = '';
    for (const timeRun in dataUpdateCronJobResetUserPoint) {
      if (dataUpdateCronJobResetUserPoint.hasOwnProperty(timeRun)) {
        const dataUpdateCronJobResetUserPointDetail =
          dataUpdateCronJobResetUserPoint[timeRun];
        let cronJobsExisted: DDXCronjobResetUserGiftPoint = null;
        let dataRunCronJob: DataOfCronJobResetUserPointInterface[] = [];
        let cronJobResetUserPointStatus =
          CronJobResetUserPointStatusEnum.INSERT;
        if (mapCronJobsExistedGetByTimeRun.hasOwnProperty(timeRun)) {
          cronJobsExisted = mapCronJobsExistedGetByTimeRun[timeRun];
          cronJobResetUserPointStatus = CronJobResetUserPointStatusEnum.UPDATE;
        }

        if (cronJobsExisted) {
          // dataUpdateCronJobResetUserPoint[timeRun].id = cronJobsExisted.id;
          dataRunCronJob = cronJobsExisted.dataRun;
        }
        const mapDataRunCronJobByUserId = objectify(
          dataRunCronJob,
          (f) => f.user_id,
          (f) => f.point,
        );
        // console.log(mapDataRunCronJobByUserId);
        for (const userId in dataUpdateCronJobResetUserPointDetail.users) {
          if (
            dataUpdateCronJobResetUserPointDetail.users.hasOwnProperty(userId)
          ) {
            const dataUpdateCronJobResetUserPointDetailOfUser =
              dataUpdateCronJobResetUserPointDetail.users[userId];
            if (
              !dataUpdateCronJobResetUserPointDetailOfUser.add &&
              !dataUpdateCronJobResetUserPointDetailOfUser.spend
            ) {
              // throw new Error(
              //   `User id ${userId} fail. Add and spend equal zero`,
              // );
              // continue;
            }

            const { spend, add } = dataUpdateCronJobResetUserPointDetailOfUser;
            if (!(userId in mapDataRunCronJobByUserId)) {
              mapDataRunCronJobByUserId[userId] = 0;
            }
            let userPointEffect = add;
            // Leave proces spend to updateDdxTrackingUserResetPoint
            // if (!(userId in groupTrackingByUserId)) {
            //   userPointEffect -= spend;
            // }

            mapDataRunCronJobByUserId[userId] += userPointEffect;
            if (mapDataRunCronJobByUserId[userId] < 0) {
              throw new Error(`User id ${userId} fail. Point reset relative`);
            }
            // if (mapDataRunCronJobByUserId[userId] == 0) {
            //   delete mapDataRunCronJobByUserId[userId];
            // }
            // dataUpdateCronJobResetUserPoint[timeRun].users[
            //   userId
            // ].finalResetPoint = mapDataRunCronJobByUserId[userId];
          }
        }
        // console.log(mapDataRunCronJobByUserId);
        let newDataRunCronJob: DataOfCronJobResetUserPointInterface[] = listify(
          mapDataRunCronJobByUserId,
          (key, value) => ({ user_id: key, point: value }),
        );
        const cronJobResetUserPointData: CronJobResetUserPointDataInterface = {
          dataRun: newDataRunCronJob,
        };
        if (cronJobsExisted) {
          cronJobResetUserPointData.id = cronJobsExisted.id;
        } else {
          cronJobResetUserPointData.timeRun =
            dataUpdateCronJobResetUserPointDetail.timeRunObject;
        }
        updateCronJobResetUserPoint.push({
          data: cronJobResetUserPointData,
          status: cronJobResetUserPointStatus,
        });
      }
    }

    // console.log(updateCronJobResetUserPoint);
    for (const record of updateCronJobResetUserPoint) {
      const dataRun = record.data.dataRun;
      for (let index = 0; index < dataRun.length; index++) {
        dataRun[index].point = formatValueOfPointResetOfUser(
          dataRun[index].point,
        );
      }
      record.data.dataRun = dataRun;

      if (CronJobResetUserPointStatusEnum.INSERT == record.status) {
        const newCronJob = new DDXCronjobResetUserGiftPoint();
        newCronJob.timeRun = record.data.timeRun;
        newCronJob.dataRun = record.data.dataRun;

        dataCronJobsInsert.push(newCronJob);
      } else {
        sqlUpdateDataRunCronJob += `
            WHEN id = ${record.data.id} THEN '${safeJsonStringify(
          record.data.dataRun,
        )}'
          `;
        idsUpdate.push(record.data.id);
      }
    }

    if (dataCronJobsInsert.length) {
      promisesUpdate.push(
        this.dDXCronjobResetUserGiftPointRepo.insert(dataCronJobsInsert),
      );
    }
    if (idsUpdate.length) {
      const sqlUpdate = `
        UPDATE ddx_cronjob_reset_user_gift_point
        SET data_run = CASE
          ${sqlUpdateDataRunCronJob}
          ELSE data_run
        END
        WHERE id IN (${idsUpdate.join(',')}) AND processed = false
      `;
      promisesUpdate.push(
        this.dDXCronjobResetUserGiftPointRepo.query(sqlUpdate),
      );
    }
  }

  private async validateUpdateDataResetUserPointCronjob(
    dataUpdateCronJobResetUserPoint: Record<
      string,
      UpdateCronJobResetUserPointInterface
    >,
    updateCronJobResetUserPoint: CronJobResetUserPointInterface[],
    cronJobInsertAndUpdate: DDXCronjobResetUserGiftPoint[],
    cronJobInTrackingIds: number[],
    resultsUpdated: any[],
  ): Promise<void> {
    let countInsert = 0;
    let countUpdate = 0;
    const cronJobIds: number[] = [...cronJobInTrackingIds];

    for (const record of updateCronJobResetUserPoint) {
      if (CronJobResetUserPointStatusEnum.INSERT == record.status) {
        countInsert++;
      } else {
        if (!cronJobIds.includes(record.data.id)) {
          cronJobIds.push(record.data.id);
        }

        countUpdate++;
      }
    }

    if (countInsert > 0) {
      const resultInserted = resultsUpdated.shift();
      // console.log(resultInserted);
      if (
        !resultInserted ||
        !resultInserted.identifiers ||
        resultInserted.identifiers.length != countInsert
      ) {
        throw new Error(
          'updateDataResetUserPointCronjob failed. Insert failed.',
        );
      }
      for (const item of resultInserted.identifiers) {
        cronJobIds.push(item.id);
      }
      // const idsInserted = resultInserted.identifiers.map((item) => item.id);
      // const newCronJobsInserted =
      //   await this.dDXCronjobResetUserGiftPointRepo.findBy({
      //     id: In(idsInserted),
      //   });
    }
    const cronJobsGetById = await this.dDXCronjobResetUserGiftPointRepo.find({
      where: {
        id: In(cronJobIds),
        processed: false,
      },
      order: {
        id: 'ASC',
      },
    });
    const mapCronJobsGetByTimeRun: Record<
      string,
      DDXCronjobResetUserGiftPoint
    > = {};
    // const mapCronJobsGetByTimeRun = objectify(
    //   newCronJobsInserted,
    //   (f) => formatToString(f.timeRun),
    // );
    for (const cronJobGetById of cronJobsGetById) {
      cronJobInsertAndUpdate.push(cronJobGetById);
      mapCronJobsGetByTimeRun[formatToString(cronJobGetById.timeRun)] =
        cronJobGetById;
    }
    for (const timeRun in dataUpdateCronJobResetUserPoint) {
      if (dataUpdateCronJobResetUserPoint.hasOwnProperty(timeRun)) {
        if (mapCronJobsGetByTimeRun.hasOwnProperty(timeRun)) {
          const cronJobs: DDXCronjobResetUserGiftPoint =
            mapCronJobsGetByTimeRun[timeRun];
          dataUpdateCronJobResetUserPoint[timeRun].id = cronJobs.id;
        }
      }
    }
    if (countUpdate > 0) {
      const resultUpdated = resultsUpdated.shift();
      if (
        !resultUpdated ||
        !Array.isArray(resultUpdated) ||
        resultUpdated.length < 2 ||
        !resultUpdated[1] ||
        resultUpdated[1] != countUpdate
      ) {
        throw new Error(
          'updateDataResetUserPointCronjob failed. Update failed.',
        );
      }
    }
  }

  async validateDataAfterProcessedByCronJob(
    updateCronJobResetUserPoint: CronJobResetUserPointInterface[],
    updateTrackingUserResetPoint: TrackingUserResetPointInterface[],
  ) {
    const idsCronJobNeedValidate = updateTrackingUserResetPoint.map(
      (item) => item.data.cronjobResetUserGiftPointId,
    );
    const uniqueIdsCronJobNeedValidate = removeDuplicateInArray(
      idsCronJobNeedValidate,
    );
    let userIdsNeedValidateTracking: number[] = [];
    const cronJobIdsNeedValidateTracking: number[] = [];
    for (const record of updateCronJobResetUserPoint) {
      userIdsNeedValidateTracking = [
        ...userIdsNeedValidateTracking,
        ...record.data.dataRun.map((item) => item.user_id),
      ];
      cronJobIdsNeedValidateTracking.push(record.data.id);
    }
    const uniqueUserIdsNeedValidateTracking = removeDuplicateInArray(
      userIdsNeedValidateTracking,
    );
    const [cronJobsNeedValidateGet, userTrackingsNeedValidateGet] =
      await Promise.all([
        this.dDXCronjobResetUserGiftPointRepo.findBy({
          id: In(uniqueIdsCronJobNeedValidate),
        }),
        this.ddxTrackingUserResetPointRepo.findBy({
          userId: In(uniqueUserIdsNeedValidateTracking),
          cronjobResetUserGiftPointId: In(cronJobIdsNeedValidateTracking),
        }),
      ]);
    // console.log('userTrackingsNeedValidateGet');
    // this.debugConsoleLog(userTrackingsNeedValidateGet);
    if (uniqueIdsCronJobNeedValidate.length != cronJobsNeedValidateGet.length) {
      throw new Error(
        'validateDataAfterProcessedByCronJob failed. Cron jobs not match.',
      );
    }
    const mapCronJobsNeedValidateGetById = objectify(
      cronJobsNeedValidateGet,
      (f) => f.id,
    );
    const mapUserTrackingsNeedValidateGetByCronJobId = group(
      userTrackingsNeedValidateGet,
      (f) => f.cronjobResetUserGiftPointId,
    );
    // console.log('updateTrackingUserResetPoint');
    // this.debugConsoleLog(updateTrackingUserResetPoint);
    const errMsgValidate: string[] = [];
    for (const record of updateTrackingUserResetPoint) {
      const cronJobValidate =
        mapCronJobsNeedValidateGetById[record.data.cronjobResetUserGiftPointId];
      // console.log('cronJobValidate');
      // console.log(cronJobValidate);
      const dataRun = cronJobValidate.dataRun;
      const mapDataRunCronJobByUserId = objectify(dataRun, (f) => f.user_id);
      const key = `cron job ${cronJobValidate.id} - user ${record.data.userId}`;
      let rsCheckValidate = '';
      if (
        TrackingUserResetPointStatusEnum.INSERT == record.status ||
        TrackingUserResetPointStatusEnum.UPDATE == record.status
      ) {
        if (!(record.data.userId in mapDataRunCronJobByUserId)) {
          rsCheckValidate = 'exists in tracking but not exists in cron job';
        } else if (
          mapDataRunCronJobByUserId[record.data.userId].point !=
          record.data.resetPoint
        ) {
          // console.log('record.data');
          // console.log(record.data);
          // console.log('mapDataRunCronJobByUserId[record.data.userId]');
          // console.log(mapDataRunCronJobByUserId[record.data.userId]);
          rsCheckValidate = 'data point not match in tracking and cronjob';
        }
      } else {
        if (record.data.userId in mapDataRunCronJobByUserId) {
          if (mapDataRunCronJobByUserId[record.data.userId].point > 0) {
            rsCheckValidate = 'removed in tracking but exists in cron job';
          }
        }
      }

      if (rsCheckValidate != '') {
        errMsgValidate.push(`${key}: ${rsCheckValidate}`);
      }
    }

    for (const record of updateCronJobResetUserPoint) {
      const dataRun = record.data.dataRun;
      const key = `cron job ${record.data.id}`;
      let rsCheckValidate = '';
      if (!(record.data.id in mapUserTrackingsNeedValidateGetByCronJobId)) {
        // rsCheckValidate = 'cron job not found in tracking';
        // errMsgValidate.push(`${key}: ${rsCheckValidate}`);
        continue;
      } else {
        const usersTrackingValidate =
          mapUserTrackingsNeedValidateGetByCronJobId[record.data.id];
        // console.log('usersTrackingValidate');
        // console.log(usersTrackingValidate);
        const mapUsersTrackingValidateByUserId = objectify(
          usersTrackingValidate,
          (f) => f.userId,
        );
        for (const item of dataRun) {
          const keyWithUserId = `${key} - user ${item.user_id}`;
          rsCheckValidate = '';
          if (item.point > 0) {
            if (!(item.user_id in mapUsersTrackingValidateByUserId)) {
              rsCheckValidate = 'exists in cron job but not exists in tracking';
            } else if (
              mapUsersTrackingValidateByUserId[item.user_id].resetPoint !=
              item.point
            ) {
              // console.log('mapUsersTrackingValidateByUserId[item.user_id]');
              // console.log(mapUsersTrackingValidateByUserId[item.user_id]);
              // console.log('item');
              // console.log(item);
              rsCheckValidate = 'data point not match in tracking and cronjob';
            }
          } else {
            if (item.user_id in mapUsersTrackingValidateByUserId) {
              if (
                mapUsersTrackingValidateByUserId[item.user_id].resetPoint > 0
              ) {
                rsCheckValidate = 'removed in cron job but exists in tracking';
              }
            }
          }

          if (rsCheckValidate != '') {
            errMsgValidate.push(`${keyWithUserId}: ${rsCheckValidate}`);
          }
        }

        const mapDataRunCronJobByUserId = objectify(dataRun, (f) => f.user_id);
        // console.log('dataRun');
        // console.log(dataRun);
        for (const userTrackingValidate of usersTrackingValidate) {
          const keyWithUserId = `${key} - user ${userTrackingValidate.userId}`;
          rsCheckValidate = '';

          if (!(userTrackingValidate.userId in mapDataRunCronJobByUserId)) {
            rsCheckValidate = 'exists in tracking but not exists in cron job';
          } else if (
            mapDataRunCronJobByUserId[userTrackingValidate.userId].point !=
            userTrackingValidate.resetPoint
          ) {
            // console.log(
            //   'mapDataRunCronJobByUserId[userTrackingValidate.userId]',
            // );
            // console.log(mapDataRunCronJobByUserId[userTrackingValidate.userId]);
            // console.log('userTrackingValidate');
            // console.log(userTrackingValidate);
            rsCheckValidate = 'data point not match in tracking and cronjob';
          }

          if (rsCheckValidate != '') {
            errMsgValidate.push(`${keyWithUserId}: ${rsCheckValidate}`);
          }
        }
      }
    }

    if (errMsgValidate.length) {
      throw new Error(
        `validateDataAfterProcessedByCronJob failed. ${errMsgValidate.join(
          '; ',
        )}.`,
      );
    }
  }

  @Transactional()
  private async updateUsersResetPointByMonth(userIds: number[]): Promise<void> {
    if (!userIds || !userIds.length) {
      return;
    }

    const trackingUserResetPointGet =
      await this.ddxTrackingUserResetPointRepo.find({
        where: {
          userId: In(userIds),
          // Comment out because we need get all case: still tracking and no tracking (spent all of points)
          // resetPoint: MoreThan(0),
        },
        order: {
          userId: 'ASC',
        },
      });
    const groupTrackingByUserId = group(
      trackingUserResetPointGet,
      (f) => f.userId,
    );
    const cronJobInTrackingIds = trackingUserResetPointGet.map(
      (item) => item.cronjobResetUserGiftPointId,
    );
    const cronJobsGet = await this.dDXCronjobResetUserGiftPointRepo.find({
      where: {
        id: In(cronJobInTrackingIds),
      },
    });
    const mapCronJobsGetById = objectify(cronJobsGet, (f) => f.id);
    const dataInsertUsersResetPointByMonth: any[] = [];

    for (const userId in groupTrackingByUserId) {
      if (userId in groupTrackingByUserId) {
        const trackingsOfUser = groupTrackingByUserId[userId];
        const dataResetPointByMonth: Record<string, number> = {};

        for (const trackingOfUser of trackingsOfUser) {
          if (
            !(trackingOfUser.cronjobResetUserGiftPointId in mapCronJobsGetById)
          ) {
            throw new Error(
              'updateUsersResetPointByMonth failed. Cron job not found.',
            );
          }
          if (trackingOfUser.resetPoint <= 0) {
            continue;
          }
          const cronJobGet =
            mapCronJobsGetById[trackingOfUser.cronjobResetUserGiftPointId];
          const monthReset = formatToString(
            cronJobGet.timeRun,
            TIME_FORMAT_ONLY_MONTH,
          );
          if (!(monthReset in dataResetPointByMonth)) {
            dataResetPointByMonth[monthReset] = 0;
          }

          dataResetPointByMonth[monthReset] += trackingOfUser.resetPoint;
        }

        if (Object.keys(dataResetPointByMonth).length > 0) {
          dataInsertUsersResetPointByMonth.push({
            userId,
            resetPointByMonth: dataResetPointByMonth,
          });
        }
      }
    }

    await this.dDXReportUserResetPointByMonthRepo.delete({
      userId: In(userIds),
    });
    if (dataInsertUsersResetPointByMonth.length) {
      await this.dDXReportUserResetPointByMonthRepo.insert(
        dataInsertUsersResetPointByMonth,
      );
    }
  }

  private removeObsoleteUpdates(data: any[]) {
    const seenDeleteIds = new Set<number>();
    const result: any[] = [];

    for (let i = data.length - 1; i >= 0; i--) {
      const item = data[i];
      const id = item.data?.id;

      if (item.status === 'DELETE' && id != null) {
        seenDeleteIds.add(id);
        result.unshift(item);
      } else if (item.status === 'UPDATE' && id != null) {
        if (!seenDeleteIds.has(id)) {
          result.unshift(item);
        }
      } else {
        result.unshift(item);
      }
    }

    return result;
  }
}
