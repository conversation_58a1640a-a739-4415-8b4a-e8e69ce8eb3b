import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Raw, In } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import {
  FeatureNoti,
  NotiDisplayTemplateType,
  PushNotiKafkaDto,
  PushNotiKafkaDtoVersion,
} from 'vtd-common-v3';
import { performance } from 'perf_hooks';
import { BaseCronJob } from './base.cron-job';
import {
  getNowAtTimezone,
  formatToString,
  getFirstDayInMonthOfNowAtTimezone,
  makeDateIsDateAtTimezone,
} from '../../../common/datetime.util';
import { LoggerService } from '../../../core';
import { AppConfig } from '../../../common/configs/app.config';
import { CronJobCodeEnum } from '../../enums/cron-job.enum';
import { CommonIdentityService } from '../common.service';
import { DDXCronjobManagement } from '../../entities/ddx_cronjob_management.entity';
import { DDXCronjobResetUserGiftPointRepository } from '../../repositories/ddx_cronjob_reset_user_gift_point.repository';
import { DDXCronjobResetUserGiftPoint } from '../../entities/ddx_cronjob_reset_user_gift_point.entity';
import { DDXTriggerCalculateUserResetGiftPoint } from '../../entities/ddx_trigger_calculate_user_reset_gift_point.entity';
import { OutboxMessageRepository } from '../../../notification/repositories/outbox-message.repository';
import { DataOfCronJobResetUserPointInterface } from '../../interfaces/reset-user-point.interface';
import { DDXReportUserResetPointByMonthRepository } from '../../repositories/ddx-report-user-reset-point-by-month.repository';
import { DDXReportWarningPointExpireInMonthNotiSendRepository } from '../../repositories/ddx-report-warning-point-expire-in-month-noti-send.repository';
import { DDXReportWarningPointExpireInMonthNotiSendEntity } from '../../entities/ddx-report-warning-point-expire-in-month-noti-send.entity';
import { DataOfReportWarningPointExpireInMonthNotiSendInterface } from '../../interfaces/ddx-report-warning-point-expire-in-month-noti-send.interface';
import {
  TIME_FORMAT_ONLY_DATE,
  TIME_FORMAT_ONLY_MONTH,
  TIME_FORMAT_ONLY_MONTH_DISPLAY,
} from '../../../common/constants/app.constant';
import { convertErrorToStringifyAbleObject } from '../../../common/utils';

@Injectable()
export class WarningPointExpireInMonthNotiSendCronJob extends BaseCronJob {
  constructor(
    private readonly configSer: ConfigService<AppConfig>,
    private readonly commonIdentityService: CommonIdentityService,
    private readonly dDXCronjobResetUserGiftPointRepo: DDXCronjobResetUserGiftPointRepository,
    private readonly outboxMessageRepo: OutboxMessageRepository,
    private readonly dDXReportUserResetPointByMonthRepo: DDXReportUserResetPointByMonthRepository,
    private readonly dDXReportWarningPointExpireInMonthNotiSendRepo: DDXReportWarningPointExpireInMonthNotiSendRepository,
  ) {
    super();
    this._logger = new LoggerService(
      WarningPointExpireInMonthNotiSendCronJob.name,
    );
  }

  async run(): Promise<void> {
    let cronJob: DDXCronjobManagement = null;
    let dataSent: DataOfReportWarningPointExpireInMonthNotiSendInterface[] = [];
    const firstDayInMonth = getFirstDayInMonthOfNowAtTimezone();
    // const firstDayInMonth = makeDateIsDateAtTimezone('2026-04-01 00:00:00');
    // console.log('firstDayInMonth');
    // console.log(firstDayInMonth);

    try {
      const startTime = performance.now();

      cronJob = await this.commonIdentityService.getCronJob(
        CronJobCodeEnum.WARNING_POINT_EXPIRE_IN_MONTH_NOTI_SEND,
      );
      if (!cronJob) {
        throw new Error('Stop process. Cron job null');
      }

      if (cronJob.isEnabled) {
        let dataWarning =
          await this.dDXReportWarningPointExpireInMonthNotiSendRepo.findOneBy({
            firstDayInMonth,
          });
        if (!dataWarning) {
          dataWarning = await this.initDataNotiWarningPointExpireInMonth(
            firstDayInMonth,
          );
        }
        await this.sendNotiWarningPointExpireInMonth(dataWarning, dataSent);

        await this.commonIdentityService.loggingCronJobRunSuccess(
          cronJob,
          dataSent,
        );
      }

      const endTime = performance.now();

      this.debugTimeRun(
        'WarningPointExpireInMonthNotiSendCronJob',
        startTime,
        endTime,
      );
    } catch (err) {
      const errObject = convertErrorToStringifyAbleObject(err);
      if (cronJob && cronJob.isEnabled) {
        await this.commonIdentityService.loggingCronJobRunFail(
          cronJob,
          dataSent,
          errObject,
        );
      }
      this._logger.error('Process failed: ', errObject);
    }
  }

  private async initDataNotiWarningPointExpireInMonth(
    firstDayInMonth: Date,
  ): Promise<DDXReportWarningPointExpireInMonthNotiSendEntity> {
    const month = formatToString(firstDayInMonth, TIME_FORMAT_ONLY_MONTH);
    const reportUsersResetPointInMonth =
      await this.dDXReportUserResetPointByMonthRepo.find({
        where: {
          resetPointByMonth: Raw((alias) => `${alias} ? '${month}'`),
        },
        order: {
          userId: 'ASC',
        },
      });
    const dataUserSend: DataOfReportWarningPointExpireInMonthNotiSendInterface[] =
      [];
    for (const reportUserResetPointInMonth of reportUsersResetPointInMonth) {
      if (reportUserResetPointInMonth.resetPointByMonth[month] > 0) {
        dataUserSend.push({
          user_id: reportUserResetPointInMonth.userId,
          point: reportUserResetPointInMonth.resetPointByMonth[month],
        });
      }
    }

    const dataWarning = new DDXReportWarningPointExpireInMonthNotiSendEntity();
    dataWarning.firstDayInMonth = firstDayInMonth;
    dataWarning.dataUserSend = dataUserSend;
    await this.dDXReportWarningPointExpireInMonthNotiSendRepo.save(dataWarning);

    return await this.dDXReportWarningPointExpireInMonthNotiSendRepo.findOneBy({
      id: dataWarning.id,
    });
  }

  private async sendNotiWarningPointExpireInMonth(
    dataWarning: DDXReportWarningPointExpireInMonthNotiSendEntity,
    dataSent: DataOfReportWarningPointExpireInMonthNotiSendInterface[],
  ): Promise<void> {
    const numBatchs = this.configSer.get<number>(
      'cronjob.sendNotiWarningPointExpireInMonth.numberBatchsInOneRunTime',
    );
    const limitInOneBatch = this.configSer.get<number>(
      'cronjob.sendNotiWarningPointExpireInMonth.limitInOneBatch',
    );
    if (!dataWarning) {
      return;
    }
    if (dataWarning.completed) {
      return;
    }

    const startTime = performance.now();

    let lastProcessedUser = dataWarning.lastProcessedUser;
    let completed = dataWarning.completed;
    const monthExpire = formatToString(
      dataWarning.firstDayInMonth,
      TIME_FORMAT_ONLY_MONTH_DISPLAY,
    );
    if (!dataWarning.dataUserSend || !dataWarning.dataUserSend.length) {
      dataWarning.completed = true;
      await this.updateDataWarning(dataWarning);
    }
    const dataUserNeedSendNoti = this.getDataUserSendNotProcess(dataWarning);
    if (!dataUserNeedSendNoti || !dataUserNeedSendNoti.length) {
      dataWarning.completed = true;
      await this.updateDataWarning(dataWarning);
    }

    for (let batchCount = 0; batchCount < numBatchs; batchCount++) {
      const fromIndex = limitInOneBatch * batchCount;
      const toIndex = fromIndex + limitInOneBatch;
      const dataUserSendNoti = dataUserNeedSendNoti.slice(fromIndex, toIndex);
      if (!dataUserSendNoti || !dataUserSendNoti.length) {
        completed = true;
        break;
      }

      await this.processSendNotiWarningPointExpireInMonth(
        monthExpire,
        dataUserSendNoti,
        lastProcessedUser,
        dataWarning,
        dataSent,
      );
    }

    const endTime = performance.now();

    this.debugTimeRun('sendNotiWarningPointExpireInMonth', startTime, endTime);
  }

  @Transactional()
  private async processSendNotiWarningPointExpireInMonth(
    monthExpire: string,
    dataProcess: DataOfReportWarningPointExpireInMonthNotiSendInterface[],
    lastProcessedUser: number,
    dataWarning: DDXReportWarningPointExpireInMonthNotiSendEntity,
    dataSent: DataOfReportWarningPointExpireInMonthNotiSendInterface[],
  ): Promise<void> {
    const dataOutboxesSendNoti: any[] = [];
    for (const element of dataProcess) {
      const kafkaDto = new PushNotiKafkaDto({
        userIds: [Number(element.user_id)],
        version: PushNotiKafkaDtoVersion.V1,
        notiDisplayTemplateType:
          NotiDisplayTemplateType.DDX_POINT_EXPIRE_IN_MONTH_WARNING,
        featureNoti: FeatureNoti.NOTI_IDENTITY_POINT,
        notiDisplayTemplateParams: {
          point_expired: element.point,
          month_expired: monthExpire,
        },
        link: '/rewards',
        buttonName: 'Đổi quà ngay',
      });
      dataOutboxesSendNoti.push(
        this.outboxMessageRepo.generateDataPushNoti(kafkaDto),
      );
      lastProcessedUser = element.user_id;
      dataSent.push(element);
    }

    dataWarning.lastProcessedUser = lastProcessedUser;
    await Promise.all([
      this.outboxMessageRepo.insert(dataOutboxesSendNoti),
      this.updateDataWarning(dataWarning),
    ]);
  }

  @Transactional()
  private async updateDataWarning(
    dataWarning: DDXReportWarningPointExpireInMonthNotiSendEntity,
  ): Promise<void> {
    if (!dataWarning) {
      return;
    }

    await this.dDXReportWarningPointExpireInMonthNotiSendRepo.save(dataWarning);
  }

  private getDataUserSendNotProcess(
    dataWarning: DDXReportWarningPointExpireInMonthNotiSendEntity,
  ): DataOfReportWarningPointExpireInMonthNotiSendInterface[] {
    if (!dataWarning) {
      return [];
    }
    if (!dataWarning.dataUserSend || !dataWarning.dataUserSend.length) {
      return [];
    }
    const { dataUserSend, lastProcessedUser } = dataWarning;

    const startIndex = dataUserSend.findIndex(
      (item) => item.user_id > lastProcessedUser,
    );
    return startIndex === -1 ? [] : dataUserSend.slice(startIndex);
  }
}
