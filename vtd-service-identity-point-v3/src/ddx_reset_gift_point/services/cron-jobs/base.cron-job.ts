import { Injectable } from '@nestjs/common';
import * as util from 'util';
import { LoggerService } from '../../../core';

@Injectable()
export class BaseCronJob {
  protected _logger: LoggerService;

  protected debugTimeRun(
    feature: string,
    startTime: number,
    endTime: number,
  ): void {
    const timeRun = (endTime - startTime) / 1000;
    this._logger.debug(`Complete ${feature} took ${timeRun} seconds`);
  }

  protected debugConsoleLog(obj: any) {
    console.log(
      util.inspect(obj, { depth: null, colors: true, compact: false }),
    );
  }
}
