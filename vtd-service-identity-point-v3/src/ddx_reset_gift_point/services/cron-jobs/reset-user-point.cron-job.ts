import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Between, In, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { performance } from 'perf_hooks';
import {
  NotiDisplayTemplateType,
  PushNotiKafkaDtoVersion,
  PushNotiKafkaDto,
  FeatureNoti,
  PushNotiKafkaDtoNotiSource,
  JavaV4WhSyncTo3rdServiceDestination,
  JavaV4WhSyncTo3rdServiceCode,
  JavaV4WhSyncData3rdServiceDetailInterface,
  JavaV4WhSyncData3rdServiceInterface,
  OutboxMessageJavaV4WhSyncData3rdServiceRequestDataInterface,
  CrmSfTypeRequestEnum,
  CrmSfTransactionTypeMainCodeEnum,
} from 'vtd-common-v3';
import { BaseCronJob } from './base.cron-job';
import { TIME_FORMAT_ONLY_DATE_SEND_NOTI } from '../../../common/constants/app.constant';
import { LoggerService } from '../../../core';
import { DateTimeUnitEnum } from '../../../common/enums/datetime.enum';
import {
  convertErrorToStringifyAbleObject,
  getNowAtTimeZoneHcm,
  randomTransactionExternalId,
} from '../../../common/utils';
import { returnOriginalTimeFromTimeToResetUserPoint } from '../../utils/reset-user-point.util';
import {
  addUnitToDate,
  getNowAtTimezone,
  roundDownDateTo,
  formatToString,
} from '../../../common/datetime.util';
import { CronJobCodeEnum } from '../../enums/cron-job.enum';
import {
  DataOfCronJobResetUserPointInterface,
  ResetPointNotificationParamsInterface,
} from '../../interfaces/reset-user-point.interface';
import { CommonIdentityService } from '../common.service';
import { DDXCronjobManagement } from '../../entities/ddx_cronjob_management.entity';
import { DDXCronjobResetUserGiftPointRepository } from '../../repositories/ddx_cronjob_reset_user_gift_point.repository';
import { OutboxMessageRepository } from '../../../notification/repositories/outbox-message.repository';
import { OutboxMessage } from '../../../notification/entities/outbox-message.entity';
import { DDXCronjobResetUserGiftPoint } from '../../entities/ddx_cronjob_reset_user_gift_point.entity';
import { UserRepository } from '../../../auth/repositories/user.repository';
import { HistoryPointRepository } from '../../repositories/history-point.repository';
import { HistoryPoint } from '../../entities/history-point.entity';
import { CrmTransactionTypeRepository } from '../../../crm-transaction-type/repositories/crm-transaction-type.repository';
import { objectify } from 'radash';
import { User } from '../../../auth/entities/user.entity';
import {
  HistoryPointActionType,
  HistoryPointType,
} from '../../constant/index.enum';
import { HistoryPointStatus } from '../../enums/reset-point.enum';
import {
  DDXTriggerCalculateUserResetGiftPointTypeEnum,
  DDXTriggerCalculateUserResetGiftPointActionTypeEnum,
} from '../../enums/tracking-user-reset-point.enum';
import { CrmTransactionTypeEntity } from '../../../crm-transaction-type/entities/crm-transaction-type.entity';
import { DDXTriggerCalculateUserResetGiftPointRepository } from '../../repositories/ddx_trigger_calculate_user_reset_gift_point.repository';
import { DDXTriggerCalculateUserResetGiftPoint } from '../../entities/ddx_trigger_calculate_user_reset_gift_point.entity';

@Injectable()
export class ResetUserPointCronJob extends BaseCronJob {
  constructor(
    private readonly commonIdentityService: CommonIdentityService,
    private readonly dDXCronjobResetUserGiftPointRepo: DDXCronjobResetUserGiftPointRepository,
    private readonly userRepo: UserRepository,
    private readonly historyPointRepo: HistoryPointRepository,
    private readonly outboxMessageRepo: OutboxMessageRepository,
    private readonly crmTransactionTypeRepo: CrmTransactionTypeRepository,
    private readonly dDXTriggerCalculateUserResetGiftPointRepo: DDXTriggerCalculateUserResetGiftPointRepository,
  ) {
    super();
    this._logger = new LoggerService(ResetUserPointCronJob.name);
  }

  async run(): Promise<void> {
    let cronJob: DDXCronjobManagement = null;
    let data: DDXCronjobResetUserGiftPoint[] = [];
    const now = getNowAtTimezone();

    try {
      const startTime = performance.now();

      cronJob = await this.commonIdentityService.getCronJob(
        CronJobCodeEnum.RESET_USER_POINT,
      );
      if (!cronJob) {
        throw new Error('Stop process. Cron job null');
      }

      if (cronJob.isEnabled) {
        await this.processResetUserPoint(data, now);

        await this.commonIdentityService.loggingCronJobRunSuccess(
          cronJob,
          data,
        );
      }

      const endTime = performance.now();

      this.debugTimeRun('ResetUserPointCronJob', startTime, endTime);
    } catch (err) {
      const errObject = convertErrorToStringifyAbleObject(err);
      if (cronJob && cronJob.isEnabled) {
        await this.commonIdentityService.loggingCronJobRunFail(
          cronJob,
          data,
          errObject,
        );
      }
      this._logger.error('Process failed: ', errObject);
    }

    await this.markUsersWillResetPoint(now);
  }

  @Transactional()
  private async processResetUserPoint(
    dataGot: DDXCronjobResetUserGiftPoint[],
    now: Date,
  ): Promise<void> {
    const startTime = performance.now();

    const timeToReset = roundDownDateTo(now, DateTimeUnitEnum.HOUR);
    let dataUpdate: DataOfCronJobResetUserPointInterface[] = [];
    const [jobResetUserPointGet, crmTransTypeResetPoint] = await Promise.all([
      this.dDXCronjobResetUserGiftPointRepo.findOne({
        where: {
          timeRun: LessThanOrEqual(timeToReset),
          processed: false,
        },
        order: {
          timeRun: 'ASC',
        },
      }),
      this.crmTransactionTypeRepo.findOneBy({
        mainCode: CrmSfTransactionTypeMainCodeEnum.RESET_POINT_365_2023,
      }),
    ]);
    if (jobResetUserPointGet) {
      dataUpdate = jobResetUserPointGet.dataRun
        ? jobResetUserPointGet.dataRun.filter((item) => item.point > 0)
        : [];
    }

    if (dataUpdate && dataUpdate.length) {
      const idsUserNeedUpdate = dataUpdate.map((item) => item.user_id);
      const usersNeedUpdateGet = await this.userRepo.findBy({
        id: In(idsUserNeedUpdate),
      });
      const mapUsersNeedUpdateById = objectify(usersNeedUpdateGet, (f) => f.id);
      const originalDateStr = formatToString(
        returnOriginalTimeFromTimeToResetUserPoint(
          jobResetUserPointGet.timeRun,
        ),
        TIME_FORMAT_ONLY_DATE_SEND_NOTI,
      );
      const expireDateStr = formatToString(
        jobResetUserPointGet.timeRun,
        TIME_FORMAT_ONLY_DATE_SEND_NOTI,
      );
      const notificationParams: ResetPointNotificationParamsInterface = {
        begin_date: originalDateStr,
        expire_date: expireDateStr,
      };

      const idsUpdate: number[] = [];
      idsUpdate.push(jobResetUserPointGet.id);
      dataGot.push(jobResetUserPointGet);
      const promisesUpdate: any[] = [];
      let resultsUpdated: any[] = [];

      this.updateDDXCronjobResetUserGiftPointProcessed(
        idsUpdate,
        promisesUpdate,
      );
      this.updateDataUserPoint(dataUpdate, promisesUpdate);
      const historyPointsInsertData = this.insertHistoryPoint(
        dataUpdate,
        mapUsersNeedUpdateById,
        promisesUpdate,
      );
      this.sendNotification(dataUpdate, notificationParams, promisesUpdate);
      this.sendDataToWarehouse(
        historyPointsInsertData,
        mapUsersNeedUpdateById,
        crmTransTypeResetPoint,
        promisesUpdate,
      );
      this.syncDataDDX(
        historyPointsInsertData,
        mapUsersNeedUpdateById,
        promisesUpdate,
      );

      resultsUpdated = await Promise.all(promisesUpdate);

      this.validateUpdateDDXCronjobResetUserGiftPointProcessed(
        idsUpdate,
        resultsUpdated,
      );
      this.validateUpdateDataUserPoint(dataUpdate, resultsUpdated);
      this.validateInsertHistoryPoint(dataUpdate, resultsUpdated);
      this.validateSendNotification(dataUpdate, resultsUpdated);
      this.validateSendDataToWarehouse(historyPointsInsertData, resultsUpdated);
      this.validateSyncDataDDX(historyPointsInsertData, resultsUpdated);

      // throw new Error('Test');
    }

    const endTime = performance.now();

    this.debugTimeRun('processResetUserPoint', startTime, endTime);
  }

  @Transactional()
  private async markUsersWillResetPoint(now: Date): Promise<void> {
    const startTime = performance.now();

    const beginNextTimeToReset = roundDownDateTo(
      addUnitToDate(now, 1, DateTimeUnitEnum.HOUR),
      DateTimeUnitEnum.HOUR,
    );
    const endNextTimeToReset = roundDownDateTo(
      addUnitToDate(beginNextTimeToReset, 1, DateTimeUnitEnum.HOUR),
      DateTimeUnitEnum.HOUR,
    );

    const nextJobResetUserPointGet =
      await this.dDXCronjobResetUserGiftPointRepo.findOne({
        where: {
          timeRun: Between(beginNextTimeToReset, endNextTimeToReset),
          processed: false,
        },
        order: {
          timeRun: 'ASC',
        },
      });
    if (nextJobResetUserPointGet) {
      const dataRun = nextJobResetUserPointGet.dataRun
        ? nextJobResetUserPointGet.dataRun.filter((item) => item.point > 0)
        : [];
      if (dataRun.length) {
        const idsUserUpdated = dataRun.map((item) => item.user_id);
        await this.userRepo.update(
          {
            id: In(idsUserUpdated),
          },
          {
            startFreezePoint: beginNextTimeToReset,
            endFreezePoint: endNextTimeToReset,
          },
        );
      }

      // throw new Error('Test');
    }

    const endTime = performance.now();

    this.debugTimeRun('markUsersWillResetPoint', startTime, endTime);
  }

  private updateDDXCronjobResetUserGiftPointProcessed(
    idsUpdate: number[],
    promisesUpdate: any[],
  ): void {
    if (!idsUpdate || !idsUpdate.length) {
      throw new Error(
        'updateDDXCronjobResetUserGiftPointProcessed failed. Ids empty.',
      );
    }

    promisesUpdate.push(
      this.dDXCronjobResetUserGiftPointRepo.update(
        {
          id: In(idsUpdate),
        },
        {
          processed: true,
        },
      ),
    );
  }

  private validateUpdateDDXCronjobResetUserGiftPointProcessed(
    idsUpdate: number[],
    resultsUpdated: any[],
  ): void {
    const resultUpdated = resultsUpdated.shift();
    if (
      !idsUpdate ||
      !idsUpdate.length ||
      !resultUpdated ||
      !resultUpdated.affected
    ) {
      throw new Error(
        'updateDDXCronjobResetUserGiftPointProcessed failed. Update failed.',
      );
    }
    if (idsUpdate.length != resultUpdated.affected) {
      throw new Error(
        'updateDDXCronjobResetUserGiftPointProcessed failed. Update failed.',
      );
    }
  }

  private updateDataUserPoint(
    dataUpdate: DataOfCronJobResetUserPointInterface[],
    promisesUpdate: any[],
  ): void {
    if (!dataUpdate || !dataUpdate.length) {
      return;
    }

    const idsUpdate: number[] = [];
    let sqlUpdateGiftPoint = '';
    const conditionUpdateGiftPoints: string[] = [];
    for (const element of dataUpdate) {
      sqlUpdateGiftPoint += `
        WHEN id = ${element.user_id} THEN gift_point - ${element.point}
      `;
      idsUpdate.push(element.user_id);
      conditionUpdateGiftPoints.push(
        `(id = ${element.user_id} AND gift_point >= ${element.point})`,
      );
    }

    if (idsUpdate.length) {
      const sqlUpdate = `
        UPDATE users
        SET gift_point = CASE
          ${sqlUpdateGiftPoint}
          ELSE gift_point
        END,
        start_freeze_point = NULL, end_freeze_point = NULL
        WHERE ${conditionUpdateGiftPoints.join(' OR ')}
      `;
      promisesUpdate.push(this.userRepo.query(sqlUpdate));
    }
  }

  private validateUpdateDataUserPoint(
    dataUpdate: DataOfCronJobResetUserPointInterface[],
    resultsUpdated: any[],
  ): void {
    if (!dataUpdate || !dataUpdate.length) {
      return;
    }
    const resultUpdated = resultsUpdated.shift();
    // this.debugConsoleLog(resultUpdated);
    if (
      !resultUpdated ||
      !Array.isArray(resultUpdated) ||
      resultUpdated.length < 2 ||
      !resultUpdated[1] ||
      resultUpdated[1] != dataUpdate.length
    ) {
      throw new Error('updateDataUserPoint failed. Update failed.');
    }
  }

  private insertHistoryPoint(
    dataUpdate: DataOfCronJobResetUserPointInterface[],
    mapUsersNeedUpdateById: Record<number, User>,
    promisesUpdate: any[],
  ): HistoryPoint[] {
    if (!dataUpdate || !dataUpdate.length) {
      return;
    }

    const historyPointsInsertData: HistoryPoint[] = [];
    for (const element of dataUpdate) {
      if (!(element.user_id in mapUsersNeedUpdateById)) {
        throw new Error(
          `insertHistoryPoint fail. User ${element.user_id} not found`,
        );
      }
      const user: User = mapUsersNeedUpdateById[element.user_id];
      const historyPointData = this.historyPointRepo.create({
        customerId: user.id,
        customerName: user.getFullName(),
        customerPhone: user.phoneNumber,
        giftPoint: element.point,
        tierPoint: 0,
        type: HistoryPointType.RESET_POINT_365_2023,
        actionType: HistoryPointActionType.RESET_POINT_365_2023,
        status: HistoryPointStatus.SUCCESS,
        transactionDate: getNowAtTimeZoneHcm(),
        transactionExternalId: randomTransactionExternalId(),
      });
      historyPointsInsertData.push(historyPointData);
    }

    if (historyPointsInsertData.length) {
      promisesUpdate.push(
        this.historyPointRepo.insert(historyPointsInsertData),
      );
    }

    return historyPointsInsertData;
  }

  private validateInsertHistoryPoint(
    dataUpdate: DataOfCronJobResetUserPointInterface[],
    resultsUpdated: any[],
  ): void {
    if (!dataUpdate || !dataUpdate.length) {
      return;
    }
    const countHistoryPointInsert = dataUpdate.length;
    const resultInserted = resultsUpdated.shift();
    if (
      !resultInserted ||
      !resultInserted.identifiers ||
      resultInserted.identifiers.length != countHistoryPointInsert
    ) {
      throw new Error('insertHistoryPoint failed. Insert failed.');
    }
  }

  private sendNotification(
    dataUpdate: DataOfCronJobResetUserPointInterface[],
    notificationParams: ResetPointNotificationParamsInterface,
    promisesUpdate: any[],
  ): void {
    if (!dataUpdate || !dataUpdate.length) {
      return;
    }

    const sendNotificationsInsertData: any[] = [];
    for (const element of dataUpdate) {
      const kafkaDto = new PushNotiKafkaDto({
        userIds: [element.user_id],
        version: PushNotiKafkaDtoVersion.V1,
        notiDisplayTemplateType: NotiDisplayTemplateType.DDX_RESET_POINT,
        notiDisplayTemplateParams: {
          ...notificationParams,
          point_reset: element.point,
        },
        source: PushNotiKafkaDtoNotiSource.MB,
        featureNoti: FeatureNoti.NOTI_RESET_POINT,
        link: '/history',
        buttonName: 'XEM LỊCH SỬ',
      });

      sendNotificationsInsertData.push(
        this.outboxMessageRepo.generateDataPushNoti(kafkaDto),
      );
    }

    if (sendNotificationsInsertData.length) {
      promisesUpdate.push(
        this.outboxMessageRepo.insert(sendNotificationsInsertData),
      );
    }
  }

  private validateSendNotification(
    dataUpdate: DataOfCronJobResetUserPointInterface[],
    resultsUpdated: any[],
  ): void {
    if (!dataUpdate || !dataUpdate.length) {
      return;
    }
    const countNotificationsSend = dataUpdate.length;
    const resultInserted = resultsUpdated.shift();
    if (
      !resultInserted ||
      !resultInserted.identifiers ||
      resultInserted.identifiers.length != countNotificationsSend
    ) {
      throw new Error('sendNotification failed. Insert failed.');
    }
  }

  private sendDataToWarehouse(
    historyPointsInsertData: HistoryPoint[],
    mapUsersNeedUpdateById: Record<number, User>,
    crmTransTypeResetPoint: CrmTransactionTypeEntity,
    promisesUpdate: any[],
  ): void {
    if (!historyPointsInsertData || !historyPointsInsertData.length) {
      return;
    }
    if (!crmTransTypeResetPoint) {
      throw new Error(
        `sendDataToWarehouse fail. Crm transaction type reset point not found`,
      );
    }

    const outboxMessagesInsertData: OutboxMessage[] = [];
    for (const hp of historyPointsInsertData) {
      if (!(hp.customerId in mapUsersNeedUpdateById)) {
        throw new Error(
          `sendDataToWarehouse fail. User ${hp.customerId} not found`,
        );
      }
      const user: User = mapUsersNeedUpdateById[hp.customerId];

      const detail: JavaV4WhSyncData3rdServiceDetailInterface = {
        code: JavaV4WhSyncTo3rdServiceCode.RESET_POINT,
        destination: JavaV4WhSyncTo3rdServiceDestination.SF,
        tListPayload: [
          {
            userId: hp.customerId,
            Transaction_Date__c: formatToString(hp.transactionDate),
            Campaign__c: null,
            Level_Points__c: 0,
            Redeem_Points__c: hp.giftPoint,
            Rule_Name__c: crmTransTypeResetPoint.description,
            Transaction_External_ID__c: hp.transactionExternalId,
            Transaction_Type__c: crmTransTypeResetPoint.name,
            Type__c: CrmSfTypeRequestEnum.SPENDING,
            Tier__c: user.tierCode,
            Earned_Tier_Point__c: 0,
            Tier_Points__c: 0,
          },
        ],
      };
      const data: JavaV4WhSyncData3rdServiceInterface = {
        transactions: [detail],
      };
      const outboxRequestData: OutboxMessageJavaV4WhSyncData3rdServiceRequestDataInterface =
        {
          data,
          authentication_token: '',
        };

      outboxMessagesInsertData.push(
        this.outboxMessageRepo.createSyncWhSyncData3rdService(
          outboxRequestData,
        ),
      );
    }

    if (outboxMessagesInsertData.length) {
      promisesUpdate.push(
        this.outboxMessageRepo.insert(outboxMessagesInsertData),
      );
    }
  }

  private validateSendDataToWarehouse(
    historyPointsInsertData: HistoryPoint[],
    resultsUpdated: any[],
  ): void {
    if (!historyPointsInsertData || !historyPointsInsertData.length) {
      return;
    }
    const countOutboxMessageInsert = historyPointsInsertData.length;
    const resultInserted = resultsUpdated.shift();
    if (
      !resultInserted ||
      !resultInserted.identifiers ||
      resultInserted.identifiers.length != countOutboxMessageInsert
    ) {
      throw new Error('sendDataToWarehouse failed. Insert failed.');
    }
  }

  private syncDataDDX(
    historyPointsInsertData: HistoryPoint[],
    mapUsersNeedUpdateById: Record<number, User>,
    promisesUpdate: any[],
  ): void {
    if (!historyPointsInsertData || !historyPointsInsertData.length) {
      return;
    }

    const ddxInsertData: DDXTriggerCalculateUserResetGiftPoint[] = [];
    for (const hp of historyPointsInsertData) {
      if (!(hp.customerId in mapUsersNeedUpdateById)) {
        throw new Error(`syncDataDDX fail. User ${hp.customerId} not found`);
      }
      const user: User = mapUsersNeedUpdateById[hp.customerId];

      const ddxData = this.dDXTriggerCalculateUserResetGiftPointRepo.create({
        customerId: user.id.toString(),
        customerName: user.getFullName(),
        customerPhone: user.phoneNumber,
        totalPointBefore: user.giftPoint,
        numberPoint: hp.giftPoint,
        totalPointAfter: user.giftPoint - hp.giftPoint,
        type: DDXTriggerCalculateUserResetGiftPointTypeEnum.RESET_POINT,
        actionType:
          DDXTriggerCalculateUserResetGiftPointActionTypeEnum.EXPIRED_POINT,
        transactionExternalId: hp.transactionExternalId,
        contentTransaction: '365 ngày không phát sinh giao dịch',
      });
      ddxInsertData.push(ddxData);
    }

    if (ddxInsertData.length) {
      promisesUpdate.push(
        this.dDXTriggerCalculateUserResetGiftPointRepo.insert(ddxInsertData),
      );
    }
  }

  private validateSyncDataDDX(
    historyPointsInsertData: HistoryPoint[],
    resultsUpdated: any[],
  ): void {
    if (!historyPointsInsertData || !historyPointsInsertData.length) {
      return;
    }
    const countDdxInsert = historyPointsInsertData.length;
    const resultInserted = resultsUpdated.shift();
    if (
      !resultInserted ||
      !resultInserted.identifiers ||
      resultInserted.identifiers.length != countDdxInsert
    ) {
      throw new Error('syncDataDDX failed. Insert failed.');
    }
  }
}
