import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { In, MoreThan } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { performance } from 'perf_hooks';
import { group, objectify, listify } from 'radash';
import { BaseCronJob } from './base.cron-job';
import { TIME_FORMAT_ONLY_MONTH } from '../../../common/constants/app.constant';
import { LoggerService } from '../../../core';
import { convertErrorToStringifyAbleObject } from '../../../common/utils';
import { formatToString } from '../../../common/datetime.util';
import { DdxTrackingUserResetPointRepository } from '../../repositories/ddx-tracking-user-reset-point.repository';
import { DDXCronjobResetUserGiftPointRepository } from '../../repositories/ddx_cronjob_reset_user_gift_point.repository';
import { DDXTriggerCalculateUserResetGiftPointRepository } from '../../repositories/ddx_trigger_calculate_user_reset_gift_point.repository';
import { DDXReportUserResetPointByMonthRepository } from '../../repositories/ddx-report-user-reset-point-by-month.repository';

@Injectable()
export class UserResetPointByMonthCalculatingCronJob extends BaseCronJob {
  constructor(
    private readonly dDXTriggerCalculateUserResetGiftPointRepo: DDXTriggerCalculateUserResetGiftPointRepository,
    private readonly ddxTrackingUserResetPointRepo: DdxTrackingUserResetPointRepository,
    private readonly dDXCronjobResetUserGiftPointRepo: DDXCronjobResetUserGiftPointRepository,
    private readonly dDXReportUserResetPointByMonthRepo: DDXReportUserResetPointByMonthRepository,
  ) {
    super();
    this._logger = new LoggerService(
      UserResetPointByMonthCalculatingCronJob.name,
    );
  }

  async run(): Promise<void> {
    const dataTriggerProcessed =
      await this.dDXTriggerCalculateUserResetGiftPointRepo.find({
        where: {
          processed: true,
        },
      });
    const userIdsNeedUpdateResetPointByMonth = dataTriggerProcessed.map(
      (item) => Number(item.customerId),
    );
    try {
      const startTime = performance.now();

      await this.updateUsersResetPointByMonth(
        userIdsNeedUpdateResetPointByMonth,
      );

      const endTime = performance.now();

      this.debugTimeRun(
        'UserResetPointByMonthCalculatingCronJob',
        startTime,
        endTime,
      );
    } catch (err) {
      const errObject = convertErrorToStringifyAbleObject(err);
      this._logger.error(
        'UserResetPointByMonthCalculatingCronJob failed: ',
        errObject,
      );
    }
  }

  @Transactional()
  private async updateUsersResetPointByMonth(userIds: number[]): Promise<void> {
    if (!userIds || !userIds.length) {
      return;
    }

    const trackingUserResetPointGet =
      await this.ddxTrackingUserResetPointRepo.find({
        where: {
          userId: In(userIds),
          // Comment out because we need get all case: still tracking and no tracking (spent all of points)
          // resetPoint: MoreThan(0),
        },
        order: {
          userId: 'ASC',
          cronjobResetUserGiftPointId: 'ASC',
        },
      });
    const groupTrackingByUserId = group(
      trackingUserResetPointGet,
      (f) => f.userId,
    );
    const cronJobInTrackingIds = trackingUserResetPointGet.map(
      (item) => item.cronjobResetUserGiftPointId,
    );
    const cronJobsGet = await this.dDXCronjobResetUserGiftPointRepo.find({
      where: {
        id: In(cronJobInTrackingIds),
      },
    });
    const mapCronJobsGetById = objectify(cronJobsGet, (f) => f.id);
    const dataInsertUsersResetPointByMonth: any[] = [];

    for (const userId in groupTrackingByUserId) {
      if (userId in groupTrackingByUserId) {
        const trackingsOfUser = groupTrackingByUserId[userId];
        const dataResetPointByMonth: Record<string, number> = {};

        for (const trackingOfUser of trackingsOfUser) {
          if (
            !(trackingOfUser.cronjobResetUserGiftPointId in mapCronJobsGetById)
          ) {
            throw new Error(
              'updateUsersResetPointByMonth failed. Cron job not found.',
            );
          }
          if (trackingOfUser.resetPoint <= 0) {
            continue;
          }
          const cronJobGet =
            mapCronJobsGetById[trackingOfUser.cronjobResetUserGiftPointId];
          const monthReset = formatToString(
            cronJobGet.timeRun,
            TIME_FORMAT_ONLY_MONTH,
          );
          if (!(monthReset in dataResetPointByMonth)) {
            dataResetPointByMonth[monthReset] = 0;
          }

          dataResetPointByMonth[monthReset] += trackingOfUser.resetPoint;
        }

        if (Object.keys(dataResetPointByMonth).length > 0) {
          dataInsertUsersResetPointByMonth.push({
            userId: Number(userId),
            resetPointByMonth: dataResetPointByMonth,
          });
        }
      }
    }

    // console.log('dataInsertUsersResetPointByMonth');
    // console.log(dataInsertUsersResetPointByMonth);
    await this.dDXReportUserResetPointByMonthRepo.delete({
      userId: In(userIds),
    });
    if (dataInsertUsersResetPointByMonth.length) {
      await this.dDXReportUserResetPointByMonthRepo.insert(
        dataInsertUsersResetPointByMonth,
      );
    }

    // throw new Error('Test');
  }
}
