import dayjs from 'dayjs';
import {
  Between,
  DataSource,
  In,
  IsNull,
  Not,
  Raw,
  MoreThanOrEqual,
  LessThan,
} from 'typeorm';

import {
  calculateMonthRange,
  makeDateIsDateAndRoundToNearestAtTimeHcm,
} from 'src/common/utils';

import { Injectable, Logger } from '@nestjs/common';

import { DDXCronjobResetUserGiftPointRepository } from 'src/ddx_reset_gift_point/repositories/ddx_cronjob_reset_user_gift_point.repository';
import { DDXReportUserHistoryPointRepository } from 'src/ddx_reset_gift_point/repositories/ddx_report_user_history_point.repository';

import { appConfig } from 'src/common/configs/app.config';
import {
  PointHistoryFilter,
  PointHistoryResponse,
} from 'src/ddx_reset_gift_point/constant/ddx.interface';
import { PointExpiredInMonthDTO } from 'src/ddx_reset_gift_point/dto/req/ddx_reset_point_month.dto';
import { GetDetailPointExpireInMonthUserReqDto } from '../../dto/req/user/get-detail-point-expire-in-month.user.req.dto';
import { GetDetailPointExpireInMonthUserResDto } from '../../dto/res/customer/get-detail-point-expire-in-month.user.res.dto';
import { CommonResponse } from 'src/ddx_reset_gift_point/dto/res/customer/common.dto';
import { AsyncNotiToCustomerResponseDto } from 'src/ddx_reset_gift_point/dto/res/customer/send-noti-response.dto';
import {
  NotificationUserFirebaseStatus,
  NotificationUserStatus,
} from 'src/enums/notification-user.enum';
import { PushNotificationRequest } from 'src/external/interfaces/vita-java.interface';
import { VitaJavaService } from 'src/external/services/vita-java.service';
import { UserSessionRepository } from 'src/notification/entities/user-session.repository';
import { NotificationUserRepository } from 'src/notification/repositories/notification-user.repository';
import { OutboxMessageRepository } from 'src/notification/repositories/outbox-message.repository';
import {
  FeatureNoti,
  NotiDisplayTemplateType,
  PushNotiKafkaDto,
  PushNotiKafkaDtoVersion,
} from 'vtd-common-v3';
import { ResetPointService } from '../reset-point.service';
import { DDXReportUserResetPointByMonthRepository } from '../../repositories/ddx-report-user-reset-point-by-month.repository';
import {
  TIME_FORMAT_ONLY_MONTH,
  TIME_FORMAT_ONLY_MONTH_DISPLAY,
} from '../../../common/constants/app.constant';
import { NUMBER_MONTHS_TO_QUERY_USER_EXPIRE_POINT_FROM_CURRENT } from '../../constant/reset-user-point.constant';
import {
  getFirstDayInMonthOfNowAtTimezone,
  changeMonthFromDate,
  formatToString,
  convertStringToDateInTimezone,
} from '../../../common/datetime.util';
import { UserSessionData } from '../../../proto/user.pb';
import { DdxTrackingUserResetPointRepository } from '../../repositories/ddx-tracking-user-reset-point.repository';

@Injectable()
export class resetPointCustomerService {
  private readonly logger = new Logger('ResetPointService');

  constructor(
    private vitaJavaService: VitaJavaService,
    private readonly resetPointService: ResetPointService,

    private userSessionRepo: UserSessionRepository,
    private outboxMessageRepo: OutboxMessageRepository,
    private notificationUserRepo: NotificationUserRepository,
    private ddxReportUserHistoryPointRepo: DDXReportUserHistoryPointRepository,
    private ddxCronjobResetUserGiftPointRepo: DDXCronjobResetUserGiftPointRepository,
    private readonly dDXReportUserResetPointByMonthRepo: DDXReportUserResetPointByMonthRepository,
    private readonly ddxTrackingUserResetPointRepo: DdxTrackingUserResetPointRepository,

    private readonly dataSource: DataSource,
  ) {
    this.initializeBatchSize();
  }

  private async initializeBatchSize() {}

  /**
   * Get monthly report for a specific customer
   * @param customerId - Customer ID
   * @param month - Month (1-12)
   * @param year - Year
   * @param page - Page number
   * @param limit - Number of records per page
   * @returns Paginated report data
   */
  async getMonthlyReport(
    customerId: string,
    month: number,
    year: number,
    page: number,
    limit: number,
  ): Promise<{ records: any[]; total: number }> {
    try {
      const { startOfMonth, endOfMonth } = calculateMonthRange(month, year - 1);
      this.logger.log('Start of month:', startOfMonth);
      this.logger.log('End of month:', endOfMonth);

      const queryBuilder =
        this.ddxReportUserHistoryPointRepo.createQueryBuilder('report');

      // Apply filters
      queryBuilder.where('report.customerId = :customerId', { customerId });
      queryBuilder.andWhere('report.transactionTime >= :startOfMonth', {
        startOfMonth,
      });
      queryBuilder.andWhere('report.transactionTime <= :endOfMonth', {
        endOfMonth,
      });

      // Get the total count of records (used for pagination)
      const total = await queryBuilder.getCount();

      // Apply ordering, pagination (skip and take), and execute the query
      const records = await queryBuilder
        .orderBy('report.transactionTime', 'ASC')
        .skip((page - 1) * limit)
        .take(limit)
        .getMany();

      return { records, total };
    } catch (e) {
      this.logger.error('Error in getMonthlyReport:', e);

      return { records: [], total: 0 };
    }
  }

  /**
   * Get expired points for a customer within a specific time range
   * @param customerId - Customer ID
   * @param timeExpired - Expiration date
   * @returns Expired points data
   */
  async getDataExpiredInMonthByUser(
    params: PointExpiredInMonthDTO,
  ): Promise<CommonResponse> {
    try {
      const { customerId, timeExpired } = params;
      const currentDate = dayjs();
      const endDate = dayjs(timeExpired).endOf('month');

      this.logger.log(
        `Filtering data for customerId: ${customerId} from ${currentDate.toISOString()} to ${endDate.toISOString()}`,
      );

      const query = `
        WITH filtered_data AS (
          SELECT 
            jsonb_array_elements(data) AS entry, 
            time_run
          FROM ddx_cronjob_reset_user_gift_point
          WHERE time_run BETWEEN $1 AND $2
            AND data @> $3
        )
        SELECT 
          SUM((entry->>'point')::FLOAT) AS "totalPoint",
          MAX(time_run) AS "latestTimeRun"
        FROM filtered_data
        WHERE entry->>'id_customer' = $4;
      `;

      const result = await this.dataSource.query(query, [
        currentDate,
        endDate,
        JSON.stringify([{ id_customer: customerId }]),
        customerId,
      ]);

      const totalPoint = result[0].totalPoint ?? 0;
      const latestTimeRun = result[0].latestTimeRun ?? null;

      return {
        response: {
          id_customer: customerId,
          totalPoint,
          latestTimeRun,
          from: currentDate.toISOString(),
          to: endDate.toISOString(),
        },
        meta: {
          msg: 'Function executed successfully',
          status: 200,
          error: 'Data customer',
        },
      };
    } catch (e) {
      this.logger.error('Error in getExpiredPoints:', e);

      return {
        response: {},
        meta: {
          msg: 'Internal server error',
          status: 500,
          error: 'Cant get data',
        },
      };
    }
  }

  // function for show history
  async getPointHistoryMe(
    filter: PointHistoryFilter,
  ): Promise<PointHistoryResponse> {
    try {
      const pageNumber = parseInt(filter.page || '1', 10) || 1;
      const limit = 20;
      const offset = (pageNumber - 1) * limit;

      // Convert actionType to an array if provided
      const actionTypeArray = filter.actionType
        ? filter.actionType.split(',').map((type) => type.trim())
        : [];

      // Build where condition
      const whereCondition: any = { customerId: filter.customerId };
      if (actionTypeArray.length > 0) {
        whereCondition.type = In(actionTypeArray);
      }

      // Fetch data
      const [data, totalRecords] =
        await this.ddxReportUserHistoryPointRepo.findAndCount({
          where: whereCondition,
          order: { transactionTime: 'DESC' },
          skip: offset,
          take: limit,
        });

      return {
        meta: {
          status: 1000,
          msg: 'Success',
        },
        response: {
          history: data.map((record) => ({
            ...record,
            transactionTimeFormatted: makeDateIsDateAndRoundToNearestAtTimeHcm(
              record.transactionTime,
            ),
          })),
        },
        pagination: {
          totalPages: Math.ceil(totalRecords / limit),
          totalRecords,
          currentPage: pageNumber,
          recordsPerPage: limit,
          last: pageNumber * limit >= totalRecords,
        },
      };
    } catch (e) {
      this.logger.error('Error fetching point history:', e);

      return {
        meta: {
          status: 500,
          msg: 'Internal Server Error',
        },
        response: null,
      };
    }
  }

  /**
   * Get list of users with expired points for notification
   * @param dateGet - Date to filter
   * @returns List of users with expired points
   */
  async getListUserExpiredNoti(dateGet: Date): Promise<any> {
    try {
      const startDate = new Date(dateGet);
      const endDate = new Date(startDate);
      endDate.setMonth(endDate.getMonth() + 1, 0);

      this.logger.log(
        `Filtering data from ${startDate.toISOString()} to ${endDate.toISOString()}`,
      );

      const records = await this.ddxCronjobResetUserGiftPointRepo.find({
        where: { timeRun: Between(startDate, endDate) },
      });

      const customerPointsMap = new Map<
        string,
        { point: number; timeRun: string }[]
      >();

      records.forEach((record) => {
        record.data.forEach((entry) => {
          const id_customer = entry.id_customer;
          const point = entry.point;
          const timeRun = record.timeRun;
          const monthKey = `${(timeRun.getMonth() + 1)
            .toString()
            .padStart(2, '0')}/${timeRun.getFullYear()}`;

          if (!customerPointsMap.has(id_customer)) {
            customerPointsMap.set(id_customer, []);
          }

          const existingEntry = customerPointsMap
            .get(id_customer)
            ?.find((e) => e.timeRun === monthKey);

          if (existingEntry) {
            existingEntry.point += point;
          } else {
            customerPointsMap
              .get(id_customer)
              ?.push({ point, timeRun: monthKey });
          }
        });
      });

      const sortedPoints = Array.from(customerPointsMap.entries())
        .sort(([idA], [idB]) => Number(idA) - Number(idB))
        .map(([id_customer, points]) => ({
          id_customer,
          points: points.sort((a, b) => {
            const [monthA, yearA] = a.timeRun.split('/').map(Number);
            const [monthB, yearB] = b.timeRun.split('/').map(Number);
            return yearA !== yearB ? yearA - yearB : monthA - monthB;
          }),
        }));

      return {
        code: 200,
        data: sortedPoints,
        msg: 'Function executed successfully',
      };
    } catch (e) {
      this.logger.error('Error in getListUserExpiredNoti:', e);

      return {
        code: 500,
        msg: 'Internal server error',
        data: {},
      };
    }
  }

  // Function to send notifications to customers about expired points
  async sentNotiToCustomer(): Promise<AsyncNotiToCustomerResponseDto> {
    try {
      this.logger.log('Try to send noti:');

      const useNotiV3 = appConfig.useNotiV3;
      const sendNoti = async (
        userId: string,
        point_expired: string,
        day_expired: string,
      ) => {
        try {
          if (useNotiV3) {
            const kafkaDto = new PushNotiKafkaDto({
              userIds: [Number(userId)],
              version: PushNotiKafkaDtoVersion.V1,
              notiDisplayTemplateType:
                NotiDisplayTemplateType.POINT_EXPIRE_WARNING,
              featureNoti: FeatureNoti.NOTI_IDENTITY_POINT,
              notiDisplayTemplateParams: { point_expired, day_expired },
              link: '/point-expired',
            });

            const outboxMsg = this.outboxMessageRepo.createPushNoti(kafkaDto);
            await this.outboxMessageRepo.save(outboxMsg);
            return;
          }

          const topic = `USER_${135137}_${dayjs().unix()}`;
          const title = `Số xu tích luỹ của bạn sắp hết hạn!`;
          const content = `Bạn có ${point_expired} xu chuẩn bị hết hạn trong tháng ${day_expired}.`;
          const description = content;
          const userSessions = await this.userSessionRepo.find({
            where: { userId: Number(userId), deviceToken: Not(IsNull()) },
          });
          const tokens = userSessions.map((item) => item.deviceToken);

          const request: PushNotificationRequest = {
            message: content,
            title,
            topic,
            type: 2,
            tokenOfTopic: tokens,
          };

          const notification = this.notificationUserRepo.create({
            userId: Number(userId),
            status: NotificationUserStatus.UNREAD,
            firebaseStatus: NotificationUserFirebaseStatus.SENT,
            title,
            content,
            description,
          });

          await this.vitaJavaService.pushNotification(request);
          await this.notificationUserRepo.save(notification);
        } catch (e) {
          this.logger.error('Error in sendNoti:', e);
        }
      };

      const referenceDate = new Date();
      const listData = await this.resetPointService.getListUserExpiredNoti(
        referenceDate,
      );
      const { data } = listData;

      if (!Array.isArray(data)) {
        this.logger.error('Dữ liệu trả về không hợp lệ:');
        return {
          message: 'Dữ liệu trả về không hợp lệ',
          data: null,
          error: null,
        };
      }

      for (const customer of data) {
        if (!customer || !Array.isArray(customer.points)) {
          this.logger.warn(
            `❌ Lỗi dữ liệu khách hàng ${customer?.id_customer || 'UNKNOWN'}`,
          );
          continue;
        }

        await Promise.all(
          customer.points.map(async (pointEntry) => {
            await sendNoti(
              customer.id_customer,
              pointEntry.point.toFixed(2).toString(),
              pointEntry.timeRun,
            );
          }),
        );
      }

      return {
        message: listData.msg,
        data: listData.data,
        error: null,
      };
    } catch (error) {
      this.logger.error('Error in sentNotiToCustomer:', error);
      return {
        message: 'Failed to execute function',
        data: null,
        error: error.message || 'Unknown error',
      };
    }
  }

  async getReportUserExpirePointByMonth(userId: number) {
    const firstDayInMonth = getFirstDayInMonthOfNowAtTimezone();
    const wheres: any[] = [];
    const rs: Record<string, any> = {};
    for (
      let index = 1;
      index <= NUMBER_MONTHS_TO_QUERY_USER_EXPIRE_POINT_FROM_CURRENT;
      index++
    ) {
      const nextMonth = changeMonthFromDate(firstDayInMonth, index);
      if (nextMonth) {
        const month = formatToString(nextMonth, TIME_FORMAT_ONLY_MONTH);
        wheres.push({
          userId,
          resetPointByMonth: Raw((alias) => `${alias} ? '${month}'`),
        });
        rs[month] = {
          display: formatToString(nextMonth, TIME_FORMAT_ONLY_MONTH_DISPLAY),
          point: 0,
        };
      }
    }

    const expirePointByMonthsGet =
      await this.dDXReportUserResetPointByMonthRepo.findOneBy(wheres);
    if (!expirePointByMonthsGet || !expirePointByMonthsGet.resetPointByMonth) {
      return rs;
    }

    const { resetPointByMonth } = expirePointByMonthsGet;
    for (const month in rs) {
      if (rs.hasOwnProperty(month) && month in resetPointByMonth) {
        rs[month]['point'] = resetPointByMonth[month];
      }
    }

    return {
      meta: {
        status: 1000,
        msg: 'OK',
      },
      response: rs,
    };
  }

  async getDetailUserExpirePointByMonth(
    user: UserSessionData,
    dto: GetDetailPointExpireInMonthUserReqDto,
  ) {
    const { year, month, page, limit } = dto;
    const offset = (page - 1) * limit;

    const firstDayInMonth = convertStringToDateInTimezone(
      `${year}-${month}`,
      TIME_FORMAT_ONLY_MONTH,
    );
    const firstDayInNextMonth = changeMonthFromDate(firstDayInMonth, 1);

    const [data, count] = await this.ddxTrackingUserResetPointRepo
      .createQueryBuilder('tracking')
      .innerJoinAndSelect(
        'tracking.cronjobResetUserGiftPoint',
        'cronjobResetUserGiftPoint',
        'time_run >= :min AND time_run < :max',
        {
          min: firstDayInMonth,
          max: firstDayInNextMonth,
        },
      )
      .where('user_id = :userId', {
        userId: user.userId,
      })
      .andWhere('reset_point > 0')
      .orderBy('cronjobResetUserGiftPoint.time_run', 'ASC')
      .offset(offset)
      .limit(limit)
      .getManyAndCount();

    return {
      meta: {
        status: 1000,
        msg: 'Success',
      },
      response: data.map(
        (item) => new GetDetailPointExpireInMonthUserResDto(item),
      ),
      pagination: {
        totalPages: Math.ceil(count / limit),
        count,
        currentPage: page,
        recordsPerPage: limit,
        last: page * limit >= count,
      },
    };

    // const data = await this.ddxCronjobResetUserGiftPointRepo.find({
    //   where: {
    //     dataRun: Raw((alias) => `${alias} @> '[{"user_id": "301085"}]'`),
    //     timeRun: Raw((alias) => `${alias} >= :min AND ${alias} < :max`, {
    //       min: firstDayInMonth,
    //       max: firstDayInNextMonth,
    //     }),
    //   },
    //   order: {
    //     timeRun: 'ASC',
    //   },
    //   skip: offset,
    //   take: limit,
    // });
  }
}
