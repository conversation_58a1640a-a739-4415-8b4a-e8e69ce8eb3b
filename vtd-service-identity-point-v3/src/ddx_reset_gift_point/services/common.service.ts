import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { LoggerService } from '../../core';
import { CronJobCodeEnum } from '../enums/cron-job.enum';
import { CronJobLoggingStatusEnum } from '../enums/cron-job-logging.enum';
import { safeJsonStringify } from '../../common/utils';
import { DDXCronjobManagementRepository } from '../repositories/ddx_cronjob_management.repository';
import { DDXCronjobManagement } from '../entities/ddx_cronjob_management.entity';
import { DDXCronjobResetUserGiftPointLoggingRepository } from '../repositories/ddx_cronjob_reset_user_gift_point_logging.repository';
import { DdxCronjobLoggingRepository } from '../repositories/ddx-cronjob-logging.repository';
import { DdxCronjobLoggingEntity } from '../entities/ddx-cronjob-logging.entity';

@Injectable()
export class CommonIdentityService {
  private _logger = new LoggerService(CommonIdentityService.name);

  constructor(
    private ddxCronjobResetUserGiftPointLoggingRepo: DDXCronjobResetUserGiftPointLoggingRepository,
    private ddxCronjobManagementRepo: DDXCronjobManagementRepository,
    private readonly ddxCronjobLoggingRepo: DdxCronjobLoggingRepository,
  ) {}

  /**
   * Logs a cron job execution with error details.
   * @param cronjobId - The ID of the cron job.
   * @param error - The error object or message to log.
   * @returns The saved log entry.\
   *
   */

  async logCronJob(cronjobId: string, error: any) {
    return this.logCron(cronjobId, null, error);
  }

  /**
   * Logs a cron job execution with data details.
   * @param cronjobId - The ID of the cron job.
   * @param data - The data to log.
   * @returns The saved log entry.
   */
  async logCronReset(cronjobId: string, data: any) {
    return this.logCron(cronjobId, data, null);
  }

  /**
   * Generic method to log cron job executions.
   * @param cronjobId - The ID of the cron job.
   * @param data - The data to log (optional).
   * @param error - The error object or message to log (optional).
   * @returns The saved log entry.
   */
  private async logCron(cronjobId: string, data: any, error: any) {
    try {
      const logEntry = this.ddxCronjobResetUserGiftPointLoggingRepo.create({
        cronjobId: Number(cronjobId),
        timeRun: new Date(),
        data,
        error,
      });

      return await this.ddxCronjobResetUserGiftPointLoggingRepo.save(logEntry);
    } catch (e) {
      console.error('❌ Error saving cron log:', e);

      throw new InternalServerErrorException('Failed to log cron job.');
    }
  }

  /**
   * Checks if the async report calculation cron job is enabled.
   * @returns A boolean indicating whether the cron job is enabled.
   */
  async canAsyncReportCalculate(): Promise<boolean> {
    try {
      const cronjob = await this.ddxCronjobManagementRepo.findOne({
        where: { cronjobName: 'cron-job-identity-async-report-calculate' },
      });

      if (!cronjob) {
        console.warn(
          '⚠️ Cronjob not found: cron-job-identity-async-report-calculate',
        );
        return false;
      }

      return cronjob.isEnabled;
    } catch (error) {
      console.error('❌ Error fetching cronjob:', error);

      return false;
    }
  }

  async canAsyncHistoryAPI(): Promise<boolean> {
    try {
      const cronjob = await this.ddxCronjobManagementRepo.findOne({
        where: { cronjobName: 'cron-job-identity-async-history-api' },
      });

      if (!cronjob) {
        console.warn(
          '⚠️ Cronjob not found: cron-job-identity-async-history-api',
        );
        return false;
      }

      return cronjob.isEnabled;
    } catch (error) {
      console.error('❌ Error fetching cronjob:', error);

      return false;
    }
  }

  async getCronJob(
    cronJobCode: CronJobCodeEnum,
  ): Promise<DDXCronjobManagement> {
    try {
      const cronjob = await this.ddxCronjobManagementRepo.findOneBy({
        cronjobName: cronJobCode,
      });
      if (!cronjob) {
        throw new Error(`⚠️ Cronjob not found: ${cronJobCode}`);
      }

      return cronjob;
    } catch (err) {
      this._logger.debug('❌ Error fetching cronjob');
      console.log(err);

      return null;
    }
  }

  async loggingCronJobRunSuccess(
    cronJob: DDXCronjobManagement,
    data?: any,
  ): Promise<boolean> {
    try {
      const loggingCronJob = new DdxCronjobLoggingEntity();
      loggingCronJob.cronJobId = cronJob.id;
      loggingCronJob.status = CronJobLoggingStatusEnum.SUCCESS;
      if (data) {
        loggingCronJob.data = data;
      }
      await this.ddxCronjobLoggingRepo.insert(loggingCronJob);

      return true;
    } catch (err) {
      this._logger.debug('❌ Error logging cronjob');
      console.log(err);

      return false;
    }
  }

  async loggingCronJobRunFail(
    cronJob: DDXCronjobManagement,
    data?: any,
    error?: any,
  ): Promise<boolean> {
    try {
      const loggingCronJob = new DdxCronjobLoggingEntity();
      loggingCronJob.cronJobId = cronJob.id;
      loggingCronJob.status = CronJobLoggingStatusEnum.FAILED;
      if (data) {
        loggingCronJob.data = data;
      }
      if (error) {
        loggingCronJob.error = error;
      }
      await this.ddxCronjobLoggingRepo.insert(loggingCronJob);

      return true;
    } catch (err) {
      this._logger.debug('❌ Error logging cronjob');
      console.log(err);

      return false;
    }
  }
}
