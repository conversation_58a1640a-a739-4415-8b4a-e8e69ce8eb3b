import {
  <PERSON>umn,
  <PERSON><PERSON>ty,
  Index,
  Join<PERSON><PERSON>umn,
  ManyTo<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { DDXCronjobResetUserGiftPoint } from './ddx_cronjob_reset_user_gift_point.entity';

@Entity('ddx_tracking_user_reset_point')
export class DdxTrackingUserResetPointEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'cronjob_reset_user_gift_point_id' })
  cronjobResetUserGiftPointId: number;

  @ManyToOne(() => DDXCronjobResetUserGiftPoint, (e) => e.trackings)
  @JoinColumn({ name: 'cronjob_reset_user_gift_point_id' })
  cronjobResetUserGiftPoint: DDXCronjobResetUserGiftPoint;

  @Column({ name: 'reset_point' })
  resetPoint: number;
}
