import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity('ddx_cronjob_reset_user_gift_point_detail')
export class DDXCronjobResetUserGiftPointDetailEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'cronjob_reset_user_gift_point_id' })
  cronjobResetUserGiftPointId: number;

  @Column({ name: 'reset_point' })
  resetPoint: number;
}
