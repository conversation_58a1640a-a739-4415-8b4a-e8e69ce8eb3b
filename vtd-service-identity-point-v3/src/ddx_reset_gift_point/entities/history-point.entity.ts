import {
  Column,
  <PERSON><PERSON>ty,
  Index,
  Join<PERSON><PERSON>umn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import {
  HistoryPointType,
  HistoryPointActionType,
  HistoryPointStatus,
} from 'vtd-common-v3';
import { User } from '../../auth/entities/user.entity';
import { SyncStatus } from '../enums/reset-point.enum';
import { TrackingSyncStatus } from '../constant/index.constant';
import { HistoryPointAttribute } from './history-point-attribute.entity';
import { EventPointHistory, StoreInvitation } from '../constant/ddx.interface';
@Entity({ name: 'history_point' })
@Index(['trackingSyncStatus'])
export class HistoryPoint {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'customer_name', length: 255, nullable: true })
  customerName: string;

  @Column({ name: 'customer_phone', length: 255, nullable: true })
  customerPhone: string;

  @Column({ name: 'gift_point', type: 'float4', nullable: true, default: 0 })
  giftPoint: number;

  @Column({ name: 'tier_point', type: 'float4', nullable: true, default: 0 })
  tierPoint: number;

  @Column({ length: 255 })
  status: HistoryPointStatus;

  @Column({ name: 'transaction_date', type: 'timestamp', nullable: true })
  transactionDate: Date;

  @Column({ length: 255, nullable: true })
  type: HistoryPointType;

  @Column({ name: 'action_type', length: 255, nullable: true })
  actionType: string;

  @Column({ name: 'customer_child_id', type: 'int4', nullable: true })
  customerChildId: number;

  @Column({ type: 'int4', nullable: true, default: 0 })
  money?: number;

  @Column({ name: 'transaction_external_id', length: 255, nullable: true })
  transactionExternalId: string;

  @Column({ length: 50, nullable: true })
  brand?: string;

  @Column({ name: 'cdp_sync_up', nullable: true, default: false })
  cdpSyncUp?: boolean;

  @Column({ name: 'is_gift_received', default: false })
  isGiftReceived: boolean;

  @Column({
    name: 'tracking_sync_status',
    type: 'enum',
    enum: TrackingSyncStatus,
    default: TrackingSyncStatus.PENDING,
  })
  trackingSyncStatus: TrackingSyncStatus;

  @Column({ name: 'error_log', type: 'text', nullable: true })
  errorLog?: string;

  // Join user
  @Column({ type: 'int4', name: 'customer_id' })
  customerId: number;

  @ManyToOne(() => User, (user) => user.historyPoints)
  @JoinColumn({ name: 'customer_id' })
  user: User;
  // End join user

  // Join history_point_attribute
  @OneToMany(() => HistoryPointAttribute, (hpAttr) => hpAttr.historyPoint)
  historyPointAttributes: HistoryPointAttribute[];

  storeInvitation: StoreInvitation;

  eventPointHistory: EventPointHistory;

  // Join user_gift
  @Column({ name: 'gift_id', type: 'int4', nullable: true })
  userGiftId?: number;
}
