import {
  Column,
  CreateDateColumn,
  Entity,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity('ddx_report_user_reset_point_by_months')
export class DDXReportUserResetPointByMonthEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'reset_point_by_month', type: 'jsonb', nullable: true })
  resetPointByMonth: Record<string, number>;
}
