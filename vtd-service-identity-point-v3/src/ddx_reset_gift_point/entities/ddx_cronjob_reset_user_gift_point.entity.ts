import {
  Column,
  CreateDateColumn,
  <PERSON>tity,
  PrimaryGeneratedColumn,
} from 'typeorm';

@Entity('ddx_cronjob_reset_user_gift_point')
export class DDXCronjobResetUserGiftPoint {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ name: 'time_run', type: 'timestamptz' })
  timeRun: Date;

  @Column({ name: 'processed', type: 'boolean', default: false })
  processed: boolean;

  @Column({ name: 'data', type: 'jsonb', default: () => "'{}'" })
  data: Record<string, any>;

  @CreateDateColumn({
    name: 'created_at',
    type: 'timestamptz',
    default: () => 'NOW()',
  })
  createdAt: Date;
}
