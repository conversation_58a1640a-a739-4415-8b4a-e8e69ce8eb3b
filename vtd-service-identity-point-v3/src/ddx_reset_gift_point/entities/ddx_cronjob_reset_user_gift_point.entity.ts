import {
  <PERSON>um<PERSON>,
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON>,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { DataOfCronJobResetUserPointInterface } from '../interfaces/reset-user-point.interface';
import { DdxTrackingUserResetPointEntity } from './ddx-tracking-user-reset-point.entity';

@Entity('ddx_cronjob_reset_user_gift_point')
export class DDXCronjobResetUserGiftPoint {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ name: 'time_run', type: 'timestamptz' })
  timeRun: Date;

  @Column({ name: 'processed', type: 'boolean', default: false })
  processed: boolean;

  @Column({ name: 'data', type: 'jsonb' })
  data: Record<string, any>;

  // TODO: Add new column data_run to run with new code of cron job calculate reset user point
  @Column({ name: 'data_run', type: 'jsonb' })
  dataRun: DataOfCronJobResetUserPointInterface[];

  @CreateDateColumn({
    name: 'created_at',
    type: 'timestamptz',
    default: () => 'NOW()',
  })
  createdAt: Date;

  @OneToMany(
    () => DdxTrackingUserResetPointEntity,
    (e) => e.cronjobResetUserGiftPoint,
  )
  trackings: DdxTrackingUserResetPointEntity[];
}
