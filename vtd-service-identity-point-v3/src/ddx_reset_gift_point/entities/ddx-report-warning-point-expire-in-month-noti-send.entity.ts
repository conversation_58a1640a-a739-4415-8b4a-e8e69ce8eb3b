import {
  Column,
  CreateDate<PERSON>olumn,
  <PERSON>tity,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { DataOfReportWarningPointExpireInMonthNotiSendInterface } from '../interfaces/ddx-report-warning-point-expire-in-month-noti-send.interface';

@Entity('ddx_report_warning_point_expire_in_month_noti_sends')
export class DDXReportWarningPointExpireInMonthNotiSendEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({
    name: 'first_day_in_month',
    type: 'timestamptz',
  })
  firstDayInMonth: Date;

  @Column({ name: 'data_user_send', type: 'jsonb', nullable: true })
  dataUserSend: DataOfReportWarningPointExpireInMonthNotiSendInterface[];

  @Column({ name: 'completed', type: 'boolean', default: false })
  completed: boolean;

  @Column({ name: 'last_processed_user', nullable: true })
  lastProcessedUser: number;
}
