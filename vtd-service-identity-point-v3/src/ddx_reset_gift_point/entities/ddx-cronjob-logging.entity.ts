import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { BaseEntityOnlyCreatedAt } from '../../common/entities/base.v1.entity';
import { CronJobLoggingStatusEnum } from '../enums/cron-job-logging.enum';

@Entity({
  name: 'ddx_cronjob_logging',
})
export class DdxCronjobLoggingEntity extends BaseEntityOnlyCreatedAt {
  @PrimaryGeneratedColumn('increment', { type: 'bigint' })
  id: number;

  @Column({ name: 'cron_job_id', type: 'integer' })
  cronJobId: number;

  @Column({ name: 'status', type: 'varchar', length: 255 })
  status: CronJobLoggingStatusEnum;

  @Column({ name: 'data', type: 'jsonb', nullable: true })
  data?: any;

  @Column({ name: 'error', type: 'jsonb', nullable: true })
  error?: any;
}
