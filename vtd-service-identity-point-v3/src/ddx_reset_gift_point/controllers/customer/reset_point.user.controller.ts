import { Controller, Get, Logger, Query } from '@nestjs/common';

import { ApiTags } from '@nestjs/swagger';
import { GLOBAL_PREFIX } from 'src/common/constants/app.constant';

import { ResetPointService } from '../../services/reset-point.service';

import { UserSessionRepository } from 'src/notification/entities/user-session.repository';
import { NotificationUserRepository } from 'src/notification/repositories/notification-user.repository';
import { OutboxMessageRepository } from 'src/notification/repositories/outbox-message.repository';

import { appConfig } from 'src/common/configs/app.config';

import {
  AuthenticateUser,
  CurrentUser,
} from 'src/common/decorators/auth.decorator';
import { CommonResponse } from 'src/ddx_reset_gift_point/dto/res/customer/common.dto';
import { PointHistoryResponse } from 'src/ddx_reset_gift_point/dto/res/customer/get-my-point-history.dto';
import { resetPointCustomerService } from 'src/ddx_reset_gift_point/services/customer/reset-point.customer.service';
import {
  InputDataInMonthByUserDTO,
  PointExpiredInMonthDTO,
  PointHistoryMeDTO,
  ResetPointInMonthDTO,
} from '../../dto/req/ddx_reset_point_month.dto';
import { GetDetailPointExpireInMonthUserReqDto } from '../../dto/req/user/get-detail-point-expire-in-month.user.req.dto';
import { UserSessionData } from '../../../proto/user.pb';
import { UseRedisCache } from '../../../redis-cache/common/decorators/use-redis-cache.decorator';

@ApiTags('API DDX Mobile Controller')
@Controller(`${GLOBAL_PREFIX}/user`)
@AuthenticateUser()
export class ResetPointControllerForMobile {
  private logger = new Logger('Identity Point Service');
  private crmSyncUsingWh = false;

  constructor(
    private userSessionRepo: UserSessionRepository,
    private outboxMessageRepo: OutboxMessageRepository,
    private readonly resetPointService: ResetPointService,
    private notificationUserRepo: NotificationUserRepository,
    private resetPointCustomerService: resetPointCustomerService,
  ) {
    // Initialize CRM sync configuration
    this.crmSyncUsingWh = appConfig.crm.options.syncUsingWh;
  }

  // API to get reset point data for a specific user in a given month
  @Get('data-expired/month/user')
  async getDataResetPointInMonthByUser(
    @Query() params: InputDataInMonthByUserDTO,
  ): Promise<{
    response: any;
    pagination: any;
    meta: any;
  }> {
    try {
      const { month, year, page, limit, customerId } = params;
      // Fetch data from the service
      const data = await this.resetPointCustomerService.getMonthlyReport(
        customerId,
        month,
        year,
        page,
        limit,
      );

      // Calculate pagination details
      const totalPages = Math.ceil(data.total / limit);

      return {
        response: data.records,
        pagination: {
          totalPages,
          totalRecords: data.total,
          page,
          recordsPerPage: limit,
          last: page >= totalPages ? 'true' : 'false',
        },
        meta: {
          msg: 'Function executed successfully',
          status: '200',
          error: '',
        },
      };
    } catch (error) {
      this.logger.error('Error in getDataResetPoint:', error);

      return {
        response: null,
        pagination: null,
        meta: {
          msg: 'Failed to execute function',
          status: '500',
          error: error.message || 'Unknown error',
        },
      };
    }
  }

  // API to get reset point data for all users in a given month
  @Get('data-expired/month')
  async getDataResetPointInMonth(@Query() params: ResetPointInMonthDTO) {
    try {
      const { month, year, page, limit, customerId } = params;

      // Validate input parameters
      const targetMonth = parseInt(month, 10);
      const targetYear = parseInt(year, 10) - 1;
      const currentPage = parseInt(page, 10) || 0;
      const pageSize = parseInt(limit, 10) || 10;

      if (isNaN(targetMonth) || targetMonth < 1 || targetMonth > 12) {
        throw new Error(
          'Invalid month parameter. Please provide a valid month (1-12).',
        );
      }
      if (isNaN(targetYear) || targetYear < 0) {
        throw new Error('Invalid year parameter. Please provide a valid year.');
      }

      // Fetch data from the service
      const data = await this.resetPointService.getMonthlyReport(
        customerId,
        targetMonth,
        targetYear,
        currentPage,
        pageSize,
      );

      // Calculate pagination details
      const totalPages = Math.ceil(data.total / pageSize);

      return {
        response: data.records,
        pagination: {
          totalPages,
          totalRecords: data.total,
          currentPage,
          recordsPerPage: pageSize,
          last: currentPage >= totalPages ? 'true' : 'false',
        },
        meta: {
          msg: 'Function executed successfully',
          status: '200',
          error: '',
        },
      };
    } catch (error) {
      this.logger.error('Error in getDataResetPoint:', error);

      return {
        message: 'Failed to execute function',
        error: error.message || 'Unknown error',
      };
    }
  }

  // API to get expired points for a specific user
  @Get('point-expired/month')
  async getDataPointExpiredInMonth(
    @Query() params: PointExpiredInMonthDTO,
  ): Promise<CommonResponse> {
    try {
      // Fetch expired points data
      return await this.resetPointCustomerService.getDataExpiredInMonthByUser(
        params,
      );
    } catch (error) {
      this.logger.error('Error in getDataResetPointInMonthByUser:', error);

      return {
        response: null,
        pagination: null,
        meta: {
          msg: 'Failed to execute function',
          status: '500',
          error: error.message || 'Unknown error',
        },
      };
    }
  }

  // API to get point history for a specific user
  @Get('point/history/me')
  async getPointHistoryMe(
    @Query() params: PointHistoryMeDTO,
  ): Promise<PointHistoryResponse> {
    try {
      const { actionType, page, customerId } = params;

      return await this.resetPointService.getPointHistoryMe({
        actionType,
        page,
        customerId,
      });
    } catch (error) {
      this.logger.error('Error in getDataResetPoint:', error);

      return {
        meta: {
          status: 500,
          msg: 'Internal Server Error',
        },
        response: null,
      };
    }
  }

  @Get('point/expire')
  async getReportUserExpirePointByMonth(@CurrentUser() user: UserSessionData) {
    return await this.resetPointCustomerService.getReportUserExpirePointByMonth(
      user.userId,
    );
  }

  @Get('point/expire/month')
  // @UseRedisCache()
  async getDetailUserExpirePointByMonth(
    @CurrentUser() user,
    @Query() req: GetDetailPointExpireInMonthUserReqDto,
  ) {
    return await this.resetPointCustomerService.getDetailUserExpirePointByMonth(
      user,
      req,
    );
  }

  // API to test notification functionality
  @Get('test/notification')
  async APIsentNotiToCustomer() {
    return await this.resetPointCustomerService.sentNotiToCustomer();
  }
}
