export enum IdentityPointSource {
  VERIFY_POINT = 'Xu xác thực sản phẩm',
  RANK_POINT = 'Xu thưởng thăng hạng',
  RANK_BONUS = 'Ưu đãi hạng thành viên',
  BIRTHDAY = 'Xu thưởng sinh nhật',
  EXPIRED_POINT = 'Hết hạn',
  REFUND_POINT = 'Hoàn xu',
  USED_POINT = 'Đã sử dụng',
  USE_POINT = 'Đã sử dụng',
  OTHER = 'Không xác định',
}
export enum IdentityPointType {
  ADD_POINT = 'Tích xu',
  SPEND_POINT = 'Dùng xu',
  OTHER = 'Không xác định',
}

export interface CronjobCustomer {
  timeRun: Date;
  data: JSON;
  id?: number;
}

export enum HistoryPointType {
  ADD_POINT = 'ADD_POINT',
  SPEND_POINT = 'SPEND_POINT',
  GIFT = 'GIFTING',
  RESET_POINT_365_2023 = 'RESET_POINT_365_2023',
}

export enum HistoryPointActionType {
  RESET_POINT_365_2023 = 'RESET_POINT_365_2023',
}

export enum IdentityType {
  ADD_POINT = 'add_point',
  SPEND_POINT = 'spend_point',
}

export enum IdentityPointActionType {
  FIRST_SCAN = 'FIRST_SCAN',
  QR_CODE = 'QR_CODE',
  FIRST_SCAN_SBPS = 'FIRST_SCAN_SBPS',
  QR_CODE_SBPS = 'QR_CODE_SBPS',
  REWARD_GIFT = 'REWARD_GIFT',
  BIRTHDAY_TITAN_2023 = 'BIRTHDAY_TITAN_2023',
  BIRTHDAY_GOLD_2023 = 'BIRTHDAY_GOLD_2023',
  BIRTHDAY_PLATINUM_2023 = 'BIRTHDAY_PLATINUM_2023',
  QR_CODE_TIER_TITAN_2023 = 'QR_CODE_TIER_TITAN_2023',
  QR_CODE_TIER_GOLD_2023 = 'QR_CODE_TIER_GOLD_2023',
  QR_CODE_TIER_PLATINUM_2023 = 'QR_CODE_TIER_PLATINUM_2023',
  TIER_UP_GOLD_2023 = 'TIER_UP_GOLD_2023',
  TIER_UP_PLATINUM_2023 = 'TIER_UP_PLATINUM_2023',
  RETURN_POINT = 'RETURN_POINT',
  RETURN_POINT_PRE_ORD = 'RETURN_POINT_PRE_ORD',
}
