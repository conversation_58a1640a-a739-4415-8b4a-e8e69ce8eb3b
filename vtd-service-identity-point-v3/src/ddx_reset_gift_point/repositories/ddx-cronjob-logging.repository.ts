import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { DdxCronjobLoggingEntity } from '../entities/ddx-cronjob-logging.entity';

@Injectable()
export class DdxCronjobLoggingRepository extends BaseRepository<DdxCronjobLoggingEntity> {
  constructor(dataSource: DataSource) {
    super(DdxCronjobLoggingEntity, dataSource);
  }
}
