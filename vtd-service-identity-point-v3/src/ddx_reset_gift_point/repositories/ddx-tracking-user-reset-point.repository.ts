import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { DdxTrackingUserResetPointEntity } from '../entities/ddx-tracking-user-reset-point.entity';

@Injectable()
export class DdxTrackingUserResetPointRepository extends BaseRepository<DdxTrackingUserResetPointEntity> {
  constructor(dataSource: DataSource) {
    super(DdxTrackingUserResetPointEntity, dataSource);
  }
}
