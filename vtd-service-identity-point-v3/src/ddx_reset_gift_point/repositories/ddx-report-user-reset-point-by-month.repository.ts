import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { DDXReportUserResetPointByMonthEntity } from '../entities/ddx-report-user-reset-point-by-month.entity';

@Injectable()
export class DDXReportUserResetPointByMonthRepository extends BaseRepository<DDXReportUserResetPointByMonthEntity> {
  constructor(dataSource: DataSource) {
    super(DDXReportUserResetPointByMonthEntity, dataSource);
  }
}
