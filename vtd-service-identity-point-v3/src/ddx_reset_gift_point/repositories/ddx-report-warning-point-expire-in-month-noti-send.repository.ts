import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { DDXReportWarningPointExpireInMonthNotiSendEntity } from '../entities/ddx-report-warning-point-expire-in-month-noti-send.entity';

@Injectable()
export class DDXReportWarningPointExpireInMonthNotiSendRepository extends BaseRepository<DDXReportWarningPointExpireInMonthNotiSendEntity> {
  constructor(dataSource: DataSource) {
    super(DDXReportWarningPointExpireInMonthNotiSendEntity, dataSource);
  }
}
