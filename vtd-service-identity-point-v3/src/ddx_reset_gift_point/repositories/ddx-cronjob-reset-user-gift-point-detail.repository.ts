import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { DDXCronjobResetUserGiftPointDetailEntity } from '../entities/ddx-cronjob-reset-user-gift-point-detail.entity';

@Injectable()
export class DDXCronjobResetUserGiftPointDetailRepository extends BaseRepository<DDXCronjobResetUserGiftPointDetailEntity> {
  constructor(dataSource: DataSource) {
    super(DDXCronjobResetUserGiftPointDetailEntity, dataSource);
  }
}
