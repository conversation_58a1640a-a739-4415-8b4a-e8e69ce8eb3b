import { NUMBER_DAYS_TO_RESET_USER_POINT } from '../constant/reset-user-point.constant';
import { DateTimeUnitEnum } from '../../common/enums/datetime.enum';
import { addUnitToDate, roundDownDateTo } from '../../common/datetime.util';
import { roundIfFloatToFixed } from '../../common/utils';

export const calculateTimeToResetUserPoint = (transactionTime: Date) => {
  return roundDownDateTo(
    addUnitToDate(
      transactionTime,
      NUMBER_DAYS_TO_RESET_USER_POINT,
      DateTimeUnitEnum.DAY,
    ),
    DateTimeUnitEnum.HOUR,
  );
};

export const returnOriginalTimeFromTimeToResetUserPoint = (resetTime: Date) => {
  return roundDownDateTo(
    addUnitToDate(
      resetTime,
      0 - NUMBER_DAYS_TO_RESET_USER_POINT,
      DateTimeUnitEnum.DAY,
    ),
    DateTimeUnitEnum.HOUR,
  );
};

export const formatValueOfPointResetOfUser = (
  valueOfPointResetOfUser: number,
) => {
  if (!valueOfPointResetOfUser) {
    return valueOfPointResetOfUser;
  }

  return roundIfFloatToFixed(valueOfPointResetOfUser, 2);
};
