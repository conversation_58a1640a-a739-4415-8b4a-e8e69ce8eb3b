import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';
import { AuthModule } from 'src/auth/auth.module';
import { ExternalModule } from 'src/external/external.module';
import { NotificationModule } from 'src/notification/notification.module';
import { ResetPointControllerForMobile } from './controllers/customer/reset_point.user.controller';
import { DDXCronjobManagementRepository } from './repositories/ddx_cronjob_management.repository';
import { DDXCronjobResetUserGiftPointRepository } from './repositories/ddx_cronjob_reset_user_gift_point.repository';
import { DDXCronjobResetUserGiftPointDetailRepository } from './repositories/ddx-cronjob-reset-user-gift-point-detail.repository';
import { DDXCronjobResetUserGiftPointLoggingRepository } from './repositories/ddx_cronjob_reset_user_gift_point_logging.repository';
import { DDXReportUserHistoryPointRepository } from './repositories/ddx_report_user_history_point.repository';
import { DDXTriggerCalculateUserResetGiftPointRepository } from './repositories/ddx_trigger_calculate_user_reset_gift_point.repository';
import { ResetPointAdminService } from './services/admin/reset-point.admin.service';
import { CommonIdentityService } from './services/common.service';
import { DDXCronjobResetUserGiftPointService } from './services/ddx_cronjob_reset_user_gift_point.service';
import { DDXReportUserHistoryPointService } from './services/ddx_report_user_history_point.service';
import { DDXTriggerCalculateUserResetGiftPointService } from './services/ddx_trigger_calculate_user.service';
import { ResetPointService } from './services/reset-point.service';
import { resetPointCustomerService } from './services/customer/reset-point.customer.service';
import { AsyncDataController } from './controllers/api-common/common-api.controller';
import { ResetHistoryPointService } from './services/sync-old-history/index.service';
import { HistoryPointRepository } from './repositories/history-point.repository';
import { DDXMapHistoryRepository } from './repositories/ddx_map_history.repository';
import { DdxTrackingUserResetPointRepository } from './repositories/ddx-tracking-user-reset-point.repository';
import { DdxCronjobLoggingRepository } from './repositories/ddx-cronjob-logging.repository';
import { DDXReportUserResetPointByMonthRepository } from './repositories/ddx-report-user-reset-point-by-month.repository';
import { DDXReportWarningPointExpireInMonthNotiSendRepository } from './repositories/ddx-report-warning-point-expire-in-month-noti-send.repository';
import { OutboxMessageRepository } from '../notification/repositories/outbox-message.repository';
import { CrmTransactionTypeRepository } from '../crm-transaction-type/repositories/crm-transaction-type.repository';
import { ResetUserPointCalculatingCronJob } from './services/cron-jobs/reset-user-point-calculating.cron-job';
import { ResetUserPointCronJob } from './services/cron-jobs/reset-user-point.cron-job';
import { UserResetPointByMonthCalculatingCronJob } from './services/cron-jobs/user-reset-point-by-month-calculating.cron-job';
import { WarningPointExpireInMonthNotiSendCronJob } from './services/cron-jobs/warning-point-expire-in-month-noti-send.cron-job';

@Module({
  imports: [
    AuthModule,
    HttpModule,
    ExternalModule,
    TerminusModule,
    NotificationModule,
  ],
  controllers: [
    AsyncDataController,
    ResetPointControllerForMobile,
    ResetPointControllerForMobile,
  ],
  providers: [
    ResetPointService,
    CommonIdentityService,
    ResetPointAdminService,
    ResetHistoryPointService,
    resetPointCustomerService,
    DDXReportUserHistoryPointService,
    DDXCronjobResetUserGiftPointService,
    DDXTriggerCalculateUserResetGiftPointService,
    ResetUserPointCalculatingCronJob,
    ResetUserPointCronJob,
    UserResetPointByMonthCalculatingCronJob,
    WarningPointExpireInMonthNotiSendCronJob,

    DDXTriggerCalculateUserResetGiftPointRepository,
    HistoryPointRepository,
    DDXMapHistoryRepository,
    DDXCronjobManagementRepository,
    DDXReportUserHistoryPointRepository,
    DDXCronjobResetUserGiftPointRepository,
    DDXCronjobResetUserGiftPointDetailRepository,
    DDXCronjobResetUserGiftPointLoggingRepository,
    DdxTrackingUserResetPointRepository,
    DdxCronjobLoggingRepository,
    DDXReportUserResetPointByMonthRepository,
    DDXReportWarningPointExpireInMonthNotiSendRepository,
    OutboxMessageRepository,
    CrmTransactionTypeRepository,
  ],
  exports: [
    ResetPointService,
    resetPointCustomerService,
    ResetHistoryPointService,
    // Export to use repository in other controller, need import ResetPointModule first
    DdxCronjobLoggingRepository,
  ],
})
export class ResetPointModule {}
