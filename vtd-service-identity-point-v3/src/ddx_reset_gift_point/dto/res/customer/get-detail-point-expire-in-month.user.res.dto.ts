import { DdxTrackingUserResetPointEntity } from '../../../entities/ddx-tracking-user-reset-point.entity';
import { formatToString } from '../../../../common/datetime.util';

export class GetDetailPointExpireInMonthUserResDto {
  point: number;
  expire: string;

  constructor(tracking: DdxTrackingUserResetPointEntity) {
    console.log(tracking);
    this.point = tracking.resetPoint;
    this.expire = tracking.cronjobResetUserGiftPoint
      ? formatToString(tracking.cronjobResetUserGiftPoint.timeRun)
      : '';
  }
}
