import { IdentityPointContentActionType } from 'vtd-common-v3';
import {
  IsValidDate,
  IsValidEnum,
  IsValidNumber,
  IsValidText,
} from '../../../../common/decorators/custom-validator.decorator';
import { IdentityType } from 'src/ddx_reset_gift_point/constant/index.enum';

export class PostRecordHistoryPointReqDTO {
  @IsValidNumber({ required: true })
  customerId: number;

  @IsValidText({ required: false })
  customerPhone: string;

  @IsValidText({ required: false })
  customerName: string;

  @IsValidNumber({ required: true })
  totalPointBefore: number;

  @IsValidNumber({ required: true })
  totalPointAfter: number;

  @IsValidNumber({ required: true })
  numberPoint: number;

  @IsValidEnum({ enum: IdentityType, required: true })
  type: IdentityType;

  @IsValidEnum({ enum: IdentityPointContentActionType, required: true })
  actionType: IdentityPointContentActionType;

  @IsValidText({ required: true })
  transactionTime: Date;

  @IsValidText({ required: true })
  transactionExternalId: string;

  @IsValidText({ required: false })
  crmTxCode: string;

  @IsValidText({ required: false })
  productName: string;
}
