import { IsValidText } from '../../../../common/decorators/custom-validator.decorator';
import { PaginationReqDto } from '../../../../common/dtos/pagination.dto';

export class GetDetailPointExpireInMonthUserReqDto extends PaginationReqDto {
  @IsValidText({
    matches: new RegExp('^20[2-3][0-9]$'),
  })
  year: string;

  @IsValidText({
    matches: new RegExp('^(0[1-9]|1[0-2])$'),
  })
  month: string;
}
