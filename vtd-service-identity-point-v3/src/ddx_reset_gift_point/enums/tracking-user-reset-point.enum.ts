export enum StatusUpdateDdxTrackingUserResetPointEnum {
  INSERT = 'insert',
  UPDATE = 'update',
  DELETE = 'delete',
}

export enum TrackingUserResetPointStatusEnum {
  INSERT = 'INSERT',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
}

export enum DDXTriggerCalculateUserResetGiftPointTypeEnum {
  RESET_POINT = 'reset_point',
}

export enum DDXTriggerCalculateUserResetGiftPointActionTypeEnum {
  EXPIRED_POINT = 'expiredPoint',
}
