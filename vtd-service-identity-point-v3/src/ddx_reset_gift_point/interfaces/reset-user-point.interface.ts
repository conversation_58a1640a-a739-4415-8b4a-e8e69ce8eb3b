import { StatusUpdateCronJobResetUserPointEnum } from '../enums/cron-job.enum';
import { CronJobResetUserPointStatusEnum } from '../enums/reset-point.enum';

export interface UpdateCronJobResetUserPointDetailOfUserInterface {
  add: number;
  spend: number;
  finalResetPoint?: number;
}

export interface UpdateCronJobResetUserPointInterface {
  id?: number;
  status?: StatusUpdateCronJobResetUserPointEnum;
  timeRunObject: Date;
  users: Record<number, UpdateCronJobResetUserPointDetailOfUserInterface>;
}

export interface DataOfCronJobResetUserPointInterface {
  user_id: number;
  point: number;
}

export interface CronJobResetUserPointDataInterface {
  id?: number;
  timeRun?: Date;
  dataRun: DataOfCronJobResetUserPointInterface[];
}

export interface CronJobResetUserPointInterface {
  data: CronJobResetUserPointDataInterface;
  status: CronJobResetUserPointStatusEnum;
}

export interface ResetPointNotificationParamsInterface {
  begin_date: string;
  expire_date: string;
  point_reset?: number;
}
