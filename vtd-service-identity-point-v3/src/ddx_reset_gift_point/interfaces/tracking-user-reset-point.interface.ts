import { DdxTrackingUserResetPointEntity } from '../entities/ddx-tracking-user-reset-point.entity';
import {
  StatusUpdateDdxTrackingUserResetPointEnum,
  TrackingUserResetPointStatusEnum,
} from '../enums/tracking-user-reset-point.enum';

export interface UpdateDdxTrackingUserResetPointInterface {
  data: DdxTrackingUserResetPointEntity;
  status?: StatusUpdateDdxTrackingUserResetPointEnum;
}

export interface TrackingUserResetPointDataInterface {
  id?: number;
  userId?: number;
  cronjobResetUserGiftPointId?: number;
  resetPoint: number;
}

export interface TrackingUserResetPointInterface {
  data: TrackingUserResetPointDataInterface;
  status: TrackingUserResetPointStatusEnum;
}

export interface CombineTrackingResetPointOfUser {
  entity?: DdxTrackingUserResetPointEntity;
  data?: TrackingUserResetPointDataInterface;
  indexOfUpdateTrackingUserResetPoint?: number;
}
