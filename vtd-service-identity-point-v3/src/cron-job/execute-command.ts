import { Command } from 'commander';
import { NestFactory } from '@nestjs/core';
import { AppModule } from 'src/app.module';
import { ResetPointService } from '../ddx_reset_gift_point/services/reset-point.service';
import { FastifyAdapter } from '@nestjs/platform-fastify';
import { CommonIdentityService } from '../ddx_reset_gift_point/services/common.service';
import { ResetUserPointCalculatingCronJob } from '../ddx_reset_gift_point/services/cron-jobs/reset-user-point-calculating.cron-job';
import { ResetUserPointCronJob } from '../ddx_reset_gift_point/services/cron-jobs/reset-user-point.cron-job';
import { UserResetPointByMonthCalculatingCronJob } from '../ddx_reset_gift_point/services/cron-jobs/user-reset-point-by-month-calculating.cron-job';
import { WarningPointExpireInMonthNotiSendCronJob } from '../ddx_reset_gift_point/services/cron-jobs/warning-point-expire-in-month-noti-send.cron-job';
import { Logger } from '@nestjs/common/services/logger.service';

const program = new Command();
const logger = new Logger('ExecuteCommand');

async function bootstrap() {
  const app = await NestFactory.create(AppModule, new FastifyAdapter());
  await app.init();
  return app;
}

async function handleError(
  err: unknown,
  commonIdentityService?: CommonIdentityService,
) {
  logger.error('Error executing cron job', err);
  process.exit(1);
}

program
  .command('cron-job-identity-async-report-calculate')
  .description('Cron job async data')
  .action(async () => {
    logger.log('Starting batch process...');

    const app = await bootstrap();
    const resetPointService = app.get(ResetPointService);
    const commonIdentityService = app.get(CommonIdentityService);

    try {
      return;
      // if (!(await commonIdentityService.canAsyncReportCalculate())) {
      //   logger.warn('⚠️ Cronjob execution is disabled.');
      //   await app.close();
      //   return;
      // }

      // const logHistory = await resetPointService.asyncUserCalculateReport();

      // commonIdentityService.logCronJob('run_history', {
      //   logHistory,
      // });
    } catch (err) {
      await handleError(err, commonIdentityService);
    }
  });

program
  .command('cron-job-identity-point-reset-user-point-calculating')
  .description('Cron job calculate user point to reset')
  .action(async () => {
    const app = await bootstrap();
    const resetUserPointCalculatingCronJob = app.get(
      ResetUserPointCalculatingCronJob,
    );

    try {
      await resetUserPointCalculatingCronJob.run();
      process.exit(0);
    } catch (err) {
      await handleError(err);
    }
  });

program
  .command('cron-job-identity-point-user-reset-point-by-month-calculating')
  .description('Cron job calculate user reset point by month')
  .action(async () => {
    const app = await bootstrap();
    const userResetPointByMonthCalculatingCronJob = app.get(
      UserResetPointByMonthCalculatingCronJob,
    );

    try {
      await userResetPointByMonthCalculatingCronJob.run();
      process.exit(0);
    } catch (err) {
      await handleError(err);
    }
  });

program
  .command('cron-job-identity-point-warning-point-expire-in-month-noti-send')
  .description('Cron job send noti warning point expire in month')
  .action(async () => {
    const app = await bootstrap();
    const warningPointExpireInMonthNotiSendCronJob = app.get(
      WarningPointExpireInMonthNotiSendCronJob,
    );

    try {
      await warningPointExpireInMonthNotiSendCronJob.run();
      process.exit(0);
    } catch (err) {
      await handleError(err);
    }
  });

program
  .command('cron-job-identity-point-reset-user-point')
  .description('Cron job reset user point')
  .action(async () => {
    const app = await bootstrap();
    const resetUserPointCronJob = app.get(ResetUserPointCronJob);

    try {
      await resetUserPointCronJob.run();
      process.exit(0);
    } catch (err) {
      await handleError(err);
    }
  });

program.parse(process.argv);
