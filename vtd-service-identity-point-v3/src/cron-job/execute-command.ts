import { Command } from 'commander';
import { NestFactory } from '@nestjs/core';
import { AppModule } from 'src/app.module';
import { ResetPointService } from '../ddx_reset_gift_point/services/reset-point.service';
import { FastifyAdapter } from '@nestjs/platform-fastify';
import { CommonIdentityService } from '../ddx_reset_gift_point/services/common.service';
import { Logger } from '@nestjs/common/services/logger.service';

const program = new Command();
const logger = new Logger('ExecuteCommand');

async function bootstrap() {
  const app = await NestFactory.create(AppModule, new FastifyAdapter());
  await app.init();
  return app;
}

async function handleError(
  err: unknown,
  commonIdentityService?: CommonIdentityService,
) {
  logger.error('Error executing cron job', err);
  if (commonIdentityService) {
    await commonIdentityService.logCronJob('run_history', err);
  }
  process.exit(1);
}

program
  .command('cron-job-identity-async-report-calculate')
  .description('Cron job async data')
  .action(async () => {
    logger.log('Starting batch process...');

    const app = await bootstrap();
    const resetPointService = app.get(ResetPointService);
    const commonIdentityService = app.get(CommonIdentityService);

    try {
      return;
      // if (!(await commonIdentityService.canAsyncReportCalculate())) {
      //   logger.warn('⚠️ Cronjob execution is disabled.');
      //   await app.close();
      //   return;
      // }

      // const logHistory = await resetPointService.asyncUserCalculateReport();

      // commonIdentityService.logCronJob('run_history', {
      //   logHistory,
      // });
    } catch (err) {
      await handleError(err, commonIdentityService);
    }
  });
program.parse(process.argv);
