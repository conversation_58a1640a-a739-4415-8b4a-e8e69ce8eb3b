import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { PushNotiKafkaDto } from 'vtd-common-v3';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { OutboxMessage } from '../entities/outbox-message.entity';
import {
  CallType,
  SyncProvider,
  OutboxMessageStatus,
  OutboxMessageType,
  SyncType,
} from '../enums/outbox-message.enum';

@Injectable()
export class OutboxMessageRepository extends BaseRepository<OutboxMessage> {
  constructor(dataSource: DataSource) {
    super(OutboxMessage, dataSource);
  }

  createPushNoti(kafkaDto: PushNotiKafkaDto) {
    return this.create({
      callType: CallType.SYNC,
      provider: SyncProvider.INTERNAL,
      request: JSON.stringify(kafkaDto),
      retryNumber: 0,
      status: OutboxMessageStatus.PROCESSING,
      type: OutboxMessageType.PUSH_NOTI,
      syncType: SyncType.IMMEDIATE,
    });
  }
}
