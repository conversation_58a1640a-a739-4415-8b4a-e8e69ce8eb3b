import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import FormData from 'form-data';
import { LoggerService } from '../../core';

@Injectable()
export class TelegramService {
  private readonly _logger = new LoggerService(TelegramService.name);
  private readonly botToken: string;
  private readonly chatId: string;
  private readonly apiUrl: string;

  constructor(private readonly configService: ConfigService) {
    this.botToken = this.configService.get<string>('telegram.botToken', '');
    this.chatId = this.configService.get<string>('telegram.chatId', '');
    this.apiUrl = `https://api.telegram.org/bot${this.botToken}`;
  }

  // async sendFile(buffer: Buffer, fileName: string): Promise<any> {
  //   const sendDocumentUrl = `${this.apiUrl}/sendDocument`;

  //   const formData = new FormData();
  //   formData.append('chat_id', this.chatId);
  //   formData.append('document', buffer, { filename: fileName });

  //   const fileSize = Buffer.byteLength(buffer);
  //   this._logger.log(`File size: ${fileSize} bytes`);

  //   try {
  //     this._logger.log('Sending file to Telegram...');
  //     const { data } = await axios.post(sendDocumentUrl, formData, {
  //       headers: formData.getHeaders(),
  //       timeout: 300000,
  //     });

  //     this._logger.log('File sent to Telegram:', data);
  //     return data;
  //   } catch (error: any) {
  //     this._logger.error(
  //       'Error sending file to Telegram:',
  //       error.response?.data || error.message,
  //     );

  //     if (error.code === 'ETIMEDOUT') {
  //       this._logger.warn('Timeout occurred, retrying...');
  //       return this.sendFile(buffer, fileName);
  //     }

  //     throw new Error('Failed to send file to Telegram');
  //   }
  // }
}
