import {
  CallHand<PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
  Inject,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRedis } from '@liaoliaots/nestjs-redis';
import { Reflector } from '@nestjs/core';
import { Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { Redis } from 'ioredis';
import { USE_REDIS_CACHE_KEY } from '../constants/redis-cache.constant';
import { createHash } from 'crypto';
import { AppConfig } from '../../../common/configs/app.config';
import { LoggerService } from '../../../core';
import { RedisNamespace } from '../../../common/configs/redis.config';

@Injectable()
export class RedisCacheInterceptor implements NestInterceptor {
  private _logger: LoggerService = new LoggerService(
    RedisCacheInterceptor.name,
  );

  constructor(
    private reflector: Reflector,
    private readonly configSer: ConfigService<AppConfig>,
    @InjectRedis() private redis: Redis,
  ) {}

  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const isCacheEnabled = this.reflector.get<boolean>(
      USE_REDIS_CACHE_KEY,
      context.getHandler(),
    );
    const cacheTtl = this.configSer.get('redisCache.ttl');

    if (!isCacheEnabled) {
      return next.handle();
    }

    this._logger.debug('Begin redis cache');
    const request = context.switchToHttp().getRequest();
    const auth = request.headers['authorization'] || '';
    const key = this.generateCacheKey(auth, request);

    const cached = await this.redis.get(key);
    if (cached) {
      this._logger.debug('Read redis cache');
      return of(JSON.parse(cached));
    }

    return next.handle().pipe(
      tap(async (response) => {
        await this.redis.set(key, JSON.stringify(response), 'EX', cacheTtl);
        this._logger.debug('Set redis cache');
      }),
    );
  }

  private generateCacheKey(auth: string, request: any): string {
    const fingerprint = JSON.stringify({
      auth,
      method: request.method,
      url: request.originalUrl || request.url,
      params: request.params,
      query: request.query,
      body: request.body,
    });

    const hash = createHash('sha256').update(fingerprint).digest('hex');
    const authPart = auth?.split(' ')?.[1]?.slice(0, 10) ?? 'noauth';
    return `cache:${authPart}:${hash}`;
  }
}
