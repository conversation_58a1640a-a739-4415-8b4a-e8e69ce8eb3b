import { Module, Global } from '@nestjs/common';
import { RedisModule as LiaoliaoRedisModule } from '@liaoliaots/nestjs-redis';
import { RedisCacheInterceptor } from './common/interceptors/redis-cache.interceptor';
import { APP_INTERCEPTOR, Reflector } from '@nestjs/core';

@Global()
@Module({
  imports: [],
  providers: [
    RedisCacheInterceptor,
    Reflector,
    {
      provide: APP_INTERCEPTOR,
      useClass: RedisCacheInterceptor,
    },
  ],
  exports: [],
})
export class RedisCacheModule {}
