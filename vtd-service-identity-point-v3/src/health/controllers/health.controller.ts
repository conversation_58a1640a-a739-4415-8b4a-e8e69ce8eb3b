import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { HealthCheck } from '@nestjs/terminus';
import { HealthCheckResult } from '@nestjs/terminus/dist/health-check';
import { GLOBAL_PREFIX } from '../../common/constants/app.constant';
import { HealthService } from '../services/health.service';

@Controller(`${GLOBAL_PREFIX}/health`)
@ApiTags('Health Controller')
export class HealthController {
  constructor(private readonly healthSer: HealthService) {}

  @Get('liveness')
  @HealthCheck()
  async checkLiveness(): Promise<HealthCheckResult> {
    return await this.healthSer.checkLiveness();
  }

  @Get('readiness')
  @HealthCheck()
  async checkReadiness(): Promise<HealthCheckResult> {
    return await this.healthSer.checkReadiness();
  }

  @Get('startup')
  @HealthCheck()
  async checkStartup(): Promise<HealthCheckResult> {
    return await this.healthSer.checkStartup();
  }
}
