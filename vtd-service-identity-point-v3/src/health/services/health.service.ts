import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  HealthCheckService,
  HttpHealthIndicator,
  MemoryHealthIndicator,
  TypeOrmHealthIndicator,
} from '@nestjs/terminus';
import { AppConfig } from '../../common/configs/app.config';

@Injectable()
export class HealthService {
  constructor(
    private configSer: ConfigService<AppConfig>,
    private healthCheckSer: HealthCheckService,
    private typeOrmHealthIndicator: TypeOrmHealthIndicator,
    private httpHealthIndicator: HttpHealthIndicator,
    private memoryHealthIndicator: MemoryHealthIndicator,
  ) {}

  async checkLiveness() {
    return await this.healthCheckSer.check([
      // error connect refuse
      // () => this.httpHealthIndicator.pingCheck('app', `http://[::1]:5000/`),
      //() =>
      //  this.memoryHealthIndicator.checkHeap('memory_heap', 1000 * 1024 * 1024), //should not use more than 1000MB
      async () =>
        await this.typeOrmHealthIndicator.pingCheck('database', {
          timeout: 5000,
        }),
    ]);
  }

  async checkReadiness() {
    return await this.healthCheckSer.check([
      //() =>
      //  this.typeOrmHealthIndicator.pingCheck('database', { timeout: 2000 }),
      //() => this.redisHealthIndicator.isHealthy('redis'),
    ]);
  }

  async checkStartup() {
    return await this.healthCheckSer.check([
      async () =>
        await this.typeOrmHealthIndicator.pingCheck('database', {
          timeout: 5000,
        }),
    ]);
  }
}
