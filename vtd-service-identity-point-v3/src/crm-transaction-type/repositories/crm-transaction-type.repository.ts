import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { BaseRepository } from '../../common/repositories/base.repositories';
import { CrmTransactionTypeEntity } from '../entities/crm-transaction-type.entity';

@Injectable()
export class CrmTransactionTypeRepository extends BaseRepository<CrmTransactionTypeEntity> {
  constructor(dataSource: DataSource) {
    super(CrmTransactionTypeEntity, dataSource);
  }
}
