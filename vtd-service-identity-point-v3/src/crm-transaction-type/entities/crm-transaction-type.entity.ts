import { Column, <PERSON>tity, OneToOne, PrimaryGeneratedColumn } from 'typeorm';

@Entity({ name: 'crm_transaction_type' })
export class CrmTransactionTypeEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ length: 64 })
  code: string;

  @Column({ length: 64 })
  name: string;

  @Column({ type: 'text' })
  description: string;

  @Column({ name: 'main_code', length: 64 })
  mainCode: string;

  @Column({ name: 'campaign_name', type: 'text', nullable: true })
  campaignName: string;

  @Column({ name: 'display_name', type: 'text', nullable: true })
  displayName: string;
}
