import dayjs from 'dayjs';
import { DateTimeUnitEnum } from './enums/datetime.enum';
import { TIME_FORMAT_CRM, TIME_ZONE_HCM } from './constants/app.constant';

export const getNowAtTimezone = (tz = TIME_ZONE_HCM) => {
  return dayjs().tz(tz).toDate();
};

export const makeDateIsDateAtTimezone = (
  date: string | number | Date,
  tz = TIME_ZONE_HCM,
) => {
  return dayjs.tz(date, tz).toDate();
};

export const getFirstDayInMonthOfNowAtTimezone = (tz = TIME_ZONE_HCM) => {
  return dayjs().tz(tz).startOf('month').toDate();
};

export const addUnitToDate = (
  date: string | number | Date,
  number: number,
  unit: DateTimeUnitEnum,
): Date => {
  if (!date) {
    return null;
  }

  return dayjs(date).add(number, unit).toDate();
};

export const roundDownDateTo = (
  date: string | number | Date,
  unit: DateTimeUnitEnum,
): Date => {
  if (!date) {
    return null;
  }
  let dateObj = dayjs(date);
  switch (unit) {
    case DateTimeUnitEnum.SECOND: {
      dateObj = dateObj.millisecond(0);
      break;
    }
    case DateTimeUnitEnum.MINUTE: {
      dateObj = dateObj.second(0).millisecond(0);
      break;
    }
    case DateTimeUnitEnum.HOUR: {
      dateObj = dateObj.minute(0).second(0).millisecond(0);
      break;
    }
    case DateTimeUnitEnum.DAY: {
      dateObj = dateObj.hour(0).minute(0).second(0).millisecond(0);
      break;
    }
    case DateTimeUnitEnum.MONTH: {
      dateObj = dateObj.date(1).hour(0).minute(0).second(0).millisecond(0);
      break;
    }
    case DateTimeUnitEnum.YEAR: {
      dateObj = dateObj
        .month(0)
        .date(1)
        .hour(0)
        .minute(0)
        .second(0)
        .millisecond(0);
      break;
    }
  }

  return dateObj.toDate();
};

export const formatToString = (
  date: string | number | Date,
  format: string = TIME_FORMAT_CRM,
): string => {
  if (!date) {
    return null;
  }

  return dayjs.tz(date).format(format ?? TIME_FORMAT_CRM);
};

export const convertStringToDateInTimezone = (
  date: string,
  format: string = TIME_FORMAT_CRM,
  tz = TIME_ZONE_HCM,
): Date => {
  if (!date) {
    return null;
  }

  return dayjs(date, format).tz(tz).toDate();
};

export const addMonthFromDate = (date: Date, month: number): Date => {
  if (!date) {
    return null;
  }
  if (month == 0) {
    return date;
  }

  return dayjs(date).add(month, 'month').toDate();
};

export const subtractMonthFromDate = (date: Date, month: number): Date => {
  if (!date) {
    return null;
  }
  if (month == 0) {
    return date;
  }

  return dayjs(date).subtract(month, 'month').toDate();
};

export const changeMonthFromDate = (date: Date, month: number): Date => {
  if (!date) {
    return null;
  }
  if (month == 0) {
    return date;
  }

  if (month > 0) {
    return addMonthFromDate(date, month);
  } else {
    return subtractMonthFromDate(date, Math.abs(month));
  }
};

export const changeMonthFromCurrent = (month: number) => {
  const current = dayjs().toDate();
  if (month == 0) {
    return current;
  }

  return changeMonthFromDate(current, month);
};
