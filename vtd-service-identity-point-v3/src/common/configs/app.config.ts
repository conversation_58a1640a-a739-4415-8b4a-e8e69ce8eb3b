import * as dotenv from 'dotenv';
import { boolean } from 'boolean';
import joi from 'joi';
import { NodeEnv } from '../enums/app.enum';
import { RecursiveKeyOf } from '../types/util.type';
dotenv.config();

export const appConfig = {
  nodeEnv: process.env.NODE_ENV,
  port: +process.env.PORT,
  runningAsCronjob: process.env.RUNNING_AS_CRONJOB === 'true',
  telegram: {
    botToken: process.env.BOT_TOKEN,
    chatId: process.env.CHAT_ID,
    // chatId: '**********',
  },
  auth: {
    cronjobToken: process.env.CRONJOB_TOKEN,
  },
  grpc: {
    userSerivce: {
      url: process.env.GRPC_USER_SERVICE_URL || 'vtd-service-user-v3:50051',
    },
    accountService: {
      url: process.env.GRPC_ACCOUNT_SERVICE_URL || 'vtd-service-user-v3:50052',
    },
  },

  externalTokens: process.env.EXTERNAL_TOKENS?.split('|') || [],
  useNotiV3: boolean(process.env.USE_NOTI_V3),
  crm: {
    options: {
      syncUsingWh: boolean(process.env.CRM_SYNC_USING_WH),
    },
  },
  vitaJava: {
    notification: {
      baseUrl: process.env.VITA_JAVA_NOTIFCATION_BASE_URL,
      pushNotiApi: process.env.VITA_JAVA_NOTIFCATION_PUSH_NOTI_API,
    },
    application: {
      baseUrl: process.env.VITA_JAVA_APPLICATION_BASE_URL,
      exchangeGift: process.env.VITA_JAVA_APPLICATION_EXCHANGE_GIFT_V2,
    },
    application_v4: {
      baseUrl: process.env.VITA_JAVA_APPLICATION_V4_BASE_URL,
    },
    wh_v4: {
      baseUrl: process.env.VITA_JAVA_WH_V4_BASE_URL,
      hashKey: process.env.VITA_JAVA_WH_V4_HASH_KEY,
    },
  },
};

export type AppConfig = Record<RecursiveKeyOf<typeof appConfig>, string>;

export const appConfigValidationSchema = joi.object({
  NODE_ENV: joi
    .string()
    .valid(...Object.values(NodeEnv))
    .required(),
  PORT: joi.number().required(),

  DB_HOST: joi.string().required(),
  DB_PORT: joi.number().required(),
  DB_USERNAME: joi.string().required(),
  DB_PASSWORD: joi.string().required(),
  DB_DATABASE: joi.string().required(),
});
