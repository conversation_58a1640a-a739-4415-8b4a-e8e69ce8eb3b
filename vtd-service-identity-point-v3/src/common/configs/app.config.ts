import * as dotenv from 'dotenv';
import { boolean } from 'boolean';
import joi from 'joi';
import { NodeEnv } from '../enums/app.enum';
import { RecursiveKeyOf } from '../types/util.type';
dotenv.config();

export const appConfig = {
  nodeEnv: process.env.NODE_ENV,
  port: +process.env.PORT,
  runningAsCronjob: process.env.RUNNING_AS_CRONJOB === 'true',
  telegram: {
    botToken: process.env.BOT_TOKEN,
    chatId: process.env.CHAT_ID,
    // chatId: '**********',
  },
  auth: {
    cronjobToken: process.env.CRONJOB_TOKEN,
  },
  grpc: {
    userSerivce: {
      url: process.env.GRPC_USER_SERVICE_URL || 'vtd-service-user-v3:50051',
    },
    accountService: {
      url: process.env.GRPC_ACCOUNT_SERVICE_URL || 'vtd-service-user-v3:50052',
    },
  },

  externalTokens: process.env.EXTERNAL_TOKENS?.split('|') || [],
  useNotiV3: boolean(process.env.USE_NOTI_V3),
  crm: {
    options: {
      syncUsingWh: boolean(process.env.CRM_SYNC_USING_WH),
    },
  },
  vitaJava: {
    notification: {
      baseUrl: process.env.VITA_JAVA_NOTIFCATION_BASE_URL,
      pushNotiApi: process.env.VITA_JAVA_NOTIFCATION_PUSH_NOTI_API,
    },
    application: {
      baseUrl: process.env.VITA_JAVA_APPLICATION_BASE_URL,
      exchangeGift: process.env.VITA_JAVA_APPLICATION_EXCHANGE_GIFT_V2,
    },
    application_v4: {
      baseUrl: process.env.VITA_JAVA_APPLICATION_V4_BASE_URL,
    },
    wh_v4: {
      baseUrl: process.env.VITA_JAVA_WH_V4_BASE_URL,
      hashKey: process.env.VITA_JAVA_WH_V4_HASH_KEY,
    },
  },
  cronjob: {
    resetUserPointCalculating: {
      limitInOneBatch:
        Number(process.env.RESET_USER_POINT_CALCULATING_LIMIT_IN_ONE_BATCH) ??
        700, // default 700
      numberBatchsInOneRunTime:
        Number(
          process.env
            .RESET_USER_POINT_CALCULATING_NUMBER_BATCHS_IN_ONE_RUN_TIME,
        ) ?? 10, // default 10
    },
    sendNotiWarningPointExpireInMonth: {
      limitInOneBatch:
        Number(
          process.env
            .WARNING_POINT_EXPIRE_IN_MONTH_NOTI_SEND_LIMIT_IN_ONE_BATCH,
        ) ?? 500, // default 500
      numberBatchsInOneRunTime:
        Number(
          process.env
            .WARNING_POINT_EXPIRE_IN_MONTH_NOTI_SEND_NUMBER_BATCHS_IN_ONE_RUN_TIME,
        ) ?? 10, // default 10
    },
  },
  redis: {
    cloud: {
      host: process.env.REDIS_CLOUD_HOST,
      port: process.env.REDIS_CLOUD_PORT,
      password: process.env.REDIS_CLOUD_PASSWORD,
      caPem: process.env.REDIS_CLOUD_CA_PEM
        ? process.env.REDIS_CLOUD_CA_PEM.replace(/\\n/gm, '\n')
        : '',
    },
    standAlone: {
      host: process.env.REDIS_STANDALONE_HOST,
      port: process.env.REDIS_STANDALONE_PORT,
      password: process.env.REDIS_STANDALONE_PASSWORD,
    },
    usingRedisCloud: boolean(process.env.REDIS_USING_REDIS_CLOUD),
  },
  redisCache: {
    ttl: process.env.REDIS_CACHE_TTL_SECONDS || 300,
  },
};

export type AppConfig = Record<RecursiveKeyOf<typeof appConfig>, string>;

export const appConfigValidationSchema = joi.object({
  NODE_ENV: joi
    .string()
    .valid(...Object.values(NodeEnv))
    .required(),
  PORT: joi.number().required(),

  DB_HOST: joi.string().required(),
  DB_PORT: joi.number().required(),
  DB_USERNAME: joi.string().required(),
  DB_PASSWORD: joi.string().required(),
  DB_DATABASE: joi.string().required(),
});
