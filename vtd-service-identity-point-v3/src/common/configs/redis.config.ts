import {
  RedisModuleAsyncOptions,
  RedisModuleOptions,
} from '@liaoliaots/nestjs-redis';
import { ConfigService } from '@nestjs/config';
import { AppConfig, appConfig } from './app.config';

export enum RedisNamespace {
  MASTER_NS = 'MASTER_NS',
  SLAVE_NS = 'SLAVE_NS',
}

export const redisConfig: RedisModuleAsyncOptions = {
  inject: [ConfigService],
  useFactory: (configSer: ConfigService<AppConfig>) => {
    const redisPassword = configSer.get('redis.standAlone.password');
    const redisHost = configSer.get('redis.standAlone.host');
    const redisPort = configSer.get('redis.standAlone.port');
    const redisCloudHost = configSer.get('redis.cloud.host');
    const redisCloudPort = configSer.get('redis.cloud.port');
    const redisCloudPassword = configSer.get('redis.cloud.password');
    const redisCloudCaPem = configSer.get('redis.cloud.caPem');
    const usingCloud = configSer.get('redis.usingRedisCloud');

    let redisConfig: RedisModuleOptions = {
      readyLog: true,
      errorLog: true,
      closeClient: true,
    };

    if (usingCloud) {
      redisConfig = {
        ...redisConfig,
        config: {
          name: 'default',
          host: redisCloudHost,
          port: Number(redisCloudPort),
          password: redisCloudPassword,
          tls: {
            ca: redisCloudCaPem,
            rejectUnauthorized: true, // Enforces server certificate validation
          },
        },
        // config: [
        //   { role: 'master', namespace: RedisNamespace.MASTER_NS },
        //   { role: 'slave', namespace: RedisNamespace.SLAVE_NS },
        // ],
      };
    } else {
      redisConfig = {
        ...redisConfig,
        config: {
          name: 'default',
          host: redisHost,
          port: Number(redisPort),
          password: redisPassword,
        },
        // config: [
        //   { role: 'master', namespace: RedisNamespace.MASTER_NS },
        //   { role: 'slave', namespace: RedisNamespace.SLAVE_NS },
        // ],
      };
    }

    return redisConfig;
  },
};
