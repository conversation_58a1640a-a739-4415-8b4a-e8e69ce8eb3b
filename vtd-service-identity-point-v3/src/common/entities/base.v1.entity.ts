import dayjs from 'dayjs';
import {
  BeforeInsert,
  BeforeUpdate,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { TIME_ZONE_HCM } from '../constants/app.constant';

export class BaseV1EntityWithoutVersion {
  @CreateDateColumn({ name: 'created_date', type: 'timestamp' })
  createdDate: Date;

  @UpdateDateColumn({ name: 'updated_date', type: 'timestamp' })
  updatedDate: Date;

  @BeforeInsert()
  beforeInsert() {
    const now = new Date(
      dayjs().tz(TIME_ZONE_HCM).format('YYYY-MM-DDTHH:mm:ss+00:00'),
    );
    this.createdDate = now;
    this.updatedDate = now;
  }

  @BeforeUpdate()
  beforeUpdate() {
    const now = new Date(
      dayjs().tz(TIME_ZONE_HCM).format('YYYY-MM-DDTHH:mm:ss+00:00'),
    );
    this.updatedDate = now;
  }
}

export class BaseEntityOnlyCreatedAt {
  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;
}
