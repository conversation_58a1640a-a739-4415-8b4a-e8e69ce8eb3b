import { createHmac } from 'crypto';
// import * as Buffer from 'buffer';
import { parsePhoneNumber } from 'awesome-phonenumber';
import clone from 'clone';
import dayjs from 'dayjs';
import {
  FORMAT_YYYY_MM_DD_HH_MM_SS,
  TIME_FORMAT_CRM,
  TIME_ZONE_HCM,
  TIME_ZONE_UTC,
} from './constants/app.constant';

import moment from 'moment-timezone';
import { HistoryPointType } from 'src/ddx_reset_gift_point/constant/index.enum';

export const genHmac = (secret: string, data: string, algorithm = 'sha256') => {
  const hmac = createHmac(algorithm, secret).update(data);
  return hmac.digest('hex');
};

export const deepClone = <T extends Record<any, any>>(obj: T): T => {
  return clone(obj);
};

export const getNowAtTimeZoneHcm = () => {
  // return new Date(
  //   dayjs().tz(TIME_ZONE).format('YYYY-MM-DDTHH:mm:ss.SSS+00:00'),
  // );
  return dayjs()
    .tz(TIME_ZONE_HCM)
    .format('YYYY-MM-DDTHH:mm:ss') as unknown as Date;
};

export const getNowAtTimeHcm = () => {
  return dayjs().tz(TIME_ZONE_HCM) as unknown as Date;
};

export function getPhoneE164(
  phone: string,
  regionCode = 'VN',
): string | undefined {
  const phoneNumber = parsePhoneNumber(phone, { regionCode });

  return phoneNumber.possible ? phoneNumber.number.e164 : undefined;
}

export function encodeStringToBase64(str: string): string {
  const buffer = Buffer.from(str);
  return buffer.toString('base64');
}

export function decodeBase64ToString(base64Str: string): string {
  const buffer = Buffer.from(base64Str, 'base64');
  return buffer.toString();
}

export function isNullOrUndefined(obj: any) {
  if (typeof obj === 'undefined' || obj === null) return true;
  return false;
}

export const camelToSnakeCase = (str: string) => {
  return (
    str[0].toLowerCase() +
    str.slice(1).replace(/[A-Z]/g, (letter) => `_${letter.toLowerCase()}`)
  );
};

//
export const getMomentTime = (): Date => {
  return moment.tz('Asia/Ho_Chi_Minh').startOf('hour').toDate();
};

export const getCurrentTimestampInTimezone = (timezone = TIME_ZONE_HCM) => {
  return dayjs().tz(timezone);
};
export const getCurrentTimestampInTimezoneInHour = (
  timezone = TIME_ZONE_HCM,
  format = FORMAT_YYYY_MM_DD_HH_MM_SS,
) => {
  return dayjs().tz(timezone).startOf('hour').format(format);
};

export const makeDateIsDateAtTimeHcm = (date: string | number | Date) => {
  return dayjs.tz(date, TIME_ZONE_HCM).toDate();
};

export const makeDateIsDateAtHCM = (date: string | number | Date) => {
  const cleanDate = typeof date === 'string' ? date.replace(/Z$/, '') : date;
  return dayjs(cleanDate).tz(TIME_ZONE_HCM, true).format();
};

export const roundToNearestHour = (date: string | number | Date) => {
  const timezone = TIME_ZONE_HCM;
  const format = FORMAT_YYYY_MM_DD_HH_MM_SS;
  return dayjs(date)
    .tz(timezone)
    .startOf('hour')
    .format(format) as unknown as Date;
};

export const subtractDurationFromNowAndRoundHour = (
  duration: number,
  unit: dayjs.ManipulateType,
  timezone = TIME_ZONE_HCM,
  format = FORMAT_YYYY_MM_DD_HH_MM_SS,
) => {
  return dayjs()
    .tz(timezone)
    .subtract(duration, unit)
    .startOf('hour')
    .format(format);
};
// make Date is Date at time hcm and round to nearest hour
export const makeDateIsDateAndRoundToNearestAtTimeHcm = (
  date: string | number | Date,
) => {
  const cleanDate = typeof date === 'string' ? date.replace(/Z$/, '') : date;
  return dayjs(cleanDate)
    .tz(TIME_ZONE_HCM, true)
    .startOf('hour')
    .format(FORMAT_YYYY_MM_DD_HH_MM_SS) as unknown as Date;
};
// make Date is Date at time hcm and round to nearest hour
export const makeDateIsDateAndRoundAtTimeHcm = (
  date: string | number | Date,
) => {
  return dayjs
    .tz(date)
    .startOf('hour')
    .format(FORMAT_YYYY_MM_DD_HH_MM_SS) as unknown as Date;
};
//

export const subtractDurationFromNow = (
  duration: number,
  unit: dayjs.ManipulateType,
  timezone = TIME_ZONE_HCM,
  format = FORMAT_YYYY_MM_DD_HH_MM_SS,
) => {
  return dayjs().tz(timezone).subtract(duration, unit).format(format);
};

// add day from day
export const addDurationFromDay = (
  date: Date,
  duration: number,
  unit: dayjs.ManipulateType,
  timezone = TIME_ZONE_HCM,
  format = FORMAT_YYYY_MM_DD_HH_MM_SS,
) => {
  const dateResult = dayjs
    .tz(date, timezone)
    .add(duration, unit)
    .format(format);
  return dateResult as unknown as Date;
};
//
export const subtractDaysAndFormat = (days: number, format: string) => {
  return getCurrentTimestampInTimezone(TIME_ZONE_HCM)
    .subtract(days, 'days')
    .startOf('hour')
    .format(format);
};

/**
 * Get the start of the month from a given date
 */
export const getStartOfMonth = (date: Date): Date => {
  return dayjs(date).startOf('month').toDate();
};

/**
 * Get the end of the month from a given date
 */
export const getEndOfMonth = (date: Date): Date => {
  return dayjs(date).endOf('month').toDate();
};
/**
 * Helper function to calculate the start and end of the month for a given date
 * @param date - The date to calculate the month range for
 * @returns An object containing startOfMonth and endOfMonth
 */
export const getStartAndEndOfMonth = (
  date: Date,
): { startOfMonth: Date; endOfMonth: Date } => {
  const startOfMonth = dayjs(date).startOf('month').toDate();
  const endOfMonth = dayjs(date).endOf('month').toDate();

  return { startOfMonth, endOfMonth };
};

/**
 * Helper function to calculate the start and end of the month for a given date range
 * @param dateFrom - Start date
 * @param dateTo - End date
 * @returns Object containing startOfMonth and endOfMonth
 */
export const getStartAndEndOfMonthBetween = (
  dateFrom: Date,
  dateTo: Date,
): { startOfMonth: Date; endOfMonth: Date } => {
  const startOfMonth = dayjs(dateFrom).startOf('month').toDate();
  const endOfMonth = dayjs(dateTo).endOf('month').toDate();

  return { startOfMonth, endOfMonth };
};

/**
 * Calculate freeze start and end times using dayjs
 * @param date - The reference date for freeze calculation
 * @param durationInHours - Duration of freeze period in hours (default: 1 hour)
 * @returns Object containing startFreeze and endFreeze as Date objects
 */
export const calculateFreezeTime = (
  date: Date,
  durationInHours = 1,
): { startFreeze: Date; endFreeze: Date } => {
  const startFreeze = dayjs(date).toDate();
  const endFreeze = dayjs(date).add(durationInHours, 'hour').toDate();

  return { startFreeze, endFreeze };
};

export const addDaysFromToday = (
  days: number,
  timezone: string = TIME_ZONE_HCM,
  format: string = FORMAT_YYYY_MM_DD_HH_MM_SS,
) => {
  return dayjs().tz(timezone).add(days, 'day').format(format);
};

/**
 * Compare date with current date in timezone
 *
 * @param date Date to compare
 * @param timezone Default Asia/Ho_Chi_Minh
 * @return -1 if date before current date, 0 if date equal current date and 1 if date after current date
 */
export const compareHourWithCurrentHourInTimezone = (
  date: string | number | Date,
  timezone = TIME_ZONE_HCM,
) => {
  const currentHour = getCurrentTimestampInTimezone(timezone).startOf('hour');
  const hourToCompare = dayjs(date).tz(timezone).startOf('hour');

  if (hourToCompare.isBefore(currentHour)) {
    return -1;
  }
  if (hourToCompare.isAfter(currentHour)) {
    return 1;
  }

  return 0;
};

// compare time with time
export const compareDateWithSDateInTimeZone = (
  date: string | number | Date,
  dateCompare: string | number | Date,
  timezone = TIME_ZONE_HCM,
) => {
  const dateConvert = dayjs(date).tz(timezone);
  const dateCompareConvert = dayjs(dateCompare).tz(timezone);

  if (dateConvert.isBefore(dateCompareConvert)) {
    return -1;
  }
  if (dateConvert.isAfter(dateCompareConvert)) {
    return 1;
  }

  return 0;
};

export const convertDateToDateInTimeZoneAndFormatToDate = (
  date: string | number | Date,
  timezone: string = TIME_ZONE_HCM,
  format: string = TIME_FORMAT_CRM,
): Date => {
  if (!date) {
    return null;
  }
  //return dayjs(date).tz(TIME_ZONE) as unknown as Date;
  return dayjs(date)
    .tz(timezone)
    .format(format ?? TIME_FORMAT_CRM) as unknown as Date;
};
export const convertDateToDateInTimeZoneAndFormatToDateNoTZ = (
  date: string | number | Date,
  format: string = TIME_FORMAT_CRM,
): Date => {
  if (!date) {
    return null;
  }
  //return dayjs(date).tz(TIME_ZONE) as unknown as Date;
  // return dayjs(date).format(format ?? TIME_FORMAT_CRM) as unknown as Date;
  const converted = dayjs(date).tz(TIME_ZONE_HCM).format(format);
  return dayjs(converted, format).toDate();
};
//
export const safeJsonParse = (data: any) => {
  if (typeof data === 'string') {
    try {
      return JSON.parse(data);
    } catch (error) {
      console.error('❌ JSON Parse Error:', error);
      return null;
    }
  }
  return data;
};

export const safeJsonStringify = (data: any) => {
  if (typeof data !== 'string') {
    try {
      return JSON.stringify(data);
    } catch (error) {
      console.error('❌ JSON Stringify Error:', error);
      return '';
    }
  }

  return data;
};

export const convertErrorToStringifyAbleObject = (err: unknown) => {
  if (err instanceof Error) {
    return {
      name: err.name,
      message: err.message,
      stack: err.stack,
      cause: (err as any).cause,
    };
  }

  return err;
};

export const getDateByFormat = (
  date: Date,
  timezone = TIME_ZONE_HCM,
  format = FORMAT_YYYY_MM_DD_HH_MM_SS,
) => {
  return dayjs(date).tz(timezone).format(format);
};

export const get365DaysAfterDateByFormat = (
  date: Date,
  timezone = TIME_ZONE_HCM,
  format = FORMAT_YYYY_MM_DD_HH_MM_SS,
) => {
  return dayjs(date).tz(timezone).add(365, 'day').format(format);
};

export const calculateMonthRange = (
  month: number,
  year: number,
): { startOfMonth: string; endOfMonth: string } => {
  const startOfMonth = dayjs(`${year}-${month}-01`)
    .tz(TIME_ZONE_HCM)
    .startOf('month')
    .format(FORMAT_YYYY_MM_DD_HH_MM_SS);
  const endOfMonth = dayjs(`${year}-${month}-01`)
    .tz(TIME_ZONE_HCM)
    .endOf('month')
    .format(FORMAT_YYYY_MM_DD_HH_MM_SS);

  return { startOfMonth, endOfMonth };
};

export const mapHistoryPointType = (
  type: HistoryPointType,
  point: number,
): string => {
  if (type === HistoryPointType.ADD_POINT) return 'add_point';
  if (type === HistoryPointType.SPEND_POINT) return 'spend_point';
  if (type === HistoryPointType.GIFT)
    return point >= 0 ? 'add_point' : 'spend_point';
  return 'unknown';
};

export const parseDateFromParam = (param: string) => {
  // Chỉ decode nếu có ký tự mã hóa URL
  const decoded = param.includes('%') ? decodeURIComponent(param) : param;
  return decoded;
};

export const sortByAttribute = <T, K extends keyof T>(
  list: T[],
  attribute: K,
  ascending = true,
): T[] => {
  return list.sort((a, b) => {
    const valueA = a[attribute] ?? Infinity;
    const valueB = b[attribute] ?? Infinity;

    if (valueA < valueB) return ascending ? -1 : 1;
    if (valueA > valueB) return ascending ? 1 : -1;
    return 0;
  });
};

export const removeDuplicateInArray = <T>(array: T[]): T[] => [
  ...new Set(array),
];

export function roundIfFloatToFixed(value: number, digits = 2): number {
  return Number.isInteger(value) ? value : parseFloat(value.toFixed(digits));
}

export const randomTransactionExternalId = () => {
  const max = 999;
  const min = 1;
  const time = dayjs().valueOf();
  const pre = time.toString(16);
  //const suffix = Math.floor(Math.random() * (max - min) + min);
  const suffix = Math.random().toString(36).substr(2, 5);
  return `${pre}_${suffix}`;
};
