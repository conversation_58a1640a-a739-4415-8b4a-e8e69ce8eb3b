FROM --platform=linux/amd64 node:18-alpine as development

ARG GIT_USER_NAME
ARG GIT_PAT

WORKDIR /usr/src/app
COPY package.json ./
# COPY yarn.lock ./
RUN apk update && apk add git
RUN apk add --update --no-cache \
    make \
    g++ \
    jpeg-dev \
    cairo-dev \
    giflib-dev \
    pango-dev

RUN git config --global url."https://$GIT_USER_NAME:$<EMAIL>/bilisoftware/vtd-common-v3".insteadOf "https://github.com/bilisoftware/vtd-common-v3"
RUN git config --global url."https://$GIT_USER_NAME:$<EMAIL>/bilisoftware/nestjs-utility".insteadOf "https://github.com/bilisoftware/nestjs-utility"
RUN yarn
COPY . ./
RUN yarn build

EXPOSE 5000
CMD ["yarn", "start:prod"]
