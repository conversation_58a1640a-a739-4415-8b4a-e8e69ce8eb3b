import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { objectify, cluster } from 'radash';
import {
  NotiDisplayTemplateType,
  PushNotiKafkaDto,
  PushNotiKafkaDtoVersion,
  FeatureNoti,
} from 'vtd-common-v3';
import { performance } from 'perf_hooks';
import dayjs from 'dayjs';
import _ from 'lodash';
import { In, IsNull, Not } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { GlobalConfig } from '../../common/config/global.config';
import {
  TIME_FORMAT_CRM,
  TIME_ZONE_HCM,
} from '../../common/constants/index.constant';
import {
  getNowAtTimeZoneHcm,
  randomTransactionExternalId,
} from '../../common/utils';
import { HistoryPoint } from '../../user/entities/history-point.entity';
import { User } from '../../user/entities/users.entity';
import { HistoryPointRepository } from '../../user/repositories/history-point.repository';
import { UserSessionRepository } from '../../user/repositories/user-session.repository';
import { UserRepository } from '../../user/repositories/user.repository';
import {
  CRM_CODE,
  CRM_CODE_2025,
  HistoryPointType,
  IListCrmType,
  IListCrmType2025,
  NUM_BATCHING,
  RANK_LAST_SCAN,
  RANK_TIER_POINT,
  UPDATE_RANK_DAY,
  USER_RANK,
  USER_RANK_NUM,
} from '../constants';
import {
  RankingUserByPaginateDto,
  RankingUserDto,
  RankingUser2025Dto,
} from '../dtos/ranking-user.dto';
import { CrmTransactionType } from '../entities/crm-transaction-type.entity';
import { OutboxMessage } from '../entities/outbox-message.entity';
import {
  NotificationUserFirebaseStatus,
  NotificationUserStatus,
} from '../enums/notification-user.enum';
import {
  CallType,
  OutboxMessageStatus,
  SyncProvider,
  SyncType,
} from '../enums/outbox-message.enum';
import { PushNotificationRequest } from '../interfaces/noti.interface';
import { CrmTransactionTypeRepository } from '../repositories/crm-transaction-type.repository';
import { NotificationUserRepository } from '../repositories/notification-user.repository';
import { OutboxMessageRepository } from '../repositories/outbox-message.repository';
import { CRON_JOB_OFFSET_PROCESSED_TYPE } from '../../cron-job-offset-processed/constants';
import { CronJobOffsetProcessedEntity } from '../../cron-job-offset-processed/entities/cron-job-offset-processed.entity';
import { CronJobOffsetProcessedRepository } from '../../cron-job-offset-processed/repositories/cron-job-offset-processed.repository';
import { WhOutboxMessageSyncData3rdServiceRequestData } from '../interfaces/vita-java.interface';
import {
  JavaV4WhSyncTo3rdServiceCode,
  JavaV4WhSyncTo3rdServiceDestination,
} from '../constants/java_v4.constant';

@Injectable()
export class UserRankingService {
  private crmSyncUsingWh = false;

  constructor(
    private userRepo: UserRepository,
    private historyPoint: HistoryPointRepository,
    private crmTransactionTypeRepo: CrmTransactionTypeRepository,
    private notificationUserRepo: NotificationUserRepository,
    private userSessionRepo: UserSessionRepository,
    private httpService: HttpService,
    private configService: ConfigService<GlobalConfig>,
    private outboxMessageRepo: OutboxMessageRepository,
    private cronJobOffsetProcessedRepo: CronJobOffsetProcessedRepository,
  ) {
    this.crmSyncUsingWh = this.configService.get('crm.options.syncUsingWh');
  }
  async rankingUser(query: RankingUserDto) {
    console.log('ranking');
    const { numBatching } = query;
    const numUser = await this.userRepo.count();

    await this.batchingUpdateAlleUser(numBatching, numUser);
    return {
      message: 'migrating...',
    };
  }

  async rankingUserVersion2(query: RankingUserDto) {
    console.log('ranking');
    const { numBatching, limit } = query;
    const [
      crmTypeTierTitan,
      crmTypeTierGold,
      crmTypeResetTier,
      crmTypeResetPoint,
    ] = await Promise.all([
      this.crmTransactionTypeRepo.findOneBy({
        code: CRM_CODE.RESET_TIER_TITAN_180_2023,
      }),
      this.crmTransactionTypeRepo.findOneBy({
        code: CRM_CODE.RESET_TIER_GOLD_180_2023,
      }),
      this.crmTransactionTypeRepo.findOneBy({
        code: CRM_CODE.RESET_TIER_365_2023,
      }),
      this.crmTransactionTypeRepo.findOneBy({
        code: CRM_CODE.RESET_POINT_365_2023,
      }),
    ]);
    const listCrmType: IListCrmType = {
      crmTypeTierTitan,
      crmTypeTierGold,
      crmTypeResetTier,
      crmTypeResetPoint,
    };
    let cronJobOffsetProcessed = await this.cronJobOffsetProcessedRepo.findOne({
      where: {
        type: CRON_JOB_OFFSET_PROCESSED_TYPE.USER_RANKING,
      },
    });
    let offset = 0;
    if (cronJobOffsetProcessed) {
      offset = cronJobOffsetProcessed.offset_number;
    } else {
      cronJobOffsetProcessed = await this.cronJobOffsetProcessedRepo.save({
        type: CRON_JOB_OFFSET_PROCESSED_TYPE.USER_RANKING,
      });
    }

    offset = Number(offset);
    let index = offset;
    let totalUsersNeedProcesed = -1;
    for (; index < limit + offset; index += Number(numBatching)) {
      //console.log('index');
      //console.log(index);
      try {
        const begin = performance.now();
        totalUsersNeedProcesed = await this.updateRankingUsers(
          index,
          numBatching,
          listCrmType,
        );
        const after = performance.now();
        console.log(
          `Call to updateRankingUsers took ${after - begin} milliseconds.`,
        );
        console.log('');
        if (0 == totalUsersNeedProcesed) {
          break;
        }
      } catch (error) {
        console.log(error);
        break;
      }
    }
    //console.log('index');
    //console.log(index);
    cronJobOffsetProcessed.offset_number = index;
    //console.log('cronJobOffsetProcessed');
    //console.log(cronJobOffsetProcessed);
    if (0 == totalUsersNeedProcesed) {
      cronJobOffsetProcessed.offset_number = 0;
    }
    //console.log('cronJobOffsetProcessed');
    //console.log(cronJobOffsetProcessed);
    await this.cronJobOffsetProcessedRepo.save(cronJobOffsetProcessed);

    return {
      message: 'done...',
    };
  }

  async rankingUserVersion2025(query: RankingUser2025Dto) {
    console.log('ranking 2025');
    const { numBatching, limit } = query;
    const arrCrmTransTypeCodes = [
      CRM_CODE_2025.RESET_TIER_PLATINUM_180_MEMBER_2025,
      CRM_CODE_2025.RESET_TIER_PLATINUM_180_TITAN_2025,
      CRM_CODE_2025.RESET_TIER_PLATINUM_180_GOLD_2025,
      CRM_CODE_2025.RESET_TIER_GOLD_180_MEMBER_2025,
      CRM_CODE_2025.RESET_TIER_GOLD_180_TITAN_2025,
      CRM_CODE_2025.RESET_TIER_TITAN_180_MEMBER_2025,
      CRM_CODE_2025.RESET_POINT_365_2023,
    ];
    const crmTransTypes = await this.crmTransactionTypeRepo.findBy({
      code: In(arrCrmTransTypeCodes),
    });
    if (!crmTransTypes || crmTransTypes.length != arrCrmTransTypeCodes.length) {
      console.log('load code crm trans type failed');
      return;
    }
    const mapCrmTransTypesByCode = objectify(crmTransTypes, (f) => f.code);
    const listCrmType: IListCrmType2025 = {
      crmTypeFromTierPlatinumToTierGold:
        mapCrmTransTypesByCode[CRM_CODE_2025.RESET_TIER_PLATINUM_180_GOLD_2025],
      crmTypeFromTierPlatinumToTierTitan:
        mapCrmTransTypesByCode[
          CRM_CODE_2025.RESET_TIER_PLATINUM_180_TITAN_2025
        ],
      crmTypeFromTierPlatinumToTierMember:
        mapCrmTransTypesByCode[
          CRM_CODE_2025.RESET_TIER_PLATINUM_180_MEMBER_2025
        ],
      crmTypeFromTierGoldToTierTitan:
        mapCrmTransTypesByCode[CRM_CODE_2025.RESET_TIER_GOLD_180_TITAN_2025],
      crmTypeFromTierGoldToTierMember:
        mapCrmTransTypesByCode[CRM_CODE_2025.RESET_TIER_GOLD_180_MEMBER_2025],
      crmTypeFromTierTitanToTierMember:
        mapCrmTransTypesByCode[CRM_CODE_2025.RESET_TIER_TITAN_180_MEMBER_2025],
      crmTypeResetPoint:
        mapCrmTransTypesByCode[CRM_CODE_2025.RESET_POINT_365_2023],
    };
    const [usersDowngradeRanking] = await Promise.all([
      this.userRepo.getListUsersProcessDowngradeRankingVersion2025(
        UPDATE_RANK_DAY,
        limit,
      ),
      /*this.userRepo.getListUsersProcessResetGiftPointVersion2025(
        RANK_LAST_SCAN,
        limit,
      ),*/
    ]);
    if (usersDowngradeRanking && usersDowngradeRanking.length) {
      const usersDowngradeRankingSplitByBatch: any[] = cluster(
        usersDowngradeRanking,
        numBatching,
      );
      //console.log('usersDowngradeRankingSplitByBatch');
      //console.log(usersDowngradeRankingSplitByBatch);
      for (const users of usersDowngradeRankingSplitByBatch) {
        try {
          const begin = performance.now();
          await this.processUsersDowngradeRanking(users, listCrmType);
          const after = performance.now();
          console.log(
            `Call to processUsersDowngradeRanking took ${
              after - begin
            } milliseconds.`,
          );
          console.log('');
        } catch (error) {
          console.log(`Call to processUsersDowngradeRanking error.`);
          console.log(error);
        }
      }
    }

    /*if (userResetGiftPoint && userResetGiftPoint.length) {
      const userResetGiftPointSplitByBatch: any[] = cluster(
        userResetGiftPoint,
        numBatching,
      );
      console.log('userResetGiftPointSplitByBatch');
      console.log(userResetGiftPointSplitByBatch);
      for (const users of userResetGiftPointSplitByBatch) {
        try {
          const begin = performance.now();
          await this.processUsersResetGiftPoint(users, listCrmType);
          const after = performance.now();
          console.log(
            `Call to processUsersResetGiftPoint took ${
              after - begin
            } milliseconds.`,
          );
          console.log('');
        } catch (error) {
          console.log(`Call to processUsersResetGiftPoint error.`);
          console.log(error);
        }
      }
    }*/

    return {
      message: 'done...',
    };
  }

  async rankingUserByPage(body: RankingUserByPaginateDto) {
    const { numBatching, pages } = body;
    const [
      crmTypeTierTitan,
      crmTypeTierGold,
      crmTypeResetTier,
      crmTypeResetPoint,
    ] = await Promise.all([
      this.crmTransactionTypeRepo.findOneBy({
        code: CRM_CODE.RESET_TIER_TITAN_180_2023,
      }),
      this.crmTransactionTypeRepo.findOneBy({
        code: CRM_CODE.RESET_TIER_GOLD_180_2023,
      }),
      this.crmTransactionTypeRepo.findOneBy({
        code: CRM_CODE.RESET_TIER_365_2023,
      }),
      this.crmTransactionTypeRepo.findOneBy({
        code: CRM_CODE.RESET_POINT_365_2023,
      }),
    ]);
    const listCrmType: IListCrmType = {
      crmTypeTierTitan,
      crmTypeTierGold,
      crmTypeResetTier,
      crmTypeResetPoint,
    };
    for (let i = 0; i < pages.length; i++) {
      this.batchingUpdatePageUser(numBatching, pages[i], listCrmType);
    }
    return {
      message: 'migrating...',
    };
  }

  async batchingUpdateAlleUser(numBatching: number, numUser: number) {
    console.time('check');
    const [
      crmTypeTierTitan,
      crmTypeTierGold,
      crmTypeResetTier,
      crmTypeResetPoint,
    ] = await Promise.all([
      this.crmTransactionTypeRepo.findOneBy({
        code: CRM_CODE.RESET_TIER_TITAN_180_2023,
      }),
      this.crmTransactionTypeRepo.findOneBy({
        code: CRM_CODE.RESET_TIER_GOLD_180_2023,
      }),
      this.crmTransactionTypeRepo.findOneBy({
        code: CRM_CODE.RESET_TIER_365_2023,
      }),
      this.crmTransactionTypeRepo.findOneBy({
        code: CRM_CODE.RESET_POINT_365_2023,
      }),
    ]);
    const listCrmType: IListCrmType = {
      crmTypeTierTitan,
      crmTypeTierGold,
      crmTypeResetTier,
      crmTypeResetPoint,
    };
    // console.log(listCrmType);
    for (let i = 0; i <= Math.floor(numUser / numBatching); i++) {
      await this.batchingUpdatePageUser(numBatching, i, listCrmType);
      // console.log(i);
      // await sleep(2000);
    }
    console.timeEnd('check');
  }

  async batchingUpdatePageUser(
    numBatching: number,
    page: number,
    listCrmType: IListCrmType,
  ) {
    try {
      const skip = page * numBatching;
      await this.updateRankingUsers(skip, numBatching, listCrmType);
    } catch (e) {
      // this.batchingUpdatePageUserRetry(numBatching, page, 2, listCrmType);
      console.log('error: ', e);
    }
  }

  async batchingUpdatePageUserRetry(
    numBatching: number,
    page: number,
    retry: number,
    listCrmType: IListCrmType,
  ) {
    try {
      const skip = page * numBatching;
      await this.updateRankingUsers(skip, numBatching, listCrmType);
    } catch (e) {
      if (retry <= 0) {
        console.log('error page:', page, ' numBatching: ', numBatching);
        console.log('error: ', e);
      } else {
        console.log('retry: ', page, retry);

        this.batchingUpdatePageUserRetry(
          numBatching,
          page,
          retry - 1,
          listCrmType,
        );
      }
    }
  }

  @Transactional()
  async updateRankingUsers(
    skip: number,
    numBatching: number,
    listCrmType: IListCrmType,
  ) {
    //const skip = page * numBatching;
    let begin = performance.now();
    const users = await this.userRepo
      .createQueryBuilder('user')
      /*.where('user.id = :id', {
        id: 132593,
      })*/
      .orderBy('user.createdDate', 'ASC')
      .take(numBatching)
      .skip(skip)
      .getMany();
    let after = performance.now();
    console.log(`Call to get users took ${after - begin} milliseconds.`);

    const outboxMessageRepo: OutboxMessage[] = [];
    const listHistoryPoint: HistoryPoint[] = [];
    const newUsers: User[] = [];
    if (!users || !users.length) {
      return 0;
    }
    const totalUsersNeedProcesed = users.length;
    const userIds: any[] = [];
    const userIdsProcess365: any[] = [];
    const userIdsProcess180: any[] = [];
    // users.forEach((user) => {
    begin = performance.now();
    for (const user of users) {
      userIds.push(user.id);
      const rankLastScan = this.checkLastScan(user.lastScanDate);
      const isUpgradeRankIn180Day = this.isUpgradeRankIn180Day(
        user.tierRankUpdatedDate,
      );

      if (rankLastScan) {
        const newUser = {
          ...user,
          tierPoint: 0,
          tierCode: USER_RANK.MEMBER,
          giftPoint: 0,
          totalPoint: 0,
        };

        // if (!_.isEqual(newUser, { ...user })) {
        if (
          user.tierCode !== newUser.tierCode ||
          user.tierPoint !== newUser.tierPoint ||
          user.giftPoint !== newUser.giftPoint ||
          user.totalPoint !== newUser.totalPoint
        ) {
          console.log(user.phoneNumber);
          // crm
          const transactionExternalId = randomTransactionExternalId();
          const transactionExternalId1 = randomTransactionExternalId();
          const resetTierTList: any[] = [];
          if (user.tierPoint) {
            listHistoryPoint.push(
              this.historyPoint.create({
                actionType: listCrmType?.crmTypeResetTier?.code,
                type: listCrmType?.crmTypeResetTier?.code,
                customerId: user.id,
                customerName: user.firstName + ' ' + user.lastName,
                status: 'SUCCESS',
                transactionDate: dayjs()
                  .tz(TIME_ZONE_HCM)
                  .format(TIME_FORMAT_CRM),
                transactionExternalId: transactionExternalId,
                customerPhone: user.phoneNumber,
                tierPoint: user.tierPoint - newUser.tierPoint,
              }),
            );
            const request = {
              userId: user.id.toString(),
              Transaction_Date__c: dayjs()
                .tz(TIME_ZONE_HCM)
                .format(TIME_FORMAT_CRM),
              Campaign__c: listCrmType.crmTypeResetTier.campaignName,
              Level_Points__c: user.tierPoint - newUser.tierPoint,
              Redeem_Points__c: 0,
              Rule_Name__c: listCrmType.crmTypeResetTier.description,
              Transaction_External_ID__c: transactionExternalId,
              Transaction_Type__c: listCrmType.crmTypeResetTier.name,
              Type__c: 'Spending',
              Tier__c: newUser.tierCode,
              Earned_Tier_Point__c: user.tierPoint - newUser.tierPoint,
              //Earned_Tier_Point__c: 0,
              Tier_Points__c: newUser.tierPoint,
            };
            resetTierTList.push(request);
            /*outboxMessageRepo.push(
              this.outboxMessageRepo.create({
                provider: SyncProvider.CRM,
                callType: CallType.SYNC,
                syncType: SyncType.IMMEDIATE,
                request: JSON.stringify([request]),
                status: OutboxMessageStatus.PROCESSING,
                createdDate: dayjs().format('YYYY-MM-DDTHH:mm:ss+00:00'),
                retryNumber: 0,
              }),
            );*/
          }

          if (user.giftPoint) {
            listHistoryPoint.push(
              this.historyPoint.create({
                actionType: listCrmType?.crmTypeResetPoint?.code,
                type: listCrmType?.crmTypeResetPoint?.code,
                customerId: user.id,
                customerName: user.firstName + ' ' + user.lastName,
                status: 'SUCCESS',
                transactionDate: dayjs()
                  .tz(TIME_ZONE_HCM)
                  .format(TIME_FORMAT_CRM),
                transactionExternalId: transactionExternalId1,
                customerPhone: user.phoneNumber,
                giftPoint: user.giftPoint,
              }),
            );
            const request1 = {
              userId: user.id.toString(),
              Transaction_Date__c: dayjs()
                .tz(TIME_ZONE_HCM)
                .format(TIME_FORMAT_CRM),
              Campaign__c: listCrmType.crmTypeResetPoint.campaignName,
              Level_Points__c: 0,
              Redeem_Points__c: user.giftPoint,
              Rule_Name__c: listCrmType.crmTypeResetPoint.description,
              Transaction_External_ID__c: transactionExternalId1,
              Transaction_Type__c: listCrmType.crmTypeResetPoint.name,
              Type__c: 'Spending',
              Tier__c: newUser.tierCode,
              Earned_Tier_Point__c: 0,
              Tier_Points__c: newUser.tierPoint,
            };
            resetTierTList.push(request1);

            /*outboxMessageRepo.push(
              this.outboxMessageRepo.create({
                provider: SyncProvider.CRM,
                callType: CallType.SYNC,
                syncType: SyncType.IMMEDIATE,
                request: JSON.stringify([request1]),
                status: OutboxMessageStatus.PROCESSING,
                createdDate: dayjs().format('YYYY-MM-DDTHH:mm:ss+00:00'),
                retryNumber: 0,
              }),
            );*/
          }
          //console.log('resetTierTList');
          //console.log(resetTierTList);
          if (resetTierTList.length) {
            userIdsProcess365.push(user.id);
            outboxMessageRepo.push(
              this.outboxMessageRepo.create({
                provider: SyncProvider.CRM,
                callType: CallType.SYNC,
                syncType: SyncType.IMMEDIATE,
                request: JSON.stringify(resetTierTList),
                status: OutboxMessageStatus.PROCESSING,
                createdDate: dayjs().format('YYYY-MM-DDTHH:mm:ss+00:00'),
                retryNumber: 0,
              }),
            );
          }

          newUsers.push(
            this.userRepo.create({
              ...newUser,
              tierRankUpdatedDate: getNowAtTimeZoneHcm(),
            }),
          );
          try {
            const useNotiV3 = this.configService.get('useNotiV3');
            if (useNotiV3) {
              const kafkaDto = new PushNotiKafkaDto({
                userIds: [user.id],
                version: PushNotiKafkaDtoVersion.V1,
                notiDisplayTemplateType:
                  NotiDisplayTemplateType.TIER_DOWN_365_2023,
                notiDisplayTemplateParams: {
                  tier_365_tier_down: 'MEMBER',
                },
                featureNoti: FeatureNoti.NOTI_USER_BIRTHDAY,
              });

              const outboxMsg = this.outboxMessageRepo.createPushNoti(kafkaDto);
              await this.outboxMessageRepo.save(outboxMsg);
            } else {
              await this.pushUpdateRankNotification(
                user.id,
                `GIẢM HẠNG VỀ MEMBER.`,
                `Về hạng Member sau 365 ngày không phát sinh giao dịch`,
                `"Về hạng Member sau 365 ngày không phát sinh giao dịch"`,
              );
            }
          } catch (e) {
            console.log(e);
          }
        }
      } else if (
        isUpgradeRankIn180Day &&
        user.tierCode !== USER_RANK.PLATINUM
      ) {
        if (
          Number(USER_RANK_NUM[user.tierCode]) - 1 >=
          Number(USER_RANK_NUM.MEMBER)
        ) {
          const indexOfRankUser = Object.values(USER_RANK_NUM).indexOf(
            (Number(USER_RANK_NUM[user.tierCode]) -
              1) as unknown as USER_RANK_NUM,
          );
          const newRank = Object.keys(USER_RANK_NUM)[indexOfRankUser];
          const newUser = {
            ...user,
            tierPoint: RANK_TIER_POINT[newRank],
            tierCode: newRank,
          };
          if (!_.isEqual(newUser, { ...user })) {
            userIdsProcess180.push(user.id);
            // crm
            const transactionExternalId = randomTransactionExternalId();
            let crmType: CrmTransactionType = {} as CrmTransactionType;
            if (user.tierCode === USER_RANK.GOLD) {
              crmType = listCrmType.crmTypeTierGold;
            } else if (user.tierCode === USER_RANK.TITAN) {
              crmType = listCrmType.crmTypeTierTitan;
            }
            listHistoryPoint.push(
              this.historyPoint.create({
                actionType: crmType?.code,
                type: crmType?.code,
                customerId: user.id,
                customerName: user.firstName + ' ' + user.lastName,
                status: 'SUCCESS',
                transactionDate: dayjs()
                  .tz(TIME_ZONE_HCM)
                  .format(TIME_FORMAT_CRM),
                transactionExternalId: transactionExternalId,
                customerPhone: user.phoneNumber,
                tierPoint: user.tierPoint - newUser.tierPoint,
              }),
            );
            const request = {
              userId: user.id.toString(),
              Transaction_Date__c: dayjs()
                .tz(TIME_ZONE_HCM)
                .format(TIME_FORMAT_CRM),
              Campaign__c: crmType?.campaignName,
              Level_Points__c: user.tierPoint - newUser.tierPoint,
              Redeem_Points__c: 0,
              Rule_Name__c: crmType?.description,
              Transaction_External_ID__c: transactionExternalId,
              Transaction_Type__c: crmType?.name,
              Type__c: 'Spending',
              Tier__c: newUser.tierCode,
              Earned_Tier_Point__c: user.tierPoint - newUser.tierPoint,
              Tier_Points__c: newUser.tierPoint,
            };
            outboxMessageRepo.push(
              this.outboxMessageRepo.create({
                provider: SyncProvider.CRM,
                callType: CallType.SYNC,
                syncType: SyncType.IMMEDIATE,
                request: JSON.stringify([request]),
                status: OutboxMessageStatus.PROCESSING,
                createdDate: dayjs().format('YYYY-MM-DDTHH:mm:ss+00:00'),
                retryNumber: 0,
              }),
            );
            newUsers.push(
              this.userRepo.create({
                ...newUser,
                tierRankUpdatedDate: getNowAtTimeZoneHcm(),
              }),
            );
            try {
              const useNotiV3 = this.configService.get('useNotiV3');
              if (useNotiV3) {
                const kafkaDto = new PushNotiKafkaDto({
                  userIds: [user.id],
                  version: PushNotiKafkaDtoVersion.V1,
                  notiDisplayTemplateType:
                    NotiDisplayTemplateType.TIER_DOWN_180_2023,
                  featureNoti: FeatureNoti.NOTI_USER_BIRTHDAY,
                });

                const outboxMsg =
                  this.outboxMessageRepo.createPushNoti(kafkaDto);
                await this.outboxMessageRepo.save(outboxMsg);
              } else {
                await this.pushUpdateRankNotification(
                  user.id,
                  `GIẢM HẠNG THÀNH VIÊN THEO CHÍNH SÁCH MỚI NĂM 2023.`,
                  `Bạn đã bị giảm hạng thành viên theo chính sách mới năm 2023. Hãy xem chính sách thành viên mới để biết...`,
                  `Bạn đã bị giảm hạng thành viên theo chính sách mới năm 2023. Hãy xem chính sách thành viên mới để biết thêm thông tin chi tiết nhé! (Xem lại hạng thành viên hiện tại để biết thêm thông tin chi tiết nhé!)`,
                );
              }
            } catch (err) {
              console.log(err);
            }
          }
        }
      }
    }
    after = performance.now();
    console.log(`Call to loop users took ${after - begin} milliseconds.`);
    // throw new Error();
    console.log('listHistoryPoint');
    console.log(listHistoryPoint);
    begin = performance.now();
    await Promise.all([
      this.outboxMessageRepo.save(outboxMessageRepo),
      this.historyPoint.save(listHistoryPoint),
      this.userRepo.save(newUsers),
    ]);
    after = performance.now();
    console.log(`Call to update users took ${after - begin} milliseconds.`);
    console.log(3);
    //console.log('userIds');
    //console.log(JSON.stringify(userIds));
    //console.log('userIdsProcess180');
    //console.log(JSON.stringify(userIdsProcess180));
    //console.log('userIdsProcess365');
    //console.log(JSON.stringify(userIdsProcess365));

    //throw new Error('Test');

    return totalUsersNeedProcesed;
  }

  @Transactional()
  private async processUsersDowngradeRanking(
    users: any[],
    listCrmType: IListCrmType2025,
  ) {
    console.log('processUsersDowngradeRanking');
    const outboxMessageRepo: OutboxMessage[] = [];
    const listHistoryPoint: HistoryPoint[] = [];
    let sqlUpdateTierCode = '';
    let sqlUpdateTierPoint = '';
    let sqlUpdateTotalPoint = '';
    const idsUserUpdate: any[] = [];
    for (const user of users) {
      console.log('user');
      console.log(user['id']);
      console.log(user['tier_code']);
      console.log(user['tier_point']);
      console.log(user['tier_rank_updated_date']);
      console.log(user['tier_point_added_after_up_rank']);
      const tierPointAddedAfterUpRank = user['tier_point_added_after_up_rank'];
      let tierPointAfterDownRank = 0;
      let tierCodeAfterDownRank = USER_RANK.MEMBER;
      let newTotalPoint = user['total_point'];
      const keysRank = Object.keys(RANK_TIER_POINT);
      const dataSyncWh: WhOutboxMessageSyncData3rdServiceRequestData = {
        authentication_token: '',
        data: {
          transactions: [],
        },
      };
      let userTriggerDownRank = false;
      const useNotiV3 = this.configService.get('useNotiV3');
      for (let index = 0; index < keysRank.length; index++) {
        const keyRank = keysRank[index];
        const tierPoint = RANK_TIER_POINT[keyRank];
        if (!tierPoint) {
          continue;
        }

        if (tierPointAddedAfterUpRank < tierPoint) {
          tierPointAfterDownRank = tierPoint - 1;
          tierCodeAfterDownRank = USER_RANK[keysRank[index - 1]];
          userTriggerDownRank = true;
          break;
        }
      }
      console.log('userTriggerDownRank');
      console.log(userTriggerDownRank);
      if (!userTriggerDownRank) {
        tierPointAfterDownRank = user['tier_point'];
        tierCodeAfterDownRank = user['tier_code'];
      }

      if (userTriggerDownRank) {
        newTotalPoint = user['gift_point'] + tierPointAfterDownRank;
        console.log('tierPointAfterDownRank');
        console.log(tierPointAfterDownRank);
        console.log('tierCodeAfterDownRank');
        console.log(tierCodeAfterDownRank);

        const transactionExternalId = randomTransactionExternalId();
        const crmType: CrmTransactionType =
          this.getCrmTransTypeFollowRule1802025(
            listCrmType,
            user['tier_code'],
            tierCodeAfterDownRank,
          );

        listHistoryPoint.push(
          this.historyPoint.create({
            actionType: crmType?.code,
            type: crmType?.code,
            customerId: user.id,
            customerName: user['first_name'] + ' ' + user['last_name'],
            status: 'SUCCESS',
            transactionDate: dayjs().tz(TIME_ZONE_HCM).format(TIME_FORMAT_CRM),
            transactionExternalId: transactionExternalId,
            customerPhone: user['phone_number'],
            tierPoint: user['tier_point'] - tierPointAfterDownRank,
          }),
        );
        const request = {
          userId: user.id.toString(),
          Transaction_Date__c: dayjs()
            .tz(TIME_ZONE_HCM)
            .format(TIME_FORMAT_CRM),
          Campaign__c: crmType?.campaignName,
          Level_Points__c: user['tier_point'] - tierPointAfterDownRank,
          Redeem_Points__c: 0,
          Rule_Name__c: crmType?.description,
          Transaction_External_ID__c: transactionExternalId,
          Transaction_Type__c: crmType?.name,
          Type__c: 'Spending',
          Tier__c: tierCodeAfterDownRank,
          Earned_Tier_Point__c: user['tier_point'] - tierPointAfterDownRank,
          Tier_Points__c: tierPointAfterDownRank,
        };
        if (this.crmSyncUsingWh) {
          dataSyncWh.data.transactions = [
            {
              code: JavaV4WhSyncTo3rdServiceCode.ADD_POINT,
              destination: JavaV4WhSyncTo3rdServiceDestination.SF,
              tListPayload: [request],
            },
          ];
          outboxMessageRepo.push(
            this.outboxMessageRepo.createSyncWhSyncData3rdService(dataSyncWh),
          );
        } else {
          outboxMessageRepo.push(
            this.outboxMessageRepo.create({
              provider: SyncProvider.CRM,
              callType: CallType.SYNC,
              syncType: SyncType.IMMEDIATE,
              request: JSON.stringify([request]),
              status: OutboxMessageStatus.PROCESSING,
              retryNumber: 0,
            }),
          );
        }
        if (useNotiV3) {
          const kafkaDto = new PushNotiKafkaDto({
            userIds: [user.id],
            version: PushNotiKafkaDtoVersion.V1,
            notiDisplayTemplateType: NotiDisplayTemplateType.TIER_DOWN_180_2025,
            featureNoti: FeatureNoti.NOTI_USER_BIRTHDAY,
          });

          outboxMessageRepo.push(
            this.outboxMessageRepo.createPushNoti(kafkaDto),
          );
        }
      }

      sqlUpdateTierCode += `
          WHEN id = ${user.id} THEN '${tierCodeAfterDownRank}'
        `;
      sqlUpdateTierPoint += `
          WHEN id = ${user.id} THEN ${tierPointAfterDownRank}
        `;
      sqlUpdateTotalPoint += `
          WHEN id = ${user.id} THEN ${newTotalPoint}
        `;

      idsUserUpdate.push(user.id);
    }
    //console.log('listHistoryPoint');
    //console.log(listHistoryPoint);
    //console.log('outboxMessageRepo');
    //console.log(outboxMessageRepo);
    const sqlUpdateUsers = `
        UPDATE users
        SET tier_code = CASE
          ${sqlUpdateTierCode}
          ELSE tier_code
        END,
        tier_point = CASE
          ${sqlUpdateTierPoint}
          ELSE tier_point
        END,
        total_point = CASE
          ${sqlUpdateTotalPoint}
          ELSE total_point
        END,
        tier_rank_updated_date = NOW(),
        tier_point_added_after_up_rank = 0
        WHERE id IN (${idsUserUpdate.join(',')})
      `;
    //console.log('sqlUpdateUsers');
    //console.log(sqlUpdateUsers);
    await Promise.all([
      this.historyPoint
        .createQueryBuilder()
        .insert()
        .values(listHistoryPoint)
        .execute(),
      this.outboxMessageRepo
        .createQueryBuilder()
        .insert()
        .values(outboxMessageRepo)
        .execute(),
      this.userRepo.query(sqlUpdateUsers),
    ]);
  }

  @Transactional()
  private async processUsersResetGiftPoint(
    users: any[],
    listCrmType: IListCrmType2025,
  ) {
    console.log('processUsersResetGiftPoint');
    const outboxMessageRepo: OutboxMessage[] = [];
    const listHistoryPoint: HistoryPoint[] = [];
    let sqlUpdateTotalPoint = '';
    const idsUserUpdate: any[] = [];
    const useNotiV3 = this.configService.get('useNotiV3');
    for (const user of users) {
      console.log('user');
      console.log(user);
      const newGiftPoint = 0;
      const newTotalPoint = user['tier_point'] + newGiftPoint;
      const dataSyncWh: WhOutboxMessageSyncData3rdServiceRequestData = {
        authentication_token: '',
        data: {
          transactions: [],
        },
      };
      console.log('newTotalPoint');
      console.log(newTotalPoint);

      const transactionExternalId = randomTransactionExternalId();
      const crmType: CrmTransactionType = listCrmType.crmTypeResetPoint;

      listHistoryPoint.push(
        this.historyPoint.create({
          actionType: crmType?.code,
          type: crmType?.code,
          customerId: user.id,
          customerName: user['first_name'] + ' ' + user['last_name'],
          status: 'SUCCESS',
          transactionDate: dayjs().tz(TIME_ZONE_HCM).format(TIME_FORMAT_CRM),
          transactionExternalId: transactionExternalId,
          customerPhone: user['phone_number'],
          giftPoint: user['gift_point'],
        }),
      );
      const request = {
        userId: user.id.toString(),
        Transaction_Date__c: dayjs().tz(TIME_ZONE_HCM).format(TIME_FORMAT_CRM),
        Campaign__c: crmType?.campaignName,
        Level_Points__c: 0,
        Redeem_Points__c: user['gift_point'],
        Rule_Name__c: crmType?.description,
        Transaction_External_ID__c: transactionExternalId,
        Transaction_Type__c: crmType?.name,
        Type__c: 'Spending',
        Tier__c: user['tier_code'],
        Earned_Tier_Point__c: 0,
        Tier_Points__c: user['tier_point'],
      };
      if (this.crmSyncUsingWh) {
        dataSyncWh.data.transactions = [
          {
            code: JavaV4WhSyncTo3rdServiceCode.ADD_POINT,
            destination: JavaV4WhSyncTo3rdServiceDestination.SF,
            payload: request,
          },
        ];
        outboxMessageRepo.push(
          this.outboxMessageRepo.createSyncWhSyncData3rdService(dataSyncWh),
        );
      } else {
        outboxMessageRepo.push(
          this.outboxMessageRepo.create({
            provider: SyncProvider.CRM,
            callType: CallType.SYNC,
            syncType: SyncType.IMMEDIATE,
            request: JSON.stringify([request]),
            status: OutboxMessageStatus.PROCESSING,
            retryNumber: 0,
          }),
        );
      }
      if (useNotiV3) {
        const kafkaDto = new PushNotiKafkaDto({
          userIds: [user.id],
          version: PushNotiKafkaDtoVersion.V1,
          notiDisplayTemplateType: NotiDisplayTemplateType.TIER_DOWN_365_2023,
          featureNoti: FeatureNoti.NOTI_USER_BIRTHDAY,
        });

        outboxMessageRepo.push(this.outboxMessageRepo.createPushNoti(kafkaDto));
      }

      sqlUpdateTotalPoint += `
        WHEN id = ${user.id} THEN ${newTotalPoint}
      `;
      idsUserUpdate.push(user.id);
    }
    //console.log('listHistoryPoint');
    //console.log(listHistoryPoint);
    //console.log('outboxMessageRepo');
    //console.log(outboxMessageRepo);
    const sqlUpdateUsers = `
        UPDATE users
        SET total_point = CASE
          ${sqlUpdateTotalPoint}
          ELSE total_point
        END,
        gift_point = 0
        WHERE id IN (${idsUserUpdate})
      `;
    //console.log('sqlUpdateUsers');
    //console.log(sqlUpdateUsers);
    await Promise.all([
      this.historyPoint
        .createQueryBuilder()
        .insert()
        .values(listHistoryPoint)
        .execute(),
      this.outboxMessageRepo
        .createQueryBuilder()
        .insert()
        .values(outboxMessageRepo)
        .execute(),
      this.userRepo.query(sqlUpdateUsers),
    ]);
  }

  async downGradeRankingUser() {
    const numUser = await this.userRepo.count();

    this.batchingUpdateAlleUser(NUM_BATCHING, numUser);
  }

  private checkLastScan(lastScanDate: Date) {
    if (!lastScanDate) return false;
    const days = dayjs().diff(dayjs(lastScanDate), 'day');
    if (days <= RANK_LAST_SCAN) return false;
    return true;
  }

  private isUpgradeRankIn180Day(tierRankUpdatedDate: Date) {
    if (!tierRankUpdatedDate) return true;
    const days = dayjs().diff(dayjs(tierRankUpdatedDate), 'day');
    if (days <= UPDATE_RANK_DAY) return false;
    return true;
  }

  async migrationTierPoint(query: RankingUserDto) {
    const { numBatching } = query;
    // const numUser = await this.userRepo.count();
    const numUser = await this.userRepo
      .createQueryBuilder('user')
      .where((qb) => {
        qb.where('user.tierRankUpdatedDate is null');
      })
      .getCount();
    // console.log(numUser);
    this.migrationTierPointAllUser(numBatching, numUser);
    return {
      message: 'migrating...',
    };
  }

  async migrationTierPointAllUser(numBatching: number, numUser: number) {
    for (let i = 0; i <= Math.floor(numUser / numBatching); i++) {
      // console.log(i);
      await this.migrateTierPointRetry(numBatching, i, 2);
    }
  }

  @Transactional()
  async migrateTierPoint(numBatching: number, page: number) {
    const skip = page * numBatching;
    const users = await this.userRepo
      .createQueryBuilder('user')
      .where((qb) => {
        qb.where('user.tierRankUpdatedDate is null');
      })
      .orderBy('user.createdDate', 'ASC')
      .take(numBatching)
      .skip(skip)
      .getMany();
    const newUser = users.map((user) => {
      if (!user.tierRankUpdatedDate) {
        // console.log(user.id);
        return this.userRepo.save({
          ...user,
          tierPoint:
            user.tierPoint % 0.5 === 0
              ? user.tierPoint * 4
              : Math.round(user.tierPoint) * 4,
          tierRankUpdatedDate: dayjs('2023-01-02'), //'2023-02-01T00:00:00.00Z'
        });
      }
    });

    await Promise.all(newUser);
  }

  async migrateTierPointRetry(
    numBatching: number,
    page: number,
    retry?: number,
  ) {
    try {
      await this.migrateTierPoint(numBatching, page);
    } catch (error) {
      // if (retry <= 0) {
      //   console.log('error page:', page, ' numBatching: ', numBatching);
      // } else {
      //   console.log('retry: ', page, retry);
      //   this.migrateTierPointRetry(numBatching, page, retry - 1);
      // }
      console.log(error);
    }
  }

  async migrationHistoryPoint(query: RankingUserDto) {
    const { numBatching } = query;
    const numHistory = await this.historyPoint.count({
      where: {
        type: HistoryPointType.ADD_POINT,
        // tierPoint: MoreThan(0),
        // isSync: In([false, null]),
      },
    });

    // console.log('numHistory: ', numHistory);
    this.migrationHistoryPointAll(numBatching, numHistory);
    return {
      message: 'migrating...',
    };
  }
  async migrationHistoryPointAll(numBatching: number, numHistory: number) {
    for (let i = 0; i <= Math.floor(numHistory / numBatching); i++) {
      // console.log(i);
      await this.migrateHistoryPointRetry(numBatching, i, 2);
    }
  }

  async migrateHistoryPointRetry(
    numBatching: number,
    page: number,
    retry?: number,
  ) {
    try {
      await this.migrateHistoryPoint(numBatching, page);
    } catch (error) {
      // if (retry <= 0) {
      //   console.log('error page:', page, ' numBatching: ', numBatching);
      // } else {
      //   console.log('retry: ', page, retry);
      //   this.migrateHistoryPointRetry(numBatching, page, retry - 1);
      // }
      console.log(error);
    }
  }
  @Transactional()
  async migrateHistoryPoint(numBatching: number, page: number) {
    const skip = page * numBatching;
    const histories = await this.historyPoint
      .createQueryBuilder('history')
      .where((qb) => {
        qb.where('history.type = :type', {
          type: HistoryPointType.ADD_POINT,
        });
        // .andWhere('history.isSync = false OR history.isSync is null');
      })
      .orderBy('history.transactionDate', 'ASC')
      .take(numBatching)
      .skip(skip)
      .getMany();
    const newHistories = histories.map((history) => {
      if (!history.isSync) {
        // console.log(history.id);
        return this.historyPoint.save({
          ...history,
          tierPoint: history.tierPoint * 4,
          isSync: true,
        });
      }
    });

    await Promise.all(newHistories);
  }

  private async pushUpdateRankNotification(
    userId: number,
    title: string,
    content: string,
    description: string,
  ) {
    const topic = `USER_${userId}_${dayjs().unix()}`;
    // const title = `THĂNG HẠNG ${rank} THÀNH CÔNG.`;
    // const content = `Bạn đã thăng hạng ${rank} thành công.`;
    // const description = `"Bạn đã thăng hạng ${rank} thành công."`;
    const userSessions = await this.userSessionRepo.find({
      where: { userId, deviceToken: Not(IsNull()) },
    });
    const tokens = userSessions.map((item) => item.deviceToken);

    const request: PushNotificationRequest = {
      message: content,
      title: title,
      topic: topic,
      type: 2,
      tokenOfTopic: tokens,
    };
    const notification = this.notificationUserRepo.create({
      userId,
      status: NotificationUserStatus.UNREAD,
      firebaseStatus: NotificationUserFirebaseStatus.SENT,
      title,
      content,
      description,
    });

    if (tokens.length) {
      await this.pushNotification(request);
    }
    await this.notificationUserRepo.save(notification);
  }

  private async pushNotification(request: PushNotificationRequest) {
    const baseUrl = this.configService.get('vitaJava.notification.baseUrl');
    const pushNotiApi = this.configService.get(
      'vitaJava.notification.pushNotiApi',
    );

    await this.httpService.axiosRef.post(`${baseUrl}${pushNotiApi}`, request);
  }

  private getCrmTransTypeFollowRule1802025(
    listCrmType: IListCrmType2025,
    currentTierCode: string,
    newTierCode: string,
  ) {
    if (!listCrmType || !currentTierCode || !newTierCode) {
      return null;
    }

    switch (currentTierCode) {
      case USER_RANK.PLATINUM:
        switch (newTierCode) {
          case USER_RANK.GOLD:
            return listCrmType.crmTypeFromTierPlatinumToTierGold;
          case USER_RANK.TITAN:
            return listCrmType.crmTypeFromTierPlatinumToTierTitan;
          case USER_RANK.MEMBER:
            return listCrmType.crmTypeFromTierPlatinumToTierMember;
        }
      case USER_RANK.GOLD:
        switch (newTierCode) {
          case USER_RANK.TITAN:
            return listCrmType.crmTypeFromTierGoldToTierTitan;
          case USER_RANK.MEMBER:
            return listCrmType.crmTypeFromTierGoldToTierMember;
        }
      case USER_RANK.TITAN:
        switch (newTierCode) {
          case USER_RANK.MEMBER:
            return listCrmType.crmTypeFromTierTitanToTierMember;
        }
    }

    return null;
  }
}
