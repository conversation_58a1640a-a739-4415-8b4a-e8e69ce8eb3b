apiVersion: apps/v1
kind: Deployment
metadata:
  name: vtd-service-loyalty-v3
  labels:
    app: vtd-service-loyalty-v3
  namespace: default
spec:
  replicas: 1

  # Avoid "ready blink" on brand new pods before they are truly stable
  minReadySeconds: 5

  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0

  selector:
    matchLabels:
      app: vtd-service-loyalty-v3
  template:
    metadata:
      labels:
        app: vtd-service-loyalty-v3
    spec:
      # nginx default timeout ~60s, add buffer for graceful shutdown
      terminationGracePeriodSeconds: 120

      tolerations:
        - key: 'name'
          value: 'api-services'
          operator: 'Equal'
          effect: 'NoSchedule'
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: name
                    operator: In
                    values:
                      - api-services
      containers:
        - name: vtd-service-loyalty-v3
          image: asia-southeast1-docker.pkg.dev/spartan-impact-319504/vtd-stage/vtd-service-loyalty-v3:latest

          resources:
            requests:
              memory: '320Mi'
              cpu: 100m
            limits:
              memory: '400Mi'
              cpu: 200m

          ports:
            - containerPort: 5000
              name: app-port

          # Give Nginx/Kong time to stop routing to this pod before process exits
          lifecycle:
            preStop:
              exec:
                command:
                  - /bin/sh
                  - -c
                  - |
                    echo "[preStop] draining window 15s..."
                    kill -TERM 1 # Kill the main process
                    sleep 15

          # Startup probe: only during boot. Safe to check DB here.
          startupProbe:
            httpGet:
              path: /api-v3/loyalty/health/startup
              port: app-port
            failureThreshold: 10
            periodSeconds: 30
            timeoutSeconds: 6

          # Liveness: process alive only (no DB/Redis dependency)
          livenessProbe:
            httpGet:
              path: /api-v3/loyalty/health/liveness
              port: app-port
            failureThreshold: 3
            periodSeconds: 30
            timeoutSeconds: 3

          # Readiness: can receive traffic; check dependencies but avoid spamming in app code (cached)
          # Faster period (10s) allows quicker detection when pod starts draining
          readinessProbe:
            httpGet:
              path: /api-v3/loyalty/health/readiness
              port: app-port
            failureThreshold: 3
            periodSeconds: 20
            timeoutSeconds: 5
          env:
            # - name: TZ
            #  value: 'Asia/Ho_Chi_Minh'
            - name: LOYALTY_SERVICE_URL
              value: '0.0.0.0'
            - name: LOYALTY_SERVICE_PORT
              value: '8889'
            #- name: TZ
            #  value: 'Asia/Ho_Chi_Minh'
            - name: DB_HOST
              value: '************'
            - name: DB_PORT
              value: '5432'
            - name: DB_USERNAME
              value: 'postgres'
            - name: DB_PASSWORD
              value: 'k0aoQ0o06cTaO6kgWg8f0GUsPzLbvG+fQ7hO/S3hnde+Pgz/RiuDMS9rcuNHOtVB'
            - name: DB_DATABASE
              value: 'vitadairy'
            - name: DB_HOST_GIFT
              value: '************'
            - name: DB_PORT_GIFT
              value: '5432'
            - name: DB_USERNAME_GIFT
              value: 'gift_owner'
            - name: DB_PASSWORD_GIFT
              value: '57da1c8ce8d438d18641514be496155cb4b2353d'
            - name: DB_DATABASE_GIFT
              value: 'vtd_db_gift'
            - name: REDIS_HOST
              value: 'redis'
            - name: REDIS_PORT
              value: '80'
            - name: GCP_CREDENTIALS_PATH
              value: 'configs/credentials/spartan-impact-credentials.json'
            - name: PROJECT_ID
              value: 'spartan-impact-319504'
            - name: PRIVATE_KEY
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            - name: CLIENT_EMAIL
              value: '<EMAIL>'
            - name: STORAGE_MEDIA_BUCKET
              value: 'vitadairy_public_dev'
            - name: NODE_ENV
              value: 'test'
            - name: NOTIFICATION_BASE_URL
              value: 'http://notification-service'
            - name: NOTIFICATION_PUSH_NOTIFICATION
              value: '/api/notification/push-notification'
            - name: BRAVO_SECRET_KEY
              value: 'REWARDS@2021BravoVitadairydSfdf29dsdRTW'
            - name: BRAVO_PARTNER
              value: 'REWARDS'
            - name: BRAVO_BASE_URL
              value: 'https://qrvitadairy.vn:60443/api/v2'
            - name: BRAVO_FUNC_GET_INFO
              value: 'GetQrInfoEx'
            - name: CRM_BASE_URL
              value: 'https://vitadairy--uat.sandbox.my.salesforce.com'
            - name: CRM_CHECK_LEAD_BY_PHONE_API
              value: '/services/apexrest/GMS_SF/sfdc_hook/vd_landing_page'
            - name: CRM_CONVERT_LEAD_API
              value: '/services/apexrest/api/lead/convert'
            - name: CRM_CONVERT_LEAD_BY_PHONE_API
              value: '/services/apexrest/api/ConvertLead/'
            - name: CRM_INSERT_TRANSACTION_API
              value: '/services/apexrest/api/Insert_Transaction'
            - name: CRM_UPDATE_LOYALTY_PROGRAM_MEMBER_API
              value: '/services/apexrest/api/v1/member'
            - name: CRM_AUTH_API
              value: '/services/oauth2/token'
            - name: CRM_AUTH_GRANT_TYPE
              value: 'refresh_token'
            - name: CRM_AUTH_CLIENT_ID
              value: '3MVG9Po2PmyYruumJguYPG0Y2AX2zvAGhtSvqCMq9Kisj0eBVK0.LXGFFt7qt2g1Xs6.FdFtW1XBNVfz1Jgmr'
            - name: CRM_AUTH_CLIENT_SECRET
              value: '****************************************************************'
            - name: CRM_AUTH_USER_NAME
              value: '<EMAIL>'
            - name: CRM_AUTH_PASSWORD
              value: 'Abcd1234!WocpID9I7IPTJk5Zvv4lA8RY4'
            - name: CRM_AUTH_PROGRAM_NAME
              value: 'a0U0T000000PjVlUAK'
            - name: CRM_REDEEM_GIFT_TRANSACTION_API
              value: '/services/apexrest/api/transaction/sendGift/'
            - name: CRM_REDEEM_GIFT_TRANSACTION_V2_API
              value: '/services/apexrest/api/transaction/sendGift/'
            - name: CRM_REDEEM_VOUCHER_TRANSACTION_API
              value: '/services/apexrest/api/transaction/sendVourcher/'
            - name: CRM_REFRESH_TOKEN
              value: '***************************************************************************************'
            - name: CRM_CHECK_ACCESS_TOKEN_API
              value: '/services/oauth2/introspect'
            - name: CRM_AUTH_CREATE_ACCESS_TOKEN_GRANT_TYPE
              value: 'password'
            - name: CRM_SYNC_USING_WH
              value: 'true'
            - name: FORCE_INVALID_TOKEN_RESPONSE_SYNC_CRM_OUTBOX
              value: '-1'
            - name: SAP_AUTH_URL
              value: 'https://login.microsoftonline.com'
            - name: SAP_AUTH_REQUEST_TOKEN_API
              value: '/116d66f5-246a-4022-99b9-381752bc99e5/oauth2/v2.0/token'
            - name: SAP_AUTH_GRANT_TYPE
              value: 'client_credentials'
            - name: SAP_AUTH_CLIENT_ID
              value: '4c96cd59-fe15-4bda-a05a-7661b0ebe60d'
            - name: SAP_AUTH_CLIENT_SECRET
              value: '****************************************'
            - name: SAP_AUTH_SCOPE
              value: 'api://0e2edc4b-a113-4611-ac5d-18ed5f22cbf9/.default'
            - name: SAP_BASE_URL
              value: '************************************'
            - name: SAP_API_KEY
              value: '5a80e2feaa744923ad449274479f9fa9'
            - name: SAP_ENABLE_GET_PRODUCT
              value: 'true'
            - name: SAP_ENABLE_LOGGING_GET_PRODUCT
              value: 'true'
            - name: SAP_REQUEST_TIMEOUT
              value: '20000'
            - name: SAP_GET_PRODUCT_BY_QR_API
              value: '/VTDApiQrTEST/v2'
            - name: SAP_GET_PRODUCT_BY_SPOON_API
              value: '/VTDApiQrTEST/v2'
            - name: VITA_GO_BASE_URL
              value: 'https://api-stg-v2.vitadairyvietnam.vn/api'
            - name: VITA_GO_ADD_PLAYGAME_TIMES_API
              value: '/game/add-times'
            - name: VITA_GO_SAVE_EXTERNAL_API
              value: '/logging/save-external-api'
            - name: VITA_JAVA_NOTIFCATION_BASE_URL
              value: 'http://notification-service'
            - name: VITA_JAVA_NOTIFCATION_PUSH_NOTI_API
              value: '/api/notification/push-notification'
            - name: VITA_JAVA_APPLICATION_BASE_URL
              value: 'http://vitadairy-reward-be'
            - name: VITA_JAVA_APPLICATION_EXCHANGE_GIFT_V2
              value: '/api/v2/point/gift-exchange'
            - name: VITA_JAVA_APPLICATION_USER_REGISTER
              value: '/api/user/register'
            - name: VITA_JAVA_APPLICATION_V4_BASE_URL
              value: 'http://vtd-be-java-gift'
            - name: VITA_JAVA_APPLICATION_V4_GIFTING_GIFT
              value: '/v4/gs/gifts/gift-gifting'
            - name: VITA_JAVA_APPLICATION_V4_GIFTING_GIFT_NO_AUTHEN_TOKEN
              value: '/v4/gs/gifts/no-auth-token/gift-gifting'
            - name: VITA_JAVA_APPLICATION_V4_REMOVE_GIFTING_GIFT_NO_AUTHEN_TOKEN
              value: '/v4/gs/gifts/no-auth-token/gift-remove-user-gift-gifting'
            - name: VITA_JAVA_APPLICATION_V4_GET_QUANTITY_GIFT
              value: '/v4/gs/gifts/gift-get-quantity'
            - name: VITA_JAVA_APPLICATION_V4_REUSE_USER_GIFT
              value: '/v4/gs/user-gifts/reuse'
            - name: VITA_JAVA_APPLICATION_V4_REUSE_USER_GIFT_NO_AUTHEN_TOKEN
              value: '/v4/gs/user-gifts/no-auth-token/reuse'
            - name: VITA_JAVA_APPLICATION_V4_GET_USER_GIFT_PRE_ORDER_ENOUGH_POINT_TO_SEND_NOTIFY
              value: '/v4/gs/user-gifts/get-pre-order-enought-point-to-send-notify'
            - name: VITA_JAVA_APPLICATION_V4_GIFTS
              value: '/v4/gs/gifts'
            - name: VITA_JAVA_APPLICATION_V4_ADMIN_GIFTS
              value: '/v4/gs/admin/gifts'
            - name: VITA_JAVA_APPLICATION_V4_GIFT_CAETGORIES
              value: '/v4/gs/gift-categories'
            - name: VITA_JAVA_APPLICATION_V4_ADMIN_GIFT_CAETGORIES
              value: '/v4/gs/admin/gift-categories'
            - name: VITA_JAVA_APPLICATION_V4_ENABLE
              value: 'true'
            - name: VITA_JAVA_WH_V4_BASE_URL
              value: 'http://vtd-be-java-warehouse'
            - name: VITA_JAVA_WH_V4_INSERT_EVENT_POINT_HISTORY_TO_WH
              value: '/v4/ws/history-event'
            - name: VITA_JAVA_WH_V4_UPDATE_EVENT_POINT_HISTORY_TO_WH
              value: '/v4/ws/history-event/update'
            - name: VITA_JAVA_WH_V4_HASH_KEY
              value: 'L1MiYZI6VCPcce56KrhvHmigsTq2yMwM'
            - name: VITA_JAVA_WH_V4_INSERT_EVENT_POINT_HISTORY_TO_WH_NO_AUTH_TOKEN
              value: '/v4/ws/no-auth-token/history-event'
            - name: VITA_JAVA_WH_V4_UPDATE_EVENT_POINT_HISTORY_TO_WH_NO_AUTH_TOKEN
              value: '/v4/ws/no-auth-token/history-event/update'
            - name: VITA_JAVA_WH_V4_SYNC_TO_3RD_SERVICE
              value: '/v4/ws/send'
            - name: VITA_JAVA_WH_V4_SYNC_TO_3RD_SERVICE_NO_AUTHEN_TOKEN
              value: '/v4/ws/int/send'
            - name: VITA_JAVA_WH_V4_SYNC_RESULT_INIT_LOYALTY_PROGRAM_MEMBER
              value: '/v4/ws/int/sync-init-sf'
            - name: VITA_JAVA_WH_V4_TRIGGER_RETRY_SF_TRANSACTION
              value: '/v4/ws/sync-retry'
            - name: ECOM_TOKEN
              value: 'iweHAyq2s9EGKEhqBLfmintGg'
            - name: ECOM_BASE_URL
              value: 'https://s.omisocial.com'
            - name: ECOM_ACTIVE_VOUCHER_BY_PHONE_API
              value: '/api/v1/vouchers/activate-by-phone/'
            - name: POPUP_MUM_TEMPLATE_ID
              value: '11'
            - name: ES_SPOON_INDEX
              value: 'vtd.spoonmst'
            - name: ES_SYNC_SF_FAIL_LOGGING_INDEX
              value: 'vtd.syncsffailedlogging'
            - name: ES_NODE
              value: 'https://vtd-elasticsearch-es-http.vtd-elasticsearch:9200'
            - name: ES_USERNAME
              value: 'elastic'
            - name: ES_PASSWORD
              value: 'FluE0S115gApAjavZ5269R75'
            - name: ES_EXCHANGE_GIFT_LOGGING_INDEX
              value: 'vtd.exchangegiftlogging'
            - name: ES_EXCHANGE_GIFT_LOGGING_MONTH_KEEP
              value: '3'
            - name: VITA_SPOON_BASE_URL
              value: 'https://api-spoon-stg-v2.vitadairyvietnam.vn'
            - name: VITA_SPOON_AUTHENTICATE
              value: '/api/authenticate'
            - name: VITA_SPOON_GET_ACCOUNT_BY_ID
              value: '/api/account'
            - name: VITA_CHECK_EVENT_Q2
              value: '266'
            - name: VITA_CHECK_EVENT_Q2_TC
              value: '286,287,288'
            - name: VITA_CHECK_EVENT_Q2_CBB
              value: '289'
            - name: VGS_BASE_URL
              value: 'https://uat-plugin.linkvn.org'
            - name: CRM_ACCESS_TOKEN
              value: '00D0T0000008ksU!AQEAQPGdKzogyB1d8CaDTB7U2UMbeaZmC4cyQMStsK3YAKmmSlUM9ITvi9bmXMh80tNUwXvBOSAvw3Mms6tBumOiT6UpFHh6'
            - name: CRM_REDEEM_STORE_API
              value: '/api/teso/send-user-info'
            - name: WEBHOOK_VGS_ACCESS_TOKEN
              value: 'REWARDS@2023VgsVitadairydcfsf15dhbRAD'
            - name: GMAIL
              value: '<EMAIL>'
            - name: GMAIL_PASS
              value: 'ooikkohtvarbjvlm'
            - name: NEW_RELIC_APP_NAME
              value: 'VTD Micro Stage'
            - name: NEW_RELIC_LICENSE_KEY
              value: 'eu01xx3cc9eb1fae5f97abe74e6a89daFFFFNRAL'
            - name: VITA_CHECK_EVENT_Q2_TC
              value: '286,287,288'
            - name: VITA_CHECK_EVENT_Q2_CBB
              value: '289'
            - name: VGS_TOKEN
              value: 'vXWx3JNWz39Xy_6yS9rxICv2X-hfiEU_5mfWRYq6bgGNVFAACmJSY3C4p--MGry8TT9bge3qNjT1_IzY94IL6zZOwOksB-l0mJ_qB7CsjN4FIEJofkCsI6UHqm_mjC1A'
            - name: VGS_USERNAME
              value: 'vitadairy_test'
            - name: VGS_ZNS_OA_ID
              value: '2601380577952053454'
            - name: VGS_SMS_BASE_URL
              value: 'https://cloudsms.vietguys.biz:4438/api'
            - name: VGS_ZALO_BASE_URL
              value: 'https://cloud.vietguys.biz:4438/api/zalo/v1/send'
            - name: VGS_CSKH_SMS_URL
              value: '/index.php'
            - name: VGS_TEMPLATE_OTP_ID
              value: '261362'
            - name: VGS_BRANCH_NAME
              value: 'VitaDairy'
            - name: VGS_ZNS_TEMPLATE_USER_GIFT_REMINDER_5_DAYS_ID
              value: '445479'
            - name: VGS_SMS_USER_GIFT_REMINDER_5_DAYS_CONTENT
              value: '[TB] Ban chua thao tac xac nhan dia chi nhan qua. Truy cap ung dung VitaDairy & hoan thanh truoc khi qua bi huy vao 48h tiep theo. Can ho tro LH 1900 633 559.'
            - name: VGS_ALLOW_SEND_ZNS_USER_GIFT_REMINDER_5_DAYS
              value: 'true'
            - name: VGS_VOICE_BASE_URL
              value: 'https://voice.vietguys.biz/ext/outboundcall.php'
            - name: VGS_VOICE_BLOCKED_PHONE_PREFIXES
              value: '099,052,056,058,092'
            - name: VGS_VOICE_OTP_API_CODE
              value: '39487643'
            - name: VGS_VOICE_OTP_PASS_CODE
              value: 'euxJ5PNjL2K80blR'
            - name: VGS_VOICE_OTP_ALLOW_SEND
              value: 'false'
            - name: BEEKIDS_BASE_URL
              value: 'https://dev-api.beekids.edu.vn'
            - name: BEEKIDS_GET_COURSES
              value: '/bkids_courses'
            - name: BEEKIDS_GET_TRAINING_PATHS
              value: '/trainings/paths'
            - name: BEEKIDS_KEY_ID
              value: '49984K0SCOAWBMF30QJMPF1U'
            - name: BEEKIDS_KEY_SECRET
              value: 'MdWvwS-dNlufq1ux-2hCFEjz9EdoM5LLdMKcLeL2gtghofsvLlwzVGmKMkciOQgj'
            - name: VITA_CHECK_EVENT_Q3_CBB
              value: '295'
            - name: VITA_CHECK_EVENT_Q3_DHA
              value: '300'
            - name: VITA_CHECK_EVENT_Q3_OPTI
              value: '301'
            - name: VITA_CHECK_EVENT_PRODUCT_01
              value: '299,298,297,296'
            - name: VITA_CHECK_EVENT_DHA_OPTI
              value: '300,301'
            - name: VITA_CHECK_EVENT_Q3_CLG
              value: '302'
            - name: VITA_EVENT_QUY1_2024_MAY_RUI_ID
              value: '500'
            - name: VITA_EVENT_THANG5_2024_BIG_C
              value: '581'
            - name: VITA_CHECK_EVENT_Q3_OGGI
              value: '303'
            - name: APP_VERSION_NAME
              value: '3.0.24'
            - name: VITA_CHECK_EVENT_Q3_CLS
              value: '304'
            - name: ES_LOG_INDEX
              value: 'log'
            - name: DB_HOST_PHYSICAL
              value: '************'
            - name: DB_PORT_PHYSICAL
              value: '1439'
            - name: DB_USERNAME_PHYSICAL
              value: 'radb'
            - name: DB_PASSWORD_PHYSICAL
              value: '#Vitadairy@2023'
            - name: DB_DATABASE_PHYSICAL
              value: 'RA_BK'
            - name: EVENT_4_LON_GOOGLE_SHEET_CLIENT_EMAIL
              value: '<EMAIL>'
            - name: EVENT_4_LON_GOOGLE_SHEET_PRIVATE_KEY
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            - name: EVENT_4_LON_GOOGLE_SHEET_SPREAD_SHEET_ID
              value: '1ezZC_Izutt9m5VuZl8f0b2iTGVdOFYYnYzfdgBySUk0'
            - name: EVENT_4_LON_GOOGLE_SHEET_APIS_SCOPES
              value: 'https://www.googleapis.com/auth/spreadsheets'
            - name: EVENT_4_LON_GOOGLE_SHEET_SHEET_NAME
              value: 'Sheet1'
            - name: EVENT_4_LON_GOOGLE_SHEET_START_COLUMN
              value: 'B'
            - name: EVENT_4_LON_GOOGLE_SHEET_END_COLUMN
              value: 'I'
            - name: HOTLINE_TOKEN
              value: 'hotline'
            - name: CRONJOB_TOKEN
              value: 'q4t7KdgXtRgdLe07y78OZ9GF5AXf1W3hlMgvuqVrdsTXZKEX4vQoVSjjcKQcD5Cy'
            - name: TELEGRAM_BASE_API_URL
              value: 'https://api.telegram.org'
            - name: TELEGRAM_NOTIFY_EVENT_WEBAPP_SBPS_DATA_CHANGE_BOT_TOKEN
              value: '7977212723:AAEIBHkeKQCLu56LN3sts7iKsdPRvf_HvbQ'
            - name: TELEGRAM_NOTIFY_EVENT_WEBAPP_SBPS_DATA_CHANGE_CHAT_ID
              value: '-1001840842072'
            - name: TELEGRAM_NOTIFY_EVENT_WEBAPP_SBPS_DATA_CHANGE_THREAD_ID
              value: '20030'
            - name: TELEGRAM_SEND_MESSAGE_END_POINT
              value: 'sendMessage'
            - name: CRM_GET_LEAD_INFO_API
              value: '/services/apexrest/api/lead/getbyid/'
            - name: CRM_CREATE_CASE_URL
              value: '/services/data/v56.0/sobjects/Case/'
            - name: CRM_ADD_POINT_URL
              value: 'https://admin-dev-v2.vitadairyvietnam.vn/dashboard/collect-coin-support/'
            - name: CRM_CREATE_CASE_OWNER_ID
              value: '00GA20000008IuDMAU'
            - name: VITA_CHECK_EVENT_Q4_DHA
              value: '313'
            - name: VGS_TEMPLATE_REMIND_DOWN_TIER_RANK_AFTER_180_DAYS_ID
              value: '280552'
            - name: VGS_TEMPLATE_REMIND_DOWN_TIER_RANK_AFTER_365_DAYS_ID
              value: '280553'
            - name: CHATBOT_TOKEN
              value: 'P7wXeA9vG3ZcB2R6jYKq'
            - name: VITA_CHECK_EVENT_Q4_CBB
              value: '315'
            - name: VITA_CHECK_EVENT_Q4_X2_XU
              value: '316'
            - name: VITA_CHECK_EVENT_Q3_2024_UP_RANK
              value: '618'
            - name: VITA_GIFT_400GR_EVENT_Q3_2024_UP_RANK
              value: '1024'
            - name: VITA_CHECK_EVENT_Q3_2024_CLBB_MASS_SAMPLING
              value: '617'
            - name: VITA_EXCLUDE_SKUS_EVENT_Q3_2024_CLBB_MASS_SAMPLING
              value: 'VMSBPS46,VMSBPS47,VMSBPS30,VMSBPS31,VMSBPS48,VMSBPS49,VMSBPS22,VMSBPS23,VMSBPS34,VMSBPS35'
            - name: VITA_CHECK_EVENT_Q3_2024_CLBB_VOUCHER_TOPUP
              value: '619'
            - name: EVOUCHER_VACXIN_GOOGLE_SHEET_SPREAD_SHEET_ID
              value: '1M4bnTeGyU_Opqy_L8W8el6gLMeV02j1uP6U38LpRU1E'
            - name: EVOUCHER_VACXIN_GOOGLE_SHEET_SHEET_NAME
              value: 'Sheet1'
            - name: EVOUCHER_VACXIN_GOOGLE_SHEET_START_COLUMN
              value: 'B'
            - name: EVOUCHER_VACXIN_GOOGLE_SHEET_END_COLUMN
              value: 'J'
            - name: USE_NOTI_V3
              value: 'true'
            - name: KAFKA_BROKER
              value: 'vtd-kafka-cluster-kafka-brokers.kafka:9092'
            - name: OUTBOX_FOURTH_RETRY_DELAY_TIME
              value: '60000'
            - name: REDIS_V2_SENTINELS
              value: 'redis-sentinel-sentinel.redis-v2:26379'
            - name: CRM_SYNC_NOTI_STATUS_FULL_URL
              value: 'https://ws-sms-campaign-dev-e8642f9a2e79.herokuapp.com/webhook/v1/app-push-vita'
            - name: CRM_SYNC_NOTI_STATUS_BEARER_TOKEN
              value: 'FdgzJOxYhRMIAfnlWPLUTfIJBkoc54CS'
            - name: CRM_SYNC_NOTI_STATUS_FULL_URL_LEGACY
              value: 'https://ws-intecom-vitadairy.wsinone.cloud/webhook/v1/app-push-vita-callback'
            - name: CRM_SYNC_NOTI_STATUS_BEARER_TOKEN_LEGACY
              value: '6QZqKoChijJ9ztJeOjDevDUSha2Ji6qg'
            - name: CLS_Q3_2024_EVENT_IDS
              value: '615'
            - name: CLS_Q4_2024_EVENT_IDS
              value: '620'
            - name: VITA_CHECK_EVENT_Q4_2024_WA_SBPS
              value: '621'
            - name: RUNNING_AS_CRONJOB
              value: 'false'
            - name: TRIPLAYZ_AUTH_TOKEN
              value: 'Cp4s5MPucU3cw6DHUw1lUwX1MBCW2sg9WFKpCeDodCdVLM25rH0j1Y6ekBSYZIZctwVq6HVoexVyv5ha2pgtmxYc2LrrYLzx9HGXEib4Wpkp8kREB46vx3v4BceS8IUz0Ts2JUrC5EieMIdGJ6hZ0WJPv2f93q2XX4SDiU4iBSi7x1hIsgbiPem0i3eRY5wDrG3f9faPMJYjZDDFu0pxqY9I6oJ8oYeQtaz9hr2RvDtxPrlpvdf9owMoCvh8uSv7Ya7u2atuIBU8AwRa8J6azvmvx5dpvkfGKXHcET7Eyp9KWfU75MxLuE9T3PoftjOWSeMtLRgLCk4IpRZt92C7VDoY5Rqu0BOCcMLRaoHxsqzl7v3wHQIvJztXzVj8zczD7SFuO5Eb6lcqvQpsviGXuFSOQ0dtE8pAis0LJBJk9sGah71GgZ542GU0u3GpBKi9Lt15efdcSsRSlFwwBQ6mB2e8qRwHmRxCBtX5acVHRmMBWhXZDaoIbtpqBRAbuaPu'
            - name: TRIPLAYZ_API_URL
              value: 'https://apistgxusodinhduong.vitadairy.vn/webhook/receive-purchase-transaction'
            - name: TRIPLAYZ_API_TOKEN
              value: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************.49fHtTzlZzJa11KuKKZRJQDLq2sKumHa6tTuTBJ5iUY'
            - name: VITA_NOTIFICATION_V3_BASE_URL
              value: 'http://vtd-service-noti-v3'
            - name: VITA_JAVA_LOYALTY_BASE_URL
              value: 'http://vtd-be-java-gift/in/gs'
            - name: VITA_EVENT_LATEST_ID
              value: '621'
            - name: VITA_EVENT_ADD_CAN_LATEST_ID
              value: '46'
            - name: LIMIT_NUMBER_OF_BLOCKED_ACCOUNT
              value: '3'
            - name: ENABLE_DEBUG_EVENT_GIFT_BY_4_STEP
              value: 'true'
            - name: JWT_SECRET_KEY
              value: 'vitadairy'
            - name: JWT_VALID_IN_SECONDS
              value: '*********'
            - name: GOOGLE_USERINFO_URL
              value: 'https://www.googleapis.com/oauth2/v2/userinfo?alt=json'
            - name: APPLE_CLIENT_ID
              value: 'vn.vitadairy.loyalty.staging'
            - name: FACEBOOK_GET_ME_URL
              value: 'https://graph.facebook.com/me'
            - name: MIGRATE_PROVINCE_GOOGLE_SHEET_CLIENT_EMAIL
              value: '<EMAIL>'
            - name: MIGRATE_PROVINCE_GOOGLE_SHEET_PRIVATE_KEY
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
            - name: MIGRATE_PROVINCE_GOOGLE_SHEET_SPREAD_SHEET_ID
              value: '1KDftzxMHpO9G8_WKqbpbuWkodYI-AGgtaBCEPMFR1NQ'
            - name: MIGRATE_PROVINCE_GOOGLE_SHEET_APIS_SCOPES
              value: 'https://www.googleapis.com/auth/spreadsheets'
            - name: MIGRATE_PROVINCE_GOOGLE_SHEET_SHEET_NAME
              value: 'Mapping'
            - name: MIGRATE_PROVINCE_GOOGLE_SHEET_START_CELL
              value: 'A2'
            - name: MIGRATE_PROVINCE_GOOGLE_SHEET_END_CELL
              value: 'N3322'
            - name: CRONJOB_SYNC_USER_BRAND_POINT_HISTORY_TO_REPORT_BATCH
              value: '1'
            - name: CRONJOB_SYNC_USER_BRAND_POINT_HISTORY_TO_REPORT_LIMIT_IN_BATCH
              value: '100'
            - name: ONBOARDING_TOOLTIP_CHECK_USER_REGISTER_DATE_ENABLE
              value: 'true'
            - name: ONBOARDING_TOOLTIP_RELEASE_DATE
              value: '2026-02-13 00:00:00.000 +0700'
