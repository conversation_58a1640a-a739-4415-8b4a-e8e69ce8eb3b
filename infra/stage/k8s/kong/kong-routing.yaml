apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gateway
  namespace: default
  annotations:
    konghq.com/plugins: cors-plugin
spec:
  ingressClassName: kong
  rules:
    - http:
        paths:
          #- path: /api/portal
          #  pathType: ImplementationSpecific
          #  backend:
          #    service:
          #      name: vtd-api-portal
          #      port:
          #        number: 80
          #- path: /api/app
          #  pathType: ImplementationSpecific
          #  backend:
          #    service:
          #      name: vtd-api-app
          #      port:
          #        number: 80
          - path: /api/product/check
            pathType: ImplementationSpecific
            backend:
              service:
                name: vtd-service-loyalty-v3
                port:
                  number: 80
          - path: /api/point/add-point
            pathType: ImplementationSpecific
            backend:
              service:
                name: vtd-service-loyalty-v3
                port:
                  number: 80
          #- path: /api/game/add-times
          #  pathType: ImplementationSpecific
          #  backend:
          #    service:
          #      name: vtd-api-app
          #      port:
          #        number: 80
          #- path: /api/logging/save-external-api
          #  pathType: ImplementationSpecific
          #  backend:
          #    service:
          #      name: vtd-api-app
          #      port:
          #        number: 80
          - path: /api-v3/loyalty
            pathType: Prefix
            backend:
              service:
                name: vtd-service-loyalty-v3
                port:
                  number: 80
          - path: /api-v3/user
            pathType: Prefix
            backend:
              service:
                name: vtd-service-user-v3
                port:
                  number: 80
          #- path: /api-v3/game
          #  pathType: Prefix
          #  backend:
          #    service:
          #      name: vtd-service-game-v3
          #      port:
          #        number: 80
          - path: /api-v3/noti
            pathType: Prefix
            backend:
              service:
                name: vtd-service-noti-v3
                port:
                  number: 80
          - path: /api-v3/worker
            pathType: Prefix
            backend:
              service:
                name: vtd-service-worker-v3
                port:
                  number: 80
          - path: /v4/gs
            pathType: Prefix
            backend:
              service:
                name: vtd-be-java-gift
                port:
                  number: 80
          - path: /v4/os
            pathType: Prefix
            backend:
              service:
                name: vtd-be-java-order
                port:
                  number: 80
          - path: /v4/ws
            pathType: Prefix
            backend:
              service:
                name: vtd-be-java-warehouse
                port:
                  number: 80
---
apiVersion: configuration.konghq.com/v1
kind: KongPlugin
metadata:
  name: cors-plugin
plugin: cors
config:
  origins: ['*']
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH']
  headers: ['*']
