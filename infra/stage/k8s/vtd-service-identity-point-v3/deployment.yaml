apiVersion: apps/v1
kind: Deployment
metadata:
  name: vtd-service-identity-point-v3
  labels:
    app: vtd-service-identity-point-v3
  namespace: default
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app: vtd-service-identity-point-v3
  template:
    metadata:
      labels:
        app: vtd-service-identity-point-v3
    spec:
      tolerations:
        - key: 'name'
          value: 'api-services'
          operator: 'Equal'
          effect: 'NoSchedule'
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: name
                    operator: In
                    values:
                      - api-services
      containers:
        - name: vtd-service-identity-point-v3
          # resources:
          #   requests:
          #     memory: '320Mi'
          #     cpu: 100m
          #   limits:
          #     memory: '400Mi'
          #     cpu: 200m
          resources:
            requests:
              memory: '256Mi'
              cpu: 50m
            limits:
              memory: '320Mi'
              cpu: 100m
          # image: nguyenvanhontk4/test_k8s:latest
          image: asia-southeast1-docker.pkg.dev/spartan-impact-319504/vtd-stage/vtd-service-identity-point-v3:latest
          ports:
            - containerPort: 5000
              name: app-port
          # startupProbe:
          #   httpGet:
          #     path: /api-v3/identity-point/health/startup
          #     port: app-port
          #   failureThreshold: 10
          #   periodSeconds: 30
          #   timeoutSeconds: 5
          # livenessProbe:
          #   httpGet:
          #     path: /api-v3/identity-point/health/liveness
          #     port: app-port
          #   failureThreshold: 6
          #   periodSeconds: 10
          #   timeoutSeconds: 5
          envFrom:
            # - secretRef:
            #     name: new-relic-secret
            - secretRef:
                name: vtd-service-identity-point-v3-secret
