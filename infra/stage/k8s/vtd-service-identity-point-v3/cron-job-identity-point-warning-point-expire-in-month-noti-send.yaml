apiVersion: batch/v1
kind: CronJob
metadata:
  name: identity-point-cron-warning-point-expire-in-month
  namespace: default
spec:
  concurrencyPolicy: Forbid
  schedule: '0 * * * *'
  timeZone: Asia/Ho_Chi_Minh 
  successfulJobsHistoryLimit: 0
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      backoffLimit: 0
      template:
        spec:
          tolerations:
            - key: 'name' 
              value: 'cron-services'
              operator: 'Equal'
              effect: 'NoSchedule'
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: name
                        operator: In
                        values:
                          - cron-services
          containers:
            - image: asia-southeast1-docker.pkg.dev/spartan-impact-319504/vtd-stage/vtd-service-identity-point-v3:latest
              name: identity-point-cron-warning-point-expire-in-month
              resources:
                requests:
                  memory: '256Mi'
                  cpu: 50m
                limits:
                  memory: '320Mi'
                  cpu: 100m
              envFrom:
                - secretRef:
                    name: vtd-service-identity-point-v3-secret
                # - secretRef:
                #     name: new-relic-secret
              env:
                - name: RUNNING_AS_CRONJOB  
                  value: 'true'
              ports:
                - containerPort: 5000
              command:
                - yarn
                - cron-job
                - cron-job-identity-point-warning-point-expire-in-month-noti-send
          restartPolicy: Never