apiVersion: batch/v1
kind: CronJob
metadata:
  name: identity-point-cron-job-async-data
  namespace: default
spec:
  concurrencyPolicy: Forbid
  # schedule: '* * * * *'  
  # schedule: '0 * * * *'  
  schedule: '*/15 * * * *'
  timeZone: Asia/Ho_Chi_Minh 
  successfulJobsHistoryLimit: 0
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      backoffLimit: 0
      template:
        spec:
          tolerations:
            - key: 'name'
              value: 'cron-services'
              operator: 'Equal'
              effect: 'NoSchedule'
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: name
                        operator: In
                        values:
                          - cron-services
          containers:
            # - image: nguyenvanhontk4/test_k8s:latest
            - image: asia-southeast1-docker.pkg.dev/spartan-impact-319504/vtd-stage/vtd-service-identity-point-v3:latest
              name: identity-point-cron-job-async-data
              # resources:
              #   requests:
              #     memory: '150Mi'
              #     cpu: 50m
              #   limits:
              #     memory: '400Mi'
              #     cpu: 200m
              resources:
                requests:
                  memory: '256Mi'
                  cpu: 50m
                limits:
                  memory: '400Mi'
                  cpu: 200m
              envFrom:
                - secretRef:
                    name: vtd-service-identity-point-v3-secret
                - secretRef:
                    name: new-relic-secret
              env:
                - name: RUNNING_AS_CRONJOB  
                  value: 'true'
              ports:
                - containerPort: 5000
              command:
                - yarn
                - cron-job
                - cron-job-identity-async-report-calculate
          restartPolicy: Never
