apiVersion: v1
kind: Secret
metadata:
  name: vtd-service-identity-point-v3-secret
  namespace: default

stringData:
  PORT: "5000"
  NODE_ENV: development
  SHOW_SQL: 'false'

  DB_HOST: ************
  DB_PORT: '5432'
  DB_USERNAME: postgres
  DB_PASSWORD: k0aoQ0o06cTaO6kgWg8f0GUsPzLbvG+fQ7hO/S3hnde+Pgz/RiuDMS9rcuNHOtVB
  DB_DATABASE: vitadairy


  GRPC_USER_SERVICE_URL: vtd-service-user-v3:50051
  GRPC_ACCOUNT_SERVICE_URL: vtd-service-user-v3:50052

  BOT_TOKEN : **********:AAHuNKh9BYhkNaWaty00lSj9yGru0rR-CD0
  CHAT_ID : "**********"

  USE_NOTI_V3 : 'true'
  CRM_SYNC_USING_WH : 'false'

  RUNNING_AS_CRONJOB: 'false'
  NEW_RELIC_APP_NAME: 'vtd-service-identity-point-v3'
  CRONJOB_TOKEN : 'q4t7KdgXtRgdLe07y78OZ9GF5AXf1W3hlMgvuqVrdsTXZKEX4vQoVSjjcKQcD5Cy'