apiVersion: batch/v1
kind: CronJob
metadata:
  name: loyalty-cron-job-cleanup-tracking-actions
  namespace: default
spec:
  schedule: '0 * * * *'
  #timeZone: "Asia/Ho_Chi_Minh"  # Specify the time zone
  concurrencyPolicy: 'Forbid'
  successfulJobsHistoryLimit: 0
  failedJobsHistoryLimit: 0
  jobTemplate:
    spec:
      backoffLimit: 0
      template:
        spec:
          tolerations:
            - key: 'name'
              value: 'cron-services'
              operator: 'Equal'
              effect: 'NoSchedule'
          affinity:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: name
                        operator: In
                        values:
                          - cron-services
          containers:
            - image: asia-southeast1-docker.pkg.dev/spartan-impact-319504/vtd-dev/vtd-service-loyalty-v3:latest
              name: loyalty-cron-job-birthday
              resources:
                requests:
                  memory: '150Mi'
                  cpu: 50m
                limits:
                  memory: '400Mi'
                  cpu: 200m
              env:
                # - name: TZ
                #  value: 'Asia/Ho_<PERSON>_Minh'
                - name: LOY<PERSON>TY_SERVICE_URL
                  value: '0.0.0.0'
                - name: LOYALTY_SERVICE_PORT
                  value: '8889'
                - name: TZ
                  value: 'Asia/Ho_<PERSON>_<PERSON>'
                - name: DB_HOST
                  value: '************'
                - name: DB_PORT
                  value: '5432'
                - name: DB_USERNAME
                  value: 'postgres'
                - name: DB_PASSWORD
                  value: 'k0aoQ0o06cTaO6kgWg8f0GUsPzLbvG+fQ7hO/S3hnde+Pgz/RiuDMS9rcuNHOtVB'
                - name: DB_DATABASE
                  value: 'vitadairy'
                - name: REDIS_HOST
                  value: 'redis'
                - name: REDIS_PORT
                  value: '80'
                - name: GCP_CREDENTIALS_PATH
                  value: 'configs/credentials/spartan-impact-credentials.json'
                - name: PROJECT_ID
                  value: 'spartan-impact-319504'
                - name: PRIVATE_KEY
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
                - name: CLIENT_EMAIL
                  value: '<EMAIL>'
                - name: STORAGE_MEDIA_BUCKET
                  value: 'vitadairy_public_dev'
                - name: NODE_ENV
                  value: 'development'
                - name: NOTIFICATION_BASE_URL
                  value: 'http://notification-service'
                - name: NOTIFICATION_PUSH_NOTIFICATION
                  value: '/api/notification/push-notification'
                - name: BRAVO_SECRET_KEY
                  value: 'REWARDS@2021BravoVitadairydSfdf29dsdRTW'
                - name: BRAVO_PARTNER
                  value: 'REWARDS'
                - name: BRAVO_BASE_URL
                  value: 'https://qrvitadairy.vn:60443/api/v2'
                - name: BRAVO_FUNC_GET_INFO
                  value: 'GetQrInfoEx'
                - name: CRM_BASE_URL
                  value: 'https://vitadairy--loyalty.sandbox.my.salesforce.com'
                - name: CRM_CHECK_LEAD_BY_PHONE_API
                  value: '/services/apexrest/GMS_SF/sfdc_hook/vd_landing_page'
                - name: CRM_CONVERT_LEAD_API
                  value: '/services/apexrest/api/lead/convert'
                - name: CRM_CONVERT_LEAD_BY_PHONE_API
                  value: '/services/apexrest/api/ConvertLead/'
                - name: CRM_INSERT_TRANSACTION_API
                  value: '/services/apexrest/api/Insert_Transaction'
                - name: CRM_AUTH_API
                  value: '/services/oauth2/token'
                - name: CRM_AUTH_GRANT_TYPE
                  value: 'refresh_token'
                - name: CRM_AUTH_CLIENT_ID
                  value: '3MVG9pgcF_Z64XngA5TbXSTGp5axZzDVbhu7W59kYO4Sdfie2CdfXz9xDdGnLDBbkKk9GBPI4HlFSPNhgcU56'
                - name: CRM_AUTH_CLIENT_SECRET
                  value: '****************************************************************'
                - name: CRM_AUTH_USER_NAME
                  value: '<EMAIL>'
                - name: CRM_AUTH_PASSWORD
                  value: 'Vita@2020VSOcHM9rHzlXcZxP2HMVbSi0R'
                - name: CRM_AUTH_PROGRAM_NAME
                  value: 'a0cBS000000IosnYAC'
                - name: CRM_REDEEM_GIFT_TRANSACTION_API
                  value: '/services/apexrest/api/transaction/sendGift/'
                - name: CRM_REDEEM_GIFT_TRANSACTION_V2_API
                  value: '/services/apexrest/api/transaction/sendGift/'
                - name: CRM_REDEEM_VOUCHER_TRANSACTION_API
                  value: '/services/apexrest/api/transaction/sendVourcher/'
                - name: CRM_REFRESH_TOKEN
                  value: '***************************************************************************************'
                - name: CRM_CHECK_ACCESS_TOKEN_API
                  value: '/services/oauth2/introspect'
                - name: CRM_AUTH_CREATE_ACCESS_TOKEN_GRANT_TYPE
                  value: 'password'
                - name: CRM_CALL_API_TIMEOUT
                  value: '300000'
                - name: CRM_SYNC_USING_WH
                  value: 'false'
                - name: FORCE_INVALID_TOKEN_RESPONSE_SYNC_CRM_OUTBOX
                  value: '-1'
                - name: VITA_GO_BASE_URL
                  value: 'https://api-sandbox.vitadairyvietnam.vn/api'
                - name: VITA_GO_ADD_PLAYGAME_TIMES_API
                  value: '/game/add-times'
                - name: VITA_GO_SAVE_EXTERNAL_API
                  value: '/logging/save-external-api'
                - name: VITA_JAVA_NOTIFCATION_BASE_URL
                  value: 'http://notification-service'
                - name: VITA_JAVA_NOTIFCATION_PUSH_NOTI_API
                  value: '/api/notification/push-notification'
                - name: VITA_JAVA_APPLICATION_BASE_URL
                  value: 'https://api-sandbox.vitadairyvietnam.vn'
                - name: VITA_JAVA_APPLICATION_EXCHANGE_GIFT_V2
                  value: '/api/v2/point/gift-exchange'
                - name: POPUP_MUM_TEMPLATE_ID
                  value: '11'
                - name: ES_SPOON_INDEX
                  value: 'vtd.spoonmst'
                - name: ES_SYNC_SF_FAIL_LOGGING_INDEX
                  value: 'vtd.syncsffailedlogging'
                - name: ES_NODE
                  value: 'https://vtd-elasticsearch-es-http.vtd-elasticsearch:9200'
                - name: ES_USERNAME
                  value: 'elastic'
                - name: ES_PASSWORD
                  value: 'FluE0S115gApAjavZ5269R75'
                - name: ES_EXCHANGE_GIFT_LOGGING_INDEX
                  value: 'vtd.exchangegiftlogging'
                - name: ES_EXCHANGE_GIFT_LOGGING_MONTH_KEEP
                  value: '3'
                - name: VITA_SPOON_BASE_URL
                  value: 'https://api-uat.vitadairyvietnam.vn'
                - name: VITA_SPOON_AUTHENTICATE
                  value: '/api/authenticate'
                - name: VITA_SPOON_GET_ACCOUNT_BY_ID
                  value: '/api/account'
                - name: VITA_CHECK_EVENT_Q2
                  value: '267'
                - name: VITA_CHECK_EVENT_Q2_TC
                  value: '286,287,288'
                - name: VITA_CHECK_EVENT_Q2_CBB
                  value: '289'
                - name: VGS_BASE_URL
                  value: 'https://uat-plugin.linkvn.org'
                - name: CRM_REDEEM_STORE_API
                  value: '/api/teso/send-user-info'
                - name: WEBHOOK_VGS_ACCESS_TOKEN
                  value: 'REWARDS@2023VgsVitadairydcfsf15dhbRAD'
                - name: GMAIL
                  value: '<EMAIL>'
                - name: GMAIL_PASS
                  value: 'ooikkohtvarbjvlm'
                - name: NEW_RELIC_APP_NAME
                  value: 'VTD Micro Dev'
                - name: NEW_RELIC_LICENSE_KEY
                  value: 'eu01xx3cc9eb1fae5f97abe74e6a89daFFFFNRAL'
                - name: BEEKIDS_BASE_URL
                  value: 'https://dev-api.beekids.edu.vn'
                - name: BEEKIDS_GET_COURSES
                  value: '/bkids_courses'
                - name: BEEKIDS_GET_TRAINING_PATHS
                  value: '/trainings/paths'
                - name: BEEKIDS_KEY_ID
                  value: '49984K0SCOAWBMF30QJMPF1U'
                - name: BEEKIDS_KEY_SECRET
                  value: 'MdWvwS-dNlufq1ux-2hCFEjz9EdoM5LLdMKcLeL2gtghofsvLlwzVGmKMkciOQgj'
                - name: VGS_TOKEN
                  value: '5c4f7493-db95-485d-bce8-f87b0890ff29'
                - name: VGS_USERNAME
                  value: 'vitadairy_test'
                - name: VGS_ZNS_OA_ID
                  value: '2601380577952053454'
                - name: VGS_SMS_BASE_URL
                  value: 'https://cloudsms.vietguys.biz:4438/api'
                - name: VGS_ZALO_BASE_URL
                  value: 'https://cloud.vietguys.biz:4438/api/zalo/v1/send'
                - name: VGS_CSKH_SMS_URL
                  value: '/index.php'
                - name: VGS_TEMPLATE_OTP_ID
                  value: '261362'
                - name: VGS_BRANCH_NAME
                  value: 'VitaDairy'
                - name: VITA_CHECK_EVENT_Q3_CBB
                  value: '295'
                - name: VITA_CHECK_EVENT_Q3_DHA
                  value: '300'
                - name: VITA_CHECK_EVENT_Q3_OPTI
                  value: '301'
                - name: VITA_CHECK_EVENT_PRODUCT_01
                  value: '299,298,297,296'
                - name: VITA_CHECK_EVENT_DHA_OPTI
                  value: '300,301'
                - name: APP_VERSION_NAME
                  value: '3.0.24'
                - name: VITA_CHECK_EVENT_Q3_CLG
                  value: '302'
                - name: VITA_EVENT_QUY1_2024_MAY_RUI_ID
                  value: '500'
                - name: VITA_EVENT_THANG5_2024_BIG_C
                  value: '581'
                - name: DB_HOST_PHYSICAL
                  value: '************'
                - name: DB_PORT_PHYSICAL
                  value: '1439'
                - name: DB_USERNAME_PHYSICAL
                  value: 'radb'
                - name: DB_PASSWORD_PHYSICAL
                  value: '#Vitadairy@2023'
                - name: DB_DATABASE_PHYSICAL
                  value: 'RA_BK'
                - name: APP_VERSION_NAME
                  value: '3.0.24 (8)'
                - name: VITA_CHECK_EVENT_Q3_OGGI
                  value: '303'
                - name: VITA_CHECK_EVENT_Q3_CLS
                  value: '304'
                - name: ES_LOG_INDEX
                  value: 'log'
                - name: CRM_ACCESS_TOKEN
                  value: '00DBS000000TzMf!AQEAQLgrYoIK5vXoUOCqRtQXUnv_2WKJ6zMqgNWroX5vbiLBwAm0tvmuZxfF2N56pWUeHuilc._37gQNgqgnA3HTaZXFI4L8'
                - name: EVENT_4_LON_GOOGLE_SHEET_CLIENT_EMAIL
                  value: '<EMAIL>'
                - name: EVENT_4_LON_GOOGLE_SHEET_PRIVATE_KEY
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
                - name: EVENT_4_LON_GOOGLE_SHEET_SPREAD_SHEET_ID
                  value: '1ezZC_Izutt9m5VuZl8f0b2iTGVdOFYYnYzfdgBySUk0'
                - name: EVENT_4_LON_GOOGLE_SHEET_APIS_SCOPES
                  value: 'https://www.googleapis.com/auth/spreadsheets'
                - name: EVENT_4_LON_GOOGLE_SHEET_SHEET_NAME
                  value: 'Sheet1'
                - name: EVENT_4_LON_GOOGLE_SHEET_START_COLUMN
                  value: 'B'
                - name: EVENT_4_LON_GOOGLE_SHEET_END_COLUMN
                  value: 'I'
                - name: HOTLINE_TOKEN
                  value: 'hotline'
                - name: CRM_GET_LEAD_INFO_API
                  value: '/services/apexrest/api/lead/getbyid/'
                - name: CRM_CREATE_CASE_URL
                  value: '/services/data/v56.0/sobjects/Case/'
                - name: CRM_ADD_POINT_URL
                  value: 'https://vita-cs.dgvdigital.net/'
                - name: CRM_CREATE_CASE_OWNER_ID
                  value: '00GBS000000F8wX2AS'
                - name: VITA_CHECK_EVENT_Q4_DHA
                  value: '313'
                - name: VGS_TEMPLATE_REMIND_DOWN_TIER_RANK_AFTER_180_DAYS_ID
                  value: '280552'
                - name: VGS_TEMPLATE_REMIND_DOWN_TIER_RANK_AFTER_365_DAYS_ID
                  value: '280553'
                - name: VITA_CHECK_EVENT_Q4_CBB
                  value: '315'
                - name: VITA_CHECK_EVENT_Q4_X2_XU
                  value: '316'
                - name: USE_NOTI_V3
                  value: 'true'
                - name: KAFKA_BROKER
                  value: vtd-kafka-cluster-kafka-brokers.kafka:9092
                - name: OUTBOX_FOURTH_RETRY_DELAY_TIME
                  value: '60000'
                - name: EVOUCHER_VACXIN_GOOGLE_SHEET_SPREAD_SHEET_ID
                  value: '1M4bnTeGyU_Opqy_L8W8el6gLMeV02j1uP6U38LpRU1E'
                - name: EVOUCHER_VACXIN_GOOGLE_SHEET_SHEET_NAME
                  value: 'Sheet1'
                - name: EVOUCHER_VACXIN_GOOGLE_SHEET_START_COLUMN
                  value: 'B'
                - name: EVOUCHER_VACXIN_GOOGLE_SHEET_END_COLUMN
                  value: 'J'
                - name: CHATBOT_TOKEN
                  value: 'P7wXeA9vG3ZcB2R6jYKq'
                - name: CRM_SYNC_NOTI_STATUS_FULL_URL
                  value: https://ca-vita.herokuapp.com/webhook/apppush
                - name: CRM_SYNC_NOTI_STATUS_BEARER_TOKEN
                  value: zo4dpOpUqFDxoAHu99A5r00M3JNJh8yf
                - name: RUNNING_AS_CRONJOB
                  value: 'true'
                - name: TRACKING_USER_ACTION_BATCH_SIZE
                  value: '1500'
                - name: TRACKING_USER_ACTION_BATCH_CYCLE
                  value: '24'
                - name: TRACKING_USER_ACTION_MAX_ITERATIONS
                  value: '10'
                - name: TRACKING_USER_ACTION_DELETE_AFTER_DAYS
                  value: '30'
              ports:
                - containerPort: 5000
              command:
                - yarn
                - cron-job
                - cron-job-cleanup-tracking-actions
          restartPolicy: Never
