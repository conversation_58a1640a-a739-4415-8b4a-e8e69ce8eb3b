# apiVersion: batch/v1
# kind: CronJob
# metadata:
#   name: loyalty-cron-job-remind-down-rank-after-180days
# spec:
#   schedule: '0 */12 * * * '
#   concurrencyPolicy: 'Forbid'
#   successfulJobsHistoryLimit: 0
#   failedJobsHistoryLimit: 0
#   jobTemplate:
#     spec:
#       backoffLimit: 0
#       template:
#         spec:
#           tolerations:
#             - key: 'name'
#               value: 'cron-services'
#               operator: 'Equal'
#               effect: 'NoSchedule'
#           affinity:
#             nodeAffinity:
#               requiredDuringSchedulingIgnoredDuringExecution:
#                 nodeSelectorTerms:
#                   - matchExpressions:
#                       - key: name
#                         operator: In
#                         values:
#                           - cron-services
#           containers:
#             - image: asia-southeast1-docker.pkg.dev/spartan-impact-319504/vtd-dev/vtd-service-loyalty-v3:latest
#               name: loyalty-cron-job-remind-down-rank-after-180days
#               env:
#                 - name: LOYALTY_SERVICE_URL
#                   value: '0.0.0.0'
#                 - name: LOYALTY_SERVICE_PORT
#                   value: '8889'
#                 - name: DB_HOST
#                   value: '************'
#                 - name: DB_PORT
#                   value: '5432'
#                 - name: DB_USERNAME
#                   value: 'postgres'
#                 - name: DB_PASSWORD
#                   value: 'k0aoQ0o06cTaO6kgWg8f0GUsPzLbvG+fQ7hO/S3hnde+Pgz/RiuDMS9rcuNHOtVB'
#                 - name: DB_DATABASE
#                   value: 'vitadairy'
#                 - name: REDIS_HOST
#                   value: 'redis'
#                 - name: REDIS_PORT
#                   value: '80'
#                 - name: GCP_CREDENTIALS_PATH
#                   value: 'configs/credentials/spartan-impact-credentials.json'
#                 - name: PROJECT_ID
#                   value: 'spartan-impact-319504'
#                 - name: PRIVATE_KEY
*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
#                 - name: CLIENT_EMAIL
#                   value: '<EMAIL>'
#                 - name: STORAGE_MEDIA_BUCKET
#                   value: 'vitadairy_public_dev'
#                 - name: NODE_ENV
#                   value: 'development'
#                 - name: NOTIFICATION_BASE_URL
#                   value: 'http://notification-service'
#                 - name: NOTIFICATION_PUSH_NOTIFICATION
#                   value: '/api/notification/push-notification'
#                 - name: BRAVO_SECRET_KEY
#                   value: 'REWARDS@2021BravoVitadairydSfdf29dsdRTW'
#                 - name: BRAVO_PARTNER
#                   value: 'REWARDS'
#                 - name: BRAVO_BASE_URL
#                   value: 'https://qrvitadairy.vn:60443/api/v2'
#                 - name: BRAVO_FUNC_GET_INFO
#                   value: 'GetQrInfoEx'
#                 - name: CRM_BASE_URL
#                   value: 'https://vitadairy--uat.sandbox.my.salesforce.com'
#                 - name: CRM_CHECK_LEAD_BY_PHONE_API
#                   value: '/services/apexrest/GMS_SF/sfdc_hook/vd_landing_page'
#                 - name: CRM_CONVERT_LEAD_API
#                   value: '/services/apexrest/api/lead/convert'
#                 - name: CRM_CONVERT_LEAD_BY_PHONE_API
#                   value: '/services/apexrest/api/ConvertLead/'
#                 - name: CRM_INSERT_TRANSACTION_API
#                   value: '/services/apexrest/api/Insert_Transaction'
#                 - name: CRM_AUTH_API
#                   value: '/services/oauth2/token'
#                 - name: CRM_AUTH_GRANT_TYPE
#                   value: 'password'
#                 - name: CRM_AUTH_CLIENT_ID
#                   value: '3MVG9Po2PmyYruumJguYPG0Y2AX2zvAGhtSvqCMq9Kisj0eBVK0.LXGFFt7qt2g1Xs6.FdFtW1XBNVfz1Jgmr'
#                 - name: CRM_AUTH_CLIENT_SECRET
#                   value: '****************************************************************'
#                 - name: CRM_AUTH_USER_NAME
#                   value: '<EMAIL>'
#                 - name: CRM_AUTH_PASSWORD
#                   value: 'Abcd1234!WocpID9I7IPTJk5Zvv4lA8RY4'
#                 - name: CRM_AUTH_PROGRAM_NAME
#                   value: 'a0U0T000000PjVlUAK'
#                 - name: CRM_REDEEM_GIFT_TRANSACTION_API
#                   value: '/services/apexrest/api/transaction/sendGift/'
#                 - name: CRM_REDEEM_VOUCHER_TRANSACTION_API
#                   value: '/services/apexrest/api/transaction/sendVourcher/'
#                 - name: VITA_GO_BASE_URL
#                   value: 'https://api-sandbox.vitadairyvietnam.vn/api'
#                 - name: VITA_GO_ADD_PLAYGAME_TIMES_API
#                   value: '/game/add-times'
#                 - name: VITA_GO_SAVE_EXTERNAL_API
#                   value: '/logging/save-external-api'
#                 - name: VITA_JAVA_NOTIFCATION_BASE_URL
#                   value: 'http://notification-service'
#                 - name: VITA_JAVA_NOTIFCATION_PUSH_NOTI_API
#                   value: '/api/notification/push-notification'
#                 - name: POPUP_MUM_TEMPLATE_ID
#                   value: '11'
#                 - name: ES_SPOON_INDEX
#                   value: 'vtd.spoonmst'
#                 - name: ES_NODE
#                   value: 'https://vtd-elasticsearch-es-http.vtd-elasticsearch:9200'
#                 - name: ES_USERNAME
#                   value: 'elastic'
#                 - name: ES_PASSWORD
#                   value: 'FluE0S115gApAjavZ5269R75'
#                 - name: VITA_SPOON_BASE_URL
#                   value: 'https://api-uat.vitadairyvietnam.vn'
#                 - name: VITA_SPOON_AUTHENTICATE
#                   value: '/api/authenticate'
#                 - name: VITA_SPOON_GET_ACCOUNT_BY_ID
#                   value: '/api/account'
#                 - name: VITA_CHECK_EVENT_Q2
#                   value: '267'
#                 - name: VITA_CHECK_EVENT_Q2_TC
#                   value: '286,287,288'
#                 - name: VITA_CHECK_EVENT_Q2_CBB
#                   value: '289'
#                 - name: VGS_BASE_URL
#                   value: 'https://uat-plugin.linkvn.org'
#                 - name: CRM_ACCESS_TOKEN
#                   value: '9a58a283-128c-48ab-87e5-2c5d90d9c3bb'
#                 - name: CRM_REDEEM_STORE_API
#                   value: '/api/teso/send-user-info'
#                 - name: WEBHOOK_VGS_ACCESS_TOKEN
#                   value: 'REWARDS@2023VgsVitadairydcfsf15dhbRAD'
#                 - name: GMAIL
#                   value: '<EMAIL>'
#                 - name: GMAIL_PASS
#                   value: 'ooikkohtvarbjvlm'
#                 - name: DB_HOST_PHYSICAL
#                   value: '************'
#                 - name: DB_PORT_PHYSICAL
#                   value: '1439'
#                 - name: DB_USERNAME_PHYSICAL
#                   value: 'radb'
#                 - name: DB_PASSWORD_PHYSICAL
#                   value: '#Vitadairy@2023'
#                 - name: DB_DATABASE_PHYSICAL
#                   value: 'RA_BK'
#                 - name: VGS_TOKEN
#                   value: '5c4f7493-db95-485d-bce8-f87b0890ff29'
#                 - name: VGS_USERNAME
#                   value: 'vitadairy_test'
#                 - name: VGS_ZNS_OA_ID
#                   value: '2601380577952053454'
#                 - name: VGS_SMS_BASE_URL
#                   value: 'https://cloudsms.vietguys.biz:4438/api'
#                 - name: VGS_ZALO_BASE_URL
#                   value: 'https://cloud.vietguys.biz:4438/api/zalo/v1/send'
#                 - name: VGS_CSKH_SMS_URL
#                   value: '/index.php'
#                 - name: VGS_TEMPLATE_OTP_ID
#                   value: '261362'
#                 - name: VGS_BRANCH_NAME
#                   value: 'VitaDairy'
#                 - name: VGS_TEMPLATE_REMIND_DOWN_TIER_RANK_AFTER_180_DAYS_ID
#                   value: '280552'
#                 - name: VGS_TEMPLATE_REMIND_DOWN_TIER_RANK_AFTER_365_DAYS_ID
#                   value: '280553'
#                 - name: USE_NOTI_V3
#                   value: 'true'
#                 - name: KAFKA_BROKER
#                   value: vtd-kafka-cluster-kafka-brokers.kafka:9092
#                 - name: RUNNING_AS_CRONJOB
#                   value: 'true'
#               ports:
#                 - containerPort: 5000
#               command:
#                 - yarn
#                 - cron-job
#                 - loyalty-cron-job-remind-down-rank-after-180days
#           restartPolicy: OnFailure
